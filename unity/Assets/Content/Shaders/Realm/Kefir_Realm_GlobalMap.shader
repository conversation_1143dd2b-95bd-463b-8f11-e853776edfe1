Shader "Kefir/Kefir_Realm_GlobalMap"
{
    Properties
    {
        _TintColor("Tint Color",Color) = (1,1,1,1) 
        [NoScaleOffset]_BaseMap("Base Color Map", 2D) = "white" {}
        [NoScaleOffset]_BaseMapCity ("Base Color Map City", 2D) = "black" {}
        [NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        _Metallic("Metallic",Range(0,1)) = 0
        
        [Space()]
        [Header(Water)]
        [NoScaleOffset]_WaterMaskMap("Water Mask",2D) = "white" {}
		_WaterMaskMultiplier("Water Mask Multiplier", Range(0, 1)) = 1
        [NoScaleOffset]_WaterBumpMap("Water Normal Map",2D) = "bump" {}
        _WaterBumpMapStrength("Water Normal Strength",Range(0,1)) = 1
        _SampleBumpMapParameters01("Sample Normal Parameteres 01 (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _SampleBumpMapParameters02("Sample Normal Parameteres 02 (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _SampleBumpMapParameters03("Sample Normal Parameteres 03 (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        
        [Space()]
        [Header(Water Fresnel)]
        _WaterFresnelPower("Water Fresnel Power", Float) = 1
        _WaterFresnelColor("Water Fresnel Color",Color) = (1,1,1,1)
        _WaterShadowEnlighting("Water Shadow Enlighting", Range(0,1)) = 0
        
        [Space()]
        [Header(Sky Water Reflection)]
        [NoScaleOffset]_SkyMap("Sky Map", Cube) = "black" {}
        _SkyMapIntensity("Sky Map Intensity",Range(0,1)) = 0.5
        _SkyMapRotation("Sky Map Rotation",Range(0,360)) = 0
        
        [Space()]
        [Header(Clouds)]
        _CloudsShadowIntensity("Clouds Shadow Intensity", Float) = 1
        _CloudsShadowPower("Clouds Shadow Power", Float) = 1
        _CloudsShadowColor("Clouds Shadow Color", Color) = (0,0,0,0)
        _CloudsMapSize("Clouds Map Size", Float) = 112
        
        [Space()]
        [Header(Fog)]
        _FogColor("Fog Color", Color) = (1,1,1,1)
        
        [Space()]
        _FogVerticalIntensity("Vertical Intencity",Range(0,1)) = 0.5
        _FogVerticalMaxHeight("Vertical Max Height",Range(-5,10)) = 3
        _FogVerticalMinHeight("Vertical Min Height",Range(-5,10)) = -1
        _FogVerticalPower("Vertical Power",Range(0,10)) = 1
        
        [Space()]
        _FogHorizontalIntensity("Horizontal Intencity",Range(0,1)) = 0.5
        _FogHorizontalMaxHeight("Horizontal Max Depth",Range(-5,10000)) = 3
        _FogHorizontalMinHeight("Horizontal Min Depth",Range(-5,10000)) = -1
        _FogHorizontalPower("Horizontal Power",Range(0,10)) = 1
    }
    
    SubShader
    {
        Tags { "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry" }

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BaseMapCity);
        SAMPLER(sampler_BaseMapCity);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_WaterMaskMap);
        SAMPLER(sampler_WaterMaskMap);
        TEXTURE2D(_WaterBumpMap);
        SAMPLER(sampler_WaterBumpMap);
        TEXTURE2D(_WaterFlowMap);
        SAMPLER(sampler_WaterFlowMap);
        TEXTURECUBE(_SkyMap);
        SAMPLER(sampler_SkyMap);
        TEXTURE2D(_CloudsShadowMap);
        SAMPLER(sampler_CloudsShadowMap);
        
        CBUFFER_START(UnityPerMaterial)
            float4 _SampleBumpMapParameters01;
            float4 _SampleBumpMapParameters02;
            float4 _SampleBumpMapParameters03;
            float _FogVerticalIntensity;
            float _FogVerticalMaxHeight;
            float _FogVerticalMinHeight;
            float _FogVerticalPower;

            float _FogHorizontalIntensity;
            float _FogHorizontalMaxHeight;
            float _FogHorizontalMinHeight;
            float _FogHorizontalPower;
            float _SkyMapRotation;
            float _CloudsMapSize;

            half3 _TintColor;
            half3 _WaterFresnelColor;
            half3 _CloudsShadowColor;
            half4 _FogColor;
        
            half _Metallic;
            half _WaterBumpMapStrength;
            half _WaterMaskMultiplier;
            half _WaterFresnelPower;
            half _WaterShadowEnlighting;
            half _SkyMapIntensity;
            half _CloudsShadowIntensity;
            half _CloudsShadowPower;
        CBUFFER_END

        float2 CalculateUV(float2 uv, float4 parameters)
        {
            float2 offset = parameters.zw * _Time.y * parameters.y;
            float2 scale = uv * parameters.x;
            return scale + offset;
        }
        
        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            float waterMask = SAMPLE_TEXTURE2D(_WaterMaskMap, sampler_WaterMaskMap, snData.uv).r;
            
            half3 normalTS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap,sampler_BumpMap, snData.uv));
            half3 ripples01 = UnpackNormal(SAMPLE_TEXTURE2D(_WaterBumpMap, sampler_WaterBumpMap, CalculateUV(snData.uv, _SampleBumpMapParameters01)));
            half3 ripples02 = UnpackNormal(SAMPLE_TEXTURE2D(_WaterBumpMap, sampler_WaterBumpMap, CalculateUV(snData.uv, _SampleBumpMapParameters02)));
            half3 ripples03 = UnpackNormal(SAMPLE_TEXTURE2D(_WaterBumpMap, sampler_WaterBumpMap, CalculateUV(snData.uv, _SampleBumpMapParameters03)));
            half3 normalMapWater = normalize(ripples01 + ripples02 + ripples03);
            normalMapWater = lerp(normalTS, normalMapWater, _WaterBumpMapStrength);
            return lerp(normalTS, normalMapWater, waterMask);
        }
        ENDHLSL
        
        Pass
        {
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float4 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                DECLARE_SH(sh, 4)
            };

            float3 RotateAroundYInDegrees (float3 vertex, float degrees)
            {
                float alpha = degrees * 3.1415 / 180.0;
                float sina, cosa;
                sincos(alpha / 2, sina, cosa);
                float3x3 m = float3x3(cosa, 0, sina, 0, 1, 0, -sina, 0, cosa);
                float3 r = float3(mul(m, vertex.xyz)).rgb;
                return r;
            }
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                
                o.uv = float4(i.uv0, i.uv1);

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)

                return o;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                float2 uv0 = i.uv.xy;
                float2 uv1 = i.uv.zw;
                
                // Textures.
                half3 albedo = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uv0).rgb;
                half4 cityAlbedo = SAMPLE_TEXTURE2D(_BaseMapCity, sampler_BaseMapCity, uv1);
                half3 cityColor = cityAlbedo.rgb;
                half cityAlpha = cityAlbedo.a; 
                albedo = lerp(albedo, cityColor, cityAlpha) * _TintColor;
                
                half metallic = _Metallic;
                half ao = 1;
                
				half waterMask = SAMPLE_TEXTURE2D(_WaterMaskMap, sampler_WaterMaskMap, uv0).r * _WaterMaskMultiplier * (1 - cityAlpha);

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(uv0));
                float3 normalWS = TransformUnpackedToWorldNormal(normalTS, tbn);

                // Init BRDFData.
                BRDFData brdfData = (BRDFData)0;
                float alpha = 1;
                
                float smoothness = waterMask;
                if (smoothness > 0)
                    smoothness = 1;
                
                InitializeBRDFData(albedo, metallic, 0, smoothness, alpha, brdfData);
                
                // Direct Specular + Diffuse.
                half3 viewDirectionWS = normalize(_WorldSpaceCameraPos.xyz - positionWS.xyz);
                Light mainLight = GetMainLight(TransformWorldToShadowCoord(positionWS), positionWS, 0);
                mainLight.shadowAttenuation = 1;
                
                float3 direct = LightingPhysicallyBased(brdfData, mainLight, normalWS, viewDirectionWS);
                direct = lerp(albedo, direct, waterMask);
                
                // Indirect Specular + Diffuse (GI).
                half3 sh = COMBINE_VERTEX_FRAGMENT_SH(normalWS, i.sh);
                MixRealtimeAndBakedGI(mainLight, normalWS, sh, 1);
                half3 gi = GlobalIllumination(brdfData, sh, ao, positionWS, normalWS, viewDirectionWS);

                // Water Fresnel.
                half3 skymapReflection = RotateAroundYInDegrees(-reflect(normalWS, viewDirectionWS), _SkyMapRotation);
                half3 cloudsReflection = lerp(SAMPLE_TEXTURECUBE(_SkyMap, sampler_SkyMap, skymapReflection).rgb, albedo, _SkyMapIntensity) * waterMask;

                float2 positionNDC = GetNormalizedScreenSpaceUV(i.positionCS.xy);
                half3 waterFresnel = PositivePow(positionNDC.y, _WaterFresnelPower) * waterMask * _WaterFresnelColor;

                gi = lerp(gi, cloudsReflection + waterFresnel, waterMask);
                
                // Clouds Shadow.
                float2 cloudsShadowUV = (positionWS.xz + _CloudsMapSize) / (2 * _CloudsMapSize);
                float cloudShadow = 1 - saturate(PositivePow(_CloudsShadowIntensity * SAMPLE_TEXTURE2D(_CloudsShadowMap, sampler_CloudsShadowMap, cloudsShadowUV).r, _CloudsShadowPower));
                
                cloudShadow = saturate(lerp(cloudShadow, cloudShadow + _WaterShadowEnlighting, waterMask));
                
                half3 cloudShadowColor = lerp(_CloudsShadowColor, 1, cloudShadow);

                // Fog.
                float fogVertical = saturate(InverseLerp(_FogVerticalMinHeight, _FogVerticalMaxHeight, positionWS.y));
                fogVertical = saturate(PositivePow(fogVertical, _FogVerticalPower) * _FogVerticalIntensity);

                float2 fogHorizontalVector = positionWS.xz - _WorldSpaceCameraPos.xz;
                float fogHorizontalDistance = dot(fogHorizontalVector, fogHorizontalVector);
                float fogHorizontal = saturate(InverseLerp(_FogHorizontalMinHeight, _FogHorizontalMaxHeight, fogHorizontalDistance));
                fogHorizontal = saturate(PositivePow(fogHorizontal, _FogHorizontalPower) * _FogHorizontalIntensity);

                float fogFactor = fogVertical + fogHorizontal;
                
                // Combining.
                half3 model = direct * cloudShadowColor + gi;
                half3 color = lerp(model, _FogColor.rgb, fogFactor);
                return half4(color, 1);
            }
            ENDHLSL
        }
        
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #define _USE_NORMAL_MAP 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }
    }
}
