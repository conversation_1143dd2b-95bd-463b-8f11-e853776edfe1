Shader "Kefir_Decor_FarLods"
{
    Properties
    { 
        [Header(Textures)]
        [NoScaleOffset]_BaseMap("Base Color Map", 2D) = "white" {}
        [NoScaleOffset]_MaskMap("MSA Map", 2D) = "white" {}
        [NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        
        [Header(Alpha Clipping)]
        [Toggle(_ALPHATEST_ON)] _AlphaClipping("Alpha Clipping", Float) = 0
        _Cutoff("Alpha Clip Threshold", Range(0,1)) = 0.5
    } 
    
    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry-96"
        }        
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_MaskMap);
        SAMPLER(sampler_MaskMap);
        
        CBUFFER_START(UnityPerMaterial)
            half _Cutoff;
        CBUFFER_END
        
        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            return UnpackNormalScale(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv), 1);
        }
        ENDHLSL

        Pass
        {
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #pragma shader_feature_local_fragment _ALPHATEST_ON

            #define _REFLECTION_PROBES_NO_BLENDING 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                DECLARE_SH(sh, 4)
            };
            
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.uv = i.uv;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                #if defined(LOD_FADE_CROSSFADE)
		            LODFadeCrossFade(i.positionCS);
                #endif

                half4 baseColor = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv).rgba;
                
                #ifdef _ALPHATEST_ON
                    clip(baseColor.a - _Cutoff);
                #endif
                
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                half3 msa = SAMPLE_TEXTURE2D(_MaskMap, sampler_MaskMap, i.uv).rgb;
                
                SurfaceData surfaceData = InitializeSurfaceData(baseColor, msa, KefirSampleNormalTS(InitializeSampleNormalData(i.uv)));
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #define _USE_NORMAL_MAP 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "Object Motion Vectors"
            Tags
            {
                "LightMode" = "MotionVectors"
            }

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ObjectMotionVectors.hlsl"
            ENDHLSL
        }
    }
}
