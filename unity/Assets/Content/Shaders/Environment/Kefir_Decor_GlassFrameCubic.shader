Shader "Kefir/Kefir_Decor_GlassFrameCubic"
{
    Properties
    {
        _BumpMap("Normal Map", 2D) = "white" {}
        _Color("Color", Color) = (1, 1, 1, 1)
        _Metallic("Metallic", Float) = 1
        _Smoothness("Smoothness", Float) = 1
        _BumpMapStrength("Normal Strength", Float) = 0.27
        _FrameBox("Frame Box", Vector) = (1,1,0,0)
        _OffsetFromCorner("Offset From Corner", Float) = 0
        _NormalRandomMin("Normal Random Min", Float) = -0.4
        _NormalRandomMax("Normal Random Max", Float) = 0.4
        _DebrisPositionScale("Debris Position Scale", Float) = 12
    }
    SubShader
    {
        Tags
        {
            "RenderPipeline" = "UniversalPipeline" "RenderType" = "Transparent" "Queue" = "Transparent"
        }
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);

        CBUFFER_START(UnityPerMaterial)
            float4 _BumpMap_ST;
            float3 _FrameBox;
            float _OffsetFromCorner;
            float _NormalRandomMin;
            float _NormalRandomMax;
            float _DebrisPositionScale;
            half4 _Color;
            half _BumpMapStrength;
            half _Metallic;
            half _Smoothness;
        CBUFFER_END
        ENDHLSL

        Pass
        {
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off
            Cull Off
            ZTest LEqual

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #define _REFLECTION_PROBES_NO_BLENDING 1
            #define _SURFACE_TYPE_TRANSPARENT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Macros.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"

            struct Attributes
            {
                float3 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                float2 uv2 : TEXCOORD2;
                float4 vertexColor : COLOR0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                float3 initialPos : TEXCOORD4;
                DECLARE_SH(sh, 5)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            struct PositionNormal
            {
                float3 position;
                float3 normal;
            };

            Varyings vert(Attributes i)
            {
                Varyings o;
                o.uv0 = TRANSFORM_TEX(i.uv0, _BumpMap);

                float3 initialPos = i.positionOS;
                float3 vertexOffset = float3(i.uv2.x - 0.5, i.uv1.y - 0.5, i.uv2.y - 0.5);
                initialPos += vertexOffset * _FrameBox;
                float3 vertexColorOffset = i.vertexColor.rgb - 0.5;
                initialPos += _FrameBox * (vertexColorOffset * float3(-1, 1, 1) * _OffsetFromCorner);
                o.initialPos = initialPos;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(initialPos);
                o.positionCS = vertexInputs.positionCS;
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)

                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                float3 halfFrameBox = _FrameBox / float3(2, 2, 2);
                halfFrameBox += 0.003;

                // Clip all pixels that outside glass box.
                if (i.initialPos.x > halfFrameBox.x || i.initialPos.x < -halfFrameBox.x || i.initialPos.y > halfFrameBox.y || i.initialPos.y < -halfFrameBox.y || i.initialPos.z > halfFrameBox.z || i.
                    initialPos.z < -halfFrameBox.z)
                {
                    clip(-1);
                }

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                half3 normalTS = UnpackNormalScale(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, i.uv0), _BumpMapStrength);

                SurfaceData surfaceData = InitializeSurfaceData(_Color, half3(_Metallic, _Smoothness, 1), normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
    }
}