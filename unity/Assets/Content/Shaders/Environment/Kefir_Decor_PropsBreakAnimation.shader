Shader "Kefir/Kefir_Decor_PropsBreakAnimation"
{
    Properties
    {
        _Scale("Scale", Float) = 1
        _WeightCoeff("Weight Coefficient", Range(1, 5)) = 1
        [Header(Color)]
        _BaseColor("Base Color (B)", Color) = (1, 1, 1, 1)
        _Base_Color_A("Base Color (A)", Color) = (1, 1, 1, 1)
        [HDR]_EmissionColor("Emission Color", Color) = (1,1,1,1)
        _Emission("Emission", Range(0,1)) = 0
        [Header(Textures)]
        [NoScaleOffset]_BaseMap("Base Map", 2D) = "white" {}
        [NoScaleOffset]_EmissionMap("EmissionMap", 2D) = "white" {}
        _Base_Map_Tiling_Offset("Base Map Tiling Offset", Vector) = (1, 1, 1, 1)
        [NoScaleOffset]_MaskMap("MSA Map", 2D) = "black" {}
        [NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        
        [Header(Direction)]
        [Toggle]_UseCustomCenter("Use Custom Center", Float) = 0
        _CenterCustom ("Center Custom", Vector) = (0,0,0,0)
        _Center("Center", Vector) = (0,0,0,0)
        _CenterHeightOffset("Center Height Offset", Float) = 1
        _HitDirection("Hit Direction", Float) = 1
        _HitVector("Hit Vector", Vector) = (0,0,0,0)
        [Toggle] _UseHitVector("Use Hit Vector", Float) = 0
        
        [Header(Push)]
        _SpreadPower("Spread Power", Range(0, 20)) = 3.47
        _SpreadRadius("Spread Radius", Range(0, 5)) = 0.46
        _PushPower("Push Power", Float) = 0.27
        _PushPowerRadius("Push Power Radius", Range(0.01, 5)) = 1.03
        _AdditionalPushPower("Additional Power", Range(-20,20)) = 0.55
        _AdditionalPushPowerRadius("Additional Power Radius", Range(0,5)) = 0.21
        _VectorGradientPush("Vector Gradient Push", Vector) = (0,1,0,0)
        _PushFalloff("Push Falloff", Range(0, 5)) = 5
        _GradientPush("Vertical Gradient Push", Float) = 0
        _GradientPushRandomMin("Vertical Gradient Push Random Min", Float) = 0
        _GradientPushRandomMax("Vertical Gradient Push Random Max", Float) = 0
        
        [Header(Rotation)]
        _MinRotation("Min Rotation", Float) = 0.5
        _MaxRotation("Max Rotation", Float) = 1     
        _GradientPushRotation("Gradient Push Rotation", Float) = 0 
        _GradientPushAxisRotation("Gradient Push Axis Rotation", Vector) = (1, 1, 1, 1) 
        _GradientPushRotationFalloff("Gradient Push Rotation Falloff", Range(0, 5)) = 5 
               
        [Header(Gravity)]
        _Gravity("Gravity", Float) = 1
        _GravityLate("Gravity Late Gradient", Float) = 1
        _GravityLateGlobal("Gravity Late Global", Float) = 0
        _FloorHeight("Floor Height", Float) = 0
        _FloorScaleReduction("Floor Scale Reduction", Float) = 80
        
        [Header(Time)]
        _TimeSpeed("Time Speed", Float) = 7
        _TimeLateByVectorFactor("Time Late By Vector Factor", Float) = 0
        _TimeLateVector("Time Late Vector", Vector) = (0,0,0,0)
        _SlowMotionWidth("Slow Motion Width", Float) = 1
        _SlowMotionOffset("Slow Motion Offset", Float) = 1
        _SlowMotionAmplitude("Slow Motion Amplitude", Range(0, 5)) = 0
        
        [Header(Debug Time)]
        [Toggle(_USE_DEBUG_TIME)] _UseDebugTime("Use Debug Time", Float) = 0
        _DebugPseudoTime("Pseudo Time", Range(0, 5)) = 1
        _StartTime("Start Time", Float) = 0
        
        [Header(Alpha Clipping)]
        [Toggle(_ALPHATEST_ON)] _AlphaClip("Alpha Clipping", Float) = 0
        _AlphaClipThreshold("Alpha Clip Threshold", Range(0,1)) = 0.5
        [Enum(UnityEngine.Rendering.CullMode)] _Cull("Cull Mode", Int) = 2
    }
    
    SubShader
    {
        Tags
        {
            "RenderPipeline" = "UniversalPipeline" "RenderType" = "Opaque" "Queue" = "Geometry-10"
        }
        
        Cull [_Cull]
                
        HLSLINCLUDE
        
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        #pragma shader_feature_local_vertex _ _USE_DEBUG_TIME
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_EmissionMap);
        TEXTURE2D(_MaskMap);
        TEXTURE2D(_BumpMap);

        CBUFFER_START(UnityPerMaterial)
            float4 _BaseMap_ST;
            float4 _Base_Map_Tiling_Offset;
            float3 _Center;
            float3 _CenterCustom;
            float3 _TimeLateVector;
            float3 _GradientPushAxisRotation;
            float3 _VectorGradientPush;
            float _PushPowerRadius;
            float _Scale;
            float _WeightCoeff;
            float _AdditionalPushPower;
            float _AdditionalPushPowerRadius;
            float _HitDirection;
            float _SpreadPower;
            float _FloorScaleReduction;
            float _SpreadRadius;
            float _FloorHeight;
            float _CenterHeightOffset;
            float _GravityLate;
            float _Gravity;
            float _SlowMotionWidth;
            float _SlowMotionOffset;
            float _SlowMotionAmplitude;
            float _PushPower;
            float _PushFalloff;
            float _MinRotation;
            float _MaxRotation;
            float _GravityLateGlobal;
            float _GradientPush;
            float _TimeSpeed;
            float _StartTime;
            float _GradientPushRandomMin;
            float _GradientPushRandomMax;
            float _GradientPushRotation;
            float _GradientPushRotationFalloff;
            float _TimeLateByVectorFactor;
            float _UseHitVector;
            float _DebugPseudoTime;
        
            half4 _Base_Color_A;
            half4 _BaseColor;
            half3 _EmissionColor;
            half3 _HitVector;
            half _UseCustomCenter;
            half _Emission;
            half _AlphaClipThreshold;
        CBUFFER_END
        
            half3 KefirSampleNormalTS(SampleNormalData snData)
            {
                return UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BaseMap, snData.uv * _Base_Map_Tiling_Offset.xy + _Base_Map_Tiling_Offset.zw));
            }

            struct PositionNormal
            {
                float3 position;
                float3 normal;
            };
            
            float GetProgress()
            {
                float progress = _Time.y - _StartTime;
                #if defined(_USE_DEBUG_TIME)
                    progress = _DebugPseudoTime;
                #endif

                // Gravity Reduction is used to make particles stay longer in peak point https://www.desmos.com/calculator/aymol7oksw?lang=ru
                float gravityReduction = _SlowMotionWidth * progress - _SlowMotionOffset;
                gravityReduction *= gravityReduction;
                gravityReduction = min(1, gravityReduction) * _SlowMotionAmplitude + (1 - _SlowMotionAmplitude);
                
                return progress *= _TimeSpeed * gravityReduction;
            }

            PositionNormal GetAnimatedPositionNormal(float3 positionOS, float4 vertexColor, float3 center, float3 normalOS)
            {
                center.y += _CenterHeightOffset;
                float debrisWeightCoeff = vertexColor.a * _WeightCoeff;
                float3 debrisPos = vertexColor.rgb * 2 - 1;
                debrisPos = float3(-debrisPos.x, debrisPos.y, debrisPos.z) * _Scale;
                float randomSeed = debrisPos.x + debrisPos.y + debrisPos.z;
                
                float distDebris = distance(debrisPos, center);
                float timeLate = dot(debrisPos, normalize(_TimeLateVector)) * _TimeLateByVectorFactor;
                timeLate -= _TimeLateByVectorFactor / 2;
                
                float progress = max(0, GetProgress() - timeLate);
                float3 hitVector = lerp(float3(0, 0, -_HitDirection), _HitVector, _UseHitVector);
                
                float pushMask = max(0, 1 - distDebris / _PushPowerRadius);
                float mainPushPower = pushMask * _PushPower;
                
                float spreadRadius = max(0, 1 - distDebris / _SpreadRadius);
                float3 spreadVelocity = _SpreadPower * (debrisPos - center) * spreadRadius;
            
                float additionalPowerRadius = max(0, 1 - distDebris / _AdditionalPushPowerRadius);
                float3 additionalVelocity = hitVector * (additionalPowerRadius * _AdditionalPushPower);
                float gradientVelocity = dot(debrisPos, normalize(_VectorGradientPush)) * _GradientPush * RandomValue(randomSeed + _StartTime, _GradientPushRandomMin, _GradientPushRandomMax);
                float3 velocity = hitVector * mainPushPower + spreadVelocity + additionalVelocity + float3(0, gradientVelocity, 0);
                
                // Gravity.
                float gravityLate = _GravityLate * RandomValue(randomSeed, 0.5, 2) * debrisPos.y;
                float gravityProgress = max(0, progress - gravityLate - _GravityLateGlobal);
                float gravity = _Gravity * gravityProgress;
                gravity *= -gravityProgress;
                
                float3 gravityVelocity = float3(0, gravity, 0);
                float currentDebrisPosY = (gravityVelocity.y + velocity.y * progress) * debrisWeightCoeff;
                float floorScaleReduction = 1 - (currentDebrisPosY - _FloorHeight);
                floorScaleReduction = 1 - floorScaleReduction / _FloorScaleReduction;

                float3 currentDebrisPos = debrisPos;
                float heightCoeff = saturate(debrisPos.y - _FloorHeight);
                float limitProgress = debrisPos.y + gradientVelocity + (1 - distDebris) + dot(velocity, velocity) - heightCoeff;
                float2 limitDebrisPosXZ = debrisPos.xz + (gravityVelocity.xz + velocity.xz * limitProgress) * debrisWeightCoeff;
                currentDebrisPos += (gravityVelocity + velocity * clamp(progress, 0, _PushFalloff)) * debrisWeightCoeff;
                float rightLimit = min(limitDebrisPosXZ.x, currentDebrisPos.x);
                float leftLimit = max(limitDebrisPosXZ.x, currentDebrisPos.x);
                currentDebrisPos.x = velocity.x <= 0 ? leftLimit : rightLimit;
                float forwardLimit = min(limitDebrisPosXZ.y, currentDebrisPos.z);
                float backLimit = max(limitDebrisPosXZ.y, currentDebrisPos.z);
                currentDebrisPos.z = velocity.z <= 0 ? backLimit : forwardLimit;
                currentDebrisPos.y = max(_FloorHeight, currentDebrisPos.y);
                
                float3 offset = positionOS - debrisPos;

                // Rotation amount.
                float randomRotationPower = RandomValue(randomSeed + _StartTime, _MinRotation, _MaxRotation);
                float gradientRotation = dot(debrisPos, normalize(_VectorGradientPush)) * _GradientPushRotation;
                float isRotationReverted = lerp(-1, 1, vertexColor.a);
                float rotation = ((randomRotationPower + gradientRotation) * sign(velocity.z) * clamp(progress, 0, _GradientPushRotationFalloff)) * debrisWeightCoeff;
                
                float limitRotation = ((randomRotationPower + gradientRotation) * sign(velocity.z) * limitProgress) * debrisWeightCoeff;
                float positiveRotationLimit = min(limitRotation, rotation);
                float negativeRotationLimit = max(limitRotation, rotation);
                rotation = velocity.z <= 0 ? negativeRotationLimit : positiveRotationLimit;

                // Rotation axis.
                float3 rotationAxis = float3(RandomRange(randomSeed), RandomRange(randomSeed - 10), RandomRange(randomSeed + 10));
                rotationAxis *= _GradientPushAxisRotation;
                float3 rotatedOffset = RotateAboutAxis(rotationAxis, rotation, offset);
                
                // Finalizing affine transformations.
                float3 finalPosition = currentDebrisPos + rotatedOffset * saturate(floorScaleReduction);
                float3 finalNormal = RotateAboutAxis(rotationAxis, rotation, normalOS);
                
                PositionNormal result;
                result.position = finalPosition;
                result.normal = finalNormal;
                return result;
            }
        
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #pragma shader_feature_local _IS_BACKFACE_NORMAL_FLIPPED
            #pragma shader_feature_local_fragment _ALPHATEST_ON
            #pragma multi_compile _ LIGHTMAP_SHADOW_MIXING
            #pragma multi_compile _ SHADOWS_SHADOWMASK
            #pragma multi_compile _ DIRLIGHTMAP_COMBINED
            #pragma multi_compile _ LIGHTMAP_ON
            #pragma multi_compile _ _REFLECTION_PROBES_ROTATED

            #if defined(_REFLECTION_PROBES_ROTATED)
                #pragma require cubearray 
            #endif
            
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"

            struct Attributes
            {
                float3 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
                float4 vertexColor : COLOR0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                DECLARE_SH(sh, 4)
                float3 posOS : TEXCOORD5;
                
            };
            
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
     

            Varyings vert(Attributes i)
            {
                Varyings o;
                o.uv = i.uv * _Base_Map_Tiling_Offset.xy + _Base_Map_Tiling_Offset.zw;;
                float3 center = (_UseCustomCenter == 1) ? _CenterCustom : _Center;

                PositionNormal animatedPosNormals = GetAnimatedPositionNormal(i.positionOS.xyz, i.vertexColor, center, i.normalOS);

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(animatedPosNormals.position);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(animatedPosNormals.normal, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                
                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                o.posOS = i.positionOS.xyz;
                
                return o;
            }

            half4 frag(Varyings i, bool facing : FRONT_FACE_SEMANTIC) : SV_Target
            {
                
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                float isFrontFace =  IS_FRONT_VFACE(facing, 1, -1);
                tbn[0] *= isFrontFace;
                tbn[2] *= isFrontFace;

                float2 uv = i.uv;
                
                half4 albedo = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uv);
                
                #ifdef _ALPHATEST_ON
                    clip(albedo.a - _AlphaClipThreshold);
                #endif

                half4 metalnessSmoothnessPaintBA = SAMPLE_TEXTURE2D(_MaskMap, sampler_BaseMap, uv);

                albedo = lerp(albedo, albedo * _BaseColor, metalnessSmoothnessPaintBA.z);
                albedo = lerp(albedo, albedo * _Base_Color_A, metalnessSmoothnessPaintBA.w);
                half3 emission = SAMPLE_TEXTURE2D(_EmissionMap, sampler_BaseMap, uv).rgb;
                emission *= _EmissionColor * _Emission;
                SurfaceData surfaceData = InitializeSurfaceData(albedo, half3(metalnessSmoothnessPaintBA.rg, 1), KefirSampleNormalTS(InitializeSampleNormalData(i.uv)));
                surfaceData.emission = emission;
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #pragma target 2.0
            
            // -------------------------------------
            // Shader Stages
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment
            
            // This is used during shadow map generation to differentiate between directional and punctual light shadows, as they use different formulas to apply Normal Bias
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW
            
            // Shadow Casting Light geometric parameters. These variables are used when applying the shadow Normal Bias and are set by UnityEngine.Rendering.Universal.ShadowUtils.SetupShadowCasterConstantBuffer in com.unity.render-pipelines.universal/Runtime/ShadowUtils.cs
            // For Directional lights, _LightDirection is used when applying shadow Normal Bias.
            // For Spot lights and Point lights, _LightPosition is used to compute the actual light direction because it is different at each shadow caster geometry vertex.
            float3 _LightDirection;
            float3 _LightPosition;
            float4 _ShadowBias;
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                half4 vertexColor : COLOR;
                float2 uv0 : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
                float2 uv0 : TEXCOORD0;
            };
               
            float3 ApplyShadowBias(float3 positionWS, float3 normalWS, float3 lightDirection)
            {
                float invNdotL = 1.0 - saturate(dot(lightDirection, normalWS));
                float scale = invNdotL * _ShadowBias.y;

                // normal bias is negative since we want to apply an inset normal offset
                positionWS = lightDirection * _ShadowBias.xxx + positionWS;
                positionWS = normalWS * scale.xxx + positionWS;
                return positionWS;
            }
            
            float4 GetShadowPositionHClip(Attributes input)
            {
                
                float3 positionWS = TransformObjectToWorld(input.positionOS.xyz);
                float3 normalWS = TransformObjectToWorldNormal(input.normalOS);

                #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                    float3 lightDirectionWS = normalize(_LightPosition - positionWS);
                #else
                    float3 lightDirectionWS = _LightDirection;
                #endif

                    float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));

                #if UNITY_REVERSED_Z
                    positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #else
                    positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #endif

                return positionCS;
            }

            Varyings ShadowPassVertex(Attributes input)
            {
                Varyings output;
                float3 center = float3(_Center.x, _Center.y, _Center.z);

                PositionNormal animatedPosNormals = GetAnimatedPositionNormal(input.positionOS.xyz, input.vertexColor, center, input.normalOS);

                VertexPositionInputs vertexInput = GetVertexPositionInputs(animatedPosNormals.position);
                output.positionCS = vertexInput.positionCS;
                output.uv0 = input.uv0;
               
                return output;
            }

            half4 ShadowPassFragment(Varyings input) : SV_TARGET
            {
                float baseColorAlpha = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uv0).a;
                float epsilon = 1e-5;
                bool baseColorClipEqual = abs(baseColorAlpha - 0.2) > epsilon;
                clip(baseColorClipEqual - 0.5);
                return 0;
            }
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #pragma target 2.0
            
            #pragma vertex DepthNormalsVertex
            #pragma fragment DepthNormalsFragment
            
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                half4 vertexColor : COLOR;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                half4 vertexColor : TEXCOORD2;
                float3x3 tbn : TEXCOORD3;
            };
                   
            Varyings DepthNormalsVertex(Attributes input)
            {
                Varyings output = (Varyings)0;

                float3 center = float3(_Center.x, _Center.y, _Center.z);

                PositionNormal animatedPosNormals = GetAnimatedPositionNormal(input.positionOS.xyz, input.vertexColor, center, input.normalOS);

                VertexPositionInputs vertexInput = GetVertexPositionInputs(animatedPosNormals.position);
                output.positionCS = vertexInput.positionCS;
                VertexNormalInputs normalInput = GetVertexNormalInputs(animatedPosNormals.normal, input.tangentOS);
                output.uv0 = input.uv0;
                output.uv1 = input.uv1;
                output.vertexColor = input.vertexColor;
                output.tbn = float3x3(
                    normalInput.tangentWS,
                    normalInput.bitangentWS,
                    normalInput.normalWS);
                return output;
            }

            void DepthNormalsFragment(
                Varyings input
                , out half4 outNormalWS : SV_Target0
            #ifdef _WRITE_RENDERING_LAYERS
                , out float4 outRenderingLayers : SV_Target1
            #endif
            )
            {
                float baseColorAlpha = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uv0).a;
                float epsilon = 1e-5;
                bool baseColorClipEqual = abs(baseColorAlpha - 0.2) > epsilon;
                clip(baseColorClipEqual - 0.5);

                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(input.uv0));
                outNormalWS = half4(TransformUnpackedToWorldNormal(normalTS, input.tbn), 0.0);

                #ifdef _WRITE_RENDERING_LAYERS
                    uint renderingLayers = GetMeshRenderingLayer();
                    outRenderingLayers = float4(EncodeMeshRenderingLayer(renderingLayers), 0, 0, 0);
                #endif
            }
            ENDHLSL
        }
Pass
        {
            Name "Object Motion Vectors"
            Tags
            {
                "LightMode" = "MotionVectors"
            }

            HLSLPROGRAM
            #ifndef OBJECT_MOTION_VECTORS_WITH_DITHERING_INCLUDED
            #define OBJECT_MOTION_VECTORS_WITH_DITHERING_INCLUDED

            #pragma multi_compile_fragment _ _FOVEATED_RENDERING_NON_UNIFORM_RASTER
            #pragma never_use_dxc metal

            #pragma exclude_renderers d3d11_9x
            #pragma target 3.5

            #pragma vertex vert
            #pragma fragment frag

            // -------------------------------------
            // Includes
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/UnityInput.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            #ifndef HAVE_VFX_MODIFICATION
            #pragma multi_compile _ DOTS_INSTANCING_ON
            #if UNITY_PLATFORM_ANDROID || UNITY_PLATFORM_WEBGL || UNITY_PLATFORM_UWP
                    #pragma target 3.5 DOTS_INSTANCING_ON
            #else
            #pragma target 4.5 DOTS_INSTANCING_ON
            #endif
            #endif // HAVE_VFX_MODIFICATION
            #if defined(_FOVEATED_RENDERING_NON_UNIFORM_RASTER)
                            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/FoveatedRendering.hlsl"
            #endif

            // -------------------------------------
            // Structs
            struct Attributes
            {
                float4 position : POSITION;
                float4 vertexColor : COLOR;
                float3 positionOld : TEXCOORD4;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float4 positionCSNoJitter : TEXCOORD0;
                float4 previousPositionCSNoJitter : TEXCOORD1;
                UNITY_VERTEX_INPUT_INSTANCE_ID
                UNITY_VERTEX_OUTPUT_STEREO
            };

            // -------------------------------------
            // Vertex
            Varyings vert(Attributes input)
            {
                Varyings output = (Varyings)0;

                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

                
                float3 center = float3(_Center.x, _Center.y, _Center.z);

                PositionNormal animatedPosNormals = GetAnimatedPositionNormal(input.position.xyz, input.vertexColor, center, float3(0,0,0));

                VertexPositionInputs vertexInput = GetVertexPositionInputs(animatedPosNormals.position);
                output.positionCS = vertexInput.positionCS;
                // Jittered. Match the frame.
                output.positionCS = vertexInput.positionCS;

                // This is required to avoid artifacts ("gaps" in the _MotionVectorTexture) on some platforms
                #if defined(UNITY_REVERSED_Z)
                output.positionCS.z -= unity_MotionVectorsParams.z * output.positionCS.w;
                #else
                                output.positionCS.z += unity_MotionVectorsParams.z * output.positionCS.w;
                #endif

                output.positionCSNoJitter = mul(_NonJitteredViewProjMatrix, mul(UNITY_MATRIX_M, input.position));

                const float4 prevPos = (unity_MotionVectorsParams.x == 1) ? float4(input.positionOld, 1) : input.position;
                output.previousPositionCSNoJitter = mul(_PrevViewProjMatrix, mul(UNITY_PREV_MATRIX_M, prevPos));

                return output;
            }

            #if defined(_FOVEATED_RENDERING_NON_UNIFORM_RASTER)
                            // Non-uniform raster needs to keep the posNDC values in float to avoid additional conversions
                            // since uv remap functions use floats
                            #define POS_NDC_TYPE float2 
            #else
            #define POS_NDC_TYPE half2
            #endif

            // -------------------------------------
            // Fragment
            half4 frag(Varyings input) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

                // Note: unity_MotionVectorsParams.y is 0 is forceNoMotion is enabled
                bool forceNoMotion = unity_MotionVectorsParams.y == 0.0;
                if (forceNoMotion)
                {
                    return half4(0.0, 0.0, 0.0, 0.0);
                }

                // Calculate positions
                float4 posCS = input.positionCSNoJitter;
                float4 prevPosCS = input.previousPositionCSNoJitter;

                POS_NDC_TYPE posNDC = posCS.xy * rcp(posCS.w);
                POS_NDC_TYPE prevPosNDC = prevPosCS.xy * rcp(prevPosCS.w);

                #if defined(_FOVEATED_RENDERING_NON_UNIFORM_RASTER)
                                // Convert velocity from NDC space (-1..1) to screen UV 0..1 space since FoveatedRendering remap needs that range.
                                half2 posUV = RemapFoveatedRenderingResolve(posNDC * 0.5 + 0.5);
                                half2 prevPosUV = RemapFoveatedRenderingPrevFrameResolve(prevPosNDC * 0.5 + 0.5);
                                
                                // Calculate forward velocity
                                half2 velocity = (posUV - prevPosUV);
                #if UNITY_UV_STARTS_AT_TOP
                                    velocity.y = -velocity.y;
                #endif
                #else
                // Calculate forward velocity
                half2 velocity = (posNDC.xy - prevPosNDC.xy);
                #if UNITY_UV_STARTS_AT_TOP
                velocity.y = -velocity.y;
                #endif

                // Convert velocity from NDC space (-1..1) to UV 0..1 space
                // Note: It doesn't mean we don't have negative values, we store negative or positive offset in UV space.
                // Note: ((posNDC * 0.5 + 0.5) - (prevPosNDC * 0.5 + 0.5)) = (velocity * 0.5)
                velocity.xy *= 0.5;
                #endif
                return half4(velocity, 0, 0);
            }
            #endif

            ENDHLSL
        }
    }
    
}
