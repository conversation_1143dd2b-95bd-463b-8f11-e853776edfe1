Shader "Kefir/Kefir_Decor_SkyscrapeSurface"
{
    Properties
    { 
        [<PERSON><PERSON>(General)]
        _Rows ("Rows", Float) = 2
        _Columns ("Columns", Float) = 2
        _NormalRandomness ("Normal Randomness", Range (0.01,0.99)) = 0.1
        _RandomSeed ("Random Seed", Float) = 10000
        _FrameThicknessX ("Frame Thickness X", Range(0,0.5)) = 0.1
        _FrameThicknessY ("Frame Thickness Y", Range(0,5)) = 0.1
        
        [Header(Normal Map)]
        [NoScaleOffset]_BumpMap ("Normal Map", 2D) = "bump" {}
        _BumpMapX ("Normal Scale X", Range(0,5)) = 1
        _BumpMapY ("Normal Scale Y", Range(0,5)) = 1
        
        [Header(Frame)]
        _FrameBaseColor ("Frame Base Color", Color) = (0,0,0,1)
        _FrameMetallic ("Frame Metallic", Range(0,1)) = 0
        _FrameSmoothness ("Frame Smothness", Range(0,1)) = 0
        
        [Header(Glass)]
        _GlassBaseColor ("Glass Base Color", Color) = (1,1,1,1)
        _GlassMetallic ("Glass Metallic", Range(0,1)) = 0
        _GlassSmoothness ("Glass Smothness", Range(0,1)) = 1
    }
    
    SubShader
    {
        Tags { "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry-90" }

        ZWrite On

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        
        CBUFFER_START(UnityPerMaterial)
            float _Rows;
            float _Columns;
            float _NormalRandomness;
            float _RandomSeed;
            float _FrameThicknessX;
            float _FrameThicknessY;

            float _BumpMapX;
            float _BumpMapY;

            half3 _FrameBaseColor;
            half3 _GlassBaseColor;
        
            half _FrameMetallic;
            half _FrameSmoothness;
            half _GlassMetallic;
            half _GlassSmoothness;
        CBUFFER_END

        float Random(float2 st, float seed)
        {
            return frac(sin(dot(st.xy, float2(12.9898, 78.233))) * seed);
        }

        float box(float2 st, float2 size)
        {
            size = 0.5 - size * 0.5;
            float2 uv = smoothstep(size, size + 1e-4, st);
            uv *= smoothstep(size, size + 1e-4, 1.0 - st);
            
            return uv.x*uv.y;
        }
        
        float3 ReusedNormalWSCalculation(half3x3 tbn, float2 flooredUV, float2 fracedUV, float randRemapped, float frameMask)
        {
            float3 neutralNormal3 = float3(0, 0, 1);
            float3 normalRandomizedWS = TransformUnpackedToWorldNormal(neutralNormal3, tbn);
            float3 normalWSReversed = float3(Random(flooredUV, 3000), Random(flooredUV, 1000), Random(flooredUV, 4000));
            normalRandomizedWS = lerp(normalWSReversed, normalRandomizedWS, randRemapped);

            float2 normalMapUV = (fracedUV - 0.5) * float2(_BumpMapX, _BumpMapY) + 0.5;
            half3 normalMapTS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, normalMapUV));
            float3 normalWS = TransformUnpackedToWorldNormal(normalMapTS, tbn);
            
            normalWS = normalize(normalWS + normalRandomizedWS);
            return lerp(normalWS, tbn[2], frameMask);
        }
        
        #define KEFIR_SAMPLE_NORMAL_WS 1
        float3 KefirSampleNormalWS(SampleNormalData snData, half3x3 tbn)
        {
            float2 transformedUV = snData.uv * float2(_Columns, _Rows);
            float2 flooredUV = floor(transformedUV);
            float2 fracedUV = frac(transformedUV);
            
            float pattern = box(fracedUV, 1 / float2(_Columns * _FrameThicknessX, _Rows * _FrameThicknessY));
            float frameMask = 1 - pattern;

            float rand = Random(flooredUV, _RandomSeed);
            float randRemapped = Remap1(rand, -1, 1, 1 - _NormalRandomness, 1);

            return ReusedNormalWSCalculation(tbn, flooredUV, fracedUV, randRemapped, frameMask);
        }
        
        ENDHLSL
        
        Pass
        {
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag

            #define _REFLECTION_PROBES_NO_BLENDING 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                DECLARE_SH(sh, 4)
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.uv = i.uv * float2(_Columns, _Rows);

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                
                return o;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                #if defined(LOD_FADE_CROSSFADE)
		            LODFadeCrossFade(i.positionCS);
	            #endif
                
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                float2 flooredUV = floor(i.uv);
                float2 fracedUV = frac(i.uv);
                
                float pattern = box(fracedUV, 1 / float2(_Columns * _FrameThicknessX, _Rows * _FrameThicknessY));
                float frameMask = 1 - pattern;

                float rand = Random(flooredUV, _RandomSeed);
                float randRemapped = Remap1(rand, -1, 1, 1 - _NormalRandomness, 1);

                half3 normalWS = ReusedNormalWSCalculation(tbn, flooredUV, fracedUV, randRemapped, frameMask);
                
                half3 albedo = lerp(_GlassBaseColor, _FrameBaseColor, frameMask);
                half metallic = lerp(_GlassMetallic, _FrameMetallic, frameMask);
                half smoothness = lerp(_GlassSmoothness, _FrameSmoothness, frameMask);
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, 1), half3(metallic, smoothness, 1));
                InputData inputData = InitializeInputData(i, positionWS, tbn, normalWS);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
        
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #define _USE_NORMAL_MAP 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }
    }
}
