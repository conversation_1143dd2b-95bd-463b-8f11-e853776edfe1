Shader "Kefir/Kefir_Decor_GlassAnimation"
{
    Properties
    {
        [Header(Base Settings)]
        _BumpMap("Normal Map", 2D) = "bump" {}
        _BumpMapStrength("NormalStr", Range(0,2)) = 1
        _Color("Color", Color) = (1, 1, 1, 1)
        _Metallic("Metallic", Float) = 1
        _Smoothness("Smoothness", Float) = 1
        _EdgeMetallic("Edge Metallic", Float) = 1
        _EdgeSmoothness("Edge Smoothness", Float) = 1
        _EdgeColor("Color", Color) = (1, 1, 1, 1)
        _EdgeAlphaSpeed("Edge Alpha Speed", Float) = 1
        _FadeHeight("Fade Height", Float) = 5
        _DebrisPositionScale("Debris Position Scale", Float) = 12
        
        [Header(Direction)]
        _Center("Center", Vector) = (0,0,0,0)
        _AngleAffectRadius("Angle Affect Radius", Float) = 1
        _HitDirection("Hit Direction", Float) = 1
        _HitVector("Hit Vector", Vector) = (0,0,0,0)
        _FrameBox("Frame Box", Vector) = (1,1,0,0)
        [Toggle] _UseHitVector("Use Hit Vector", Float) = 0
        
        [Header(Push)]
        _SpreadPower("Spread Power", Range(0, 20)) = 1
        _SpreadRadius("Spread Radius", Range(0, 5)) = 1
        _PushPower("Push Power", Float) = 0.8
        _PushPowerRadius("Push Power Radius", Range(0.01, 5)) = 0.32
        _AdditionalPushPower("Additional Power", Range(0,20)) = 0.59
        _AdditionalPushPowerRadius("Additional Power Radius", Range(0,5)) = 0.44
        
        
        [Header(Gravity)]
        _Gravity("Gravity", Float) = 1
        _GravityLate("Gravity Late", Float) = 1
        
        [Header(Rotation)]
        _VelocityAffectRotation("Velocty Affect Rotation", Float) = 1
        _MaxVelocityAffectRotation("Max Velocty Affect Rotation", Float) = 1
        _PassiveNormalRotation("Passive Normal Rotation", Range(0, 1)) = 0.076
        _LateRotation("Late Rotation", Range(0,3)) = 0.199
        _NormalRandomMin("Normal Random Rotation Min", Float) = -0.4
        _NormalRandomMax("Normal Random Rotation Max", Float) = 0.4
        
        [Header(Time)]
        [Toggle(_USE_DEBUG_TIME)] _UseDebugTime("Use Debug Time", Float) = 0
        _DebugPseudoTime("Pseudo Time", Range(0, 5)) = 1
        _TimeSpeed("Time Speed", Float) = 7
        _StartTime("Start Time", Float) = 0
        
        [Header(Frame)]
        [NoScaleOffset]_FrameMask("Frame Mask", 2D) = "white" {}
        _FrameSize("Frame Size", Float) = 0
        _FrameBorders("SDF Frame Borders", Float ) = 0
        _FrameOffset("SDF Frame Offset", Float ) = 0
    }
    
    SubShader
    {
        Tags
        {
            "RenderPipeline" = "UniversalPipeline" "RenderType" = "Transparent" "Queue" = "Transparent"
        }
        Blend SrcAlpha OneMinusSrcAlpha
        ZWrite Off
        Cull Off
        ZTest LEqual
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include_with_pragmas "Assets/Utils/SkySystem/Shaders/FogInclude.cginc"

        TEXTURE2D(_BumpMap);
        TEXTURE2D(_FrameMask);
        SAMPLER(sampler_FrameMask);
        SAMPLER(sampler_BumpMap);

        CBUFFER_START(UnityPerMaterial)
            float4 _BumpMap_ST;
            float4 _FrameMask_ST;
            float4 _FrameBox;
            float3 _Center;
        
            float _PushPowerRadius;
            float _AngleAffectRadius;
            float _AdditionalPushPower;
            float _VelocityAffectRotation;
            float _MaxVelocityAffectRotation;
            float _AdditionalPushPowerRadius;
            float _SpreadPower;
            float _HitDirection;
            float _NormalRandomMin;
            float _NormalRandomMax;
            float _SpreadRadius;
            float _BreakMapStrength;
            float _FrameBorders;
            float _FrameOffset;
            float _GravityLate;
            float _DebugPseudoTime;
            float _Gravity;
            float _PushPower;
            float _FadeHeight;
            float _PassiveNormalRotation;
            float _LateRotation;
            float _TimeSpeed;
            float _FrameSize;
            float _StartTime;
            float _DebrisPositionScale;

            half4 _Color;
            half4 _EdgeColor;
            half3 _HitVector;
        
            half _BumpMapStrength;
            half _UseHitVector;
            half _Metallic;
            half _Smoothness;
            half _EdgeMetallic;
            half _EdgeSmoothness;
            half _EdgeAlphaSpeed;
        CBUFFER_END
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #pragma multi_compile _ _USE_DEBUG_TIME

            #define _REFLECTION_PROBES_NO_BLENDING 1
            #define _SURFACE_TYPE_TRANSPARENT 1
            
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Macros.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

            struct Attributes
            {
                float3 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
                float4 vertexColor : COLOR0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv: TEXCOORD0;
                float4x3 tbn_positionWS: TEXCOORD1;
                float3 initialPos : TEXCOORD4;
                DECLARE_SH(sh, 5)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
                        
            float2 UvSlice9(float2 x, float2 t, float2 h)
            {
                float2 i = floor(x + h) - h;
                float2 f = frac(x + h) - h;

                f *= t;
                f += h;
                f = clamp(f, 0.0, 1.0);

                return i + f;
            }
                        
            struct PositionNormal
            {
                float3 position;
                float3 normal;
            };
            
            float GetGlassProgress()
            {
                float progress = (_Time.y - _StartTime);
                #if defined(_USE_DEBUG_TIME)
                    progress = _DebugPseudoTime;
                #endif
                return progress *= _TimeSpeed;
            }
            
            PositionNormal GetAnimatedPositionNormal(float3 positionOS, float3 vertexColor, float3 center, float3 normalOS)
            {
                float3 debrisPos = (vertexColor * 2 - 1) * _DebrisPositionScale;
                debrisPos = float3(-debrisPos.x, -debrisPos.z, debrisPos.y);
                debrisPos = RotateAboutAxis(float3(0, 0, 1), _StartTime, debrisPos);
                float randomSeed = debrisPos.x + debrisPos.y + debrisPos.z;
                
                float distDebris = distance(debrisPos, float3(0, 0, 0));
                float progress = GetGlassProgress();
                float3 hitVector = (_UseHitVector > 0) ? _HitVector : float3(0, 0, -_HitDirection);

                float3 rotationAxis = normalize(debrisPos);
                rotationAxis = float3(-rotationAxis.y, rotationAxis.x, -rotationAxis.z);
                
                float anglePower = dot(normalize(debrisPos), hitVector);
                anglePower = acos(anglePower);
                anglePower =  HALF_PI / anglePower;
                float pushMask = max(0, 1 - distDebris / _PushPowerRadius * anglePower * _AngleAffectRadius);
                float mainPushPower = pushMask * _PushPower;
                
                float spreadRadius = max(0, 1 - distDebris / _SpreadRadius);
                float3 spreadVelocity = _SpreadPower * debrisPos * spreadRadius;
                float additionalPowerRadius = max(0, 1 - distDebris / _AdditionalPushPowerRadius);
                float3 additionalVelocity = (hitVector * additionalPowerRadius * _AdditionalPushPower);
                float3 velocity = (hitVector * mainPushPower) + spreadVelocity + additionalVelocity;
                
                float gravityLate = _GravityLate*RandomValue(randomSeed, 0.5, 2) * (debrisPos.y + (_DebrisPositionScale / 2));
                float gravityProgress = max(0,progress - gravityLate);
                float gravityVelocityWorld = _Gravity * gravityProgress;
                gravityVelocityWorld *= -gravityProgress;
                float3 gravityVelocity = mul((float3x3)unity_WorldToObject, float3(0, gravityVelocityWorld, 0));
                
                float3 targetPos = positionOS + gravityVelocity + (velocity * progress);
                
                float3 offset = positionOS - debrisPos;
                float3 currentDebrisPos = targetPos - offset;
                
                float randomRotationPower = RandomValue(randomSeed, 0.4, 1);
                float rotation = min(_MaxVelocityAffectRotation, anglePower * randomRotationPower * length(velocity) * _VelocityAffectRotation) * sign(velocity.z) * progress;
                
                float3 rotatedOffset = RotateAboutAxis(rotationAxis, rotation, offset);
                
                float lateRotation = _LateRotation * (1 - step(0.05, mainPushPower)) * gravityProgress / 2;
                float3 randomLateRotationAxis = float3(RandomRange(randomSeed), RandomRange(randomSeed-10), RandomRange(randomSeed+10));
                
                float3 lateRotatedOffset = RotateAboutAxis(randomLateRotationAxis, lateRotation, rotatedOffset);
                float3 finalPosition = currentDebrisPos + lateRotatedOffset + center;
                
                // Using different rotation for normal, for faking glass shattered reflection effect.
                float rotationCoeffNorm = max(_PassiveNormalRotation, 1 - distDebris / (_PushPowerRadius));
                float randomStartNormalRotation =  RandomValue(debrisPos.x + debrisPos.y + debrisPos.z, _NormalRandomMin, _NormalRandomMax);
                float rotationNormal = rotation + (rotationCoeffNorm * randomStartNormalRotation * progress);
                float3 rotatedNormal = RotateAboutAxis(rotationAxis, rotationNormal, normalOS);
                float3 finalNormal = RotateAboutAxis(randomLateRotationAxis, lateRotation , rotatedNormal);
                
                PositionNormal result;
                result.position = finalPosition;
                result.normal = finalNormal;
                return result;
            }

            Varyings vert(Attributes i)
            {
                Varyings o;
                float randomRotation = _StartTime;
                o.uv = i.uv;
                float3 center = float3(_Center.x, _Center.y, 0);
                float3 rotatedPosOS = RotateAboutAxis(float3(0, 0, 1), _StartTime, i.positionOS.xyz);
                float3 initialPos = RotateAboutAxis(float3(0, 0, 1), randomRotation, i.positionOS.xyz + RotateAboutAxis(float3(0, 0, 1), -_StartTime, center));

                float3 animatedNormalOS = i.normalOS;
                PositionNormal animatedPosNormals = GetAnimatedPositionNormal(rotatedPosOS, i.vertexColor.rgb, center, animatedNormalOS);

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(animatedPosNormals.position);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(animatedPosNormals.normal, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.initialPos = initialPos;
                
                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                
                return o;
            }

            half4 frag(Varyings i, bool facing : FRONT_FACE_SEMANTIC) : SV_Target
            {
                float2 halfFrameBox = _FrameBox.xy / 2;
                float isFrontFace = IS_FRONT_VFACE(facing, 1, -1);
                float edgeMask = 1.0 - step(0.0, i.uv.x + i.uv.y);
                
                // Clip all pixels that outside glass box and backfaces.
                if ((isFrontFace < 1 && edgeMask < 1) || i.initialPos.x > halfFrameBox.x || i.initialPos.x < -halfFrameBox.x || i.initialPos.y > halfFrameBox.y || i.initialPos.y < -halfFrameBox.y )
                {
                    clip(-1);
                }
                
                float2 frameUV = i.initialPos.xy / _FrameBox.xy + 0.5;
                float frameMask = SAMPLE_TEXTURE2D(_FrameMask, sampler_FrameMask, UvSlice9(frameUV, _FrameBox.xy * _FrameSize, float2(0.5, 0.5))).r;
                float d = frameMask.r + _FrameOffset;
                // Clip pixels that are in frame mask.
                if (d < _FrameBorders)
                {
                    clip(-1);    
                }
                
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                half4 normalMap = SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, TRANSFORM_TEX(i.uv, _BumpMap));
                half3 normalTS = UnpackNormalScale(normalMap, _BumpMapStrength);
                
                half metallic = lerp(_Metallic, _EdgeMetallic, edgeMask);
                half smoothness = lerp(_Smoothness, _EdgeSmoothness, edgeMask);
                half4 color = lerp(_Color, _EdgeColor, edgeMask);
                
                half alphaHeightReduce = saturate(positionWS.y - GetObjectPositionWS().y + _FadeHeight); 
                half alpha = color.a * alphaHeightReduce;
                half edgeAlpha = 1-step(GetGlassProgress() * _EdgeAlphaSpeed + 0.5, length(i.initialPos-_Center));
                edgeAlpha *= alpha;
                alpha = lerp(alpha, edgeAlpha, edgeMask);
                alpha = saturate(alpha);

                SurfaceData surfaceData = InitializeSurfaceData(half4(color.rgb, alpha), half3(metallic, smoothness, 1), normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                half4 model = UniversalFragmentPBR(inputData, surfaceData);
                return MixFogForObjects(positionWS, model);
            }
            ENDHLSL
        }
    }
}
