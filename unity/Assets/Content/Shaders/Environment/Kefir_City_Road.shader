Shader "Kefir/Kefir_City_Road"
{
    Properties
    { 
        [NoScaleOffset]_Base_Map("Base Map", 2D) = "white" {}
        [NoScaleOffset]_Normal("Normal", 2D) = "white" {}
        [NoScaleOffset]_Layers_Mask("Layers Mask", 2D) = "white" {}
        [NoScaleOffset]_Layer_1_Base_Map("Layer 1 Base Map", 2D) = "white" {}
        [NoScaleOffset]_Layer_1_Normal("Layer 1 Normal", 2D) = "white" {}
        _Layer_1_Mask_Tiling("Layer 1 Mask Tiling", Vector) = (0.02, 0.02, 0, 0)
        _Layer_1_Mask_Alpha("Layer 1 Mask Alpha", Float) = 1
        [NoScaleOffset]_Layer_2_Base_Map("Layer 2 Base Map", 2D) = "white" {}
        [NoScaleOffset]_Layer_2_Normal("Layer 2 Normal", 2D) = "white" {}
        _Layer_2_Mask_Tiling("Layer 2 Mask Tiling", Vector) = (0.02, 0.02, 0, 0)
        _Layer_2_Mask_Alpha("Layer 2 Mask Alpha", Float) = 1
        _SpecularMultiplier("Specular Multiplier", Range(0, 1)) = 1
    } 
    
    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry+100"
        }
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

        TEXTURE2D(_Base_Map);
        SAMPLER(sampler_Base_Map);
        TEXTURE2D(_Normal);
        SAMPLER(sampler_Normal);
        TEXTURE2D(_Layers_Mask);
        SAMPLER(sampler_Layers_Mask);
        TEXTURE2D(_Layer_1_Base_Map);
        SAMPLER(sampler_Layer_1_Base_Map);
        TEXTURE2D(_Layer_1_Normal);
        SAMPLER(sampler_Layer_1_Normal);
        TEXTURE2D(_Layer_2_Base_Map);
        SAMPLER(sampler_Layer_2_Base_Map);
        TEXTURE2D(_Layer_2_Normal);
        SAMPLER(sampler_Layer_2_Normal);
        
        CBUFFER_START(UnityPerMaterial)
            float2 _Layer_1_Mask_Tiling;
            float2 _Layer_2_Mask_Tiling;
            half _SpecularMultiplier;
            half _Layer_1_Mask_Alpha;
            half _Layer_2_Mask_Alpha;
        CBUFFER_END

        half3 ReusedNormalTSCalculation(float2 uv, half layer1Factor, half layer2Factor)
        {
            half3 baseNormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_Normal, sampler_Normal, uv));
            half3 layer1NormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_Layer_1_Normal, sampler_Layer_1_Normal, uv));
            half3 layer2NormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_Layer_2_Normal, sampler_Layer_2_Normal, uv));

            half3 normalTS = lerp(baseNormalTS, layer1NormalTS, layer1Factor);
            return lerp(normalTS, layer2NormalTS, layer2Factor);
        }

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            half vertexAlphaMaskDisable = snData.vertexColor.b;
            
            float2 layer1UV = snData.positionWS.xz * _Layer_1_Mask_Tiling;
            float2 layer2UV = snData.positionWS.xz * _Layer_2_Mask_Tiling;
            
            half layer1Factor = saturate(lerp(SAMPLE_TEXTURE2D(_Layers_Mask, sampler_Layers_Mask, layer1UV).r * _Layer_1_Mask_Alpha, 1, vertexAlphaMaskDisable)) * snData.vertexColor.r;
            half layer2Factor = saturate(lerp(SAMPLE_TEXTURE2D(_Layers_Mask, sampler_Layers_Mask, layer2UV).g * _Layer_2_Mask_Alpha, 1, vertexAlphaMaskDisable)) * snData.vertexColor.g;
            return ReusedNormalTSCalculation(snData.uv, layer1Factor, layer2Factor);
        }
        
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #define _SPECULAR_SETUP 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
                float4 color : COLOR0;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                float4 color : TEXCOORD4;
                DECLARE_SH(sh, 5)
            };
            
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                
                o.uv = i.uv;
                o.color = i.color;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                
                UnpackTBNAndPositionWS(i.tbn_positionWS,tbn,positionWS);

                half4 baseAlbedo = SAMPLE_TEXTURE2D(_Base_Map, sampler_Base_Map, i.uv);

                // MSAA produces artifacts on roads that result in color values interpolated out of [0..1] range. It can be solved using saturate or centroid interpolation(float4 color : TEXCOORD5_centroid), but it's
                // slower than default mode, so while color saturate don't produce noticeable artifacts use it.
                i.color = saturate(i.color);
                
                half vertexAlphaMaskDisable = i.color.b;

                float2 layer1UV = positionWS.xz * _Layer_1_Mask_Tiling;
                half4 layer1Albedo = SAMPLE_TEXTURE2D(_Layer_1_Base_Map, sampler_Layer_1_Base_Map, i.uv);
                half layer1Factor = saturate(lerp(SAMPLE_TEXTURE2D(_Layers_Mask, sampler_Layers_Mask, layer1UV).r * _Layer_1_Mask_Alpha, 1, vertexAlphaMaskDisable)) * i.color.r;
                
                float2 layer2UV = positionWS.xz * _Layer_2_Mask_Tiling;
                half4 layer2Albedo = SAMPLE_TEXTURE2D(_Layer_2_Base_Map, sampler_Layer_2_Base_Map, i.uv);
                half layer2Factor = saturate(lerp(SAMPLE_TEXTURE2D(_Layers_Mask, sampler_Layers_Mask, layer2UV).g * _Layer_2_Mask_Alpha, 1, vertexAlphaMaskDisable)) * i.color.g;
                
                half4 albedo = lerp(baseAlbedo, layer1Albedo, layer1Factor);
                albedo = lerp(albedo, layer2Albedo, layer2Factor);

                half3 specular = albedo.rgb * albedo.a * _SpecularMultiplier;
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo.rgb, 1), half3(0, albedo.a, 1), ReusedNormalTSCalculation(i.uv, layer1Factor, layer2Factor),
                    half3(0.0, 0.0, 0.0), 0, 0, specular);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #define _USE_NORMAL_MAP 1
            #define _USE_POSITION_WS 1
            #define _USE_VERTEX_COLOR 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }
    }
}
