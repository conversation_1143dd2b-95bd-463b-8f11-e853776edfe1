Shader "Kefir/Kefir_Decor_VegetationSSS"
{
       Properties
    { 
        [Header(Textures)]
        [NoScaleOffset]_BaseMap ("Base Map", 2D) = "white" {}
        [NoScaleOffset]_BumpMap ("Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_MaskMap ("MS Map | (R) — Metallic, (G) — Smothness, (B) — AO", 2D) = "white" {}
        _Metallic ("Metallic", Range(0, 1)) = 1
        _Smoothness ("Smoothneess", Range(0, 1)) = 1
        _AO ("AO", Range(0, 1)) = 1
        
        
        [Header(Bend and Leaf Wind)]
        _BendingStrength ("Bending Strength", Float) = 0.025
        _LeafBendingLate("Leaf Bending Late", Float) = -0.2
        _LeafBendDelayWave("Leaf Bending Delay Wave", Float) = 0.1
        
        _LeafShakeDelayWave("Leaf Shake Delay Wave", Float) = 5
        _LeafShakeStrength("Leaf Shake Strength", Float) = 0.04
        _LeafShakeSpeed("Lead Shake Speed", Float) = 8
        _Delay("Delay", Float) = 0
        
        
        [Header(Bend Strength by Distance)]
        _DistanceStrengthStart("Distance Strength Start", float) = 1
        _DistanceStrengthEnd("Distance Strength End", float) = 5
        _MaxDistanceStrengthFactor("Max Distance Strength", float) = 2
		
		[Header(Recalculate Normals)]
		[Toggle]_RecalculateNormals("Recalculate Normals", Range(0, 1)) = 1
		[Toggle]_MirrorBackfacedNormals("Mirror Backfaced Normals", Range(0, 1)) = 1
        
        [Header(SSS)]
        _SSSIntensity("SSS Intencity", Range(0,1)) = 1
        [NoScaleOffset]_SSSThickness_Map("SSS Thickness Map", 2D) = "white" {}
        _SSSDistortion("SSS Distortion", Range(0,1)) = 0
        _SSSSaturation("SSS Saturation", Float) = 1
        _SSSBrightness("SSS Brightness", Float) = 1
    }
    
    SubShader
    {
        Tags { "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry+436" }

        Cull Off
        Blend One Zero
        ZTest LEqual
        ZWrite On

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        #include "Assets/Content/ShaderLibrary/GlobalWindUniforms.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_MaskMap);
        SAMPLER(sampler_MaskMap);
        TEXTURE2D(_SSSThickness_Map);
        SAMPLER(sampler_SSSThickness_Map);

        CBUFFER_START(UnityPerMaterial)
            float _BendingStrength;
            float _LeafBendingLate;
            float _LeafShakeDelayWave;
            float _LeafShakeSpeed;
            float _LeafShakeStrength;
            float _DistanceStrengthStart;
            float _DistanceStrengthEnd;
            float _MaxDistanceStrengthFactor;
            float _LeafBendDelayWave;
            float _Delay;
            float _RecalculateNormals;
            float _MirrorBackfacedNormals;
            half _Metallic;
            half _Smoothness;
            half _AO;
            half _SSSIntensity;
            half _SSSDistortion;
            half _SSSSaturation;
            half _SSSBrightness;
        CBUFFER_END

        struct Attributes
            {
                float3 positionOS     : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                float4 vertexColor : COLOR0;
            };
        
        float random(float seed, float minValue, float maxValue)
        {
            float r = frac(sin(seed) * 10000);
            return lerp(minValue, maxValue, r);
        }
        
        float3 Kefir_Veg_Bending(float3 positionOS, float3 positionWS, float3 objectPosition, float4 vertexColor, float coef, float late)
        {
            float3 posDif = positionWS - _WorldSpaceCameraPos;
            float distanceToCamera = dot(posDif, posDif);
            float normalizedDistanceToCamera = saturate((distanceToCamera - _DistanceStrengthStart) / (_DistanceStrengthEnd - _DistanceStrengthStart));
            float distanceStrength = 1 + normalizedDistanceToCamera * _MaxDistanceStrengthFactor;
            float power = max(0, coef);
            float3 pivot = vertexColor.rgb;
            float3 diff = positionWS - pivot;
            
            // FBF
            float doubleHeight = positionOS.g * positionOS.g;
            float f = (4 - positionOS.g + doubleHeight) * doubleHeight;
            float t = _Time.y;
            float distanceAlongWind = dot(float2(positionOS.x, positionOS.z), normalize(_GlobalWindDirection.xz));
            float delayFactor = distanceAlongWind;
            float leafWindDelay = delayFactor;
            delayFactor *= _LeafBendDelayWave * power;
            
            leafWindDelay *= _LeafShakeDelayWave;
            
            // Apply delay to time.
            t += delayFactor;
            t += late * _Delay;
            float bending = (_BendingStrength * distanceStrength / 4) / 100000 * sin(t);
            float2 bendingVector = float2(cos(t), 0.5) * bending * f;

            // Wind direction.
            float3 objectPositionScale = objectPosition * float3(2, 2, 0.25);
            float wind = sin(objectPositionScale.r + objectPositionScale.b + t);
            wind = wind * 0.5 + 0.5;

            float uvDelay = late * _Delay;
            float2 windVector = sin(_GlobalWindDirection.xz * sin(t)) * float2(wind, wind);
            float3 upVector = float3(0, 1, 0);
            float3 adjustedLeafWind = lerp(_GlobalWindDirection, upVector, (saturate(distanceAlongWind)) * abs(1-pivot.y - 0.25));
            adjustedLeafWind = normalize(adjustedLeafWind);
            float3 leafWind = adjustedLeafWind * (sin((_Time.y + leafWindDelay + uvDelay) * _LeafShakeSpeed) + 2);
            leafWind *=  lerp(_LeafShakeStrength, _LeafShakeStrength*1.5, sin(t/2)+1);
            leafWind *= power * distanceStrength;
            
            pivot += leafWind * _GlobalWindStrength;

            diff += pivot;
            
            // Combined Bending.
            float2 windBending = windVector * bendingVector * _GlobalWindStrength;
            float3 windCombined = diff + float3(windBending.x, 0, windBending.y);
            return windCombined;
        }

        void RecalculateNormals(float3 normalOS, float3 tangentOS, float3 positionOS, float3 objectPositionWS, float4 vertexColor, float coef, float late,
            float3 positionWS, out float3 tangentWS, out float3 bitangentWS, out float3 normalWS)
        {
            float3 binormal = cross(normalOS, tangentOS);
            const float offset = 0.01;
            float3 tangentOffsetOS = positionOS + tangentOS * offset;
            float3 bitangentOffsetOS = positionOS + binormal * offset;               
            float3 tangentOffsetAnimatedWS = Kefir_Veg_Bending(tangentOffsetOS, TransformObjectToWorld(tangentOffsetOS), objectPositionWS, vertexColor, coef, late);
            float3 bitangentOffsetAnimatedWS = Kefir_Veg_Bending(bitangentOffsetOS, TransformObjectToWorld(bitangentOffsetOS), objectPositionWS, vertexColor, coef, late);
            tangentWS = SafeNormalize(tangentOffsetAnimatedWS - positionWS);
            bitangentWS = SafeNormalize(bitangentOffsetAnimatedWS - positionWS);
            normalWS = -cross(bitangentWS, tangentWS);
        }

        VertexNormalInputs RecalculateNormalsOptional(float3 normalOS, float4 tangentOS, float3 positionOS, float3 objectPositionWS, float4 vertexColor, float coef, float late, float3 positionWS)
        {
            VertexNormalInputs normalInputs;
                if (_RecalculateNormals)
                {
                    float3 tangentWS;
                    float3 bitangentWS;
                    float3 normalWS;
                    RecalculateNormals(
                        normalOS,
                        tangentOS.xyz,
                        positionOS.xyz,
                        objectPositionWS,
                        vertexColor,
                        coef,
                        late,
                        positionWS,
                        tangentWS,
                        bitangentWS,
                        normalWS);

                    normalInputs.tangentWS = tangentWS;
                    normalInputs.bitangentWS = bitangentWS;
                    normalInputs.normalWS = normalWS;        
                }
                else
                {
                    normalInputs = GetVertexNormalInputs(normalOS, tangentOS);
                }

            return normalInputs;
        }

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            return UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv));
        }
        
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag

            #pragma multi_compile _ _REFLECTION_PROBES_ROTATED

            #if defined(_REFLECTION_PROBES_ROTATED)
                #pragma require cubearray 
            #endif
            
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                DECLARE_SH(sh, 4)
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            float3 Kefir_Fast_SSS(float3 baseColor, float thickness, float saturation, float brightness, float3 lightDirectionWS, float3 viewDirectionWS, float shadowAttenuation)
            {
                float LdotV = max(0,dot(-lightDirectionWS, viewDirectionWS));
                float luma = dot(baseColor, float3(0.2126729, 0.7151522, 0.0721750));
                float3 processedBaseColor =  luma.xxx + saturation.xxx * (baseColor - luma.xxx);
                processedBaseColor *= (brightness * thickness * LdotV * shadowAttenuation);
                return  processedBaseColor;
            }
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                float3 objectPositionWS = GetObjectPositionWS();
                float3 positionWS = Kefir_Veg_Bending(
                    i.positionOS,
                    TransformObjectToWorld(i.positionOS),
                    objectPositionWS,
                    i.vertexColor,
                    i.uv1.y,
                    i.uv1.x);

                VertexNormalInputs normalInputs = RecalculateNormalsOptional(
                    i.normalOS,
                    i.tangentOS,
                    i.positionOS,
                    objectPositionWS,
                    i.vertexColor,
                    i.uv1.y,
                    i.uv1.x,
                    positionWS);
                
                o.positionCS = TransformWorldToHClip(positionWS);               

                PackTBNAndPositionWS(positionWS, normalInputs, o.tbn_positionWS);

                o.uv = i.uv;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }
            
            half4 frag(Varyings i, bool facing : SV_IsFrontFace) : SV_Target
            {
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(i.positionCS);
                #endif

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                // Textures.
                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv);
                clip(baseMap.a - 0.5);
                
                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(i.uv));
                tbn[2] = lerp(-tbn[2], tbn[2], saturate(facing + (1 - _MirrorBackfacedNormals)));
                               
                half3 msMap = SAMPLE_TEXTURE2D(_MaskMap, sampler_MaskMap, i.uv).rgb;
                
                half3 albedo = baseMap.rgb;
                half metallic = msMap.r * _Metallic;
                half smoothness = msMap.g * _Smoothness;
                half ao = msMap.b * _AO;
                
                // SSS.
                Light mainLight = GetMainLight(TransformWorldToShadowCoord(positionWS.xyz), positionWS.xyz, 0);
                float3 viewDirectionWS = normalize(_WorldSpaceCameraPos.xyz - positionWS.xyz);
                half thickness = SAMPLE_TEXTURE2D(_SSSThickness_Map, sampler_SSSThickness_Map, i.uv).r;
                half3 sss = Kefir_Fast_SSS(baseMap.rgb, thickness, _SSSSaturation, _SSSBrightness,  mainLight.direction, viewDirectionWS, mainLight.shadowAttenuation);
                
                sss = lerp(sss, 0, facing);
                sss = lerp(0, sss, _SSSIntensity);
                sss = clamp(sss, 0, 1);
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, 1), half3(metallic, smoothness, ao), normalTS, sss);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
                
            }
            ENDHLSL
        }
        
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull Off

            HLSLPROGRAM
            #pragma target 2.0
            
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment
            
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW
            #pragma multi_compile _ LOD_FADE_CROSSFADE

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            
            float3 _LightDirection;
            float3 _LightPosition;
            float4 _ShadowBias;

            struct Varyings
            {
                float2 uv           : TEXCOORD0;
                float4 positionCS   : SV_POSITION;
            };
            
            float3 ApplyShadowBias(float3 positionWS, float3 normalWS, float3 lightDirection)
            {
                float invNdotL = 1.0 - saturate(dot(lightDirection, normalWS));
                float scale = invNdotL * _ShadowBias.y;

                // normal bias is negative since we want to apply an inset normal offset
                positionWS = lightDirection * _ShadowBias.xxx + positionWS;
                positionWS = normalWS * scale.xxx + positionWS;
                return positionWS;
            }
            
            float4 GetShadowPositionHClip(Attributes i)
            {
                float3 objectPositionWS = GetObjectPositionWS();

                float3 positionWS = Kefir_Veg_Bending(
                    i.positionOS,
                    TransformObjectToWorld(i.positionOS),
                    objectPositionWS,
                    i.vertexColor,
                    i.uv1.y,
                    i.uv1.x);

                VertexNormalInputs normalInputs = RecalculateNormalsOptional(
                    i.normalOS,
                    i.tangentOS,
                    i.positionOS,
                    objectPositionWS,
                    i.vertexColor,
                    i.uv1.y,
                    i.uv1.x,
                    positionWS);

                #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                    float3 lightDirectionWS = normalize(_LightPosition - positionWS);
                #else
                    float3 lightDirectionWS = _LightDirection;
                #endif

                    float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalInputs.normalWS, lightDirectionWS));

                #if UNITY_REVERSED_Z
                    positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #else
                    positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #endif

                return positionCS;
            }

            Varyings ShadowPassVertex(Attributes input)
            {
                Varyings output;
                output.positionCS = GetShadowPositionHClip(input);
                output.uv = input.uv;
                return output;
            }

            half4 ShadowPassFragment(Varyings input) : SV_TARGET
            {
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(input.positionCS);
                #endif
                
                float alpha = SAMPLE_TEXTURE2D(_BaseMap,sampler_BaseMap, input.uv).a;
                clip(alpha - 0.5);
                return 0;
            }
            
            ENDHLSL
        }
        
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #pragma target 2.0
            
            #pragma vertex DepthNormalsVertex
            #pragma fragment DepthNormalsFragment
            
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"

            #pragma multi_compile _ LOD_FADE_CROSSFADE

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
                float2 uv : TEXCOORD0;
                half3x3 tbn : TEXCOORD1;
            };
            
            Varyings DepthNormalsVertex(Attributes i)
            {
                Varyings o = (Varyings)0;
                
                float3 objectPositionWS = GetObjectPositionWS();
                
                float3 positionWS = Kefir_Veg_Bending(i.positionOS, TransformObjectToWorld(i.positionOS), objectPositionWS, i.vertexColor, i.uv1.y, i.uv1.x);
                o.positionCS = TransformWorldToHClip(positionWS);
                
                VertexNormalInputs normalInputs = RecalculateNormalsOptional(
                    i.normalOS,
                    i.tangentOS,
                    i.positionOS,
                    objectPositionWS,
                    i.vertexColor,
                    i.uv1.y,
                    i.uv1.x,
                    positionWS);

                o.uv = i.uv;
                o.tbn = half3x3(
                    normalInputs.tangentWS,
                    normalInputs.bitangentWS,
                    normalInputs.normalWS
                    );
                
                return o;
            }

            void DepthNormalsFragment(
                Varyings i,
                bool facing : FRONT_FACE_SEMANTIC,
                out half4 outNormalWS : SV_Target0
            #ifdef _WRITE_RENDERING_LAYERS
                , out float4 outRenderingLayers : SV_Target1
            #endif
            )
            {
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(i.positionCS);
                #endif
                
                float alpha = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv).a;
                clip(alpha - 0.5);

                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(i.uv));
                i.tbn[2] = lerp(-i.tbn[2], i.tbn[2], facing);
                float3 normalWS = TransformUnpackedToWorldNormal(normalTS, i.tbn);
                
                outNormalWS = half4(normalWS,0.0); 

                #ifdef _WRITE_RENDERING_LAYERS
                    uint renderingLayers = GetMeshRenderingLayer();
                    outRenderingLayers = float4(EncodeMeshRenderingLayer(renderingLayers), 0, 0, 0);
                #endif
            }
            
            ENDHLSL
        }
    }
}
