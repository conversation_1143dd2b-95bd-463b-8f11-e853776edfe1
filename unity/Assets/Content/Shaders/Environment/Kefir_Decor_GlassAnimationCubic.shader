Shader "Kefir/Kefir_Decor_GlassAnimationCubic"
{
    Properties
    {
        _BumpMap("Normal Map", 2D) = "white" {}
        _Color("Color", Color) = (1, 1, 1, 1)
        _Metallic("Metallic", Float) = 1
        _Smoothness("Smoothness", Float) = 1
        _BumpMapStrength("Normal Strength", Float) = 0.27
        _FrameBox("Frame Box", Vector) = (1,1,0,0)
        _NormalRandomMin("Normal Random Min", Float) = -0.4
        _NormalRandomMax("Normal Random Max", Float) = 0.4
        _Center("Center", Vector) = (0,0,0,0)
        _FadeHeight("Fade Height", Float) = 5
        _HitDirection("Hit Direction", Float) = 1
        _CenterRadiusCubic("Center Radius", Range(0.01, 5)) = 0.888
        _TimeRandomMin("Time Random Min", Float) = -0.15
        _TimeRandomMax("Time Random Max", Float) = 0.15
        _GravitySpeed("Gravity Speed", Float) = 1
        _GravityLate("Gravity Late", Float) = 1
        [Toggle(_USE_DEBUG_TIME)] _UseDebugTime("Use Debug Time", Float) = 0
        _DebugPseudoTime("Pseudo Time", Range(0, 5)) = 1
        _Gravity("Gravity", Float) = 1
        _GravitySpeedUp("Gravity Speed Up", Float) = 1
        _PushPowerCubic("Push Power", Float) = 0.2
        _Rotation("Rotation", Range(0, 20)) = 1
        _PassiveNormalRotation("Passive Normal Rotation", Range(0, 1)) = 0.076
        _PowerCoeff("Power Coeff", Float) = 1
        _StartPushUp("Start Push Up", Range(0,3)) = 1
        _Speed("Speed", Float) = 1
        _StartTime("StartTime", Float) = 0
        _DebrisPositionScale("Debris Position Scale", Float) = 12
    }
    SubShader
    {
        Tags
        {
            "RenderPipeline" = "UniversalPipeline" "RenderType" = "Transparent" "Queue" = "Transparent"
        }
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include_with_pragmas "Assets/Utils/SkySystem/Shaders/FogInclude.cginc"
        
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);

        CBUFFER_START(UnityPerMaterial)
            float4 _BumpMap_ST;
            float3 _FrameBox;
            float3 _Center;
        
            float _CenterRadiusCubic;
            float _HitDirection;
            float _NormalRandomMin;
            float _NormalRandomMax;
            float _TimeRandomMin;
            float _TimeRandomMax;
            float _GravitySpeed;
            float _GravityLate;
            float _DebugPseudoTime;
            float _Gravity;
            float _GravitySpeedUp;
            float _PushPowerCubic;
            float _Rotation;
            float _FadeHeight;
            float _PassiveNormalRotation;
            float _PowerCoeff;
            float _StartPushUp;
            float _Speed;
            float _StartTime;
            float _DebrisPositionScale;
        
            half4 _Color;
            half _Smoothness;
            half _Metallic;
            half _BumpMapStrength;
        CBUFFER_END
        ENDHLSL

        Pass
        {
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off
            Cull Off
            ZTest LEqual

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            
            #pragma shader_feature _ _USE_DEBUG_TIME

            #define _REFLECTION_PROBES_NO_BLENDING 1
            #define _SURFACE_TYPE_TRANSPARENT 1

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Macros.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"

            struct Attributes
            {
                float3 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                float2 uv2 : TEXCOORD2;
                float4 vertexColor : COLOR0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float4x3 tbn_positionWS: TEXCOORD1;
                float3 initialPos : TEXCOORD4;
                DECLARE_SH(sh, 5)
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            float3 GetDebrisPositionFromVertexColor(float3 vertexColor)
            {
                return vertexColor * 2 - 1;
            }

            float GetPower(float power, float pseudoTime, float powerCoeff)
            {
                float targetPower = power * powerCoeff;
                targetPower = targetPower * pseudoTime + 1;
                return power / targetPower;
            }

            struct PositionNormal
            {
                float3 position;
                float3 normal;
            };
            
            PositionNormal GetAnimatedPositionNormal(float3 positionOS, float3 vertexColor, float3 center, float3 vertexOffset, float3 normalOS)
            {
                float3 debrisPos = GetDebrisPositionFromVertexColor(vertexColor) * _DebrisPositionScale;
                debrisPos = float3(-debrisPos.x, debrisPos.z, -debrisPos.y) ;
                float distDebris = distance(debrisPos+vertexOffset, center);
                
                float progress = _Time.y  - _StartTime;
                #if defined(_USE_DEBUG_TIME)
                    progress = _DebugPseudoTime;
                #endif
                progress += RandomValue(debrisPos.x + debrisPos.y + debrisPos.z, _TimeRandomMin, _TimeRandomMax);
                progress *= _Speed;
                float speedCoeff = max(0, 1 - distDebris / _CenterRadiusCubic);
                float forcePos = speedCoeff * GetPower(_PushPowerCubic, progress, _PowerCoeff) * progress;

                float strength = distDebris * _GravitySpeed + _GravityLate;
                float gravityInterpolant = max(0.001, progress - strength);
                float pushUpCoeff = _StartPushUp / gravityInterpolant / strength;
                float gravity = _Gravity - pushUpCoeff;
                float gravityProgress = gravityInterpolant * gravityInterpolant * _GravitySpeedUp;
                float posY = lerp(0, -gravity, gravityProgress);

                float3 gravityVelocity = mul((float3x3)unity_WorldToObject, float3(0, posY, 0));
                float3 targetPos = positionOS + gravityVelocity + normalize(debrisPos+vertexOffset) * float3(forcePos,0,forcePos);

                float3 offset = positionOS - (debrisPos+vertexOffset);
                float3 currentDebrisPos = targetPos - offset;

                float rotationCoeff = max(0, 1 - distDebris / _CenterRadiusCubic);
                float rotation = rotationCoeff * _Rotation * progress;
                float rotationCoeffNorm = max(_PassiveNormalRotation, 1 - distDebris / _CenterRadiusCubic);
                float rotationNorm = rotationCoeffNorm * _Rotation * progress;

                float3 axis = normalize(debrisPos+vertexOffset);
                axis = float3(-axis.y, axis.x, axis.z);

                float3 rotatedOffset = RotateAboutAxis(axis, rotation, offset);
                float3 finalVertexPos = currentDebrisPos + rotatedOffset;
                
                //Using different rotation for normal, for faking glass shattered reflection effect
                float3 rotatedNormalOS = RotateAboutAxis(axis, rotationNorm + RandomValue(debrisPos.x + debrisPos.y + debrisPos.z, _NormalRandomMin, _NormalRandomMax), normalOS);
                PositionNormal result;
                result.position = finalVertexPos;
                result.normal = rotatedNormalOS;
                return result;
            }

            Varyings vert(Attributes i)
            {
                Varyings o;
                o.uv0 = TRANSFORM_TEX(i.uv0, _BumpMap);
                float3 vertexOffset = float3(i.uv2.x - 0.5, i.uv1.y - 0.5, i.uv2.y - 0.5);
                float3 initialPos = i.positionOS;
                initialPos += vertexOffset * _FrameBox;

                float3 animatedNormalOS = i.normalOS;
                PositionNormal animatedPosNormals = GetAnimatedPositionNormal(initialPos, i.vertexColor.rgb, _Center, vertexOffset * _FrameBox, animatedNormalOS);

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(animatedPosNormals.position);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(animatedPosNormals.normal, i.tangentOS);
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.initialPos = initialPos;
                
                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                
                return o;
            }

            half4 frag(Varyings i, bool facing : FRONT_FACE_SEMANTIC) : SV_Target
            {
                float3 halfFrameBox = _FrameBox / float3(2, 2, 2);
                halfFrameBox += 0.001;
                
                // Clip all pixels that outside glass box
                float3 framePosOrientier = i.initialPos;
                if (framePosOrientier.x > halfFrameBox.x || framePosOrientier.x < -halfFrameBox.x || framePosOrientier.y > halfFrameBox.y || framePosOrientier.y < -halfFrameBox.y  || framePosOrientier.z > halfFrameBox.z || framePosOrientier.z < -halfFrameBox.z)
                {
                    clip(-1);
                }

                float isFrontFace = IS_FRONT_VFACE(facing, -1, 1);
                
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                tbn[0] *= isFrontFace;
                tbn[2] *= isFrontFace;

                half4 normalMap = SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, i.uv0);
                half3 normalTS = UnpackNormalScale(normalMap, _BumpMapStrength);
                
                SurfaceData surfaceData = InitializeSurfaceData(_Color, half3(_Metallic, _Smoothness, 1), normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                half4 model = UniversalFragmentPBR(inputData, surfaceData);
                return MixFogForObjects(positionWS, model);
            }
            ENDHLSL
        }
    }
}