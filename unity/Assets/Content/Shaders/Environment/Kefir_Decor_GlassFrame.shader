Shader "Kefir/Kefir_Decor_GlassFrame"
{
    Properties
    {
        _BumpMap("Normal Map", 2D) = "bump" {}
        _Color("Color", Color) = (1, 1, 1, 1)
        _Metallic("Metallic", Float) = 1
        _Smoothness("Smoothness", Float) = 1
        _BumpMapStrength("NormalStr", Float) = 1
        _FrameBox("Frame Box", Vector) = (1,1,0,0)
        _FrameMask("Frame Mask", 2D) = "white" {}
        _FrameSize("Frame Size", Float) = 0
        _FrameBorders("SDF Frame Borders", Float ) = 0
        _FrameOffset("SDF Frame Offset", Float ) = 0
    }
    SubShader
    {
        Tags
        {
            "RenderPipeline" = "UniversalPipeline" "RenderType" = "Transparent" "Queue" = "Transparent"
        }

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

        TEXTURE2D(_BumpMap);
        TEXTURE2D(_FrameMask);
        SAMPLER(sampler_FrameMask);
        SAMPLER(sampler_BumpMap);

        CBUFFER_START(UnityPerMaterial)
            float4 _BumpMap_ST;
            float4 _FrameMask_ST;
            float4 _FrameBox;
            float _FrameBorders;
            float _FrameOffset;
            float _FrameSize;
            half4 _Color;
            half _BumpMapStrength;
            half _Metallic;
            half _Smoothness;
        CBUFFER_END
        ENDHLSL

        Pass
        {
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off
            Cull Back
            ZTest LEqual

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #define _REFLECTION_PROBES_NO_BLENDING 1
            #define _SURFACE_TYPE_TRANSPARENT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Macros.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

            struct Attributes
            {
                float3 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                float3 positionOS : TEXCOORD4;
                DECLARE_SH(sh, 5)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings vert(Attributes i)
            {
                Varyings o;
                o.uv = TRANSFORM_TEX(i.uv, _BumpMap);

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS);
                o.positionCS = vertexInputs.positionCS;
                o.positionOS = i.positionOS;
                
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS);
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                
                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                
                return o;
            }
            
            float2 UvSlice9(float2 x, float2 t, float2 h)
            {
                float2 i = floor(x + h) - h;
                float2 f = frac(x + h) - h;

                f *= t;
                f += h;
                f = clamp(f, 0.0, 1.0);

                return i + f;
            }

            half4 frag(Varyings i) : SV_Target
            {
                float2 halfFrameBox = _FrameBox.xy / 2;
                
                // Clip all pixels that outside glass box.
                if (i.positionOS.x > halfFrameBox.x || i.positionOS.x < -halfFrameBox.x || i.positionOS.y > halfFrameBox.y || i.positionOS.y < -halfFrameBox.y)
                {
                    clip(-1);
                }

                float2 frameUV = i.positionOS.xy / _FrameBox.xy + 0.5;
                float frameMask = SAMPLE_TEXTURE2D(_FrameMask, sampler_FrameMask, UvSlice9(frameUV, _FrameBox.xy*_FrameSize, float2(0.5,0.5))).r; 
                float d = frameMask.r + _FrameOffset;
                
                // Clip pixels that are outside frame mask.
                if (d > _FrameBorders)
                {
                    clip(-1);
                }

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS,tbn,positionWS);

                half3 normalTS = UnpackNormalScale(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, i.uv), _BumpMapStrength);
                
                SurfaceData surfaceData = InitializeSurfaceData(_Color, half3(_Metallic, _Smoothness, 1), normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
    }
}
