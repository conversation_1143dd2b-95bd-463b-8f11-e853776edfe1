Shader "Kefir/Kefir_Decor_Bush"
{
       Properties
    { 
        [NoScaleOffset]_BaseMap("Base Color Map",2D) = "white" {}
        _BaseColor("Base Color", Color) = (1,1,1,1)
        _SecondaryColor("Secondary Color", Color) = (1,1,1,1)
        _ThirdColor("Third Color", Color) = (1,1,1,1)
        _FourthColor("Forth Color", Color) = (1,1,1,1)
        
        [Space()]
        [NoScaleOffset]_BumpMap ("Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_MaskMap ("MS Map | (R) — Metallic, (G) — Smothness, (B) — AO", 2D) = "white" {}
        _Metallic ("Metallic", Range(0, 1)) = 1
        _Smoothness ("Smoothneess", Range(0, 1)) = 1
        _AO ("AO", Range(0, 1)) = 1
        
        [Header(Dusting)]
        [Toggle(_DUSTING)] _Dusting("Dusting", Float) = 0
        _DustingFactor ("Dusting Factor", Range(0, 1)) = 0.5
        _DustingHeight ("Dusting Height", Range(0, 2)) = 0.5
        _DustingHeightSmoothBorder ("Dusting Height Smooth Border", Range(0, 1)) = 0.2
        
        [Space()]
        [Header(Alpha Clipping)]
        [Toggle(_ALPHATEST_ON)] _AlphaClipping("Alpha Clipping", Float) = 0
        _Cutoff("Alpha Clip Threshold", Range(0,1)) = 0.5
        
        [Space()]
        [Header(Dissolve)]
        [Toggle] _IsDissolve ("Is Dissolve", Float) = 0
        _MinDissolveDistance("Min Dissolve Distance", Float) = 2
        _MaxDissolveDistance("Max Dissolve Distance", Float) = 4
        _MinDissolve("Min Dissolve", Range(0,1)) = 0
        _MaxDissolve("Max Dissolve", Range(0,1)) = 1
        _ExtraDistanceBehindCamera("Extra distance behind camera", Range(-1, 0)) = -0.5
        
        [Space()]
        [Header(Wind Influence)]
        _WindBendingResistence("_Wind Bending Resistence", Float) = 0.1
    }
    
    SubShader
    {
        Tags { "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent-300" }
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/GlobalWindUniforms.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);

        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);

        TEXTURE2D(_MaskMap);
        SAMPLER(sampler_MaskMap);

        float3 _PlayerCharacterPositionWS;
        
        CBUFFER_START(UnityPerMaterial)
            float _MinDissolveDistance;
            float _MaxDissolveDistance;
            float _MinDissolve;
            float _MaxDissolve;
            float _ExtraDistanceBehindCamera;
            float _WindBendingResistence;

            half4 _BaseColor;
            half4 _SecondaryColor;
            half4 _ThirdColor;
            half4 _FourthColor;
            half _Cutoff;

            half _Metallic;
            half _Smoothness;
            half _AO;

            half _DustingFactor;
            half _DustingHeight;
            half _DustingHeightSmoothBorder;

            half _IsDissolve;
        CBUFFER_END

        float3 Kefir_Veg_Bending(float3 positionOS, float3 positionWS, float3 objectPositionWS)
        {
            // FBF
            float doubleHeight = positionOS.g * positionOS.g;
            float f = (4 - positionOS.g + doubleHeight) * doubleHeight;

            float bending = _GlobalWindStrength *_WindBendingResistence * _SinTime.w;
            float2 bendingVector = float2(_CosTime.w, 0.5) * bending * f;

            // Wind direction
            float3 objectPositionScale = objectPositionWS * float3(2, 2, 0.25);
            float wind = sin(objectPositionScale.r + objectPositionScale.b + _Time.y);
            wind = wind * 0.5 + 0.5;

            float2 windVector = sin(_GlobalWindDirection.xz * _SinTime.w) * float2(wind, wind);

            // Combined Bending
            float2 windBending = windVector * bendingVector;
            float3 windCombined = float3(windBending.x, 0, windBending.y) + positionWS;
            return windCombined;
        }

        VertexNormalInputs RecalculatePositionAndNormals(float3 normalOS, float3 tangentOS, float3 positionOS, float3 objectPositionWS, out float3 animatedPositionWS)
        {
            float3 binormal = cross(normalOS, tangentOS);
            const float offset = 0.01;
            float3 tangentOffsetOS = positionOS + tangentOS * offset;
            float3 bitangentOffsetOS = positionOS + binormal * offset;               
            animatedPositionWS = Kefir_Veg_Bending(positionOS, TransformObjectToWorld(positionOS), objectPositionWS);
            float3 tangentOffsetAnimatedWS = Kefir_Veg_Bending(tangentOffsetOS, TransformObjectToWorld(tangentOffsetOS), objectPositionWS);
            float3 bitangentOffsetAnimatedWS = Kefir_Veg_Bending(bitangentOffsetOS, TransformObjectToWorld(bitangentOffsetOS), objectPositionWS);                
            float3 tangentWS = normalize(tangentOffsetAnimatedWS - animatedPositionWS);
            float3 bitangentWS = normalize(bitangentOffsetAnimatedWS - animatedPositionWS);
            float3 normalWS = -cross(bitangentWS, tangentWS);

            VertexNormalInputs normalInputs;
            normalInputs.bitangentWS = bitangentWS;
            normalInputs.tangentWS = tangentWS;
            normalInputs.normalWS = normalWS;

            return normalInputs;
        }
        
        ENDHLSL
        
        Pass
        {
            Cull Off
            
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            #pragma shader_feature_local_fragment _ALPHATEST_ON
            #pragma shader_feature_fragment _DUSTING
            
            #define _SURFACE_TYPE_TRANSPARENT 1
            
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"
            #include_with_pragmas "Assets/Utils/SkySystem/Shaders/FogInclude.cginc"

            #if defined(_DUSTING)
                #define NO_SHADER_GRAPH 1
                #include "Assets/ContentDraft/ShaderLibrary/Dusting.hlsl"
            #endif
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float4 vertexColor : COLOR;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float4 vertexColor : COLOR;
                float3 uv_Dithering : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                DECLARE_SH(sh, 4)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            half3 KefirSampleNormalTS(SampleNormalData snData)
            {
                return UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv));
            }
            
            Varyings vert(Attributes i)
            {
                Varyings o;
                
                float3 objectPositionWS = GetObjectPositionWS();

                // Recalculating position and normals.
                float3 positionWS;
                VertexNormalInputs normalInputs = RecalculatePositionAndNormals(i.normalOS, i.tangentOS.xyz, i.positionOS.xyz, objectPositionWS, positionWS);
                
                o.positionCS = TransformWorldToHClip(positionWS);
                
                o.uv_Dithering.xy = i.uv;

                if (_IsDissolve)
                {
                    float2 a = _WorldSpaceCameraPos.xz;
                    float2 b = _PlayerCharacterPositionWS.xz;
                    float2 p = objectPositionWS.xz;

                    float2 ab = b - a;
                    float2 ap = p - a;
                    float t = clamp(dot(ap, ab) / dot(ab, ab), _ExtraDistanceBehindCamera, 1.0f);
                    float2 closestPoint = a + t * ab;

                    float dithering = distance(closestPoint, p);
                    dithering = InverseLerp(_MinDissolveDistance, _MaxDissolveDistance, dithering);
                    dithering = clamp(dithering, _MinDissolve, _MaxDissolve);
                    o.uv_Dithering.z = dithering;
                }

                o.vertexColor = i.vertexColor;

                PackTBNAndPositionWS(positionWS, normalInputs, o.tbn_positionWS);

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(i.positionCS);
                #endif

                if (_IsDissolve)
                {
                    Dither(i.uv_Dithering.z, GetNormalizedScreenSpaceUV(i.positionCS.xy));
                }

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                float2 uv = i.uv_Dithering.xy;
                
                // Textures.
                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uv);

                #ifdef _ALPHATEST_ON
                    clip(baseMap.a - _Cutoff);
                #endif
                
                // Base Color.
                half3 baseColor = BlendOverlay(baseMap.rgb, _BaseColor.rgb, _BaseColor.a);
                baseColor = BlendMultiply(baseColor, _SecondaryColor.rgb, i.vertexColor.r);
                baseColor = BlendMultiply(baseColor, _ThirdColor.rgb, i.vertexColor.g);
                baseColor = BlendMultiply(baseColor, _FourthColor.rgb, i.vertexColor.b);

                // MSA.
                half3 msMap = SAMPLE_TEXTURE2D(_MaskMap, sampler_MaskMap, uv).rgb;
                half metallic = msMap.r * _Metallic;
                half smoothness = msMap.g * _Smoothness;
                half ao = msMap.b * _AO;
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(baseColor, 1), half3(metallic, smoothness, ao), KefirSampleNormalTS(InitializeSampleNormalData(uv)));
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);

                // Dusting.
                #if defined(_DUSTING)
                        float3 resultAldebo;
                        // Don't care about smoothness.
                        half resultSmoothness;
                        Dusting(sampler_BaseMap, _DustingFactor, _DustingHeight, _DustingHeightSmoothBorder, positionWS, inputData.normalWS, surfaceData.albedo, smoothness, resultAldebo, resultSmoothness);
                        surfaceData.albedo = resultAldebo;
                #endif
                
                half4 model = UniversalFragmentPBR(inputData, surfaceData);
                return MixFogForObjects(positionWS, model);
            }
            ENDHLSL
        }
        
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
    }
}
