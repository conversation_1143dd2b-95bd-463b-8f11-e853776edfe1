Shader "Kefir/Kefir_Decor_Flag_VAT"
{
    Properties
    { 
        [NoScaleOffset]_BaseMap("Base Color Map", 2D) = "white" {}
        [NoScaleOffset]_VATPos("VAT Position", 2D) = "white" {}
        [NoScaleOffset]_VATNormals("VAT Normals", 2D) = "white" {}
        _TextureWidth("Texture Width", float) = 0
        _AnimationSpeed("Animation Speed", float) = 1
        _Metallic("Metallic", Range(0,1)) = 0
        _Smoothness("Smoothness", Range(0,1)) = 0
    } 
    
    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry-20"
        }
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_VATPos);
        SAMPLER(sampler_VATPos);
        TEXTURE2D(_VATNormals);
        SAMPLER(sampler_VATNormals);
        
        CBUFFER_START(UnityPerMaterial)
            float _TextureWidth;
            float _AnimationSpeed;
            float _Metallic;
            float _Smoothness;
        CBUFFER_END
        
        float3 UnpackDataFromTexture(float3 packedData)
        {
            float3 unpackedData = packedData * 2 - 1;
            unpackedData.y *=- 1;
            unpackedData.yz = unpackedData.zy;
            return unpackedData;
        }
        
        ENDHLSL
        
        Pass
        {
            Cull Off
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 normalWS : TEXCOORD1;
                float3 positionWS : TEXCOORD2;
                DECLARE_SH(sh, 3)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings vert(Attributes i)
            {
                Varyings o;
                
                float pixel = 1.0 / _TextureWidth;
                float half_pixel = pixel * 0.5;
                float2 vatUV = i.uv1 + float2(half_pixel, _Time.y * _AnimationSpeed);
                
                float3 unsignedPositionOS = SAMPLE_TEXTURE2D_LOD(_VATPos, sampler_VATPos, vatUV, 0).xyz;
                float3 unsignedNormalOS = SAMPLE_TEXTURE2D_LOD(_VATNormals, sampler_VATNormals, vatUV, 0).xyz;
                
                float3 positionOS = UnpackDataFromTexture(unsignedPositionOS);
                float3 normalOS = UnpackDataFromTexture(unsignedNormalOS);

                normalOS = normalize(normalOS);
                o.normalWS = TransformObjectToWorldNormal(normalOS);
                
                VertexPositionInputs vertexInputs = GetVertexPositionInputs(positionOS);
                o.positionCS = vertexInputs.positionCS;
                o.positionWS = vertexInputs.positionWS;
                
                o.uv = i.uv0;

                OUTPUT_VERTEX_SH(o.normalWS, o.sh)
                return o;
            } 
            
            half4 frag(Varyings i, bool facing : FRONT_FACE_SEMANTIC) : SV_Target
            {
                float isFrontFace =  IS_FRONT_VFACE(facing, -1, 1);
                half4 baseColor = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv).rgba;
                float3 normalWS = i.normalWS * isFrontFace;
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(baseColor.rgb, 1), half3(_Metallic, _Smoothness, 1));
                InputData inputData = InitializeInputData(i, i.positionWS, GetFakeTBN(normalWS), normalWS);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull Off

            HLSLPROGRAM
            #pragma target 2.0
            
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment
            
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW
            #pragma multi_compile _ LOD_FADE_CROSSFADE

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            
            float3 _LightDirection;
            float3 _LightPosition;
            float4 _ShadowBias;
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS     : NORMAL;
                float2 uv0           : TEXCOORD0;
                float2 uv1          : TEXCOORD1;
            };

            struct Varyings
            {
                float2 uv           : TEXCOORD0;
                float4 positionCS   : SV_POSITION;
            };
            
            float3 ApplyShadowBias(float3 positionWS, float3 normalWS, float3 lightDirection)
            {
                float invNdotL = 1.0 - saturate(dot(lightDirection, normalWS));
                float scale = invNdotL * _ShadowBias.y;

                // normal bias is negative since we want to apply an inset normal offset
                positionWS = lightDirection * _ShadowBias.xxx + positionWS;
                positionWS = normalWS * scale.xxx + positionWS;
                return positionWS;
            }
            
            float4 GetShadowPositionHClip(Attributes input)
            {
                float pixel = 1.0 / _TextureWidth;
                float half_pixel = pixel * 0.5;
                float2 vatUV = input.uv1 + float2(half_pixel, _Time.y * _AnimationSpeed);
                
                float3 unsignedPositionOS = SAMPLE_TEXTURE2D_LOD(_VATPos, sampler_VATPos, vatUV, 0).xyz;
                float3 unsignedNormalOS = SAMPLE_TEXTURE2D_LOD(_VATNormals, sampler_VATNormals, vatUV, 0).xyz;
                
                float3 positionOS = UnpackDataFromTexture(unsignedPositionOS);
                float3 normalOS = UnpackDataFromTexture(unsignedNormalOS);

                normalOS = normalize(normalOS);
                
                VertexPositionInputs vertexInput = GetVertexPositionInputs(positionOS);

                float3 positionWS = vertexInput.positionWS;
                float3 normalWS = TransformObjectToWorldNormal(normalOS);

                #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                    float3 lightDirectionWS = normalize(_LightPosition - positionWS);
                #else
                    float3 lightDirectionWS = _LightDirection;
                #endif

                    float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));

                #if UNITY_REVERSED_Z
                    positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #else
                    positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #endif

                return positionCS;
            }

            Varyings ShadowPassVertex(Attributes input)
            {
                Varyings output;
                
                output.positionCS = GetShadowPositionHClip(input);
                output.uv = input.uv0;
                return output;
            }

            half4 ShadowPassFragment(Varyings input) : SV_TARGET
            {
                #if defined(LOD_FADE_CROSSFADE)
		            LODFadeCrossFade(input.positionCS);
	            #endif

                return 0;
            }
            
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            Cull Off
            ZWrite On

            HLSLPROGRAM
            #pragma target 2.0
            
            #pragma vertex DepthNormalsVertex
            #pragma fragment DepthNormalsFragment
            
            struct Attributes
            {
                float4 positionOS     : POSITION;
                float3 normal       : NORMAL;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                half3 normalWS     : TEXCOORD2;
            };
                   
            Varyings DepthNormalsVertex(Attributes input)
            {
                Varyings output = (Varyings)0;
                float pixel = 1.0 / _TextureWidth;
                float half_pixel = pixel * 0.5;
                float2 vatUV = input.uv1 + float2(half_pixel, _Time.y*_AnimationSpeed);
                
                float3 unsignedPositionOS = SAMPLE_TEXTURE2D_LOD(_VATPos, sampler_VATPos, vatUV, 0).xyz;
                float3 unsignedNormalOS = SAMPLE_TEXTURE2D_LOD(_VATNormals, sampler_VATNormals, vatUV, 0).xyz;
                
                float3 positionOS = UnpackDataFromTexture(unsignedPositionOS);
                float3 normalOS = UnpackDataFromTexture(unsignedNormalOS);

                normalOS = normalize(normalOS);
                output.positionCS = TransformObjectToHClip(positionOS);
                
                VertexNormalInputs normalInput = GetVertexNormalInputs(normalOS);
                output.uv0 = input.uv0;
                output.normalWS = half3(normalInput.normalWS);
                return output;
            }

            void DepthNormalsFragment(
                Varyings input
                , out half4 outNormalWS : SV_Target0
            #ifdef _WRITE_RENDERING_LAYERS
                , out float4 outRenderingLayers : SV_Target1
            #endif
            )
            {
                outNormalWS = half4(input.normalWS, 0.0);
                #ifdef _WRITE_RENDERING_LAYERS
                    uint renderingLayers = GetMeshRenderingLayer();
                    outRenderingLayers = float4(EncodeMeshRenderingLayer(renderingLayers), 0, 0, 0);
                #endif
            }
            ENDHLSL
        }
    }
}
