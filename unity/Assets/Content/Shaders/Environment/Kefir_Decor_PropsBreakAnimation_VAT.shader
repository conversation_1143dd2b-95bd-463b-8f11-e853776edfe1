Shader "Kefir/Kefir_Decor_PropsBreakAnimation_VAT"
{
    Properties
    {
        [Header(Color)]
        _BaseColor("Base Color (B)", Color) = (1, 1, 1, 1)
        _Base_Color_A("Base Color (A)", Color) = (1, 1, 1, 1)
        [HDR]_EmissionColor("Emission Color", Color) = (1,1,1,1)
        _Emission("Emission", Range(0,1)) = 0
        [Header(Textures)]
        [NoScaleOffset]_BaseMap("Base Map", 2D) = "white" {}
        [NoScaleOffset]_EmissionMap("EmissionMap", 2D) = "white" {}
        _Base_Map_Tiling_Offset("Base Map Tiling Offset", Vector) = (1, 1, 1, 1)
        [NoScaleOffset]_MaskMap("MSA Map", 2D) = "black" {}
        [NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        [Header(VAT Settings)]
        _PositionTexture ("Position Texture", 2D) = "black" {}
        _RotationTexture ("Rotation Texture", 2D) = "black" {}
        _TotalFrames ("Total Frames", Float) = 30
        _AnimationFrameCount("Animation Frame Count", Float) = 10
        _AnimationIndex("Animation Index", Float) = 0
        _StartTime("Start Time Animation", Float) = 0
        _ObjectsCount("Objects Count in Animation", Float) = 0
        _BoundingBoxSize("Bounding Box Size", Float) = 0
        [Toggle(_USE_DEBUG_TIME)] _UseDebugTime("Use Debug Time", Float) = 0
        _DebugProgress ("Debug Progress", Range(0, 1)) = 0
        _TimeSpeed("Time Speed", Float) = 10
    }
    SubShader
    {
        Tags
        {
            "RenderPipeline" = "UniversalPipeline" "RenderType" = "Opaque" "Queue" = "Geometry"
        }
 
        HLSLINCLUDE
        
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

        #pragma shader_feature _ _USE_DEBUG_TIME
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BumpMap);
        TEXTURE2D(_MaskMap);
        TEXTURE2D(_EmissionMap);
        TEXTURE2D(_RotationTexture);
        TEXTURE2D(_PositionTexture);
        SAMPLER(sampler_PositionTexture);
        CBUFFER_START(UnityPerMaterial)
            float4 _Base_Map_Tiling_Offset;
            float _TotalFrames;
            float _ObjectsCount;
            float _DebugProgress;
            float _TimeSpeed;
            float _StartTime;
            float _AnimationIndex;
            float _AnimationFrameCount;
            float _BoundingBoxSize;
            half4 _BaseColor;
            half4 _Base_Color_A;
            half3 _EmissionColor;
            half _Emission;
        CBUFFER_END

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            return UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BaseMap, snData.uv));
        }

        float3 RotateByQuaternion(float3 pos, float4 quat)
        {
            float3 t = 2 * cross(quat.xyz, pos);
            return pos + quat.w * t + cross(quat.xyz, t);
        }

        float4 InverseQuat(float4 q)
        {
            return float4(-q.xyz, q.w);
        }

        float4 MulQuat(float4 q1, float4 q2)
        {
            return float4(
                q1.w*q2.x + q1.x*q2.w + q1.y*q2.z - q1.z*q2.y,
                q1.w*q2.y - q1.x*q2.z + q1.y*q2.w + q1.z*q2.x,
                q1.w*q2.z + q1.x*q2.y - q1.y*q2.x + q1.z*q2.w,
                q1.w*q2.w - q1.x*q2.x - q1.y*q2.y - q1.z*q2.z
            );
        }   

        float GetProgress()
        {
            float progress = _Time.y;
            
            progress -= _StartTime;
            progress *= _TimeSpeed;
            
            progress = min(_AnimationFrameCount, progress);
            #if defined(_USE_DEBUG_TIME)
                progress = _AnimationFrameCount * _DebugProgress;
            #endif
            return progress;
        }
        
        struct PositionNormal
        {
            float3 position;
            float3 normal;
        };

        PositionNormal GetAnimatedPositionNormal(float3 positionOS, float4 vertexColor, float3 normalOS, float currentClipFrame, float animationIndex)
        {
            float absoluteFrameIndex = (_AnimationFrameCount * _AnimationIndex + _AnimationIndex * 2) + currentClipFrame;

            float frameNorm = (absoluteFrameIndex + 0.5) / _TotalFrames;
            frameNorm = frameNorm - floor(frameNorm);

            float3 finalPosition = 0;
            float3 finalNormal = 0;

            float4 boneIndexes = vertexColor;
            
            float boneIndex = boneIndexes.r;

            float pixel = 1.0 / _ObjectsCount;
            float bonePixelIdx = round(boneIndex * (_ObjectsCount));

            float U = (bonePixelIdx + 0.5) * pixel;  
            float2 texUV = float2(U, frameNorm);

            float3 objectStartPivot = vertexColor.yzw * 2 - 1;
            objectStartPivot *= _BoundingBoxSize;
            float3 objectPos = SAMPLE_TEXTURE2D_LOD(_PositionTexture,sampler_PositionTexture, texUV, 0).rgb;

            float3 vertOffset = positionOS - objectStartPivot;

            float4 objectRotation = SAMPLE_TEXTURE2D_LOD(_RotationTexture,sampler_PositionTexture, texUV, 0);
            
            float3 rotatedVertex = RotateByQuaternion(vertOffset, objectRotation);
            finalPosition += rotatedVertex + objectPos + objectStartPivot;
            
            float3 rotatedNormal = RotateByQuaternion(normalOS, objectRotation);
            finalNormal += rotatedNormal;
             
            PositionNormal result;
            result.position = finalPosition;
            result.normal = finalNormal;
            return result;
        }

        PositionNormal GetFinalPositionAndNormalOS(float3 positionOS, float4 vertexColor, float3 normalOS)
        {
            PositionNormal animatedPosNormals_A = GetAnimatedPositionNormal(positionOS, vertexColor, normalOS, GetProgress(), _AnimationIndex);
            PositionNormal result;
            result.position = animatedPosNormals_A.position;
            result.normal = animatedPosNormals_A.normal;
            return result;
        }
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            #pragma vertex LitPassVertex
            #pragma fragment LitPassFragment
            

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
                float4 vertexColor : COLOR;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS: TEXCOORD1;
                DECLARE_SH(sh, 4)
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            Varyings LitPassVertex(Attributes i)
            {
                Varyings o;
                o.uv = i.uv;
                PositionNormal positionAndNormalOS = GetFinalPositionAndNormalOS(i.positionOS.xyz, i.vertexColor, i.normalOS);

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(positionAndNormalOS.position);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(positionAndNormalOS.normal, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)   
                return o;
            }

            half4 LitPassFragment(Varyings i, bool facing : FRONT_FACE_SEMANTIC) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                float isFrontFace =  IS_FRONT_VFACE(facing, 1, -1);
                tbn[0] *= isFrontFace;
                tbn[2] *= isFrontFace;

                float2 uv = i.uv;
                
                half4 albedo = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uv);
                
                #ifdef _ALPHATEST_ON
                    clip(albedo.a - _AlphaClipThreshold);
                #endif

                half4 metalnessSmoothnessPaintBA = SAMPLE_TEXTURE2D(_MaskMap, sampler_BaseMap, uv);

                albedo = lerp(albedo, albedo * _BaseColor, metalnessSmoothnessPaintBA.z);
                albedo = lerp(albedo, albedo * _Base_Color_A, metalnessSmoothnessPaintBA.w);
                half3 emission = SAMPLE_TEXTURE2D(_EmissionMap, sampler_BaseMap, uv).rgb;
                emission *= _EmissionColor * _Emission;
                SurfaceData surfaceData = InitializeSurfaceData(albedo, half3(metalnessSmoothnessPaintBA.rg, 1), KefirSampleNormalTS(InitializeSampleNormalData(i.uv)));
                surfaceData.emission = emission;
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #pragma target 2.0
            
            // -------------------------------------
            // Shader Stages
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment
            
            // This is used during shadow map generation to differentiate between directional and punctual light shadows, as they use different formulas to apply Normal Bias
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW
            
            // Shadow Casting Light geometric parameters. These variables are used when applying the shadow Normal Bias and are set by UnityEngine.Rendering.Universal.ShadowUtils.SetupShadowCasterConstantBuffer in com.unity.render-pipelines.universal/Runtime/ShadowUtils.cs
            // For Directional lights, _LightDirection is used when applying shadow Normal Bias.
            // For Spot lights and Point lights, _LightPosition is used to compute the actual light direction because it is different at each shadow caster geometry vertex.
            float3 _LightDirection;
            float3 _LightPosition;
            float4 _ShadowBias;
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS     : NORMAL;
                float4 vertexColor : COLOR;
                float2 uv0 : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
            };
               
            float3 ApplyShadowBias(float3 positionWS, float3 normalWS, float3 lightDirection)
            {
                float invNdotL = 1.0 - saturate(dot(lightDirection, normalWS));
                float scale = invNdotL * _ShadowBias.y;

                // normal bias is negative since we want to apply an inset normal offset
                positionWS = lightDirection * _ShadowBias.xxx + positionWS;
                positionWS = normalWS * scale.xxx + positionWS;
                return positionWS;
            }
            

            Varyings ShadowPassVertex(Attributes input)
            {
                Varyings output;

                PositionNormal positionAndNormalOS = GetFinalPositionAndNormalOS(input.positionOS.xyz, input.vertexColor, input.normalOS);

                float3 positionWS = TransformObjectToWorld(positionAndNormalOS.position);
                float3 normalWS = TransformObjectToWorldNormal(positionAndNormalOS.normal);

                #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                    float3 lightDirectionWS = normalize(_LightPosition - positionWS);
                #else
                    float3 lightDirectionWS = _LightDirection;
                #endif

                    float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));

                #if UNITY_REVERSED_Z
                    positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #else
                    positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #endif

                output.positionCS = positionCS;
                return output;
            }

            half4 ShadowPassFragment(Varyings input) : SV_TARGET
            {
                return 0;
            }
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #pragma target 2.0
            
            #pragma vertex DepthNormalsVertex
            #pragma fragment DepthNormalsFragment
            
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            
            struct Attributes
            {
                float4 positionOS     : POSITION;
                float3 normalOS       : NORMAL;
                float4 tangentOS : TANGENT;
                half4 vertexColor : COLOR;
                float2 uv0 : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                half4 vertexColor : TEXCOORD2;
                float3x3 tbn : TEXCOORD3;
            };
                   
            Varyings DepthNormalsVertex(Attributes input)
            {
                Varyings output = (Varyings)0;

                PositionNormal positionAndNormalOS = GetFinalPositionAndNormalOS(input.positionOS.xyz, input.vertexColor, input.normalOS);

                VertexPositionInputs vertexInput = GetVertexPositionInputs(positionAndNormalOS.position);
                output.positionCS = vertexInput.positionCS;
                VertexNormalInputs normalInput = GetVertexNormalInputs(positionAndNormalOS.normal, input.tangentOS);
                output.uv0 = input.uv0;
                output.vertexColor = input.vertexColor;
                output.tbn = float3x3(
                    normalInput.tangentWS,
                    normalInput.bitangentWS,
                    normalInput.normalWS);
                return output;
            }

            void DepthNormalsFragment(
                Varyings input
                , out half4 outNormalWS : SV_Target0
            #ifdef _WRITE_RENDERING_LAYERS
                , out float4 outRenderingLayers : SV_Target1
            #endif
            )
            {
                float baseColorAlpha = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uv0).a;
                float epsilon = 1e-5;
                bool baseColorClipEqual = abs(baseColorAlpha - 0.2) > epsilon;
                clip(baseColorClipEqual - 0.5);

                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(input.uv0));
                outNormalWS = half4(TransformUnpackedToWorldNormal(normalTS, input.tbn), 0.0);

                #ifdef _WRITE_RENDERING_LAYERS
                    uint renderingLayers = GetMeshRenderingLayer();
                    outRenderingLayers = float4(EncodeMeshRenderingLayer(renderingLayers), 0, 0, 0);
                #endif
            }
            ENDHLSL
        }
    }
}
