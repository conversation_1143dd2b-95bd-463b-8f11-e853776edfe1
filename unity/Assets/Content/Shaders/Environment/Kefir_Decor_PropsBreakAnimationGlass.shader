Shader "Kefir/Kefir_Decor_PropsBreakAnimationGlass"
{
    Properties
    {
        _Scale("Scale", Float) = 1
        _WeightCoeff("Weight Coefficient", Range(1, 5)) = 1
        [Header(Base Settings)]
        _BumpMap("Normal Map", 2D) = "bump" {}
        _BumpMapStrength("NormalStr", Range(0,2)) = 1
        _Color("Color", Color) = (1, 1, 1, 1)
        _Metallic("Metallic", Float) = 1
        _Occlusion("Occlusion", Range(0, 1)) = 1
        _Smoothness("Smoothness", Float) = 1        
        [Header(Direction)]
        _Center("Center", Vector) = (0,0,0,0)
        _CenterHeightOffset("Center Height Offset", Float) = 1
        _HitDirection("Hit Direction", Float) = 1
        _HitVector("Hit Vector", Vector) = (0,0,0,0)
        [Toggle] _UseHitVector("Use Hit Vector", Float) = 0
        
        [Header(Push)]
        _SpreadPower("Spread Power", Range(0, 20)) = 3.47
        _SpreadRadius("Spread Radius", Range(0, 5)) = 0.46
        _PushPower("Push Power", Float) = 0.27
        _PushPowerRadius("Push Power Radius", Range(0.01, 5)) = 1.03
        _AdditionalPushPower("Additional Power", Range(-20,20)) = 0.55
        _AdditionalPushPowerRadius("Additional Power Radius", Range(0,5)) = 0.21
        _VectorGradientPush("Vector Gradient Push", Vector) = (0,1,0,0)
        _GradientPush("Gradient Push", Float) = 0
        _GradientPushRandomMin("Gradient Push Random Min", Float) = 0
        _GradientPushRandomMax("Gradient Push Random Max", Float) = 0
                
        [Header(Gravity)]
        _Gravity("Gravity", Float) = 1
        _GravityLate("Gravity Late Gradient", Float) = 1
        _GravityLateGlobal("Gravity Late Global", Float) = 0
        _FloorHeight("Floor Height", Float) = 0
        _FloorScaleReduction("Floor Scale Reduction", Float) = 80
        
        [Header(Rotation)]
        _MinRotation("Min Rotation", Float) = 0.5
        _MaxRotation("Max Rotation", Float) = 1     
        _GradientPushRotation("Gradient Push Rotation", Float) = 0   
        [Header(Time)]
        [Toggle(_USE_DEBUG_TIME)] _UseDebugTime("Use Debug Time", Float) = 0
        _DebugPseudoTime("Pseudo Time", Range(0, 5)) = 1
        _TimeSpeed("Time Speed", Float) = 7
        _StartTime("Start Time", Float) = 0
        _TimeLateByVectorFactor("Time Late By Vector Factor", Float) = 0
        _TimeLateVector("Time Late Vector", Vector) = (0,0,0,0)

        [Header(Alpha Clipping)]
        [Toggle(_ALPHATEST_ON)] _AlphaClip("Alpha Clipping", Float) = 0
        _AlphaClipThreshold("Alpha Clip Threshold", Range(0,1)) = 0.5
        [Enum(UnityEngine.Rendering.CullMode)] _Cull("Cull Mode", Int) = 2

    }
    
    SubShader
    {
        Tags
        {
            "RenderPipeline" = "UniversalPipeline" "RenderType" = "Transparent" "Queue" = "Transparent"
        }
        Blend SrcAlpha OneMinusSrcAlpha
        ZWrite Off
        ZTest LEqual
        Cull [_Cull]
           
        HLSLINCLUDE
        
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include_with_pragmas "Assets/Utils/SkySystem/Shaders/FogInclude.cginc"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

        #pragma shader_feature_local_vertex _ _USE_DEBUG_TIME

        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);

        CBUFFER_START(UnityPerMaterial)
            float4 _BumpMap_ST;
            float3 _Center;
            float3 _VectorGradientPush;
            float3 _TimeLateVector;
        
            float _PushPowerRadius;
            float _Scale;
            float _WeightCoeff;
            float _AdditionalPushPower;
            float _AdditionalPushPowerRadius;
            float _HitDirection;
            float _SpreadPower;
            float _FloorScaleReduction;
            float _SpreadRadius;
            float _FloorHeight;
            float _CenterHeightOffset;
            float _GravityLate;
            float _Gravity;
            float _PushPower;
            float _MinRotation;
            float _MaxRotation;
            float _GravityLateGlobal;
            float _GradientPush;
            float _TimeSpeed;
            float _StartTime;
            float _GradientPushRandomMin;
            float _GradientPushRandomMax;
            float _GradientPushRotation;
            float _TimeLateByVectorFactor;
            float _DebugPseudoTime;

            half4 _Color;
            half3 _HitVector;
            half _AlphaClipThreshold;
            half _UseHitVector;
            half _Metallic;
            half _Smoothness;
            half _Occlusion;
            half _BumpMapStrength;
        CBUFFER_END

            struct PositionNormal
            {
                float3 position;
                float3 normal;
            };
            
            float GetProgress()
            {
                float progress = (_Time.y  - _StartTime);
                #if defined(_USE_DEBUG_TIME)
                    progress = _DebugPseudoTime;
                #endif
                return progress *= _TimeSpeed;
            }
            
            PositionNormal GetAnimatedPositionNormal(float3 positionOS, float4 vertexColor, float3 center, float3 normalOS)
            {
                center.y += _CenterHeightOffset;
                float debrisWeightCoeff = vertexColor.a * _WeightCoeff;
                float3 debrisPos = vertexColor.rgb * 2 - 1;
                debrisPos = float3(-debrisPos.x, debrisPos.y, debrisPos.z) * _Scale;
                float randomSeed = debrisPos.x + debrisPos.y + debrisPos.z;
                
                float distDebris = distance(debrisPos, center);
                float timeLate = dot(debrisPos, normalize(_TimeLateVector)) * _TimeLateByVectorFactor;
                timeLate -= _TimeLateByVectorFactor / 2;
                float progress = max(0, GetProgress() - timeLate);
                float3 hitVector = lerp(float3(0, 0, -_HitDirection), _HitVector, _UseHitVector);
                
                float pushMask = max(0, 1 - distDebris / _PushPowerRadius);
                float mainPushPower = pushMask * _PushPower;
                
                float spreadRadius = max(0, 1 - distDebris / _SpreadRadius);
                float3 spreadVelocity = _SpreadPower * (debrisPos - center) * spreadRadius;
            
                float additionalPowerRadius = max(0, 1 - distDebris / _AdditionalPushPowerRadius);
                float3 additionalVelocity = hitVector * (additionalPowerRadius * _AdditionalPushPower);
                float gradientVelocity = dot(debrisPos, normalize(_VectorGradientPush)) * _GradientPush * RandomValue(randomSeed + _StartTime, _GradientPushRandomMin, _GradientPushRandomMax);
                float3 velocity = hitVector * mainPushPower + spreadVelocity + additionalVelocity + float3(0, gradientVelocity, 0);
                
                float gravityLate = _GravityLate * RandomValue(randomSeed, 0.5, 2) * debrisPos.y;
                float gravityProgress = max(0, progress - gravityLate - _GravityLateGlobal);
                float gravity = _Gravity * gravityProgress;
                gravity *= -gravityProgress;
                float3 gravityVelocity = float3(0, gravity, 0);
                float currentDebrisPosY = (gravityVelocity.y + velocity.y * progress) * debrisWeightCoeff;
                float floorScaleReduction = 1 - (currentDebrisPosY - _FloorHeight);
                floorScaleReduction = 1 - floorScaleReduction / _FloorScaleReduction;

                float3 currentDebrisPos = debrisPos;
                float heightCoeff = saturate(debrisPos.y - _FloorHeight);
                float limitProgress = debrisPos.y + gradientVelocity + (1 - distDebris) + dot(velocity, velocity) - heightCoeff;
                float2 limitDebrisPosXZ = debrisPos.xz + (gravityVelocity.xz + velocity.xz * limitProgress) * debrisWeightCoeff;
                currentDebrisPos += (gravityVelocity + velocity * progress) * debrisWeightCoeff;
                float rightLimit = min(limitDebrisPosXZ.x, currentDebrisPos.x);
                float leftLimit = max(limitDebrisPosXZ.x, currentDebrisPos.x);
                currentDebrisPos.x = velocity.x <= 0 ? leftLimit : rightLimit;
                float forwardLimit = min(limitDebrisPosXZ.y, currentDebrisPos.z);
                float backLimit = max(limitDebrisPosXZ.y, currentDebrisPos.z);
                currentDebrisPos.z = velocity.z <= 0 ? backLimit : forwardLimit;
                currentDebrisPos.y = max(_FloorHeight, currentDebrisPos.y);
                
                float3 offset = positionOS - debrisPos;
            
                float randomRotationPower = RandomValue(randomSeed + _StartTime, _MinRotation, _MaxRotation);
                float gradientRotation = dot(debrisPos, normalize(_VectorGradientPush)) * _GradientPushRotation;
                float rotation = ((randomRotationPower + gradientRotation) * sign(velocity.z) * progress) * debrisWeightCoeff;
                float limitRotation = ((randomRotationPower + gradientRotation) * sign(velocity.z) * limitProgress) * debrisWeightCoeff;
                float positiveRotationLimit = min(limitRotation, rotation);
                float negativeRotationLimit = max(limitRotation, rotation);
                rotation = velocity.z <= 0 ? negativeRotationLimit : positiveRotationLimit;
            
                float3 rotationAxis = float3(RandomRange(randomSeed), RandomRange(randomSeed - 10), RandomRange(randomSeed + 10));
                float3 rotatedOffset = RotateAboutAxis(rotationAxis, rotation, offset);
                
                float3 finalPosition = currentDebrisPos + rotatedOffset * saturate(floorScaleReduction);
                float3 finalNormal = RotateAboutAxis(rotationAxis, rotation, normalOS);
                
                PositionNormal result;
                result.position = finalPosition;
                result.normal = finalNormal;
                return result;
            }
        
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #define _REFLECTION_PROBES_NO_BLENDING 1
            #define _SURFACE_TYPE_TRANSPARENT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Macros.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            struct Attributes
            {
                float3 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
                float4 vertexColor : COLOR0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv: TEXCOORD0;
                float4x3 tbn_positionWS: TEXCOORD1;
                DECLARE_SH(sh, 4)
                float3 posOS: TEXCOORD5;
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            Varyings vert(Attributes i)
            {
                Varyings o;
                o.uv = i.uv;

                PositionNormal animatedPosNormals = GetAnimatedPositionNormal(i.positionOS.xyz, i.vertexColor, _Center, i.normalOS);

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(animatedPosNormals.position);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(animatedPosNormals.normal, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                
                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                o.posOS = i.positionOS.xyz;
                
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                half3 normalTS = UnpackNormalScale(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, TRANSFORM_TEX(i.uv, _BumpMap)), _BumpMapStrength);
                half3 albedo = _Color.rgb;

                SurfaceData surfaceData = InitializeSurfaceData(half4(saturate(albedo),  _Color.a), half3(_Metallic, _Smoothness, _Occlusion), normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                half4 model = UniversalFragmentPBR(inputData, surfaceData);
                return MixFogForObjects(positionWS, model);
            } 
            ENDHLSL
        }
    }
}
