Shader "Kefir/Kefir_Decor_Seafront"
{
    Properties
    {
        [NoScaleOffset]_BaseMap("BaseMap (RGB) Smoothness (A)", 2D) = "white" {}
        [NoScaleOffset]_BumpMap("Normal", 2D) = "white" {}
        [NoScaleOffset]_BaseMapLayer1("Base Map Layer 1", 2D) = "white" {}
        _NoiseAndLeaksMask("Noise(R) Leaks(G) Mask", 2D) = "white" {}
        _LeaksColor("Leaks Color", Color) = (1, 1, 1, 1)
        _LayersBlend("Layers Blend", Range(0, 1)) = 0
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry-50"}
        
        HLSLINCLUDE
              
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BaseMapLayer1);
        SAMPLER(sampler_BaseMapLayer1);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_NoiseAndLeaksMask);
        SAMPLER(sampler_NoiseAndLeaksMask);
               
        CBUFFER_START(UnityPerMaterial)
            float4 _NoiseAndLeaksMask_ST;
            float4 _LeaksColor;
            float _LayersBlend;
        CBUFFER_END

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            return UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv));
        }
        
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            
            #pragma vertex LitPassVertex
            #pragma fragment LitPassFragment

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float4 vertexColor : COLOR0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                DECLARE_SH(sh, 5)
                half4 vertexColor : TEXCOORD6;
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings LitPassVertex(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                
                o.uv = i.uv;
                o.vertexColor = i.vertexColor;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                
                return o;
            } 
            
            half4 LitPassFragment(Varyings i) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                half4 baseColor = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv);
                
                // Triplanar for X and Z.
                float2 uvTriX = float2(positionWS.z, positionWS.y) * _NoiseAndLeaksMask_ST.xy + _NoiseAndLeaksMask_ST.zw;
                float2 uvTriZ = float2(positionWS.x, positionWS.y) * _NoiseAndLeaksMask_ST.xy + _NoiseAndLeaksMask_ST.zw;
                float2 normalTriXZ = abs(tbn[2]).xz;
                normalTriXZ /= normalTriXZ.x + normalTriXZ.y;
                float2 noiseAndLeaksMaskX = SAMPLE_TEXTURE2D(_NoiseAndLeaksMask, sampler_NoiseAndLeaksMask, uvTriX).rg;
                float2 noiseAndLeaksMaskZ = SAMPLE_TEXTURE2D(_NoiseAndLeaksMask, sampler_NoiseAndLeaksMask, uvTriZ).rg;
                float2 noiseAndLeaksMask = saturate(noiseAndLeaksMaskX * normalTriXZ.x + noiseAndLeaksMaskZ * normalTriXZ.y);

                // Final color blend.
                float noiseMask = noiseAndLeaksMask.r;
                float blendedNoise = lerp(noiseMask * _LayersBlend, 1, i.vertexColor.z) * i.vertexColor.x;
                half3 layerBaseColor = SAMPLE_TEXTURE2D(_BaseMapLayer1, sampler_BaseMapLayer1, i.uv).rgb;
                half3 blendedColor = lerp(baseColor.rgb, layerBaseColor, blendedNoise);                                
                float leaksMask = noiseAndLeaksMask.g;
				half3 leaksColor = _LeaksColor.rgb * blendedColor;
                half3 finalColor = lerp(blendedColor, leaksColor, leaksMask);
                half smoothness = baseColor.a;

                SurfaceData surfaceData = InitializeSurfaceData(half4(finalColor, 1), half3(0, smoothness, 1), KefirSampleNormalTS(InitializeSampleNormalData(i.uv)));
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }

            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #define _USE_NORMAL_MAP 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }
    }
}
