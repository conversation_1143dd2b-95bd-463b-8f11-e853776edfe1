Shader "Kefir/Kefir_Pools"
{
    Properties
    { 
        [Header(Ripples)]
        [NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        _SampleBumpMapParameters01("Sample Normal Parameteres 01 (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _SampleBumpMapParameters02("Sample Normal Parameteres 02 (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _SampleBumpMapParameters03("Sample Normal Parameteres 03 (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _BumpMapStrength("Normal Strength",Range(0,1)) = 1
        _DistortionStrength("Distortion Strength", Float) = 0.1
        
        [Space()]
        [Header(Tint)]
        _TintColor("Tint Color", Color) = (1,1,1,1)
        _TintCausticsColor("Tint Caustics Color", Color) = (1,1,1,1)
        
        [Space()]
        [Header(Transparency)]
        _TransparencyFade("Transparency Fade", Range(0,1)) = 1
        _TransparencyFadePower("Transparency Fade Power", Float) = 1
        
        [Space()]
        [Header(Water Effects)]
        _WaterEffectsNormalStrengthMultiplier("Water Effects Normal Strength Multiplier", Float) = 2
        _WaterEffectsNormalSmoothnessFactor("Water Effects Normal Smoothness Factor", Range(0, 1)) = 0.2
        _WaterEffectsHeightDistance("Water Effects Heeight Distance", Float) = 0.5
        
        [Space()]
        [Header(Caustics and Displacement)]
        _CausticsMap("Caustics Map", 2D) = "black" {}
        _SampleCausticsParameters("Sample Caustics Parameteres (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _CausticsDistortionMap("(RG) Caustics Distortion Map | (B) Vertex Displace", 2D) = "white" {}
        _SampleCausticsDistortionParameters("Sample Caustics Distortion Parameteres (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _CausticsIntensity("Caustics Intensity", Range(0,1)) = 0
        _CausticsDistortionIntensity ("Caustics Distortion Intensity", Float) = 1
        
        _SampleDisplacementParameters("Sample Displacement Parameteres (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _DisplacementIntensity("Displacement Intensity", Float) = 0.01
        
        [Space()]
        [Header(Duckweed)]
        [NoScaleOffset]_DuckweedMap("Duckweed Map", 2D) = "black" {}
        [NoScaleOffset]_DuckweedAlbedo("Duckweed Albedo", 2D) = "white" {}
        [NoScaleOffset]_DuckweedNormalMap("Duckweed Normal Map", 2D) = "bump" {}
        _DuckweedScaleOffset("Duckweed Scale Offset",Vector) = (1,1,0,0)
        _DuckweedMetallic("Duckweed Metallic", Range(0,1)) = 0
        _DuckweedSmoothness("Duckweed Smoothness", Range(0,1)) = 0
        
        [Space()]
        [Header(Duckweed Flow)]
        _DuckweedDistortion("Duckweed Distortion", Range(0,1)) = 0.01 
        
        
    }
    
    SubShader
    {
        Tags 
        { 
            "RenderPipeline"="UniversalPipeline"
            "RenderType"="Transparent"
            "UniversalMaterialType" = "Unlit"
            "Queue"="Transparent" 
        }

        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);

        TEXTURE2D(_WaterEffectsMap);
        SAMPLER(sampler_WaterEffectsMap);
        float4 _WaterEffectsMap_TexelSize;

        TEXTURE2D(_CausticsMap);
        SAMPLER(sampler_CausticsMap);
        
        TEXTURE2D(_CausticsDistortionMap);
        SAMPLER(sampler_CausticsDistortionMap);

        TEXTURE2D(_DuckweedMap);
        SAMPLER(sampler_DuckweedMap);
        TEXTURE2D(_DuckweedAlbedo);
        SAMPLER(sampler_DuckweedAlbedo);
        TEXTURE2D(_DuckweedNormalMap);
        
        CBUFFER_START(UnityPerMaterial)
            float4 _SampleBumpMapParameters01;
            float4 _SampleBumpMapParameters02;
            float4 _SampleBumpMapParameters03;
            float4 _SampleCausticsDistortionParameters;
            float4 _SampleCausticsParameters;
            float4 _SampleDisplacementParameters;
            float4 _DuckweedScaleOffset;
            float3 _WaterEffectsBottomLeftCorner;
            float3 _WaterEffectsTopRightCorner;
        
            float _BumpMapStrength;
            float _DistortionStrength;
            float _WaterEffectsNormalStrengthMultiplier;
            float _WaterEffectsNormalSmoothnessFactor;
            float _WaterEffectsHeightDistance;
            float _CausticsIntensity;
            float _CausticsSize;
            float _CausticsBlendDistanceY;
            float _CausticsDistortionIntensity;
            float _TransparencyFade;
            float _TransparencyFadePower;
            float _DuckweedDistortion;
            float _DisplacementIntensity;
        
            half4x4 _WaterCausticsMainLightDir;
            half4 _TintColor;
            half4 _TintCausticsColor;
            half _DuckweedMetallic;
            half _DuckweedSmoothness;
        CBUFFER_END
        ENDHLSL

        Pass
        {
            ZWrite On
            Blend SrcAlpha OneMinusSrcAlpha
            
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            #pragma shader_feature_local_fragment _CAUSTICS

            #define _SURFACE_TYPE_TRANSPARENT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/CameraTextures.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float4 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                float4 positionNDC : TEXCOORD4;
                DECLARE_SH(sh, 5)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            float2 CalculateUV(float2 uv, float4 parameters)
            {
                float2 offset = parameters.zw * _Time.y * parameters.y;
                float2 scale = uv * parameters.x;
                return scale + offset;
            }

            void RecalculateNormals(float3 positionWS, float displacement, inout float3 tangentWS, inout float3 bitangentWS, inout float3 normalWS)
            {
                const float offset = 0.01;
                float3 tangentOffsetWS = positionWS + tangentWS * offset;
                float3 bitangentOffsetWS = positionWS + bitangentWS * offset;               
                float3 tangentOffsetAnimatedWS = tangentOffsetWS + float3(0,1,0) * displacement;
                float3 bitangentOffsetAnimatedWS = bitangentOffsetWS + float3(0,1,0) * displacement;
                tangentWS = SafeNormalize(tangentOffsetAnimatedWS - positionWS);
                bitangentWS = SafeNormalize(bitangentOffsetAnimatedWS - positionWS);
                normalWS = -cross(bitangentWS, tangentWS);
            }

            float GetAccurateHeight(half2 uv)
            {
                float rawH = SAMPLE_TEXTURE2D(_WaterEffectsMap, sampler_WaterEffectsMap, uv).b;

                #if !SHADER_API_MOBILE
                float2 texelSize = _WaterEffectsMap_TexelSize.xy;
                float rawH1 = SAMPLE_TEXTURE2D(_WaterEffectsMap, sampler_WaterEffectsMap, uv + float2(1.0, 0.0) * texelSize).b;
                float rawH2 = SAMPLE_TEXTURE2D(_WaterEffectsMap, sampler_WaterEffectsMap, uv + float2(-1.0, 0.0) * texelSize).b;
                float rawH3 = SAMPLE_TEXTURE2D(_WaterEffectsMap, sampler_WaterEffectsMap, uv + float2(0.0, 1.0) * texelSize).b;
                float rawH4 = SAMPLE_TEXTURE2D(_WaterEffectsMap, sampler_WaterEffectsMap, uv + float2(0.0, -1.0) * texelSize).b;

                // Search for farthest depth, this prevents MSAA artifacts similar to described in this thread:
                // https://forum.unity.com/threads/fixing-screen-space-directional-shadows-and-anti-aliasing.379902/
                rawH = max(rawH, max(max(rawH1, rawH2), max(rawH3, rawH4)));
                #endif
                return rawH;
            }
            
            Varyings vert(Attributes i)
            {
                Varyings o;
                float3 positionWS = TransformObjectToWorld(i.positionOS.xyz);
                float4 causticsDistortionMap = SAMPLE_TEXTURE2D_LOD(_CausticsDistortionMap, sampler_CausticsDistortionMap, CalculateUV(positionWS.xz, _SampleDisplacementParameters), 0);
                float displacement = _DisplacementIntensity * causticsDistortionMap.b;
                positionWS += float3(0,1,0) * displacement;
                
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                
                VertexPositionInputs vertexInputs;
                vertexInputs.positionWS = positionWS;
                vertexInputs.positionVS = TransformWorldToView(positionWS);
                vertexInputs.positionCS = TransformWorldToHClip(positionWS);
                float4 ndc = vertexInputs.positionCS * 0.5f;
                vertexInputs.positionNDC.xy = float2(ndc.x, ndc.y * _ProjectionParams.x) + ndc.w;
                vertexInputs.positionNDC.zw = vertexInputs.positionCS.zw;
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.positionCS = vertexInputs.positionCS;
                o.positionNDC = vertexInputs.positionNDC;
                
                o.uv.xy = i.uv0;
                o.uv.zw = i.uv0 * _DuckweedScaleOffset.xy + _DuckweedScaleOffset.zw;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                
                return o;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                float depth = GetAccurateDepthMin(i.positionNDC.xy / i.positionNDC.w);
                
                float linearEyeDepth = LinearEyeDepth(depth, _ZBufferParams);

                float alpha = DepthFade(i.positionNDC, linearEyeDepth, _TransparencyFade);
                alpha = pow(alpha, _TransparencyFadePower);

                half3 viewDirectionWS = normalize(_WorldSpaceCameraPos.xyz - positionWS.xyz);
                
                // Water Effects.
                float2 watterEffectsUV = float2(
                                    InverseLerp(_WaterEffectsBottomLeftCorner.x, _WaterEffectsTopRightCorner.x, positionWS.x),
                                    InverseLerp(_WaterEffectsBottomLeftCorner.z, _WaterEffectsTopRightCorner.z, positionWS.z));
                
                half4 waterEffectsColor = SAMPLE_TEXTURE2D(_WaterEffectsMap, sampler_WaterEffectsMap, watterEffectsUV);

                // Water Effects Height Mask.
                float waterHeightMask = GetAccurateHeight(watterEffectsUV);
                waterHeightMask = abs(positionWS.y - waterHeightMask);
                waterHeightMask = saturate(InverseLerp(_WaterEffectsHeightDistance, 0, waterHeightMask)) * waterEffectsColor.a;

                // Normals Calculation.
                waterEffectsColor = waterEffectsColor * 2 - 1;
                waterEffectsColor.b = max(1.0e-16, sqrt(1.0 - saturate(dot(waterEffectsColor.xy, waterEffectsColor.xy))));
                half3 waterEffectsNormal = lerp(SafeNormalize(waterEffectsColor.rgb), float3(0.5,0.5,1), _WaterEffectsNormalSmoothnessFactor);

                // Sampling Ripples Normal Map (refraction and normalWS).
                half3 ripples01TS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, CalculateUV(positionWS.xz, _SampleBumpMapParameters01)));
                half3 ripples02TS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, CalculateUV(positionWS.xz, _SampleBumpMapParameters02)));
                half3 ripples03TS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, CalculateUV(positionWS.xz, _SampleBumpMapParameters03)));
                half3 normalTS = SafeNormalize(ripples01TS + ripples02TS + ripples03TS);
                normalTS = lerp(normalTS, waterEffectsNormal * _WaterEffectsNormalStrengthMultiplier, waterHeightMask);
                
                float3 normalWS = TransformUnpackedToWorldNormal(normalTS,tbn);
                normalWS = lerp(tbn[2], normalWS, _BumpMapStrength * alpha);
                
                // Refraction Offset.
                float2 refractionOffset = normalTS.xy * _DistortionStrength;
                float4 screenPosOffseted = i.positionNDC;
                screenPosOffseted.xy += refractionOffset;
                float2 refractionMaskOffset = (refractionOffset + i.positionNDC.xy) / i.positionNDC.w;
                
                // Refraction Mask.
                float screenPosRefractedDepth01 = GetAccurateDepthMax(refractionMaskOffset.xy);
                float3 screenPosRefractedWorldPos = ReconstructPositionWorld(screenPosRefractedDepth01, refractionMaskOffset.xy);
                float refractionMask = saturate(positionWS.y - screenPosRefractedWorldPos.y);
                
                // ScreenPos Final.
                float4 screenPos = lerp(i.positionNDC, screenPosOffseted, refractionMask);
                
                // Reconstructed World poistion for caustics and tint.
                float3 reconstructedPositionWS = ReconstructPositionWorld(depth,(i.positionCS.xy + refractionOffset) / _ScaledScreenParams.xy);
                reconstructedPositionWS = lerp(reconstructedPositionWS, screenPosRefractedWorldPos, refractionMask);

                // Caustics.
                float4 causticsDistortionMap = SAMPLE_TEXTURE2D(_CausticsDistortionMap, sampler_CausticsDistortionMap, CalculateUV(positionWS.xz, _SampleCausticsDistortionParameters));
                float3 lightUVs = mul(float4(reconstructedPositionWS, 1), _WaterCausticsMainLightDir).xyz;
                float2 causticsUV = lightUVs.xy;
                causticsUV += causticsDistortionMap.xy * _CausticsDistortionIntensity;
                half3 caustics = SAMPLE_TEXTURE2D(_CausticsMap, sampler_CausticsMap, CalculateUV(causticsUV, _SampleCausticsParameters)).rgb * _TintCausticsColor.rgb * _CausticsIntensity;
                
                // Sampling Scene Color.
                half3 sceneColor = GetSceneColor(screenPos) * _TintColor.rgb;

                // Duckweed.
                float2 duckweedUV = i.uv.zw + normalWS.xz * _DuckweedDistortion;
                half3 duckweedAlbedo = SAMPLE_TEXTURE2D(_DuckweedAlbedo, sampler_DuckweedAlbedo, duckweedUV).rgb;
                float3 duckweedNormalWS = mul(UnpackNormal(SAMPLE_TEXTURE2D(_DuckweedNormalMap, sampler_DuckweedAlbedo, duckweedUV)), tbn);
                float duckweedMetallic = _DuckweedMetallic;
                float duckweedSmoothness = _DuckweedSmoothness;
                float duckweedMask = SAMPLE_TEXTURE2D(_DuckweedMap, sampler_DuckweedMap, i.uv.xy + normalWS.xy * _DuckweedDistortion).r;
                
                half3 albedo = lerp(0, duckweedAlbedo, duckweedMask);
                half metallic = lerp(1, duckweedMetallic, duckweedMask);
                half smoothness = lerp(1, duckweedSmoothness, duckweedMask);
                normalWS = lerp(normalWS, duckweedNormalWS, duckweedMask);
                caustics = lerp(caustics, 0, duckweedMask);

                // TODO: change custom lighting model to be more look like Ocean Water, once both this and Ocean are in master
                // Custom lighting model.
                BRDFData brdfData = (BRDFData)0;
                InitializeBRDFData(albedo, metallic, 0.5, smoothness, alpha, brdfData);
                half3 bakedGI = COMBINE_VERTEX_FRAGMENT_SH(normalWS, i.sh);
                half3 giColor = GlobalIllumination(brdfData, bakedGI, 1, positionWS, normalWS, viewDirectionWS, 0);
                
                float fresnel = FresnelEffect(normalWS, viewDirectionWS, 1);

                // PBR. (For Duckweed)
                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, alpha), half3(metallic, smoothness, 1), half3(0.0, 0.0, 1.0), 0);
                InputData inputData = InitializeInputData(i, positionWS, tbn, normalWS);
                half3 pbr = UniversalFragmentPBR(inputData, surfaceData).rgb;

                // Blend custom lighting and duckweed PBR. 
                half3 model = lerp(sceneColor, giColor, fresnel);
                model = lerp(model, caustics, saturate(caustics).x);
                model = lerp(pbr, model, 1- duckweedMask);
                return half4(model + caustics, alpha);
            }
            ENDHLSL
        }
    }
}
