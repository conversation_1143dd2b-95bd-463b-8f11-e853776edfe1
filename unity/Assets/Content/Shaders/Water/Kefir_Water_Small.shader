Shader "Kefir/Kefir_Water_Small"
{
    Properties
    { 
        [Header(Water Tint)]
        _Darkening("Darkening",Range(0,1)) = 0
        _WaterCloseTintColor("Water Close Tint Color", Color) = (1,1,1,1)
        _WaterFarTintColor("Water Far Tint Color", Color) = (1,1,1,1)
        _WaterFarCloseTintFadePower("Water Close/Far Tint Fade Power", Float) = 1
        [NoScaleOffset]_WaterTintMap("Water Tint Map", 2D) = "white" {}
        [Space()]
        _WaterDeepTintColor("Water Deep Tint Color", Color) = (1,1,1,1)
        _WaterDeepDistance("Water Deep Distance",Range(0,1)) = 1
        
        [Header(Ripples)]
        [NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        _SampleBumpMapParameters01("Sample Normal Parameteres 01 (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _SampleBumpMapParameters02("Sample Normal Parameteres 02 (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _SampleBumpMapParameters03("Sample Normal Parameteres 03 (X — Size | Y — Speed | ZW — Direction)", Vector) = (1,1,1,1)
        _DistortionStrength("Distortion Strength", Float) = 0.1
        
        [Header(Foam)]
        [NoScaleOffset]_FoamMap("Foam Map",2D) = "black" {}
        _FoamDistortion("Foam Distortion",Float) = 1
        
        [Header(Caustics)]
        [Toggle(_CAUSTICS)] _Caustics("Caustics", Float) = 0
        _CausticsIntensity("Caustics Intencity", Range(0,1)) = 1
        _CausticsSize("Caustics Size", Range(0,1)) = 0.15
        _CausticsBlendDistanceY("Caustics Blend Distance Y", Range(0,10)) = 5
        [NoScaleOffset]_CausticsTexture("Caustics Texture", 2D) = "white" {}
        
        [Header(Transparency)]
        _TransparencyFade("Transparency Fade",Range(0,1)) = 1
        _TransparencyFadePower("Transparency Fade Power", Float) = 1
    }
    
    SubShader
    {
        Tags { 
            "RenderPipeline"="UniversalPipeline"
            "RenderType"="Transparent"
            "UniversalMaterialType" = "Unlit"
            "Queue"="Transparent" 
        }
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

        TEXTURE2D(_WaterTintMap);
        SAMPLER(sampler_WaterTintMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_FoamMap);
        SAMPLER(sampler_FoamMap);
        TEXTURE2D(_CausticsTexture);
        SAMPLER(sampler_CausticsTexture);

        CBUFFER_START(UnityPerMaterial)
            float4 _SampleBumpMapParameters01;
            float4 _SampleBumpMapParameters02;
            float4 _SampleBumpMapParameters03;
            float _Darkening;
            float _WaterFarCloseTintFadePower;
            float _WaterDeepDistance;
            float _DistortionStrength;
            float _FoamDistortion;
            float _CausticsIntensity;
            float _CausticsSize;
            float _CausticsBlendDistanceY;
            float _TransparencyFade;
            float _TransparencyFadePower;
        
            half3 _WaterCloseTintColor;
            half3 _WaterFarTintColor;
            half3 _WaterDeepTintColor;
        CBUFFER_END
        ENDHLSL
        
        Pass
        {
            ZWrite On
            Blend SrcAlpha OneMinusSrcAlpha
            
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            #pragma shader_feature_local_fragment _CAUSTICS

            #define _SURFACE_TYPE_TRANSPARENT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/CameraTextures.hlsl"
            #include "Assets/Content/ShaderLibrary/Water.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float4 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                float4 positionNDC : TEXCOORD4;
                DECLARE_SH(sh, 5)
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;
                o.positionNDC = vertexInputs.positionNDC;
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                
                o.uv.xy = i.uv0;
                o.uv.zw = i.uv1;
                
                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }

            float2 CalculateUV(float2 uv, float4 parameters)
            {
                float2 offset = parameters.zw * (_Time.y * parameters.y);
                float2 scale = uv * parameters.x;
                return scale + offset;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                // Sampling Ripples Normal Map (refraction and normalWS).
                half3 ripples01TS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, CalculateUV(i.uv.xy, _SampleBumpMapParameters01)));
                half3 ripples02TS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, CalculateUV(i.uv.xy, _SampleBumpMapParameters02)));
                half3 ripples03TS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, CalculateUV(i.uv.xy, _SampleBumpMapParameters03)));
                half3 normalTS = normalize(ripples01TS + ripples02TS + ripples03TS);

                float2 refractionOffset = normalTS.xy * _DistortionStrength;
                float4 screenPosOffseted = i.positionNDC;
                screenPosOffseted.xy += refractionOffset;
                float2 refractionMaskOffset = refractionOffset + i.positionCS.xy;
                
                // View Direction.
                half3 viewDirectionWS = normalize(_WorldSpaceCameraPos.xyz - positionWS.xyz);
                
                // Foam Depth Fade.
                float depth = GetAccurateDepthMin(i.positionCS.xy / _ScaledScreenParams.xy);
                
                float linearEyeDepth = LinearEyeDepth(depth, _ZBufferParams);
                
                // Refraction Mask.
                float refractionMask = RefractionMask(positionWS, refractionMaskOffset / _ScaledScreenParams.xy);
                
                // ScreenPos Final.
                float4 screenPos = lerp(i.positionNDC, screenPosOffseted, refractionMask);
                
                // Reconstructed World poistion for caustics and tint.
                float3 reconstructedPositionWS = ReconstructPositionWorld(depth, (i.positionCS.xy + refractionOffset) / _ScaledScreenParams.xy);

                // Sampling Scene Color.
                float4 reconstructedShadowCoord = TransformWorldToShadowCoord(reconstructedPositionWS);
                float shadowAttenuation = MainLightShadow(reconstructedShadowCoord, reconstructedPositionWS, 0, _MainLightOcclusionProbes);
                float darkening = lerp(0, _Darkening, shadowAttenuation);
                half3 sceneColor = GetSceneColor(screenPos) - darkening;

                // Tint color.
                float tintColorDepthFade = FresnelEffect(tbn[2], viewDirectionWS, _WaterFarCloseTintFadePower);
                half3 tintColor = lerp(_WaterCloseTintColor, _WaterFarTintColor, tintColorDepthFade);

                // Foam.
                float2 foamUV = i.uv.xy + normalTS.xy * _FoamDistortion;
                half foam = SAMPLE_TEXTURE2D(_FoamMap, sampler_FoamMap, foamUV).r;
                
                // Caustics.
                half3 caustics = 0;
                #if defined(_CAUSTICS)
                    caustics = CalculateCaustics(_CausticsSize, _CausticsBlendDistanceY, positionWS.y, reconstructedPositionWS, _CausticsTexture, sampler_CausticsTexture) * _CausticsIntensity;
                #endif

                // Deep water.
                float3 deepnessDistance = positionWS.xyz - reconstructedPositionWS;
                float deepness = dot(deepnessDistance, deepnessDistance) * _WaterDeepDistance;
                deepness = saturate(deepness);
                deepness *= SAMPLE_TEXTURE2D(_WaterTintMap, sampler_WaterTintMap, i.uv.xy).r;
                half3 emission = saturate(sceneColor * tintColor + foam + caustics);
                emission = lerp(emission, _WaterDeepTintColor, deepness);
                
                // Transparency.
                half alpha = DepthFade(i.positionNDC, linearEyeDepth, _TransparencyFade);
                alpha = pow(alpha, _TransparencyFadePower);
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(0, 0, 0, alpha), half3(1, 1, 1), normalTS, emission);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
    }
}
