Shader "Kefir/Kefir_FountainWater"
{
    Properties
    {
        [Header(Base Settings)]
        [NoScaleOffset]_BaseMap("Displacement (R), Waves (G), Second Color (B)", 2D) = "white" {}
        _Height("Height", Float) = 0
        [NoScaleOffset]_BumpMap("Normal Map", 2D) = "white" {}
        _NormalStrength("Normal Strength", Float) = 1
        [NoScaleOffset]_FoamNoises("Foam Noise1 (R), Foam Noise2 (G)", 2D) = "black" {}
        _NoisesSettings("Noise1 Scale (X), Noise1 Speed (Y), Noise2 Scale (Z), Noise2 Speed (W)", Vector) = (1, 1, 1, 1)
        _BaseColor("Base Color", Color) = (1, 1, 1 ,1)
        _SecondColor("Second Color", Color) = (1, 1, 1 ,1)
        _Smoothness("Smoothness", Range(0, 1)) = 1
        _Metallic("Metallic", Range(0, 1)) = 1
        [Header(Flipbook)]
        [RangeInt]_FlipbookRowsCount("Flipbook Rows Count", Float) = 8
        _FlipbookSpeed("Flipbook Speed", Float) = 1
        [Header(AO)]
        _AOIntensity("AO Intensity", Range(0, 3)) = 1
        _AOMax("AO Max", Float) = 5
        [Header(Scene Color)]
        _DistortionIntensity("Distortion Intensity", Float) = 1
        _SceneColorIntensity("Scene Color Intensity", Range(0, 1)) = 1
        [Header(Caustics)]
        [NoScaleOffset]_CausticsTexture("Caustics Texture", 2D) = "white" {}
        _CausticsIntensity("Caustics Intencity", Range(0, 1)) = 1
        _CausticsSize("Caustics Size", Range(0, 1)) = 0.15
        _CausticsBlendDistanceY("Caustics Blend Distance Y", Range(0, 10)) = 5
        [Header(Foams)]
        _FoamsColor("Foams Color", Color) = (1, 1, 1 ,1)
        _MainFoamsDistortion("Main Foams Distortion", Float) = 1
        _MainFoamsIntensity("Main Foams Intensity", Float) = 1
        _AdditionalFoamsDistortion("Additional Foams Distortion", Float) = 1
        _AdditionalFoamsIntensity("Additional Foams Intensity", Float) = 1
        _WavesFoamsDistortion("Waves Foams Distortion", Float) = 1       
        _WavesFoamsIntensity("Waves Foams Intensity", Float) = 1
    }
    SubShader
    {
        Tags { "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent" }

        HLSLINCLUDE
        
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

            TEXTURE2D(_BaseMap);
            SAMPLER(sampler_BaseMap);
            TEXTURE2D(_BumpMap);
            SAMPLER(sampler_BumpMap);
            TEXTURE2D(_FoamNoises);
            SAMPLER(sampler_FoamNoises);
            TEXTURE2D(_CausticsTexture);
            SAMPLER(sampler_CausticsTexture);
        
            CBUFFER_START(UnityPerMaterial)      
                float4 _NoisesSettings;
                float _Height;
                float _FlipbookRowsCount;
                float _FlipbookSpeed;
                float _NormalStrength;
                float _MainFoamsDistortion;
                float _AdditionalFoamsDistortion;
                float _WavesFoamsDistortion;
                float _MainFoamsIntensity;
                float _AdditionalFoamsIntensity;
                float _WavesFoamsIntensity;
                float _DistortionIntensity;
                float _SceneColorIntensity;
                float _CausticsIntensity;
                float _CausticsSize;
                float _CausticsBlendDistanceY;
                half4 _BaseColor;
                half4 _SecondColor;
                half4 _FoamsColor;
                half _Smoothness;
                half _Metallic;
                half _AOIntensity;
                half _AOMax;
            CBUFFER_END  
                  
        ENDHLSL
        
        Pass
        {          
        	Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off
            
            HLSLPROGRAM

            #pragma vertex vert
            #pragma fragment frag

            #define _SURFACE_TYPE_TRANSPARENT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/CameraTextures.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            #include "Assets/Content/ShaderLibrary/Water.hlsl"
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                half4 vertexColor : COLOR0;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float2 flipbookUV : TEXCOORD0;
                DECLARE_SH(sh, 1)
                float4 noisesUV : TEXCOORD2;
                float4 foamsWavesColorMasks : TEXCOORD3;
                float4x3 tbn_positionWS: TEXCOORD4;
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            Varyings vert(Attributes i)
            {
                Varyings o;

                float flipbookSpeed = _Time.y * _FlipbookSpeed;
                float2 uv = i.positionOS.xz * 0.5 + 0.5;
                float2 flipbookUV = GetFlipbookUV(uv, _FlipbookRowsCount, _FlipbookRowsCount * _FlipbookRowsCount, flipbookSpeed);

                float3 baseMap = SAMPLE_TEXTURE2D_LOD(_BaseMap, sampler_BaseMap, flipbookUV, 0).rgb;
                float displacementMask = baseMap.r + baseMap.g + _Height;
                o.foamsWavesColorMasks = float4(i.vertexColor.r, i.vertexColor.g, baseMap.r * baseMap.g, baseMap.b);
                float3 recalculatedPositionOS = float3(i.positionOS.x, displacementMask, i.positionOS.z);

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(recalculatedPositionOS);
				o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                
                PackTBNAndPositionWS(vertexInputs.positionWS, normalInputs, o.tbn_positionWS);
                
                o.flipbookUV = flipbookUV;
                o.noisesUV = float4(uv * _NoisesSettings.x + _Time.y * _NoisesSettings.y, uv * _NoisesSettings.z + _Time.y * _NoisesSettings.w);

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                half3 normalTS = UnpackNormalScale(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, i.flipbookUV), _NormalStrength);

                float noise = SAMPLE_TEXTURE2D(_FoamNoises, sampler_FoamNoises, i.noisesUV.xy).r;
                float noise2 = SAMPLE_TEXTURE2D(_FoamNoises, sampler_FoamNoises, i.noisesUV.zw).g;
                float finalNoise = noise * noise2;
                half4 finalColor = lerp(_BaseColor, _SecondColor, saturate(i.foamsWavesColorMasks.b + i.foamsWavesColorMasks.a));
                float foamsMask = saturate(i.foamsWavesColorMasks.r * _MainFoamsIntensity - finalNoise * _MainFoamsDistortion)
                                + saturate(i.foamsWavesColorMasks.g * _AdditionalFoamsIntensity - finalNoise * _AdditionalFoamsDistortion)
                                + saturate(i.foamsWavesColorMasks.b * _WavesFoamsIntensity - finalNoise * _WavesFoamsDistortion);
                half3 foamsColor = _FoamsColor.rgb * foamsMask;
                finalColor.rgb += foamsColor;

                float2 positionNDC = GetNormalizedScreenSpaceUV(i.positionCS.xy);
                float depth = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, sampler_ScreenTexturesDepth_linear_clamp, positionNDC);
                float3 reconstructedPositionWS = ReconstructPositionWorld(depth, positionNDC);
                half3 caustics = CalculateCaustics(_CausticsSize, _CausticsBlendDistanceY, positionWS.y, reconstructedPositionWS, _CausticsTexture, sampler_CausticsTexture) * _CausticsIntensity;

                half ao = clamp(1, _AOMax, LinearEyeDepth(depth, _ZBufferParams)) * _AOIntensity;

                float2 distortionUV = positionNDC + normalTS.xy * _DistortionIntensity;
                half3 sceneColor = GetSceneColor(float4(distortionUV, 0, 1)) * _SceneColorIntensity;
                
                SurfaceData surfaceData = InitializeSurfaceData(finalColor, half3(_Metallic, _Smoothness, ao), normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                half4 model = UniversalFragmentPBR(inputData, surfaceData);
                return half4(saturate(model.rgb + sceneColor) + caustics, model.a);
            }

            ENDHLSL
        }
    }
}
