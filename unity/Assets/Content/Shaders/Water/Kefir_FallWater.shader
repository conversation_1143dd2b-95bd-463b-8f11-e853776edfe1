Shader "Kefir/Kefir_FallWater"
{
    Properties
    {
        [Header(Base Settings)]
        [NoScaleOffset]_BaseMap("Splashes Mask Part1 (R), Splashes Mask Part2 (G), Alpha Mask Part1 (B), Alpha Mask Part2 (A))", 2D) = "white" {}
        [NoScaleOffset]_BumpPack("Normal Map Part1 (RG), Normal Map Part2 (BA)", 2D) = "white" {}
        _NormalStrength("Normal Strength", Float) = 1
        _BaseColor("Base Color", Color) = (1, 1, 1 ,1)
        _SecondColor("Second Color", Color) = (1, 1, 1, 1)
        _SunIntensity("Sun Intensity", Float) = 1
        _EmissionMax("Emission Max", Float) = 5
        _Metallic("Metallic", Range(0, 1)) = 0.5
        _Smoothness("Smoothness", Range(0, 1)) = 0.5
        [Header(Flipbook)]
        [RangeInt]_FlipbookRowsCount("Flipbook Rows Count", Float) = 4
        _FlipbookSpeed("Flipbook Speed", Float) = 1
        [Header(AO)]
        _AOIntensity("AO Intensity", Range(0, 3)) = 1
        _AOMax("AO Max", Float) = 5
        [Header(Scene Color)]
        _DistortionIntensity("Distortion Intensity", Float) = 1
        _SceneColorIntensity("Scene Color Intensity", Range(0, 1)) = 1
        [Header(Vertex Animation)]
        [NoScaleOffset]_VertexAnimationNoise("Vertex Animation Noise", 2D) = "white" {}
        _NoiseScale("Noise Scale", Float) = 1
        _NoiseIntensity("Noise Intensity", Float) = 1
        _VertexAnimationSpeed("Vertex Animation Speed", Float) = 1
        _VertexAnimationIntensity("Vertex Animation Intensity", Float) = 1
        _VertexAnimationAmplitude("Vertex Animation Amplitude", Float) = 1
    }
    SubShader
    {
        Tags { "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent" }

        HLSLINCLUDE
        
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        
            TEXTURE2D(_BaseMap);
            SAMPLER(sampler_BaseMap);
            TEXTURE2D(_BumpPack);
            SAMPLER(sampler_BumpPack);
            TEXTURE2D(_VertexAnimationNoise);
            SAMPLER(sampler_VertexAnimationNoise);
        
            CBUFFER_START(UnityPerMaterial)
                float _SunIntensity; // Used for BOOST_MAIN_LIGHT_INTENSITY.
                float _NormalStrength;
                float _FlipbookRowsCount;
                float _FlipbookSpeed;
                float _DistortionIntensity;
                float _SceneColorIntensity;
                float _NoiseScale;
                float _VertexAnimationIntensity;
                float _VertexAnimationSpeed;
                float _VertexAnimationAmplitude;
                float _NoiseIntensity;
                half4 _BaseColor;
                half4 _SecondColor;
                half _EmissionMax;
                half _Metallic;
                half _Smoothness;
                half _AOIntensity;
                half _AOMax;
            CBUFFER_END  
                  
        ENDHLSL
        
        Pass
        {
            Cull Off
        	Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off
            
            HLSLPROGRAM

            #pragma vertex vert
            #pragma fragment frag

            #define _SURFACE_TYPE_TRANSPARENT 1
            #define BOOST_MAIN_LIGHT_INTENSITY 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/CameraTextures.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
                    
            struct Attributes
            {
                float4 positionOS : POSITION;
                float4 uv : TEXCOORD0;
                float4 uv1 : TEXCOORD1;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                half4 vertexColor : COLOR0;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float3 flipbookUVCurrentChannel : TEXCOORD0;
                float4 uv : TEXCOORD1;
                DECLARE_SH(sh, 2)
                float4x3 tbn_positionWS : TEXCOORD3;
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            float3 VertexAnimation(float3 positionOS, float3 normalOS, half vertexColor, float2 uv)
            {
                float2 noiseUV = uv * _NoiseScale + _Time.y;
                half noiseSample = SAMPLE_TEXTURE2D_LOD(_VertexAnimationNoise, sampler_VertexAnimationNoise, noiseUV, 0).r;
                noiseSample = (noiseSample * 2 - 1) * _NoiseIntensity;
                float noise = sin(_Time.y * _VertexAnimationSpeed + noiseSample) * _VertexAnimationAmplitude;
                return positionOS + noise * normalOS * vertexColor * _VertexAnimationIntensity;
            }

            void RecalculatePositionAndNormals(float3 normalOS, float3 tangentOS, float3 positionOS, half vertexColor, float2 uv, out float3 recalculatedPositionOS, out float3 recalculatedNormalOS)
            {
                float3 binormal = cross(normalOS, tangentOS);
                const float offset = 0.01;
                float3 tangentOffsetOS = positionOS + tangentOS * offset;
                float3 bitangentOffsetOS = positionOS + binormal * offset;               
                recalculatedPositionOS = VertexAnimation(positionOS, normalOS, vertexColor, uv);
                float3 tangentOffsetAnimatedOS = VertexAnimation(tangentOffsetOS, normalOS, vertexColor, uv);
                float3 bitangentOffsetAnimatedOS = VertexAnimation(bitangentOffsetOS, normalOS, vertexColor, uv);                
                float3 recalculatedTangentOS = normalize(tangentOffsetAnimatedOS - recalculatedPositionOS);
                float3 recalculatedBitangentOS = normalize(bitangentOffsetAnimatedOS - recalculatedPositionOS);
                recalculatedNormalOS = -cross(recalculatedBitangentOS, recalculatedTangentOS);
            }

            Varyings vert(Attributes i)
            {
                Varyings o;

                float flipbookProgress = _Time.y * _FlipbookSpeed;
                
                o.flipbookUVCurrentChannel.xy = GetFlipbookUV(i.uv.xy, _FlipbookRowsCount, _FlipbookRowsCount * _FlipbookRowsCount, flipbookProgress);
                o.flipbookUVCurrentChannel.z = fmod(floor(flipbookProgress), 2) == 0;

                float3 recalculatedPositionOS;
                float3 recalculatedNormalOS;
                RecalculatePositionAndNormals(i.normalOS, i.tangentOS.xyz, i.positionOS.xyz, i.vertexColor.r, i.uv.xy, recalculatedPositionOS, recalculatedNormalOS);
                
                VertexPositionInputs vertexInputs = GetVertexPositionInputs(recalculatedPositionOS);
				o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(recalculatedNormalOS);
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                o.uv = float4(i.uv.xy, i.uv1.xy);
                
                return o;
            }

            half4 frag(Varyings i, bool facing : FRONT_FACE_SEMANTIC) : SV_Target
            {
                float isFrontFace =  IS_FRONT_VFACE(facing, 1, -1);

                float2 flipbookUV = i.flipbookUVCurrentChannel.xy;
                float currentChannel = i.flipbookUVCurrentChannel.z;
                
                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, flipbookUV);
                half2 splashesAndAlphaMasks = lerp(baseMap.rb, baseMap.ga, currentChannel);          

                half3x3 tbn;
                float3 positionWS;               
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                tbn[0] *= isFrontFace;
                tbn[2] *= isFrontFace;

                half4 normalMap = SAMPLE_TEXTURE2D(_BumpPack, sampler_BumpPack, flipbookUV);
                half4 firstNormalMap;
                half4 secondNormalMap;
                #if defined(UNITY_NO_DXT5nm)
                    //Recovering B channel for mobile devices as RGB is expected in UnpackNormalRGB method.
                    half4 unpackedNormalMap = normalMap * 2 - 1;  
                    firstNormalMap = half4(normalMap.r, normalMap.g, max(1.0e-16, sqrt(1 - saturate(dot(unpackedNormalMap.rg, unpackedNormalMap.rg)))) * 0.5 + 0.5, 0);
                    secondNormalMap = half4(normalMap.b, normalMap.a, max(1.0e-16, sqrt(1 - saturate(dot(unpackedNormalMap.ba, unpackedNormalMap.ba)))) * 0.5 + 0.5, 0);
                #else
                    firstNormalMap = half4(normalMap.r, normalMap.g, 0, 1);
                    secondNormalMap = half4(normalMap.b, normalMap.a, 0, 1);
                #endif
                half3 normalTS = UnpackNormalScale(lerp(firstNormalMap, secondNormalMap, currentChannel), _NormalStrength);
                
                float depth = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, sampler_ScreenTexturesDepth_linear_clamp, i.positionCS.xy / _ScaledScreenParams.xy);
                half ao = clamp(1, _AOMax, LinearEyeDepth(depth, _ZBufferParams)) * _AOIntensity;

                float2 positionNDC = GetNormalizedScreenSpaceUV(i.positionCS.xy) + normalTS.xy * _DistortionIntensity;               
                half3 sceneColor = GetSceneColor(float4(positionNDC.x, positionNDC.y, 0, 1)) * _SceneColorIntensity;

                half4 finalColor = lerp(_BaseColor, _SecondColor, splashesAndAlphaMasks.r);
                half alpha = splashesAndAlphaMasks.g * finalColor.a * saturate(i.uv.w);
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(finalColor.rgb, alpha), half3(_Metallic, _Smoothness, ao), normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                half4 model = UniversalFragmentPBR(inputData, surfaceData);
                return half4(min(model.rgb, _EmissionMax) + sceneColor, alpha);
            }
            ENDHLSL
        }
    }
}
