Shader "Kefir/Kefir_Vehicle_Paint"
{
    Properties
    { 
        [Header(Textures)]
        [NoScaleOffset]_BaseMap("Base Map", 2D) = "white" {}
        [NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_MaskMap("MAS Map", 2D) = "white" {}
        [NoScaleOffset]_Emission_Map("Emission Map", 2D) = "white" {}
        [NoScaleOffset]_Interior_Map("Interior Map", 2D) = "white" {}
        [NoScaleOffset]_NoisesMap("Dust(R) Scuffs(G) Soaking(B) Map", 2D) = "white" {}
        _InteriorTile("Interior Tile", Float) = 1
        [NoScaleOffset]_FlakesBumpMap("Flakes Normal Map", 2D) = "bump" {}
        _FlakesTile("Flakes Tile", Float) = 100
        _FlakesIntencity("FlakesIntencity", Range(0, 1)) = 0.015
        
        [Header(Print)]
        [MaterialToggle] _UsingPrint("Use Print", Float) = 0
        [MaterialToggle] _ApplyPrintWithoutMask("Apply Print Without Mask", Float) = 0
        [MaterialToggle] _ApplyPrintOnSubcolor("Dont Paint SubColor Detail", Float) = 0
        [NoScaleOffset]_PrintMap("Print Map", 2D) = "white" {}
        _PrintOffset("Print Offset(XY) Tiling(ZW)", Vector) = (0, 0, 0, 0)
        
        [Header(LightBlock)]
        _FrontLightColor("FrontLightColor", Color) = (0, 0, 0, 0)
        _LampIntencity("Lamp Intencity", Vector) = (30, 0, 0, 0)
        _PanelLights("Panel Lights", Float) = 0
        _LightsTint("Light Tint", Range(0, 2)) = 1
        
        [Header(Color)]
        _MainColor("MainColor",Color) = (0, 0, 0, 0)
        _SubColor("SubColor",Color) = (0, 0, 0, 0)
        [MaterialToggle] _MoldingsFromHull("Moldings From Hull", Float) = 0
        _HullSmoothness("Hull Smoothness", Range(0, 1)) = 0.6
        _HullClearCoat("Hull Clear Coat", Range(0, 1)) = 0
        _HullCoatSmoothness("Hull Coat Smoothness", Range(0, 1)) = 1
        _HullMetalic("Hull Metalic", Range(0, 1)) = 0.4
        _DiskColor("DiskColor", Color) = (0.7169812, 0.7169812, 0.7169812, 0)
        _DiskSmoothness("Disk Smoothness", Range(0, 1)) = 0.5
        _DiskMetalic("Disk Metalic", Range(0, 1)) = 0.5
        [MaterialToggle] _InteriorMode("Interior from BaseMap", Float) = 0
        _InteriorColor("Interior Color", Color) = (1, 1, 1, 0)
        _InteriorMetalic("Interior Metalic", Range(0, 1)) = 1
        _InteriorSmoothness("Interior Smoothness", Range(0, 1)) = 1
        
        
        [Header(Fresnel)]
        [MaterialToggle] _UseFresnel("UseFresnel", Float) = 1
        _FresnelColor("Fresnel Color",Color) = (1, 1, 1, 0)
        _FresnelPower("Fresnel Power", Range(0, 5)) = 1
        [MaterialToggle] _FresnelAdd("Fresnel Add Operation", Float) = 0
        [MaterialToggle] _FresnelMultiply("Fresnel Multiply Operation", Float) = 0
        [MaterialToggle] _FresnelReplace("Fresnel Replace Operation", Float) = 0
        
        [Header(Dust)]
        _DustColor("DustColor", Color) = (0.6509434, 0.45256, 0.2118637, 0)
        _DustAlpha("Dust Alpha", Range(0,1)) = 1
        _DustSmoothnessReduction("Dust Smoothness Reduction", Range(0, 2)) = 1.65
        _DustPassiveFactor("Dust Passive Factor", Range(0,1)) = 0.2
        _DustTilingOffset("Dust Tiling(XY) Offset(ZW)", Float) = (1, 1, 0, 0)
        _DustGradientMin("Dust Gradient Min", Range(0, 1)) = 0
        _DustGradientMax("Dust Gradient Max", Range(0, 1)) = 1
        _DustHeight("DustHeight", Range(0, 1)) = 0
        _DustingSpeed("Dusting Speed", Float) = 1
        
        [Header(Scuffs)]
        _ScuffsIntensity("Scuffs Intensity", Range(-1, 1)) = 1
        _ScuffsBesidesHull("Scuffs Besides Hull", Range(0,1)) = 0
        _ScuffsBlend("Scuffs Blend", Range(0, 1)) = 0
        _ScuffsTilingOffset("Scuffs Tiling(XY) Offset(ZW)", Float) = (10, 10, 0, 0)
        _ScuffValueMin("Scuff Value Min", Range(0, 1)) = 0
        _ScuffValueMax("Scuff Value Max", Range(0, 1)) = 1
        _ScurffColor("Scuff Color", Color) = (0,0,0,0)
        [Header(Antichrome)]
        _AntichromeColor("Antichrome Color", Color) = (1, 1, 1, 1)
        _AntichromeMetalic("Antichrome Metalic Multiplier", Range(0, 1)) = 1
        _AntichromeSmoothness("Antichrome Smoothness Multiplier", Range(0, 1)) = 1
        
        
        [Header(Soaking)]
        _WaterMapTiling("WaterMap Tilling", Float) = 1
        _WetSharpness("WetSharpness", Range(0,0.99)) = 0
        _NoiseSharpness("NoiseSharpness", Float) = 0
        [NoScaleOffset]_WetMap("WetMap", 3D) = "white" {}
        _GroupBoundsCenterOS("GroupBoundsCenterOS", Vector) = (0, 0, 0, 0)
        _GroupBoundsSize("GroupBoundsSize", Vector) = (0, 0, 0, 0)
        _WaterPower("WaterPower", Float) = 0
        
        [HideInInspector] _AmbientProbeBlendFactor ("AmbientProbeBlendFactor", Float) = 0
        [HideInInspector] _Custom_SHAr ("_Custom_SHAr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAg ("_Custom_SHAg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAb ("_Custom_SHAb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBr ("_Custom_SHBr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBg ("_Custom_SHBg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBb ("_Custom_SHBb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHC ("_Custom_SHC", Vector) = (0, 0, 0, 0)
    } 
    
    SubShader
    {
        Tags { "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry-60" }       

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_MaskMap);
        TEXTURE2D(_PrintMap);
        SAMPLER(sampler_PrintMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_FlakesBumpMap);
        TEXTURE2D(_Interior_Map);
        SAMPLER(sampler_Interior_Map);
        TEXTURE2D(_Emission_Map);
        TEXTURE2D(_NoisesMap);
        SAMPLER(sampler_NoisesMap);
        TEXTURE3D(_WetMap);
        SAMPLER(sampler_WetMap);
        
        CBUFFER_START(UnityPerMaterial)
            float4x4 _GroupParentTransformWorldToLocal;
            float4 _Custom_SHAr;
            float4 _Custom_SHAg;
            float4 _Custom_SHAb;
            float4 _Custom_SHBr;
            float4 _Custom_SHBg;
            float4 _Custom_SHBb;
            float4 _Custom_SHC;
            float4 _PrintOffset;
            float4 _DustTilingOffset;
            float4 _ScuffsTilingOffset;
            float3 _GroupBoundsCenterOS;
            float3 _GroupBoundsSize;
            float _DustingSpeed;
            float _MoldingsFromHull;
            float _ScuffsBesidesHull;
            float _HullClearCoat;
            float _ScuffValueMin;
            float _ScuffValueMax;
            float _DustPassiveFactor;
            float _DustGradientMin;
            float _DustGradientMax;
            float _InteriorMetalic;
            float _FresnelAdd;
            float _FresnelMultiply;
            float _FresnelReplace;
            float _InteriorMode;
            float _FresnelPower;
            float _PanelLights;
            float _DustHeight;
            float _LightsTint;
            float _InteriorTile;
            float _FlakesIntencity;
            float _FlakesTile;
            float _WetSharpness;
            float _NoiseSharpness;
            float _WaterPower;
            float _WaterMapTiling;
            float _ScuffsIntensity;
            float _ScuffsBlend;
            float _AmbientProbeBlendFactor;
        
            half4 _MainColor;
            half4 _SubColor;
            half4 _ScurffColor;
            half4 _FrontLightColor;
            half4 _DiskColor;
            half4 _DustColor;
            half4 _FresnelColor;
            half3 _AntichromeColor;
            half3 _InteriorColor;
            half3 _LampIntencity;
            half _AntichromeMetalic;
            half _AntichromeSmoothness;
            half _DiskSmoothness;
            half _InteriorSmoothness;
            half _DustSmoothnessReduction;
            half _HullMetalic;
            half _HullSmoothness;
            half _HullCoatSmoothness;
            half _DiskMetalic;
            half _DustAlpha;
            half _UseFresnel;
            half _UsingPrint;
            half _ApplyPrintOnSubcolor;
            half _ApplyPrintWithoutMask;
        CBUFFER_END

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            float moldingsMask = max(1 - abs((0.8f - snData.vertexColor.g) * 255), 0);
            float mainColorMask = saturate((1 - snData.vertexColor.x) * 5.0f);
            mainColorMask += moldingsMask * _MoldingsFromHull;
            
            half3 normalMapTS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv));
            half3 flakesNormalMapTS = UnpackNormal(SAMPLE_TEXTURE2D(_FlakesBumpMap, sampler_BumpMap, snData.uv1 * _FlakesTile));
            flakesNormalMapTS = half3(flakesNormalMapTS.xy * _FlakesIntencity, flakesNormalMapTS.z);
            flakesNormalMapTS *= mainColorMask;

            normalMapTS += flakesNormalMapTS;
            return normalMapTS;
        }
        
        ENDHLSL

        Pass
        {
            Stencil
            {
                // Overwrites after character to 0 for BlobShadow.
                Ref 0
                Comp Always
                Pass Replace
                WriteMask 128
            }
            
            HLSLPROGRAM
            #define _CLEARCOAT 1
            
            #pragma vertex vert
            #pragma fragment frag

            #pragma multi_compile _ _REFLECTION_PROBES_ROTATED

            #if defined(_REFLECTION_PROBES_ROTATED)
                #pragma require cubearray 
            #endif

            #define _LIGHT_PROBES_AMBIENT_AND_CUSTOM_BLEND 1
            #define _GLOBAL_REFLECTION_PROBE_VEHICLE 1
            #define AMBIENT_OCCLUSION_FOR_DYNAMIC_OBJECT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #include "Assets/Content/ShaderLibrary/Debugging.hlsl"
            
            struct Attributes
            {
                float3 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float4 vertexColor : COLOR0;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                float2 uv2 : TEXCOORD2;
                float2 uv3 : TEXCOORD3;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float4 uv01 : TEXCOORD0;
                float4 uv23 : TEXCOORD1;
                float4x3 tbn_positionWS : TEXCOORD2;
                float3 positionOS : TEXCOORD5;
                float3 normalOS : TEXCOORD6;
                float4 vertexColor : TEXCOORD7;
                DECLARE_SH(sh, 8)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.normalOS = i.normalOS;
                o.positionOS = i.positionOS;
                o.vertexColor = i.vertexColor;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)

                o.uv01 = float4(i.uv0, i.uv1);
                o.uv23 = float4(i.uv2, i.uv3);
                
                return o;
            } 
            
            half4 frag(Varyings i) : SV_Target
            {
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(i.positionCS);
                #endif
                
                float2 uv0 = i.uv01.xy;
                
                half alphaLod = SAMPLE_TEXTURE2D_LOD(_BaseMap, sampler_BaseMap, uv0 , 0).a;
                float epsilon = 1e-5;
                bool baseColorClipEqual = abs(alphaLod - 0.2) > epsilon;
                clip(baseColorClipEqual - 0.5);

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                float2 uv1 = i.uv01.zw;
                float2 uv2 = i.uv23.xy;
                float2 uv3 = i.uv23.zw;

                float mainColorMask = saturate((1 - i.vertexColor.x) * 5.0f);
                float reversedMainColorMask = 1 - mainColorMask;
                float subColorMask = saturate(1 - (i.vertexColor.x * 1.25f));
                float interiorMask = max(1 - abs(i.vertexColor.g * 255), 0);
             
                float wheelMask = max(1 - abs(i.vertexColor.b * 255), 0);
                float moldingsMask = max(1 - abs((0.8f - i.vertexColor.g) * 255), 0);
                mainColorMask += moldingsMask * _MoldingsFromHull;
                float antichromeMask = max(1 - abs((0.8f - i.vertexColor.b) * 255), 0);
                float detailsMask = reversedMainColorMask - interiorMask - wheelMask;
                float mainSubDiff = mainColorMask - subColorMask;
                
                half4 hullColor = _MainColor * mainSubDiff + _SubColor * subColorMask;
                half4 wheelDiskColor = _DiskColor * wheelMask;

                // Print.
                half4 printColor = SAMPLE_TEXTURE2D(_PrintMap, sampler_PrintMap, (uv3 * _PrintOffset.zw) + _PrintOffset.xy);
                float printMask = lerp(mainColorMask - subColorMask * _ApplyPrintOnSubcolor, 1, _ApplyPrintWithoutMask);
                printColor = lerp(hullColor, printColor, printColor.a * printMask);
                hullColor = lerp(hullColor, printColor, _UsingPrint);
                
                // Fresnel Effect.
                float4 fresnelOperationResult = _FresnelColor * _FresnelReplace + 
                         (_FresnelColor + hullColor) * _FresnelAdd + 
                         (_FresnelColor * hullColor) * _FresnelMultiply;
                float fresnel = FresnelEffect(tbn[2], GetWorldSpaceNormalizeViewDir(positionWS.xyz), _FresnelPower);
                fresnel *= mainColorMask;

                hullColor = lerp(hullColor, fresnelOperationResult, fresnel * _UseFresnel);
                
                // Dust.
                _DustHeight = min(_DustHeight, 1 / _DustingSpeed);
                _DustHeight *= _DustingSpeed;

                float passiveDustHeight = _DustHeight;
                _DustHeight += 1 - _DustGradientMax;
                
                float dustingFactor = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, uv1 * _DustTilingOffset.xy + _DustTilingOffset.zw).r;
                dustingFactor *= 1.15f;
                float finalDustingFactor = dustingFactor * max(_DustHeight - uv2.y, _DustHeight * _DustPassiveFactor * step(uv2.y, 1));
                float passiveDusting = dustingFactor * passiveDustHeight * _DustPassiveFactor * step(uv2.y, 1);
                passiveDusting = 1 - passiveDusting;
                finalDustingFactor = saturate(finalDustingFactor * (1 - interiorMask));
                finalDustingFactor = 1 - finalDustingFactor;
                finalDustingFactor = smoothstep(_DustGradientMin, _DustGradientMax, finalDustingFactor);
                finalDustingFactor = min(finalDustingFactor, passiveDusting);
                float reversedFinalDustingFactor = 1 - finalDustingFactor;

                // Normal.
                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(uv0, uv1, 0, i.vertexColor.rgb));
                
                // Scuffs.
                float scuffsMap = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, uv1 * _ScuffsTilingOffset.xy + _ScuffsTilingOffset.zw).g;
                float scuffsIntensity = saturate(scuffsMap + _ScuffsIntensity);
                scuffsIntensity = smoothstep(_ScuffValueMin, _ScuffValueMax, scuffsIntensity);
                scuffsIntensity = lerp(scuffsIntensity, scuffsIntensity * _ScuffsBesidesHull, reversedMainColorMask);
                
                // Soaking.
                float3 wetNoiseMapUV = i.positionOS;
                float3 wetNoiseBlend = abs(i.normalOS);
                wetNoiseBlend /= dot(wetNoiseBlend, 1.0f);
                
                float wetNoiseMapX = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, wetNoiseMapUV.zy * _WaterMapTiling).b;
                float wetNoiseMapY = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, wetNoiseMapUV.xz * _WaterMapTiling).b;
                float wetNoiseMapZ = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, wetNoiseMapUV.xy * _WaterMapTiling).b;
                float wetNoise = wetNoiseMapX * wetNoiseBlend.x + wetNoiseMapY * wetNoiseBlend.y + wetNoiseMapZ * wetNoiseBlend.z;
                float4x4 GroupParentTransformWorldToLocal = _GroupParentTransformWorldToLocal;
                float3 wetEffectUV = mul(GroupParentTransformWorldToLocal, float4(positionWS.xyz, 1)).rgb;
                wetEffectUV -= _GroupBoundsCenterOS;
                wetEffectUV /= _GroupBoundsSize;
                wetEffectUV += 0.5f;

                float wetEffect = SAMPLE_TEXTURE3D(_WetMap, sampler_WetMap, wetEffectUV).r;
                float wetSharpness = _WetSharpness;
                wetEffect = (wetEffect - wetSharpness * 0.5) / (1 - wetSharpness);
                float wetFactor = saturate(wetEffect * (_NoiseSharpness + 1) + (wetNoise - 1) * _NoiseSharpness);
                wetFactor *= _WaterPower;
                float reversedWetFactor = wetFactor;
                wetFactor = 1 - wetFactor;
                
                // MSA.
                half3 msa = SAMPLE_TEXTURE2D(_MaskMap, sampler_BaseMap, uv0).rbg;
                half2 metallicSmoothness = msa.rg;
                metallicSmoothness *= reversedMainColorMask - wheelMask;

                half2 hullMetallicSmoothness = half2(_HullMetalic, _HullSmoothness);
                hullMetallicSmoothness *= mainColorMask;

                half2 diskMetallicSmoothness = half2(_DiskMetalic, _DiskSmoothness);
                diskMetallicSmoothness *= wheelMask; 
                
                half2 metallicSmoothnessFinal = metallicSmoothness + hullMetallicSmoothness + diskMetallicSmoothness;
                half metallic = metallicSmoothnessFinal.r;
                metallic = lerp(metallic, metallic * _AntichromeMetalic, antichromeMask);
                metallic = lerp(metallic, metallic * _InteriorMetalic, interiorMask);
                metallic *= finalDustingFactor;
                metallic = saturate(metallic);
                
                half smoothness = metallicSmoothnessFinal.g; 
                smoothness = lerp(smoothness, smoothness * _AntichromeSmoothness, antichromeMask);
                smoothness = lerp(smoothness, smoothness * _InteriorSmoothness, interiorMask);
                smoothness = lerp(smoothness, 1, reversedWetFactor);
                half scuffFinalSmoothness = lerp(smoothness, saturate(smoothness - scuffsIntensity), _ScuffsBlend);
                smoothness = min(scuffFinalSmoothness, lerp(saturate(smoothness - (_DustSmoothnessReduction - 1) * reversedFinalDustingFactor), smoothness * finalDustingFactor, reversedFinalDustingFactor));

                smoothness = saturate(smoothness);
                
                half ao = msa.b * i.vertexColor.a;
                
                // BaseMap + LightTint.
                half4 baseColor = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uv0);
                half4 finalBaseColor = baseColor * detailsMask;
                finalBaseColor *= baseColor.a;
                finalBaseColor += baseColor * (1 - baseColor.a) * _LightsTint;
                
                // Interior.
                half3 interiorColor = (SAMPLE_TEXTURE2D(_Interior_Map, sampler_Interior_Map, uv1 * _InteriorTile).rgb * (1 - _InteriorMode)).rgb + (baseColor * _InteriorMode).rgb;
                interiorColor *= _InteriorColor;
                interiorColor *= interiorMask;
                
                // Lights.
                half4 emissionMap = SAMPLE_TEXTURE2D(_Emission_Map, sampler_BaseMap, uv0);
                float emissionIntensity = emissionMap.r + emissionMap.g + emissionMap.b;
                half3 lampEmission = _LampIntencity * emissionMap.rgb;
                lampEmission = lampEmission.x + lampEmission.y + lampEmission.z;
                lampEmission *= baseColor.rgb;
                half3 frontLight = emissionMap.r * _FrontLightColor.rgb * _PanelLights;
                frontLight = lerp(frontLight, lampEmission, emissionMap.a);

                half3 emission = lerp(0, frontLight, emissionIntensity);

                // Albedo.
                half3 albedo = saturate((hullColor.rgb + wheelDiskColor.rgb +  interiorColor + (finalBaseColor.rgb * (1 - printColor.a))));
                albedo = lerp(albedo, _ScurffColor.rgb, scuffsIntensity * _ScurffColor.a);
                albedo = lerp(albedo, _AntichromeColor * albedo, antichromeMask);
                albedo = lerp(albedo, _DustColor.rgb, reversedFinalDustingFactor * _DustAlpha);            
                albedo *= wetFactor;

                half clearCoatMask = _HullClearCoat * finalDustingFactor;
                clearCoatMask *= mainColorMask;
                clearCoatMask = lerp(clearCoatMask, saturate(clearCoatMask - scuffsIntensity), _ScuffsBlend);

                half clearCoatSmoothness = saturate(_HullCoatSmoothness * finalDustingFactor);
                clearCoatSmoothness = lerp(clearCoatSmoothness, saturate(clearCoatSmoothness - scuffsIntensity), _ScuffsBlend);

                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, 1), half3(metallic, smoothness, ao), normalTS, emission, clearCoatMask, clearCoatSmoothness);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #pragma target 2.0
            
            // -------------------------------------
            // Shader Stages
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment
            
            // This is used during shadow map generation to differentiate between directional and punctual light shadows, as they use different formulas to apply Normal Bias
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW

            #pragma multi_compile_fragment _ LOD_FADE_CROSSFADE
            #if defined(LOD_FADE_CROSSFADE)
                #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #endif
            
            // Shadow Casting Light geometric parameters. These variables are used when applying the shadow Normal Bias and are set by UnityEngine.Rendering.Universal.ShadowUtils.SetupShadowCasterConstantBuffer in com.unity.render-pipelines.universal/Runtime/ShadowUtils.cs
            // For Directional lights, _LightDirection is used when applying shadow Normal Bias.
            // For Spot lights and Point lights, _LightPosition is used to compute the actual light direction because it is different at each shadow caster geometry vertex.
            float3 _LightDirection;
            float3 _LightPosition;
            float4 _ShadowBias;
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS     : NORMAL;
                float2 uv0 : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
                float2 uv0 : TEXCOORD0;
            };
               
            float3 ApplyShadowBias(float3 positionWS, float3 normalWS, float3 lightDirection)
            {
                float invNdotL = 1.0 - saturate(dot(lightDirection, normalWS));
                float scale = invNdotL * _ShadowBias.y;

                // normal bias is negative since we want to apply an inset normal offset
                positionWS = lightDirection * _ShadowBias.xxx + positionWS;
                positionWS = normalWS * scale.xxx + positionWS;
                return positionWS;
            }
            
            float4 GetShadowPositionHClip(Attributes input)
            {
                float3 positionWS = TransformObjectToWorld(input.positionOS.xyz);
                float3 normalWS = TransformObjectToWorldNormal(input.normalOS);

                #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                    float3 lightDirectionWS = normalize(_LightPosition - positionWS);
                #else
                    float3 lightDirectionWS = _LightDirection;
                #endif

                    float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));

                #if UNITY_REVERSED_Z
                    positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #else
                    positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #endif

                return positionCS;
            }

            Varyings ShadowPassVertex(Attributes input)
            {
                Varyings output;
                output.positionCS = GetShadowPositionHClip(input);
                output.uv0 = input.uv0;
                return output;
            }

            half4 ShadowPassFragment(Varyings input) : SV_TARGET
            {
                float baseColorAlpha = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uv0).a;
                float epsilon = 1e-5;
                bool baseColorClipEqual = abs(baseColorAlpha - 0.2) > epsilon;
                clip(baseColorClipEqual - 0.5);

                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(input.positionCS);
                #endif
                
                return 0;
            }
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #pragma target 2.0
            
            #pragma vertex DepthNormalsVertex
            #pragma fragment DepthNormalsFragment

            #pragma multi_compile_fragment _ LOD_FADE_CROSSFADE
            #if defined(LOD_FADE_CROSSFADE)
                #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #endif
            
            struct Attributes
            {
                float4 positionOS     : POSITION;
                float3 normal       : NORMAL;
                float4 tangentOS : TANGENT;
                half4 vertexColor : COLOR;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                half4 vertexColor : TEXCOORD2;
                float3x3 tbn : TEXCOORD3;
            };
                   
            Varyings DepthNormalsVertex(Attributes input)
            {
                Varyings output = (Varyings)0;
                
                output.positionCS = TransformObjectToHClip(input.positionOS.xyz);

                VertexPositionInputs vertexInput = GetVertexPositionInputs(input.positionOS.xyz);
                VertexNormalInputs normalInput = GetVertexNormalInputs(input.normal, input.tangentOS);
                output.uv0 = input.uv0;
                output.uv1 = input.uv1;
                output.vertexColor = input.vertexColor;
                output.tbn = float3x3(
                    normalInput.tangentWS,
                    normalInput.bitangentWS,
                    normalInput.normalWS);
                return output;
            }

            void DepthNormalsFragment(
                Varyings input
                , out half4 outNormalWS : SV_Target0
            #ifdef _WRITE_RENDERING_LAYERS
                , out float4 outRenderingLayers : SV_Target1
            #endif
            )
            {
                float baseColorAlpha = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uv0).a;
                float epsilon = 1e-5;
                bool baseColorClipEqual = abs(baseColorAlpha - 0.2) > epsilon;
                clip(baseColorClipEqual - 0.5);

                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(input.positionCS);
                #endif

                SampleNormalData snData = InitializeSampleNormalData(input.uv0, input.uv1, 0, input.vertexColor.rgb);
                half3 normalTS = KefirSampleNormalTS(snData);
                outNormalWS = half4(TransformUnpackedToWorldNormal(normalTS, input.tbn), 0.0);

                #ifdef _WRITE_RENDERING_LAYERS
                    uint renderingLayers = GetMeshRenderingLayer();
                    outRenderingLayers = float4(EncodeMeshRenderingLayer(renderingLayers), 0, 0, 0);
                #endif
            }
            ENDHLSL
        }
    }
}
