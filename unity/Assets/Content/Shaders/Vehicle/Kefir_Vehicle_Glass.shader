Shader "Kefir/Kefir_Vehicle_Glass"
{
    Properties
    { 
        [NoScaleOffset]_BaseMap("DustMask", 2D) = "white" {}
        [NoScaleOffset]_NoisesMap("Dust(R) Scuffs(G) Soaking(B) Noise", 2D) = "white" {}
        [Header(Color)]
        _FrontGlassColor("Front Glass Color", Color) = (0, 0, 0, 0)
        _BackGlassColor("Back Glass Color", Color) = (0, 0, 0, 0) 
        
        _FrontLightsGlassColor("Front Lights Glass Color", Color) = (0, 0, 0, 0)
        _BackLightsGlassColor("Back Lights Glass Color", Color) = (0, 0, 0, 0)
        
        _GlassSmoothness("GlassSmoothness", Range(0, 1)) = 0
        _GlassMetalic("GlassMetalic", Range(0, 1)) = 0
        
        [MaterialToggle] _FramesUsingCustomColor("Frames Using Custom Color", Float) = 0
        _FramesColor("Frames Color", Color) = (0, 0, 0, 0)
        
        [Header(Dust)]
        _DustColor("DustColor", Color) = (0.6509434, 0.45256, 0.2118637, 0)
        [Normal][NoScaleOffset]_DustNormal("DustNormal", 2D) = "bump" {}
        _DustTilingOffset("Dust Tiling(XY) Offset(ZW)", Float) = (1, 1, 0, 0)
        _DustGradientMin("Dust Gradient Min", Range(0, 1)) = 0
        _DustGradientMax("Dust Gradient Max", Range(0, 1)) = 1
        _DustHeight("DustHeight", Range(0, 1)) = 0
        _DustHeightOffset("DustHeightOffset", Float) = 0
        _DustingSpeed("Dusting Speed", Float) = 1
        
        [Header(Scuffs)]
        _ScuffsIntensity("Scuffs Intensity", Range(-1, 1)) = 1
        _ScuffsBlend("Scuffs Blend", Range(0, 1)) = 0
        _ScuffsTilingOffset("Scuffs Tiling(XY) Offset(ZW)", Float) = (10, 10, 0, 0)
        
        [Header(Soaking)]
        _WaterMapTiling("WaterMap Tilling", Float) = 1
        _WetSharpness("WetSharpness", Range(0, 1)) = 0.5
        _NoiseSharpness("NoiseSharpness", Float) = 1
        _WaterPower("WaterPower", Range(0, 1)) = 0.6
        [NoScaleOffset]_WetMap("WetMap", 3D) = "white" {}
        _GroupBoundsCenterOS("GroupBoundsCenterOS", Vector) = (0, 0, 0, 0)
        _GroupBoundsSize("GroupBoundsSize", Vector) = (0, 0, 0, 0)
        
        [HideInInspector] _AmbientProbeBlendFactor ("AmbientProbeBlendFactor", Float) = 0
        [HideInInspector] _Custom_SHAr ("_Custom_SHAr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAg ("_Custom_SHAg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAb ("_Custom_SHAb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBr ("_Custom_SHBr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBg ("_Custom_SHBg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBb ("_Custom_SHBb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHC ("_Custom_SHC", Vector) = (0, 0, 0, 0)
    } 
    
    SubShader
    {
        Tags
        {
            "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent"
        }
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_DustNormal);
        SAMPLER(sampler_DustNormal);
        TEXTURE2D(_NoisesMap);
        SAMPLER(sampler_NoisesMap);
        TEXTURE3D(_WetMap);
        SAMPLER(sampler_WetMap);
        
        CBUFFER_START(UnityPerMaterial)
            float4x4 _GroupParentTransformWorldToLocal;
            float4 _Custom_SHAr;
            float4 _Custom_SHAg;
            float4 _Custom_SHAb;
            float4 _Custom_SHBr;
            float4 _Custom_SHBg;
            float4 _Custom_SHBb;
            float4 _Custom_SHC;
            float4 _DustTilingOffset;
            float4 _ScuffsTilingOffset;
            float3 _GroupBoundsSize;
            float3 _GroupBoundsCenterOS;
            float _DustingSpeed;
            float _NoiseSharpness;
            float _DustHeightOffset;
            float _WetSharpness;
            float _DustGradientMin;
            float _DustGradientMax;
            float _DustHeight;
            float _ScuffsBlend;
            float _WaterPower;
            float _WaterMapTiling;
            float _FramesUsingCustomColor;
            float _AmbientProbeBlendFactor;
            half4 _FrontGlassColor;
            half4 _BackGlassColor;
            half4 _FrontLightsGlassColor;
            half4 _BackLightsGlassColor;
            half4 _DustColor;
            half3 _FramesColor;
            half _GlassSmoothness;
            half _GlassMetalic;
            half _ScuffsIntensity;
        CBUFFER_END
        ENDHLSL

        Pass
        {
            Cull Back
            Blend One OneMinusSrcAlpha
            ZTest LEqual
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag

            #pragma multi_compile _ _REFLECTION_PROBES_ROTATED

            #if defined(_REFLECTION_PROBES_ROTATED)
                #pragma require cubearray 
            #endif
            
            #define _ALPHAPREMULTIPLY_ON 1
            #define _LIGHT_PROBES_AMBIENT_AND_CUSTOM_BLEND 1
            #define _GLOBAL_REFLECTION_PROBE_VEHICLE 1
            #define _SURFACE_TYPE_TRANSPARENT 1
            #define AMBIENT_OCCLUSION_FOR_DYNAMIC_OBJECT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            #include_with_pragmas "Assets/Utils/SkySystem/Shaders/FogInclude.cginc"

            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float4 vertexColor : COLOR0;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                float2 uv2 : TEXCOORD2;
                float2 uv3 : TEXCOORD3;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float4 uv01 : TEXCOORD0;
                float4 uv23 : TEXCOORD1;
                float4x3 tbn_positionWS : TEXCOORD2;
                float3 positionOS : TEXCOORD5;
                float3 normalOS : TEXCOORD6;
                float4 vertexColor : TEXCOORD7;
                DECLARE_SH(sh, 8)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.normalOS = i.normalOS;
                o.positionOS = i.positionOS.xyz;
                o.vertexColor = i.vertexColor;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)

                o.uv01 = float4(i.uv0, i.uv1);
                o.uv23 = float4(i.uv2, i.uv3);
                
                return o;
            } 

            half4 frag(Varyings i) : SV_Target
            {
                #if defined(LOD_FADE_CROSSFADE)
		            LODFadeCrossFade(i.positionCS);
                #endif
                
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                float framesMask = max(1 - abs(i.vertexColor.r * 255), 0);
                
                float frontLightsMask = saturate((1 - i.vertexColor.g) * 5.0f);
                float backLightsMask = saturate(1 - (i.vertexColor.g * 1.25f));
                frontLightsMask -= backLightsMask;
                
                float frontGlassMask = saturate((1 - i.vertexColor.b) * 5.0f);
                float backGlassMask = saturate(1 - (i.vertexColor.b * 1.25f));
                frontGlassMask -= backGlassMask;
                
                // Dust.
                _DustHeight = min(_DustHeight,1/_DustingSpeed);
                _DustHeight *= _DustingSpeed;
                _DustHeight = lerp(_DustHeight, 1, _DustHeightOffset);
                half dustingFactor = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, i.uv01.zw * _DustTilingOffset.xy + _DustTilingOffset.zw).r;
                dustingFactor *= 1.15f;
                
                half finalDustingFactor = dustingFactor * max(_DustHeight - i.uv23.y, _DustHeight * 0.2 * (1 - saturate(i.uv23.y-1)));
                finalDustingFactor = smoothstep(_DustGradientMin, _DustGradientMax, finalDustingFactor);

                finalDustingFactor *= SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv01.xy).r;
                finalDustingFactor = saturate(finalDustingFactor);
                finalDustingFactor *= _DustColor.a;
                
                // Glass Color.
                half3 glassColor = frontGlassMask * _FrontGlassColor.rgb;
                glassColor += backGlassMask * _BackGlassColor.rgb;
                glassColor += frontLightsMask * _FrontLightsGlassColor.rgb;
                glassColor += backLightsMask * _BackLightsGlassColor.rgb;
                glassColor = lerp(glassColor, glassColor * (1-framesMask) + framesMask * _FramesColor, _FramesUsingCustomColor);
                glassColor = saturate(glassColor);
                
                // Scuffs.
                half scuffsIntensity = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, i.uv01.zw * _ScuffsTilingOffset.xy + _ScuffsTilingOffset.zw).g + _ScuffsIntensity;
                scuffsIntensity = saturate(scuffsIntensity);
                
                // Soaking.
                float3 wetNoiseMapUV = i.positionOS;
                half3 wetNoiseBlend = abs(i.normalOS.xyz);
                wetNoiseBlend /= dot(wetNoiseBlend, 1.0f);
                
                float wetNoiseMapX = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, wetNoiseMapUV.zy * _WaterMapTiling).b;
                float wetNoiseMapY = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, wetNoiseMapUV.xz * _WaterMapTiling).b;
                float wetNoiseMapZ = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, wetNoiseMapUV.xy * _WaterMapTiling).b;
                float wetNoise = wetNoiseMapX * wetNoiseBlend.x + wetNoiseMapY * wetNoiseBlend.y + wetNoiseMapZ * wetNoiseBlend.z;
                float4x4 GroupParentTransformWorldToLocal = _GroupParentTransformWorldToLocal;
                float3 wetEffectUV = mul(GroupParentTransformWorldToLocal, float4(positionWS, 1)).rgb;
                wetEffectUV -= _GroupBoundsCenterOS;
                wetEffectUV /= _GroupBoundsSize;
                wetEffectUV += 0.5f;

                float wetEffect = SAMPLE_TEXTURE3D(_WetMap, sampler_WetMap, wetEffectUV).r;
                float wetSharpness = min(_WetSharpness, 0.99);
                wetEffect = (wetEffect - wetSharpness * 0.5) / (1 - wetSharpness);
                float wetFactor = saturate(wetEffect * (_NoiseSharpness + 1) + (wetNoise - 1) * _NoiseSharpness);
                wetFactor *= _WaterPower;
                wetFactor = 1 - wetFactor;
                
                // Normal.
                half3 dustNormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_DustNormal, sampler_DustNormal, i.uv01.zw * 20));
                half3 normalTS = lerp(dustNormalTS, float3(0, 0, 1), 1 - finalDustingFactor);
                
                // MSA.
                half metallic = _GlassMetalic * (1 - finalDustingFactor);
                half smoothness = _GlassSmoothness;
                smoothness = lerp(smoothness, smoothness - scuffsIntensity, _ScuffsBlend);
                smoothness = lerp(smoothness, 1, 1 - wetFactor);
                smoothness = smoothness * (1 - finalDustingFactor);
                smoothness = saturate(smoothness);
                metallic = saturate(metallic);
             
                // Alpha.
                half alpha = frontGlassMask * _FrontGlassColor.a;
                alpha += backGlassMask * _BackGlassColor.a;
                alpha += frontLightsMask * _FrontLightsGlassColor.a;
                alpha += backLightsMask * _BackLightsGlassColor.a;
                alpha += framesMask;
                alpha += finalDustingFactor;
                alpha = saturate(alpha);
                
                half3 albedo = lerp(glassColor, _DustColor.rgb, finalDustingFactor);
                albedo *= wetFactor;
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, alpha), half3(metallic, smoothness, 1), normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                half4 model = UniversalFragmentPBR(inputData, surfaceData);
                return MixFogForObjects(positionWS.xyz, model);
            }
            ENDHLSL
        }
        
    }
}
