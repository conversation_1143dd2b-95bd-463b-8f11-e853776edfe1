Shader "Kefir/Kefir_Vehicle_Glass_Decor"
{
    Properties
    {      
        [NoScaleOffset]_BaseMap("Dust(R) Cracks(G) Mask", 2D) = "white" {}
        [NoScaleOffset]_NoisesMap("Dust(R) Scuffs(G) Noise", 2D) = "white" {}
        [Header(Color)]
        _FrontGlassColor("Front Glass Color", Color) = (0, 0, 0, 0)
        _BackGlassColor("Back Glass Color", Color) = (0, 0, 0, 0) 
        
        _FrontLightsGlassColor("Front Lights Glass Color", Color) = (0, 0, 0, 0)
        _BackLightsGlassColor("Back Lights Glass Color", Color) = (0, 0, 0, 0)
        
        _GlassSmoothness("GlassSmoothness", Range(0, 1)) = 0
        _GlassMetalic("GlassMetalic", Range(0, 1)) = 0
        
        [MaterialToggle] _FramesUsingCustomColor("Frames Using Custom Color", Float) = 0
        _FramesColor("Frames Color", Color) = (0, 0, 0, 0)
        
        [Header(Dust)]
        _DustColor("DustColor", Color) = (0.6509434, 0.45256, 0.2118637, 0)
        [Normal][NoScaleOffset]_DustNormal("DustNormal", 2D) = "bump" {}
        _DustTilingOffset("Dust Tiling(XY) Offset(ZW)", Float) = (1, 1, 0, 0)
        _DustMax("DustMax", Range(0, 1)) = 0.7
        _DustHeight("DustHeight", Range(0, 1)) = 0
        
        [Header(Cracks)]
        _CracksColor("Cracks Color", Color) = (1, 1, 1, 1)
        _CracksIntensity("Cracks Intensity", Float) = 1
        
        [Header(Scuffs)]
        _ScuffsIntensity("Scuffs Intensity", Range(-1, 1)) = 1
        _ScuffsBlend("Scuffs Blend", Range(0, 1)) = 0
        _ScuffsTilingOffset("Scuffs Tiling(XY) Offset(ZW)", Float) = (10, 10, 0, 0)
    } 
    
    SubShader
    {
        Tags { "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent" }

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_DustNormal);
        SAMPLER(sampler_DustNormal);
        TEXTURE2D(_NoisesMap);
        SAMPLER(sampler_NoisesMap);
        
        CBUFFER_START(UnityPerMaterial)
            float4 _DustTilingOffset;
            float4 _ScuffsTilingOffset;
            float _DustMax;
            float _DustHeight;
            float _ScuffsBlend;
            float _FramesUsingCustomColor;
            float _CracksIntensity;
            half4 _DustColor;
            half4 _FrontGlassColor;
            half4 _BackGlassColor;
            half4 _FrontLightsGlassColor;
            half4 _BackLightsGlassColor;
            half3 _FramesColor;
            half3 _CracksColor;
            half _GlassSmoothness;
            half _GlassMetalic;
            half _ScuffsIntensity;
        CBUFFER_END
        ENDHLSL
        
        Pass
        {
            Cull Back
            Blend One OneMinusSrcAlpha
            ZTest LEqual
            HLSLPROGRAM

            #define _ALPHAPREMULTIPLY_ON 1
            #define _SURFACE_TYPE_TRANSPARENT 1
            
            #pragma vertex vert
            #pragma fragment frag
            
            #define _GLOBAL_REFLECTION_PROBE_VEHICLE 1
            #define _SURFACE_TYPE_TRANSPARENT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            #include_with_pragmas "Assets/Utils/SkySystem/Shaders/FogInclude.cginc"
                        
            struct Attributes
            {
                float3 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float4 vertexColor : COLOR0;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                float2 uv2 : TEXCOORD2;
                float2 uv3 : TEXCOORD3;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float4 uv01 : TEXCOORD0;
                float4 uv23 : TEXCOORD1;
                float4x3 tbn_positionWS : TEXCOORD2;
                float3 positionOS : TEXCOORD5;
                float3 normalOS : TEXCOORD6;
                half4 vertexColor : TEXCOORD7;
                DECLARE_SH(sh, 8)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS);
                o.positionCS = vertexInputs.positionCS;
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.normalOS = i.normalOS;
                o.positionOS = i.positionOS;
                o.vertexColor = i.vertexColor;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)

                o.uv01 = float4(i.uv0, i.uv1);
                o.uv23 = float4(i.uv2, i.uv3);
                
                return o;
            } 

            half4 frag(Varyings i) : SV_Target
            {
                #if defined(LOD_FADE_CROSSFADE)
		            LODFadeCrossFade(i.positionCS);
                #endif
                
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                float framesMask = max(1 - abs(i.vertexColor.r * 255),0);
                
                float frontLightsMask = saturate((1 - i.vertexColor.g) * 5.0f);
                float backLightsMask = saturate(1 - i.vertexColor.g * 1.25f);
                frontLightsMask -= backLightsMask;
                
                float frontGlassMask = saturate((1 - i.vertexColor.b) * 5.0f);
                float backGlassMask = saturate(1 - i.vertexColor.b * 1.25f);
                frontGlassMask -= backGlassMask;
                
                // Dust and Cracks.
                half dustingFactor = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, i.uv01.zw * _DustTilingOffset.xy + _DustTilingOffset.zw).r;
                dustingFactor *= 1.15f;

                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv01.xy);               
                half finalDustingFactor = min(_DustHeight,_DustMax) - i.uv23.y;
                finalDustingFactor *= dustingFactor;
                finalDustingFactor *= baseMap.r;
                finalDustingFactor = saturate(finalDustingFactor);

                half3 scratches = saturate(baseMap.g * _CracksColor * _CracksIntensity);              

                // Glass Color.              
                half3 glassColor = frontGlassMask * _FrontGlassColor.rgb;
                glassColor += backGlassMask * _BackGlassColor.rgb;
                glassColor += frontLightsMask * _FrontLightsGlassColor.rgb;
                glassColor += backLightsMask * _BackLightsGlassColor.rgb;
                glassColor = lerp(glassColor, glassColor * (1 - framesMask) + framesMask * _FramesColor, _FramesUsingCustomColor);
                glassColor = saturate(glassColor);

                // Final Color.
                half3 albedo = lerp(glassColor, _DustColor.rgb, finalDustingFactor);
                albedo += scratches;
                
                // Scuffs.
                half scuffsIntensity = SAMPLE_TEXTURE2D(_NoisesMap, sampler_NoisesMap, i.uv01.zw * _ScuffsTilingOffset.xy + _ScuffsTilingOffset.zw).g + _ScuffsIntensity;
                scuffsIntensity = saturate(scuffsIntensity);
                              
                // Normal.
                half3 dustNormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_DustNormal, sampler_DustNormal, i.uv01.zw * 20));
                half3 normalTS = lerp(dustNormalTS, float3(0, 0, 1), 1 - finalDustingFactor);

                // MSA.
                half metallic = _GlassMetalic * (1 - finalDustingFactor);
                
                half smoothness = _GlassSmoothness;
                smoothness = lerp(smoothness, smoothness - scuffsIntensity, _ScuffsBlend);
                smoothness = smoothness * (1 - finalDustingFactor);
                smoothness = saturate(smoothness);
                metallic = saturate(metallic);
                
                // Alpha.
                half alpha = frontGlassMask * _FrontGlassColor.a;
                alpha += backGlassMask * _BackGlassColor.a;
                alpha += frontLightsMask * _FrontLightsGlassColor.a;
                alpha += backLightsMask * _BackLightsGlassColor.a;
                alpha += framesMask;
                alpha += finalDustingFactor;
                alpha = saturate(alpha);
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, alpha), half3(metallic, smoothness, 1), normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                half4 model = UniversalFragmentPBR(inputData, surfaceData);
                return MixFogForObjects(positionWS, model);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
    }
}
