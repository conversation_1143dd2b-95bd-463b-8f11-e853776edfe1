Shader "Kefir/Kefir_Vehicle_BlobShadow"
{
    Properties  
    {	      
          [Toggle(_DECOR)] _DecorToggle("Decor?", Float) = 0
          [NoScaleOffset]_BaseMap("Shadow Tex", 2D) = "black"{}
          _Color("Color", Color) = (0, 0, 0, 1)
          _Width("Width", Float) = 1
          _Height("Height", Float) = 1
          _UpperLimit("Upper Limit", Range(0, 1)) = 0.2
          [Header(Cutout)]
          _IntensityY("Intensity Y", Float) = 2
          _SideCut("Side Cut", Range(-1, 0)) = -0.3
          _BlobCenterOffsetY("Blob Center Offset Y", Range(0, 1)) = 0.5  
          [HideInInspector] _BlobIntensity("Blob Intensity", Float) = 1
    }
    SubShader
    {
        Tags { "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent-100" }
        
        Stencil
        {
            Ref 0
            Comp Equal
            ReadMask 128
        }
        
        HLSLINCLUDE
      
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
       
        CBUFFER_START(UnityPerMaterial)
            float _Width;
            float _Height;
            float _IntensityY;
            float _SideCut;
            float _BlobCenterOffsetY;
            float _UpperLimit;
            half4 _Color;
            half _BlobIntensity;
        CBUFFER_END
        ENDHLSL
        
        Pass
        {
            Blend SrcAlpha OneMinusSrcAlpha
            Cull Front
            ZWrite Off
            ZTest GEqual
            
            HLSLPROGRAM

            #pragma vertex vert
            #pragma fragment frag

            #pragma shader_feature_local_fragment _DECOR

            #include "Assets/Content/ShaderLibrary/CameraTextures.hlsl"
            
            struct Attributes
            {
                float4 positionOS : POSITION;
            };
                        
            struct Varyings
            {
                float4 positionCS : SV_POSITION;
            };
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                o.positionCS = TransformObjectToHClip(i.positionOS.xyz);
                
                return o;
            }

            float3 GetProjectionPointWS(float2 screenPos)
            {
                float depthRaw = SampleSceneDepth(screenPos);
                #if !UNITY_REVERSED_Z
                    depthRaw = depthRaw * 2 - 1;
                #endif
                return ComputeWorldSpacePosition(screenPos, depthRaw, UNITY_MATRIX_I_VP);
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                // Projection position.
                float2 screenPos = GetNormalizedScreenSpaceUV(i.positionCS.xy);
                float3 projectionPointWS = GetProjectionPointWS(screenPos);
                float3 projectionPointOS = TransformWorldToObject(projectionPointWS);

                // Shape.
                half sphere = smoothstep(0, 0.5, 1 - saturate(length(projectionPointOS)));
                float2 uvPos = projectionPointOS.xz + 0.5;
                float2 scaledUv = float2(uvPos.x * _Width, uvPos.y * _Height) - (float2(_Width, _Height) * 0.5 - 0.5);
                half shadowSample = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, scaledUv).r;

                float checkUp = smoothstep(0, saturate(projectionPointOS.y), _UpperLimit);
                half alpha;
                #ifdef _DECOR
                    alpha = sphere * _Color.a * shadowSample * checkUp;
                #else
                    // Check shadow direction.
                    const float angleLimit = -0.15;
                    const float smoothDirectionIntensity = 50;
                    const float smoothDirectionHeightOffset = 0.85;
                    float3 DDX = ddx(projectionPointWS);
                    float3 DDY = ddy(projectionPointWS);
                    float3 shadowDirectionWS = normalize(cross(DDX, DDY));
                    float directionScalar = dot(shadowDirectionWS, float3(0, -1, 0));
                    float smoothDirectionFactor = Pow4(1 - rcp(smoothDirectionHeightOffset - projectionPointOS.y)) * smoothDirectionIntensity;
                    half checkDirection = saturate(step(angleLimit, directionScalar) + smoothDirectionFactor) * checkUp;

                    // Low cutout.
                    float3 directionCutPosOS = float3(abs(projectionPointOS.x), projectionPointOS.y, abs(projectionPointOS.z));              
                    half lowCutout = smoothstep(0, 1, (dot(directionCutPosOS, float3(_SideCut, 1, _SideCut)) + _BlobCenterOffsetY) * _IntensityY);

                    alpha = sphere * _Color.a * shadowSample * checkDirection * _BlobIntensity * lowCutout;
                #endif               
                               
                return half4(_Color.rgb, alpha);                
            }
            ENDHLSL
        }
    }
}
