Shader "Kefir/Kefir_Terrain_Rocks"
{
    Properties
    {
        [Header(Splat Map)]
        [NoScaleOffset]_SplatMap("Splat Map", 2D) = "red" {}
        _SplatMapWorldSize("SplatMapWorldSize", Float) = 0
        _SplatMapWorldOffset("SplatMapWorldOffset", Float) = 0
        
        [Header(Noise)]
        [NoScaleOffset]_LayersNoiseMap("Layers Noise Map", 2D) = "black" {}
        _LayersNoiseScale("Layers Noise Scale", Float) = 10
        
        [Header(Layer0)]
        [NoScaleOffset]_Layer0BaseMap("Layer0 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer0BumpMap("Layer0 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer0MaskMap("Layer0 Mask Map", 2D) = "grey" {}
        _Layer0TilingOffset("Layer0 Tiling Offset", Vector) = (1, 1, 0, 0)
        
        [Header(Layer1)]
        [NoScaleOffset]_Layer1BaseMap("Layer1 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer1BumpMap("Layer1 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer1MaskMap("Layer1 Mask Map", 2D) = "grey" {}
        _Layer1TilingOffset("Layer1 Tiling Offset", Vector) = (1, 1, 0, 0)
        _Layer1Contrast("Layer1 Contrast", Float) = 1
        
        [Header(Layer2)]
        [NoScaleOffset]_Layer2BaseMap("Layer2 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer2BumpMap("Layer2 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer2MaskMap("Layer2 Mask Map", 2D) = "grey" {}
        _Layer2TilingOffset("Layer2 Tiling Offset", Vector) = (1, 1, 0, 0)
        _Layer2Contrast("Layer2 Contrast", Float) = 1
        
        [Header(Layer3)]
        [NoScaleOffset]_Layer3BaseMap("Layer3 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer3BumpMap("Layer3 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer3MaskMap("Layer3 Mask Map", 2D) = "grey" {}
        _Layer3TilingOffset("Layer3 Tiling Offset", Vector) = (1, 1, 0, 0)
        _Layer3Contrast("Layer3 Contrast", Float) = 1
        
        [Header(Layer4)]
        [NoScaleOffset]_Layer4BaseMap("Layer4 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer4BumpMap("Layer4 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer4MaskMap("Layer4 Mask Map", 2D) = "grey" {}
        _Layer4TilingOffset("Layer4 Tiling Offset", Vector) = (0, 0, 0, 0)
        _Layer4Contrast("Layer4 Contrast", Float) = 1

        [Header(Side)]
        [NoScaleOffset]_Side1BaseMap("Side 1 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Side1BumpMap("Side 1 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Side1MaskMap("Side 1 Mask Map", 2D) = "grey" {}
        [NoScaleOffset]_Side2BaseMap("Side 2 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Side2BumpMap("Side 2 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Side2MaskMap("Side 2 Mask Map", 2D) = "grey" {}
        [NoScaleOffset]_SideNoise("Side Noise Map", 2D) = "grey" {}
        _Smoothstepmin("smoothstep min", Float) = 0
        _Smoothstepmax("smoothstep max", Float) = 1
    } 
    
    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry-101"
        }
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        TEXTURE2D(_SplatMap);
        SAMPLER(sampler_SplatMap);
        TEXTURE2D(_LayersNoiseMap);
        SAMPLER(sampler_LayersNoiseMap);

        TEXTURE2D(_Layer0BaseMap);
        SAMPLER(sampler_Layer0BaseMap);
        TEXTURE2D(_Layer1BaseMap);
        SAMPLER(sampler_Layer1BaseMap);
        TEXTURE2D(_Layer2BaseMap);
        SAMPLER(sampler_Layer2BaseMap);
        TEXTURE2D(_Layer3BaseMap);
        SAMPLER(sampler_Layer3BaseMap);
        TEXTURE2D(_Layer4BaseMap);

        TEXTURE2D(_Layer0MaskMap);
        TEXTURE2D(_Layer1MaskMap);
        TEXTURE2D(_Layer2MaskMap);
        TEXTURE2D(_Layer3MaskMap);
        TEXTURE2D(_Layer4MaskMap);

        TEXTURE2D(_Layer0BumpMap);
        SAMPLER(sampler_Layer0BumpMap);
        TEXTURE2D(_Layer1BumpMap);
        TEXTURE2D(_Layer2BumpMap);
        TEXTURE2D(_Layer3BumpMap);
        TEXTURE2D(_Layer4BumpMap);

        TEXTURE2D(_Side1BaseMap);
        TEXTURE2D(_Side2BaseMap);
        TEXTURE2D(_Side1BumpMap);
        TEXTURE2D(_Side2BumpMap);
        TEXTURE2D(_Side1MaskMap);
        TEXTURE2D(_Side2MaskMap);
        TEXTURE2D(_SideNoise);
        
        CBUFFER_START(UnityPerMaterial)
            float4 _Layer0TilingOffset;
            float4 _Layer1TilingOffset;
            float4 _Layer2TilingOffset;
            float4 _Layer3TilingOffset;
            float4 _Layer4TilingOffset;
            float _SplatMapWorldSize;
            float _SplatMapWorldOffset;
            float _LayersNoiseScale;
            float _Layer1Contrast;
            float _Layer2Contrast;
            float _Layer3Contrast;
            float _Layer4Contrast;
            float _Smoothstepmin;
            float _Smoothstepmax;
        CBUFFER_END
        
        #include "TerrainCommon.hlsl"
        
        void ReusedBlending(float2 uv, float2 uv1, inout half sideBlendFactor, inout half3 normalTS, out half4 side2Albedo)
        {
            half3 side1NormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_Side1BumpMap, sampler_Layer0BumpMap, uv));
            half3 side2NormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_Side2BumpMap, sampler_Layer0BumpMap, uv1));

            side2Albedo = SAMPLE_TEXTURE2D(_Side2BaseMap, sampler_Layer0BumpMap, uv1);

            half3 combinedSideNormalTS = lerp(side1NormalTS, side2NormalTS, side2Albedo.a);

            half sideNoise = SAMPLE_TEXTURE2D(_SideNoise, sampler_Layer0BumpMap, uv).r;
            sideBlendFactor = smoothstep(_Smoothstepmin + sideBlendFactor, _Smoothstepmax + sideBlendFactor, sideNoise);
            normalTS = lerp(combinedSideNormalTS, normalTS, sideBlendFactor);
        }

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            half4 side2Albedo;
            half3 normalTS = MixTerrainSplatsNormalTS(snData.positionWS);
            half sideBlendFactor = snData.vertexColor.r;
            ReusedBlending(snData.uv, snData.uv1, sideBlendFactor, normalTS, side2Albedo);
            return normalTS;
        }
        
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"
            #include "TerrainCommon.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float4 color : COLOR0;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float4x3 tbn_positionWS : TEXCOORD0;
                float4 color : TEXCOORD3;
                float4 uv : TEXCOORD4;
                DECLARE_SH(sh, 5)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.color = i.color;
                o.uv.xy = i.uv0;
                o.uv.zw = i.uv1;
                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(i.positionCS);
                #endif

                half3x3 tbn;
                float3 positionWS;
                half3 albedo;
                half3 msa;
                half3 normalTS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                MixTerrainSplats(positionWS, albedo, msa, normalTS);

                half4 side2Albedo;
                half sideBlendFactor = i.color.r;
                ReusedBlending(i.uv.xy, i.uv.zw, sideBlendFactor, normalTS, side2Albedo);
    
                half3 side1Albedo = SAMPLE_TEXTURE2D(_Side1BaseMap, sampler_Layer0BaseMap, i.uv.xy).rgb;
                
                half3 side1MSA = SAMPLE_TEXTURE2D(_Side1MaskMap, sampler_Layer0BaseMap, i.uv.xy).rgb;
                half3 side2MSA = SAMPLE_TEXTURE2D(_Side2MaskMap, sampler_Layer0BaseMap, i.uv.zw).rgb;
                
                half3 combinedSideAlbedo = lerp(side1Albedo, side2Albedo.rgb, side2Albedo.a);
                half3 combinedSideMSA = lerp(side1MSA, side2MSA, side2Albedo.a);
                
                albedo = lerp(combinedSideAlbedo, albedo, sideBlendFactor);
                msa = lerp(combinedSideMSA, msa, sideBlendFactor);

                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, 1), msa, normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #define _USE_NORMAL_MAP 1
            #define _USE_UV1 1
            #define _USE_VERTEX_COLOR 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }
    }
}
