Shader "Kefir/Kefir_Terrain_LOD"
{
    Properties
    {
        [Header(Splat Map)]
        _SplatMapWorldSize("SplatMapWorldSize", Float) = 0
        _SplatMapWorldOffset("SplatMapWorldOffset", Float) = 0
        
        [Header(BaseMap)]
        [NoScaleOffset]_BaseMap("Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_MaskMap("Mask Map", 2D) = "grey" {}
        
        [Header(WetSand)]
        [Toggle(_WET_SAND_ENABLED)]_WET_SAND_ENABLED("WetSandEnabled", Float) = 0
        _WetSandMetallic("WetSandMetallic", Range(0, 1)) = 0.1
        _WetSandSmoothness("WetSandSmoothness", Range(0, 1)) = 1
        _WetSandColor0("WetSandColor0", Color) = (0.08076719, 0.08076719, 0.3113208, 1)
        _WetSandColor1("WetSandColor1", Color) = (0.08076719, 0.08076719, 0.3113208, 1)
        _WetSandDistanceFromShoreMinMax("WetSandDistanceFromShoreMinMax", Vector) = (-1.5, 2, 0, 0)
        _WetSandHeightMinMax("WetSandHeightMinMax", Vector) = (0, 0, 0, 0)
        _WetSandMetallicAndSmoothnessHeightEndMinMax("WetSandMetallicAndSmoothnessHeightEndMinMax", Vector) = (0, 0, 0, 0)
        [NoScaleOffset]_WetSandNoise("WetSandNoise", 2D) = "white" {}
        _WetSandNoiseScale("WetSandNoiseScale", Float) = 0
        _WetSandNoiseDistanceToShorePower("WetSandNoiseDistanceToShorePower", Float) = 0
    } 
    
    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry+130"
        }
        
        Offset 0.01, 0.01
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_MaskMap);
        SAMPLER(sampler_MaskMap);

        TEXTURE2D(_WetSandNoise);
        SAMPLER(sampler_WetSandNoise);
        
        CBUFFER_START(UnityPerMaterial)
            float2 _WetSandDistanceFromShoreMinMax;
            float2 _WetSandHeightMinMax;
            float2 _WetSandMetallicAndSmoothnessHeightEndMinMax;
            float _WetSandNoiseScale;
            float _WetSandNoiseDistanceToShorePower;
            float _SplatMapWorldSize;
            float _SplatMapWorldOffset;
            half3 _WetSandColor0;
            half3 _WetSandColor1;
            half _WetSandMetallic;
            half _WetSandSmoothness;
        CBUFFER_END
        
        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            float2 uv = (snData.positionWS.xz + _SplatMapWorldOffset) / _SplatMapWorldSize;
            return UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, uv));
        }
        
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag

            #pragma shader_feature_local _WET_SAND_ENABLED

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"
            
            #if defined(_WET_SAND_ENABLED)
                #include "WetSandCommon.hlsl"
            #endif
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float4x3 tbn_positionWS : TEXCOORD0; //  TEXCOORD 0-3
                DECLARE_SH(sh, 4)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            } 
            
            half4 frag(Varyings i) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                float2 uv = (positionWS.xz + _SplatMapWorldOffset) / _SplatMapWorldSize;
                half3 albedo = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uv).rgb;
                half3 msa = SAMPLE_TEXTURE2D(_MaskMap, sampler_MaskMap, uv).rgb;
                half3 normalTS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, uv));
                
                #if defined(_WET_SAND_ENABLED)
                    MixInWetSand(positionWS, albedo, msa, normalTS);
                #endif
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, 1), msa, normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #define _USE_NORMAL_MAP 1
            #define _USE_POSITION_WS 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }
    }
}
