Shader "Kefir/Kefir_Road_Terrain_Blend"
{
    Properties
    {
        [Header(Blend)]
        [NoScaleOffset]_BlendNoiseMap("Blend Noise Map", 2D) = "white" {}
        _BlendNoiseScale("Blend Noise Scale", Range(0, 1)) = 1
        _BlendNoisePower("Blend Noise Power", Range(0, 1)) = 1
        _BlendNoiseEdge0("Blend Noise Edge0", Range(0, 1)) = 1
        _BlendNoiseEdge1("Blend Noise Edge1", Range(0, 1)) = 1
                
        [Header(Splat Map)]
        [NoScaleOffset]_SplatMap("Splat Map", 2D) = "red" {}
        _SplatMapWorldSize("SplatMapWorldSize", Float) = 0
        _SplatMapWorldOffset("SplatMapWorldOffset", Float) = 0
        
        [Header(Noise)]
        [NoScaleOffset]_LayersNoiseMap("Layers Noise Map", 2D) = "black" {}
        _LayersNoiseScale("Layers Noise Scale", Float) = 10
        
        [Header(Layer0)]
        [NoScaleOffset]_Layer0BaseMap("Layer0 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer0BumpMap("Layer0 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer0MaskMap("Layer0 Mask Map", 2D) = "grey" {}
        _Layer0TilingOffset("Layer0 Tiling Offset", Vector) = (1, 1, 0, 0)
        
        [Header(Layer1)]
        [NoScaleOffset]_Layer1BaseMap("Layer1 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer1BumpMap("Layer1 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer1MaskMap("Layer1 Mask Map", 2D) = "grey" {}
        _Layer1TilingOffset("Layer1 Tiling Offset", Vector) = (1, 1, 0, 0)
        _Layer1Contrast("Layer1 Contrast", Float) = 1
        
        [Header(Layer2)]
        [NoScaleOffset]_Layer2BaseMap("Layer2 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer2BumpMap("Layer2 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer2MaskMap("Layer2 Mask Map", 2D) = "grey" {}
        _Layer2TilingOffset("Layer2 Tiling Offset", Vector) = (1, 1, 0, 0)
        _Layer2Contrast("Layer2 Contrast", Float) = 1
        
        [Header(Layer3)]
        [NoScaleOffset]_Layer3BaseMap("Layer3 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer3BumpMap("Layer3 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer3MaskMap("Layer3 Mask Map", 2D) = "grey" {}
        _Layer3TilingOffset("Layer3 Tiling Offset", Vector) = (1, 1, 0, 0)
        _Layer3Contrast("Layer3 Contrast", Float) = 1
        
        [Header(Layer4)]
        [NoScaleOffset]_Layer4BaseMap("Layer4 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer4BumpMap("Layer4 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer4MaskMap("Layer4 Mask Map", 2D) = "grey" {}
        _Layer4TilingOffset("Layer4 Tiling Offset", Vector) = (0, 0, 0, 0)
        _Layer4Contrast("Layer4 Contrast", Float) = 1
    } 
    
    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry+120"
        }
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        
        TEXTURE2D(_SplatMap);
        SAMPLER(sampler_SplatMap);
        TEXTURE2D(_LayersNoiseMap);
        SAMPLER(sampler_LayersNoiseMap);

        TEXTURE2D(_Layer0BaseMap);
        SAMPLER(sampler_Layer0BaseMap);
        TEXTURE2D(_Layer1BaseMap);
        SAMPLER(sampler_Layer1BaseMap);
        TEXTURE2D(_Layer2BaseMap);
        SAMPLER(sampler_Layer2BaseMap);
        TEXTURE2D(_Layer3BaseMap);
        SAMPLER(sampler_Layer3BaseMap);
        TEXTURE2D(_Layer4BaseMap);
        
        TEXTURE2D(_Layer0MaskMap);
        TEXTURE2D(_Layer1MaskMap);
        TEXTURE2D(_Layer2MaskMap);
        TEXTURE2D(_Layer3MaskMap);
        TEXTURE2D(_Layer4MaskMap);

        TEXTURE2D(_Layer0BumpMap);
        SAMPLER(sampler_Layer0BumpMap);
        TEXTURE2D(_Layer1BumpMap);
        TEXTURE2D(_Layer2BumpMap);
        TEXTURE2D(_Layer3BumpMap);
        TEXTURE2D(_Layer4BumpMap);

        TEXTURE2D(_BlendNoiseMap);
        SAMPLER(sampler_BlendNoiseMap);
        
        CBUFFER_START(UnityPerMaterial)
            float4 _Layer0TilingOffset;
            float4 _Layer1TilingOffset;
            float4 _Layer2TilingOffset;
            float4 _Layer3TilingOffset;
            float4 _Layer4TilingOffset;
            float _SplatMapWorldSize;
            float _SplatMapWorldOffset;
            float _LayersNoiseScale;
            float _Layer1Contrast;
            float _Layer2Contrast;
            float _Layer3Contrast;
            float _Layer4Contrast;
            float _BlendNoiseScale;
            float _BlendNoiseEdge0;
            float _BlendNoiseEdge1;
            half _BlendNoisePower;
        CBUFFER_END

        #include "TerrainCommon.hlsl"

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            return MixTerrainSplatsNormalTS(snData.positionWS);
        }
        
        ENDHLSL

        Pass
        {
            Blend SrcAlpha OneMinusSrcAlpha
            
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                DECLARE_SH(sh, 4)
            };
            
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                
                o.uv = i.uv;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            } 
            
            half4 frag(Varyings i) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                half3 albedo;
                half3 msa;
                half3 normalTS;
                UnpackTBNAndPositionWS(i.tbn_positionWS,tbn,positionWS);
                MixTerrainSplats(positionWS, albedo, msa, normalTS);
                
                half blendNoise = SAMPLE_TEXTURE2D(_BlendNoiseMap, sampler_BlendNoiseMap, positionWS.xz * _BlendNoiseScale).r * _BlendNoisePower;
                half alpha = smoothstep(_BlendNoiseEdge0 + blendNoise, _BlendNoiseEdge1 + blendNoise, i.uv.x);

                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, alpha), msa, normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #define _USE_NORMAL_MAP 1
            #define _USE_POSITION_WS 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }
    }
}
