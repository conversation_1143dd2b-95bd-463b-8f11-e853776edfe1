Shader "Kefir/Kefir_Terrain"
{
    Properties
    {
        [Header(Splat Map)]
        [NoScaleOffset]_SplatMap("Splat Map", 2D) = "red" {}
        _SplatMapWorldSize("SplatMapWorldSize", Float) = 0
        _SplatMapWorldOffset("SplatMapWorldOffset", Float) = 0
        
        [Header(Noise)]
        [NoScaleOffset]_LayersNoiseMap("Layers Noise Map", 2D) = "black" {}
        _LayersNoiseScale("Layers Noise Scale", Float) = 10
        
        [Header(Layer0)]
        [NoScaleOffset]_Layer0BaseMap("Layer0 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer0BumpMap("Layer0 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer0MaskMap("Layer0 Mask Map", 2D) = "grey" {}
        _Layer0TilingOffset("Layer0 Tiling Offset", Vector) = (1, 1, 0, 0)
        
        [Header(Layer1)]
        [NoScaleOffset]_Layer1BaseMap("Layer1 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer1BumpMap("Layer1 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer1MaskMap("Layer1 Mask Map", 2D) = "grey" {}
        _Layer1TilingOffset("Layer1 Tiling Offset", Vector) = (1, 1, 0, 0)
        _Layer1Contrast("Layer1 Contrast", Float) = 1
        
        [Header(Layer2)]
        [NoScaleOffset]_Layer2BaseMap("Layer2 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer2BumpMap("Layer2 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer2MaskMap("Layer2 Mask Map", 2D) = "grey" {}
        _Layer2TilingOffset("Layer2 Tiling Offset", Vector) = (1, 1, 0, 0)
        _Layer2Contrast("Layer2 Contrast", Float) = 1
        
        [Header(Layer3)]
        [NoScaleOffset]_Layer3BaseMap("Layer3 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer3BumpMap("Layer3 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer3MaskMap("Layer3 Mask Map", 2D) = "grey" {}
        _Layer3TilingOffset("Layer3 Tiling Offset", Vector) = (1, 1, 0, 0)
        _Layer3Contrast("Layer3 Contrast", Float) = 1
        
        [Header(Layer4)]
        [NoScaleOffset]_Layer4BaseMap("Layer4 Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_Layer4BumpMap("Layer4 Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_Layer4MaskMap("Layer4 Mask Map", 2D) = "grey" {}
        _Layer4TilingOffset("Layer4 Tiling Offset", Vector) = (0, 0, 0, 0)
        _Layer4Contrast("Layer4 Contrast", Float) = 1
        
        [Header(WetSand)]
        [Toggle(_WET_SAND_ENABLED)]_WET_SAND_ENABLED("WetSandEnabled", Float) = 0
        _WetSandMetallic("WetSandMetallic", Range(0, 1)) = 0.1
        _WetSandSmoothness("WetSandSmoothness", Range(0, 1)) = 1
        _WetSandColor0("WetSandColor0", Color) = (0.08076719, 0.08076719, 0.3113208, 1)
        _WetSandColor1("WetSandColor1", Color) = (0.08076719, 0.08076719, 0.3113208, 1)
        _WetSandDistanceFromShoreMinMax("WetSandDistanceFromShoreMinMax", Vector) = (-1.5, 2, 0, 0)
        _WetSandHeightMinMax("WetSandHeightMinMax", Vector) = (0, 0, 0, 0)
        _WetSandMetallicAndSmoothnessHeightEndMinMax("WetSandMetallicAndSmoothnessHeightEndMinMax", Vector) = (0, 0, 0, 0)
        [NoScaleOffset]_WetSandNoise("WetSandNoise", 2D) = "white" {}
        _WetSandNoiseScale("WetSandNoiseScale", Float) = 0
        _WetSandNoiseDistanceToShorePower("WetSandNoiseDistanceToShorePower", Float) = 0
        
        [Header(Wind)]
        [Toggle(_WIND_ENABLED)]_WIND_ENABLED("WindEnabled", Float) = 0
        [NoScaleOffset]_WindNoises("Main (R), Subtract (G), Blend (B)", 2D) = "black" {}
        _WindColor("Wind Color", Color) = (0, 0, 0, 1)
        _WindSpeed("Wind Speed", Float) = 1
        _WindSmoothness("Wind Smoothness", Range(0, 1)) = 0
        _MainMaskIntensity("Main Mask Intensity", Float) = 1
        _SubtractMaskIntensity("Subtract Mask Intensity", Float) = 1
        _WindMainNoiseSize("Wind Main Noise Size", Vector) = (0.2, 0.02, 0, 0)
        _WindSubtractNoiseSize("Wind Subtract Noise Size", Vector) = (0.01, 0.01, 0, 0)
        _WindBlendNoiseSize("Wind Blend Noise Size", Vector) = (0.3, 0.3, 0 , 0)
        _DistortionMinMax("Distortion Min Max", Vector) = (0.1, 0.6, 0, 0)
        _WindMainNoiseSpeedMinMax("Wind Main Noise Speed Min Max", Vector) = (0.1, 1, 0 , 0)
        _WindSubtractNoiseSpeedMinMax("Wind Subtract Noise Speed Min Max", Vector) = (0.05, 0.15, 0 , 0)
        _WindFadeoutDistanceSq("Wind Fadeout Distance Sq", Float) = 1000
    } 
    
    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry+110"
        }
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        TEXTURE2D(_SplatMap);
        SAMPLER(sampler_SplatMap);
        TEXTURE2D(_LayersNoiseMap);
        SAMPLER(sampler_LayersNoiseMap);

        TEXTURE2D(_Layer0BaseMap);
        SAMPLER(sampler_Layer0BaseMap);
        TEXTURE2D(_Layer1BaseMap);
        SAMPLER(sampler_Layer1BaseMap);
        TEXTURE2D(_Layer2BaseMap);
        SAMPLER(sampler_Layer2BaseMap);
        TEXTURE2D(_Layer3BaseMap);
        SAMPLER(sampler_Layer3BaseMap);
        TEXTURE2D(_Layer4BaseMap);
        
        TEXTURE2D(_Layer0MaskMap);
        TEXTURE2D(_Layer1MaskMap);
        TEXTURE2D(_Layer2MaskMap);
        TEXTURE2D(_Layer3MaskMap);
        TEXTURE2D(_Layer4MaskMap);

        TEXTURE2D(_Layer0BumpMap);
        SAMPLER(sampler_Layer0BumpMap);
        TEXTURE2D(_Layer1BumpMap);
        TEXTURE2D(_Layer2BumpMap);
        TEXTURE2D(_Layer3BumpMap);
        TEXTURE2D(_Layer4BumpMap);

        TEXTURE2D(_WetSandNoise);
        SAMPLER(sampler_WetSandNoise);

        TEXTURE2D(_WindNoises);
        SAMPLER(sampler_WindNoises);
        
        CBUFFER_START(UnityPerMaterial)
            float4 _Layer0TilingOffset;
            float4 _Layer1TilingOffset;
            float4 _Layer2TilingOffset;
            float4 _Layer3TilingOffset;
            float4 _Layer4TilingOffset;
        
            float2 _WetSandDistanceFromShoreMinMax;
            float2 _WetSandHeightMinMax;
            float2 _WetSandMetallicAndSmoothnessHeightEndMinMax;

            float2 _WindMainNoiseSize;
            float2 _WindSubtractNoiseSize;
            float2 _WindBlendNoiseSize;
            float2 _WindMainNoiseSpeedMinMax;
            float2 _WindSubtractNoiseSpeedMinMax;

            float2 _DistortionMinMax;
        
            float _SplatMapWorldSize;
            float _SplatMapWorldOffset;
            float _LayersNoiseScale;
            float _Layer1Contrast;
            float _Layer2Contrast;
            float _Layer3Contrast;
            float _Layer4Contrast;
            float _WetSandMetallic;
            float _WetSandSmoothness;
        
            float _WetSandNoiseScale;
            float _WetSandNoiseDistanceToShorePower;
        
            float _WindSpeed;
            float _WindFadeoutDistanceSq;
        
            half4 _WindColor;
            half3 _WetSandColor1;
            half3 _WetSandColor0;
            half _MainMaskIntensity;
            half _SubtractMaskIntensity;
            half _WindSmoothness;
        CBUFFER_END
        
        #include "Assets/Content/ShaderLibrary/GlobalWindUniforms.hlsl"
        #include "TerrainCommon.hlsl"

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            return MixTerrainSplatsNormalTS(snData.positionWS);
        }
        
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag

            #pragma shader_feature_local _WET_SAND_ENABLED
            #pragma shader_feature_local _WIND_ENABLED

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"
            #include "TerrainCommon.hlsl"

            #if defined(_WET_SAND_ENABLED)
                #include "WetSandCommon.hlsl"
            #endif
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float4x3 tbn_positionWS : TEXCOORD0;
                DECLARE_SH(sh, 4)
                #if defined(_WIND_ENABLED)
                    float4 mainAndBlendNoisesUV : TEXCOORD5;
                    float4 subtractNoiseUVAndSpeedAndFadeout : TEXCOORD6;
                #endif 
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)

                #if defined(_WIND_ENABLED)
                    float speed = _WindSpeed * _Time.y;
                    float2 rotatedUV = vertexInputs.positionWS.x * float2(_GlobalWindDirection.z, -_GlobalWindDirection.x);
                    rotatedUV += vertexInputs.positionWS.z * (-_GlobalWindDirection.xz);
                    o.mainAndBlendNoisesUV = float4(rotatedUV * _WindMainNoiseSize, rotatedUV * _WindBlendNoiseSize);
                    float2 subtractScaledUV = rotatedUV * _WindSubtractNoiseSize;
                    float3 posDif = vertexInputs.positionWS - _WorldSpaceCameraPos;
                    float distanceToCameraSq = dot(posDif, posDif);
                    float windFadeout = saturate(_WindFadeoutDistanceSq / distanceToCameraSq);
                    o.subtractNoiseUVAndSpeedAndFadeout = float4(subtractScaledUV, speed, windFadeout);
                #endif 
                
                return o;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                half3 albedo;
                half3 msa;
                half3 normalTS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                MixTerrainSplats(positionWS, albedo, msa, normalTS);
                
                #if defined(_WET_SAND_ENABLED)
                    MixInWetSand(positionWS, albedo, msa, normalTS);
                #endif

                half smoothness = msa.g;
                half3 finalColor = albedo;              
                
                #if defined(_WIND_ENABLED)
                    float speed = i.subtractNoiseUVAndSpeedAndFadeout.z;

                    float2 blendNoiseUV = i.mainAndBlendNoisesUV.zw;
                    float blendIntensity = lerp(_DistortionMinMax.y, _DistortionMinMax.x, _GlobalWindStrength);
                    float blendNoiseMask = SAMPLE_TEXTURE2D(_WindNoises, sampler_WindNoises, blendNoiseUV).b;
                    float distortionUV = (blendNoiseMask * 2 - 1) * blendIntensity;

                    float2 mainNoiseUV = i.mainAndBlendNoisesUV.xy;
                    half mainMaskFast = SAMPLE_TEXTURE2D(_WindNoises, sampler_WindNoises, mainNoiseUV + float2(distortionUV, speed * _WindMainNoiseSpeedMinMax.y)).r;
                    half mainMaskSlow = SAMPLE_TEXTURE2D(_WindNoises, sampler_WindNoises, mainNoiseUV + float2(distortionUV, speed * _WindMainNoiseSpeedMinMax.x)).r * 5;
                    half windMask = lerp(mainMaskSlow, mainMaskFast, _GlobalWindStrength) * _MainMaskIntensity;

                    float2 subtractNoiseUV = i.subtractNoiseUVAndSpeedAndFadeout.xy;
                    half subtractMaskFast = SAMPLE_TEXTURE2D(_WindNoises, sampler_WindNoises, subtractNoiseUV + float2(0, speed * _WindSubtractNoiseSpeedMinMax.y)).g;
                    half subtractMaskSlow = SAMPLE_TEXTURE2D(_WindNoises, sampler_WindNoises, subtractNoiseUV + float2(0, speed * _WindSubtractNoiseSpeedMinMax.x)).g * 4;
                    half subtractMask = lerp(subtractMaskSlow, subtractMaskFast, _GlobalWindStrength) * _SubtractMaskIntensity;

                    half fadeout = i.subtractNoiseUVAndSpeedAndFadeout.w;
                    half finalWindMask = saturate(windMask - subtractMask) * blendNoiseMask * fadeout * _GlobalWindStrength;

                    smoothness = lerp(msa.g, _WindSmoothness, finalWindMask);
                    finalColor = albedo + finalWindMask * _WindColor;
                #endif             

                SurfaceData surfaceData = InitializeSurfaceData(half4(finalColor, 1), half3(msa.r, smoothness, msa.b), normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #define _USE_NORMAL_MAP 1
            #define _USE_POSITION_WS 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }
    }
}
