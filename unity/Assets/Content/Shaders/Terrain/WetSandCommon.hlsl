#ifndef WET_SAND_LOD_COMMON_INCLUDED
#define WET_SAND_COMMON_INCLUDED

#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
#include "Assets/Utils/Builder/Content/Water/Shaders/WaterTopDownTextures.hlsl"

void MixInWetSand(float3 positionWS, inout half3 albedo, inout half3 msa, inout half3 normalTS)
{
    float2 topDownUV = GetTopDownTextureUV(positionWS);
    float distanceToShore = GetDistanceToShore(topDownUV);
    half noise = SAMPLE_TEXTURE2D_LOD(_WetSandNoise, sampler_WetSandNoise, topDownUV * _WetSandNoiseScale, 0).r;
    distanceToShore += noise * _WetSandNoiseDistanceToShorePower;
    
    float distanceFactor = smoothstep(_WetSandDistanceFromShoreMinMax.x, _WetSandDistanceFromShoreMinMax.y, distanceToShore);
    half heightFactor = 1 - smoothstep(_WetSandHeightMinMax.x, _WetSandHeightMinMax.y, positionWS.y);

    half3 wetSandColor = lerp(_WetSandColor0, _WetSandColor1, saturate(heightFactor / (heightFactor + distanceFactor)));
    float colorFactor = saturate((heightFactor + distanceFactor) / 2);
    albedo = lerp(albedo, albedo * wetSandColor, colorFactor);
        
    float smoothnessAndMetallicFactor = smoothstep(_WetSandMetallicAndSmoothnessHeightEndMinMax.x, _WetSandMetallicAndSmoothnessHeightEndMinMax.y, max(positionWS.y, -distanceToShore)) * colorFactor;
    msa = lerp(msa, half3(_WetSandMetallic, _WetSandSmoothness, msa.b), smoothnessAndMetallicFactor);
    normalTS = lerp(normalTS, half3(0, 0, 1), smoothnessAndMetallicFactor);
}
#endif
