#ifndef TERRAIN_COMMON_INCLUDED
#define TERRAIN_COMMON_INCLUDED

#include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

void ReusedSplatMapCalculation(float3 positionWS, out float4 splatMap, out float2 layer0UV, out float2 layer1UV, out float2 layer2UV, out float2 layer3UV, out float2 layer4UV)
{
    float2 uv = (positionWS.xz + _SplatMapWorldOffset) / _SplatMapWorldSize;
    splatMap = SAMPLE_TEXTURE2D(_SplatMap, sampler_SplatMap, uv);

    float2 noiseUV = uv * _LayersNoiseScale;
    float4 layersNoise = SAMPLE_TEXTURE2D(_LayersNoiseMap, sampler_LayersNoiseMap, noiseUV);
    float layer1WithFade = FadeTransition(layersNoise.x, splatMap.x, _Layer1Contrast);
    float layer2WithFade = FadeTransition(layersNoise.y, splatMap.y, _Layer2Contrast);
    float layer3WithFade = FadeTransition(layersNoise.z, splatMap.z, _Layer3Contrast);
    float layer4WithFade = FadeTransition(layersNoise.w, splatMap.w, _Layer4Contrast);
    splatMap = float4(layer1WithFade, layer2WithFade, layer3WithFade, layer4WithFade);

    layer0UV = uv * _Layer0TilingOffset.xy + _Layer0TilingOffset.zw;
    layer1UV = uv * _Layer1TilingOffset.xy + _Layer1TilingOffset.zw;
    layer2UV = uv * _Layer2TilingOffset.xy + _Layer2TilingOffset.zw;
    layer3UV = uv * _Layer3TilingOffset.xy + _Layer3TilingOffset.zw;
    layer4UV = uv * _Layer4TilingOffset.xy + _Layer4TilingOffset.zw;
}

half3 ReusedNormalTSCalculation(float4 splatMap, float2 layer0UV, float2 layer1UV, float2 layer2UV, float2 layer3UV, float2 layer4UV)
{
    half3 layer0NormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_Layer0BumpMap, sampler_Layer0BumpMap, layer0UV));
    half3 layer1NormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_Layer1BumpMap, sampler_Layer0BumpMap, layer1UV));
    half3 layer2NormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_Layer2BumpMap, sampler_Layer0BumpMap, layer2UV));
    half3 layer3NormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_Layer3BumpMap, sampler_Layer0BumpMap, layer3UV));
    half3 layer4NormalTS = UnpackNormal(SAMPLE_TEXTURE2D(_Layer4BumpMap, sampler_Layer0BumpMap, layer4UV));

    half3 normalTS = lerp(layer0NormalTS, layer1NormalTS, splatMap.x);
    normalTS = lerp(normalTS, layer2NormalTS, splatMap.y);
    normalTS = lerp(normalTS, layer3NormalTS, splatMap.z);
    normalTS = lerp(normalTS, layer4NormalTS, splatMap.w);
    return normalTS;
}

void MixTerrainSplats(float3 positionWS, out half3 albedo, out half3 msa, out half3 normalTS)
{
    float4 splatMap;
    float2 layer0UV;
    float2 layer1UV;
    float2 layer2UV;
    float2 layer3UV;
    float2 layer4UV;
    ReusedSplatMapCalculation(positionWS, splatMap, layer0UV, layer1UV, layer2UV, layer3UV, layer4UV);

    half3 layer0Color = SAMPLE_TEXTURE2D(_Layer0BaseMap, sampler_Layer0BaseMap, layer0UV).rgb;
    half3 layer1Color = SAMPLE_TEXTURE2D(_Layer1BaseMap, sampler_Layer1BaseMap, layer1UV).rgb;
    half3 layer2Color = SAMPLE_TEXTURE2D(_Layer2BaseMap, sampler_Layer2BaseMap, layer2UV).rgb;
    half3 layer3Color = SAMPLE_TEXTURE2D(_Layer3BaseMap, sampler_Layer3BaseMap, layer3UV).rgb;
    half3 layer4Color = SAMPLE_TEXTURE2D(_Layer4BaseMap, sampler_Layer3BaseMap, layer4UV).rgb;

    albedo = lerp(layer0Color, layer1Color, splatMap.x);
    albedo = lerp(albedo, layer2Color, splatMap.y);
    albedo = lerp(albedo, layer3Color, splatMap.z);
    albedo = lerp(albedo, layer4Color, splatMap.w);

    half3 layer0MSA = SAMPLE_TEXTURE2D(_Layer0MaskMap, sampler_Layer0BaseMap, layer0UV).rgb;
    half3 layer1MSA = SAMPLE_TEXTURE2D(_Layer1MaskMap, sampler_Layer1BaseMap, layer1UV).rgb;
    half3 layer2MSA = SAMPLE_TEXTURE2D(_Layer2MaskMap, sampler_Layer2BaseMap, layer2UV).rgb;
    half3 layer3MSA = SAMPLE_TEXTURE2D(_Layer3MaskMap, sampler_Layer3BaseMap, layer3UV).rgb;
    half3 layer4MSA = SAMPLE_TEXTURE2D(_Layer4MaskMap, sampler_Layer3BaseMap, layer4UV).rgb;

    msa = lerp(layer0MSA, layer1MSA, splatMap.x);
    msa = lerp(msa, layer2MSA, splatMap.y);
    msa = lerp(msa, layer3MSA, splatMap.z);
    msa = lerp(msa, layer4MSA, splatMap.w);

    normalTS = ReusedNormalTSCalculation(splatMap, layer0UV, layer1UV, layer2UV, layer3UV, layer4UV);
}

half3 MixTerrainSplatsNormalTS(float3 positionWS)
{
    float4 splatMap;
    float2 layer0UV;
    float2 layer1UV;
    float2 layer2UV;
    float2 layer3UV;
    float2 layer4UV;
    ReusedSplatMapCalculation(positionWS, splatMap, layer0UV, layer1UV, layer2UV, layer3UV, layer4UV);
    return ReusedNormalTSCalculation(splatMap, layer0UV, layer1UV, layer2UV, layer3UV, layer4UV);
}
#endif
