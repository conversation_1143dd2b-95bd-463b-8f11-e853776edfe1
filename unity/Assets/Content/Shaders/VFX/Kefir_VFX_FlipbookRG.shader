Shader "Kefir/Kefir_VFX_FlipbookRG"
{
    Properties
    {
        [Header(Base Settings)]
        [NoScaleOffset]_BaseMap("Base Map (Part1 (R), Part2 (G))", 2D) = "white" {}
        _BaseColor("BaseColor", Color) = (1, 1, 1 ,1)
        _InShadowColor("In Shadow Color", Color) = (1, 1, 1 ,1)
        [Header(Flipbook Settings)]
        [RangeInt]_FlipbookRowsCount("Flipbook Rows Count", Float) = 4
        [RangeInt]_FlipbookTilesCount("Flipbook Tiles Count In One Channel", Float) = 16
        [Toggle]_UseSpeedOrLifetime("Use Lifetime (Off), Use Speed (On)", Float) = 0
        _Speed("Speed", Float) = 1
        [Header(Blend Mode)]
        [Enum(UnityEngine.Rendering.BlendMode)]_SourceBlend("Source Blend", Float) = 5
        [Enum(UnityEngine.Rendering.BlendMode)]_DestBlend("Destination Blend", Float) = 10
    }
    SubShader
    {
        Tags { "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent" }

        HLSLINCLUDE
        
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
        
            TEXTURE2D(_BaseMap);
            SAMPLER(sampler_BaseMap);
        
            CBUFFER_START(UnityPerMaterial)
                float _FlipbookRowsCount;
                float _FlipbookTilesCount;
                float _UseSpeedOrLifetime;
                float _Speed;
                half4 _BaseColor;
                half4 _InShadowColor;
            CBUFFER_END  
                  
        ENDHLSL
        
        Pass
        {
            Blend [_SourceBlend] [_DestBlend]
            ZWrite Off
            
            HLSLPROGRAM

            #pragma vertex vert
            #pragma fragment frag

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
                    
            struct Attributes
            {
                float4 positionOS : POSITION;
                float4 customData1 : TEXCOORD0;
                float3 normalOS : NORMAL;
                half4 vertexColor : COLOR0; 
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float3 flipbookUVCurrentChannel : TEXCOORD0;
                float3 positionWS: TEXCOORD1;
                half4 vertexColor : TEXCOORD2;
            };

            Varyings vert(Attributes i)
            {
                Varyings o;
                
                float2 uv = i.customData1.xy;
                float lifetime = i.customData1.z;
                float flipbookProgress = _UseSpeedOrLifetime * _Speed * _Time.y + lifetime * 2 + 1;

                o.flipbookUVCurrentChannel.xy = GetFlipbookUV(uv, _FlipbookRowsCount, _FlipbookTilesCount, flipbookProgress);
                o.flipbookUVCurrentChannel.z = fmod(floor(flipbookProgress), 2) == 0;
                
                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;
                o.positionWS = vertexInputs.positionWS;

                o.vertexColor = half4(i.vertexColor.rgb, i.vertexColor.a * _BaseColor.a);            
                
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                half2 alphaMask = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.flipbookUVCurrentChannel.xy).rg;
                half alpha = lerp(alphaMask.r, alphaMask.g, i.flipbookUVCurrentChannel.z);
                alpha *= i.vertexColor.a;             
                
                half shadowAttenuation = MainLightShadow(TransformWorldToShadowCoord(i.positionWS), i.positionWS, 0, _MainLightOcclusionProbes);
                half3 color = lerp(_InShadowColor.rgb, _BaseColor.rgb, shadowAttenuation);
                color *= i.vertexColor.rgb;
          
                return half4(color, alpha);
            }

            ENDHLSL
        }
    }
}
