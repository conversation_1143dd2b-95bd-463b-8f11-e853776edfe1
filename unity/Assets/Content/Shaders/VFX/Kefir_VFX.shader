Shader "Kefir/Kefir_VFX"
{
    Properties
    {
        [NoScaleOffset]_MainTex("Main Texture", 2D) = "white" {}
        _Color("Color", Color) = (0, 0, 0, 0)
        [Toggle(_USESOFTPARTICLE)]_USESOFTPARTICLE("Use Soft Particle Factor?", Float) = 1
        [Toggle(_USEFRESNELALPHA)]_USEFRESNELALPHA("Use Fresnel Alpha", Float) = 0
        _FresnelAlphaMin("Fresnel Alpha Min", Float) = 0.25
        _FresnelAlphaMax("Fresnel Alpha Max", Float) = 0.65
        _FresnelAlphaPower("Fresnel Alpha", Float) = 0.75
        [Toggle(_USINGSCENESHADOWS)]_USINGSCENESHADOWS("Using Scene Shadows", Float) = 0
        [Toggle(_SHADOWS_PER_VERTEX)]_SHADOWS_PER_VERTEX("Scene Shadows Per Vertex", Float) = 0
        _AlphaPowerInShadows("Alpha Power In Shadows", Float) = 1
        _LightColor("Light Color", Color) = (0.5882353, 0.6352941, 0.6588235, 0)
        _LightIntensity("Light Intensity", Float) = 0.07
        _HeightMin("Height Min", Float) = 0.16
        _HeightMax("Height Max", Float) = 1.35
        
        [Header(FlowMap Distortion)]
        [Toggle(_FLOW_DISTORTION)] _FlowDistiortion("Flow Distortion Enabled", Float) = 0
        [NoScaleOffset]_FlowMap("FlowMap", 2D) = "white" {}
        _FlowSpeed("Flow Speed", float) = 0
        _FlowPower("Flow Power", Range(0,0.5)) = 0
        
        [Header(Blend Mode)]
        [Enum(UnityEngine.Rendering.BlendMode)]_SourceBlend("Source Blend", Float) = 5
        [Enum(UnityEngine.Rendering.BlendMode)]_DestBlend("Destination Blend", Float) = 10
    } 
    
    SubShader
    {
        Tags { "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent" }
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        
        #pragma multi_compile_local_fragment _ _FLOW_DISTORTION

        TEXTURE2D(_MainTex);
        SAMPLER(sampler_MainTex);
        TEXTURE2D(_FlowMap);
        SAMPLER(sampler_FlowMap);
        
        CBUFFER_START(UnityPerMaterial)
            float _SoftParticleFactor;
            float _FresnelAlphaMin;
            float _AlphaPowerInShadows;
            float _FresnelAlphaMax;
            float _FresnelAlphaPower;
            float _LightIntensity;
            float _HeightMin;
            float _HeightMax;
            float _FlowSpeed;
            float _FlowPower;
            float _AmbientProbeBlendFactor;
        
            half3 _Color;
            half3 _LightColor;
        CBUFFER_END
        ENDHLSL
        
        Pass
        {
            Cull Off
            Blend [_SourceBlend] [_DestBlend]
            ZTest LEqual
            ZWrite Off
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            #pragma multi_compile _ _MAIN_LIGHT_SHADOWS _MAIN_LIGHT_SHADOWS_CASCADE _MAIN_LIGHT_SHADOWS_SCREEN
            #pragma multi_compile _ _SHADOWS_SOFT
            #pragma shader_feature_local _ _USINGSCENESHADOWS
            #pragma shader_feature_local_fragment _ _USESOFTPARTICLE
            #pragma shader_feature_local_fragment _ _USEFRESNELALPHA
            #pragma shader_feature_local _ _SHADOWS_PER_VERTEX

            #pragma multi_compile _ _LIGHT_LAYERS

            #define _LIGHT_PROBES_AMBIENT_BLEND 1
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/CameraTextures.hlsl" 
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Shadows.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
                        
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float4 vertexColor : COLOR0;
                float4 uv01 : TEXCOORD0;
                float4 customData1 : TEXCOORD1;
                float4 customData2 : TEXCOORD2;
                float3 center : TEXCOORD3;
            };

            struct Varyings
            {
                float4 positionHCS  : SV_POSITION;
                float4 uv01 : TEXCOORD0;
                float4 customData1 : TEXCOORD1;
                float4 customData2 : TEXCOORD2;
                float3 positionWS: TEXCOORD3;
                float4 center_vertexShadow: TEXCOORD4;
                float3 normalWS : TEXCOORD5;
                float3 sh : TEXCOORD6;
                float4 vertexColor : COLOR0;
            };

            Varyings vert(Attributes i)
            {
                Varyings o;
                
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                o.normalWS = normalInputs.normalWS;
                
                VertexPositionInputs vertexInput = GetVertexPositionInputs(i.positionOS.xyz); 
                o.positionHCS = vertexInput.positionCS;
                o.positionWS = vertexInput.positionWS;
                
                o.vertexColor = i.vertexColor;
                
                o.uv01 = float4(i.uv01);
                o.customData1 = i.customData1;
                o.customData2 = i.customData2;

                _AmbientProbeBlendFactor = 1-o.customData2.w;
                
                #if defined(_SHADOWS_PER_VERTEX) && defined(_USINGSCENESHADOWS)
                    float shadowAttenuation = MainLightShadow(TransformWorldToShadowCoord(i.center), i.center, 0, _MainLightOcclusionProbes);
                #else
                    float shadowAttenuation = 0;
                #endif
                o.center_vertexShadow = float4(i.center, shadowAttenuation);
                
                o.sh = SampleSH(normalInputs.normalWS);
                return o;
            } 
            half4 frag(Varyings i, float facing : VFACE) : SV_Target
            {
                float2 uv = i.uv01.xy;
                float3 center = i.center_vertexShadow.xyz;

                #ifdef _FLOW_DISTORTION
                    float2 flowMap = SAMPLE_TEXTURE2D(_FlowMap, sampler_FlowMap, uv).rg;
                    flowMap = Remap2(flowMap, 0, 1, -1, 1);
                    flowMap *= _FlowPower*i.customData1.b;

                    float flowSpeed = _Time.y * _FlowSpeed;
                    float2 flowProgress1 = flowSpeed;
                    float2 flowProgress2 = flowSpeed + 0.5;
                    flowProgress1 = frac(flowProgress1) * flowMap;
                    flowProgress2 = frac(flowProgress2) * flowMap;
                    float2 flowUV1 = flowProgress1 + uv;
                    float2 flowUV2 = flowProgress2 + uv;
                    float3 mainTexStep1 = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, flowUV1).rga;
                    float3 mainTexStep2 = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, flowUV2).rga;

                    float3 mainTex = lerp(mainTexStep1, mainTexStep2, abs((frac(flowSpeed) - 0.5)/0.5));
                    
                
                #else
                    float distortionMask = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, uv).b;
                    float distortionFactor = i.customData1.b;
                    float2 uvDistorted = uv + float2((distortionMask * (i.uv01 * distortionFactor)).r, 0);
                    
                    float3 mainTex = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, uvDistorted).rga;
                #endif
                
                float baseMap = mainTex.r;
                float dissolveMask = mainTex.g;
                float alphaMask = mainTex.b;
                
                //<Dissolve>
                float dissolve = i.customData1.r;
                float dissolveSharpness = i.customData1.g;
                dissolveSharpness = clamp(dissolveSharpness,0 , 0.5);
                float finalDissolve = alphaMask - smoothstep(dissolveSharpness, 1 - dissolveSharpness, (1 - dissolveMask) + lerp(-1 + dissolveSharpness, 1 - dissolveSharpness, dissolve));
                finalDissolve = saturate(finalDissolve);
                //</Dissolve>

                #ifdef _USEFRESNELALPHA
                //<Fresnel Alpha>
                    float3 fresnelNormal = i.normalWS * (facing * 2.0 - 1.0);
                    float fresnelResult = 1 - FresnelEffect(fresnelNormal, GetWorldSpaceNormalizeViewDir(i.positionWS), _FresnelAlphaPower);
                    fresnelResult = smoothstep(_FresnelAlphaMin, _FresnelAlphaMax, fresnelResult);
                    finalDissolve *= fresnelResult;
                //</Fresnel Alpha>
                #endif

                #ifdef _USESOFTPARTICLE
                //<Depth Texture>
                    float depth = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, sampler_ScreenTexturesDepth_linear_clamp, i.positionHCS.xy / _ScaledScreenParams.xy);
                    float linearEyeDepth = LinearEyeDepth(depth, _ZBufferParams);
                    //</Depth Texture>
                    
                    //<Soft particle>
                    float softParticleValue = i.customData2.r;
                    float softParticleFactor = softParticleValue * (linearEyeDepth - i.positionHCS.w);
                    softParticleFactor = saturate(softParticleFactor);
                    
                    finalDissolve *= softParticleFactor;
                //</Soft particle>
                #endif
                
                //<Color>
                half alpha = i.vertexColor.a * finalDissolve;
                half3 color = saturate(i.vertexColor.rgb * baseMap) * i.customData1.a * _Color;
                //</Color>
                
                //<Fake Light>
                float lightMask = (i.positionWS - center).y;
                float lightIntensity = _LightIntensity - i.customData2.y;
                float lightHeightMin = _HeightMin;
                float lightHeightMax = _HeightMax;
                lightMask = smoothstep(lightHeightMin, lightHeightMax, lightMask);
                lightMask *= lightIntensity;

                color += lightMask * _LightColor;
                //</Fake Light>
                
                #ifdef _USINGSCENESHADOWS
                //<Realtime Shadows>
                    #ifdef _SHADOWS_PER_VERTEX
                        float shadowAttenuation = i.center_vertexShadow.w;
                    #else
                        float shadowAttenuation = MainLightShadow(TransformWorldToShadowCoord(i.positionWS), i.positionWS, 0, _MainLightOcclusionProbes);
                    #endif
                    color = lerp(color, color * i.sh, 1 - shadowAttenuation);
                    alpha = lerp(alpha, alpha * _AlphaPowerInShadows, 1 - shadowAttenuation);
                //</Realtime Shadows>
                #endif
                
                return half4(color, alpha); 
            }
            ENDHLSL
        }
    }
}
