Shader "Kefir/Kefir_Weapon_Aim_Glass"
{
    Properties
    { 
        _Color ("Base Color", Color) = (0,0,0,0)
        _ColorR ("R Channel Color", Color) = (0,0,0,0)
        _ColorG ("G Channel Color", Color) = (0,0,0,0)
        _Metallic ("Metallic", Range(0,1)) = 1
        _Smoothness ("Smoothness", Range(0,1)) = 1
        
        [Space()]
        [Header(Dithering)]
        [Toggle] _IsCharacterDithering("Is Character Dithering", Float) = 0
        _DitherAlpha ("Dither Alpha", Range(0,1)) = 1
        
        [HideInInspector]_AmbientProbeBlendFactor("AmbientProbeBlendFactor", Float) = 0
    }
    
    HLSLINCLUDE
    
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/Lighting/Functions/SHHelpers.hlsl"

        CBUFFER_START(UnityPerMaterial)
            float _AmbientProbeBlendFactor;
            half4 _Color;
            half4 _ColorR;
            half4 _ColorG;
            half _Smoothness;
            half _Metallic;

            half _IsCharacterDithering;
            half _DitherAlpha;

        CBUFFER_END
    ENDHLSL
    
    SubShader
    {
        Tags { "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent" }
        
        Pass
        {
            
            ZWrite On
            Blend SrcAlpha OneMinusSrcAlpha
            
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag

            #define _SURFACE_TYPE_TRANSPARENT 1
            #define _LIGHT_PROBES_AMBIENT_BLEND 1
            #define AMBIENT_OCCLUSION_FOR_DYNAMIC_OBJECT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv0 : TEXCOORD0;
                half4 color : COLOR0;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS : TEXCOORD1;
                half4 color : TEXCOORD4;
                DECLARE_SH(sh, 5)
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.uv.xy = i.uv0;

                o.color = lerp(_Color, _ColorR, i.color.r);
                o.color = lerp(o.color, _ColorG, i.color.g);
                o.color.rgb *= o.color.a;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                if (_IsCharacterDithering)
                {
                    Dither(_DitherAlpha, GetNormalizedScreenSpaceUV(i.positionCS.xy));
                }
                
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                SurfaceData surfaceData = InitializeSurfaceData(i.color, half3(_Metallic, _Smoothness, 1));
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }

            ENDHLSL
        }

        Pass
        {
            Name "Object Motion Vectors"

            Tags
            {
                "LightMode" = "MotionVectors"
            }

            HLSLPROGRAM
            #define _ALLOW_CHARACTER_DITHERING 1
            #define _NO_BASE_MAP 1
            #include_with_pragmas"Assets/Content/ShaderLibrary/Passes/ObjectMotionVectors.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "TransparentDepthPostpass"
            Tags
            {
                "LightMode" = "TransparentDepthPostpass"
            }

            Cull Back
            ZTest LEqual
            ZWrite On
            ColorMask R
            HLSLPROGRAM
            #define _ALLOW_CHARACTER_DITHERING 1
            #define _NO_BASE_MAP 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthOnly.hlsl"
            ENDHLSL
        }
    }
}
