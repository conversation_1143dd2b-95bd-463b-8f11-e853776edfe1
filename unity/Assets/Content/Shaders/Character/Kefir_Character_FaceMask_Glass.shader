Shader "Kefir/Kefir_Character_FaceMask_Glass"
{
    Properties
    {
        _Color("Base Color", Color) = (1, 1, 1, 1)
        [Header(Textures)]
        [NoScaleOffset]_BaseMap("Base Map", 2D) = "white" {}
        [NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        _BumpMapTilingOffset("Normal Tiling(XY) & Offset(ZW)", Vector) = (1, 1, 0, 0)

        [Header(PBR)]
        _Metallic("Metallic", Range(0,1)) = 0
        _Smothness("Smotheness", Range(0,1)) = 0
        _OcclusionMap("Occlusion", Range(0,1)) = 0
        _BumpMapStrength("Normal Strength", Range(0,1)) = 0

        [Header(Dithering)]
        [Toggle] _IsCharacterDithering("Is Character Dithering", Float) = 0
        _DitherAlpha ("Dither Alpha", Range(0,1)) = 1

        [Header(Alpha Clipping)]
        [Toggle(_ALPHATEST_ON)] _AlphaClipping("Alpha Clipping", Float) = 0
        _Cutoff("Alpha Clip Threshold", Range(0,1)) = 0.5

        [HideInInspector]_AmbientProbeBlendFactor("AmbientProbeBlendFactor", Float) = 0
        [HideInInspector] _Custom_SHAr ("_Custom_SHAr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAg ("_Custom_SHAg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAb ("_Custom_SHAb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBr ("_Custom_SHBr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBg ("_Custom_SHBg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBb ("_Custom_SHBb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHC ("_Custom_SHC", Vector) = (0, 0, 0, 0)
        
        [HideInInspector] _HeightLimitPositionWS("Height Limit Position", Vector) = (0, 0, 0, 0)
        [HideInInspector] _HeightLimitNormalWS("Height Limit Normal", Vector) = (0, 0, 0, 0)
        [HideInInspector] _HeightLimitEvaluate("Height Limit Evaluate", Float) = 0
    }

    SubShader
    {
        Tags
        {
            "RenderType" = "Transparent" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent"
        }

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);

        CBUFFER_START(UnityPerMaterial)
            float4 _Custom_SHAr;
            float4 _Custom_SHAg;
            float4 _Custom_SHAb;
            float4 _Custom_SHBr;
            float4 _Custom_SHBg;
            float4 _Custom_SHBb;
            float4 _Custom_SHC;
        
            float4 _BumpMapTilingOffset;

            float3 _HeightLimitPositionWS;
            float3 _HeightLimitNormalWS;

            float _HeightLimitEvaluate;
        
            half4 _Color;
        
            half _Cutoff;
            half _Metallic;
            half _Smothness;
            half _OcclusionMap;
            half _BumpMapStrength;

            half _IsCharacterDithering;
            half _DitherAlpha;

            half _AmbientProbeBlendFactor;
        CBUFFER_END

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            return UnpackNormalScale(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv * _BumpMapTilingOffset.rg + _BumpMapTilingOffset.ba), _BumpMapStrength);
        }
        ENDHLSL

        Pass
        {
            Cull Back
            Blend SrcAlpha OneMinusSrcAlpha
            ZWrite Off
            ZTest LEqual
            HLSLPROGRAM

            #pragma vertex vert
            #pragma fragment frag

            #pragma shader_feature_local_fragment _ALPHATEST_ON
            #pragma multi_compile _ _REFLECTION_PROBES_ROTATED

            #if defined(_REFLECTION_PROBES_ROTATED)
                #pragma require cubearray 
            #endif

            #define _SURFACE_TYPE_TRANSPARENT 1
            #define _LIGHT_PROBES_AMBIENT_AND_CUSTOM_BLEND 1
            #define AMBIENT_OCCLUSION_FOR_DYNAMIC_OBJECT 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv: TEXCOORD0;
                float4x3 tbn_positionWS: TEXCOORD1;
                DECLARE_SH(sh, 4)
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            Varyings vert(Attributes i)
            {
                Varyings o;

                float3 limitedPositionOS = ProjectPointOntoPlane(i.positionOS.xyz, TransformWorldToObject(_HeightLimitPositionWS), TransformWorldToObjectNormal(_HeightLimitNormalWS));
                float3 finalPositionOS = lerp(i.positionOS.xyz, limitedPositionOS, _HeightLimitEvaluate);

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(finalPositionOS);
                o.positionCS = vertexInputs.positionCS;
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.uv = i.uv;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                float2 positionNDC = GetNormalizedScreenSpaceUV(i.positionCS.xy);

                if (_IsCharacterDithering)
                {
                    Dither(_DitherAlpha, positionNDC);
                }

                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv).rgba;

                half3 albedo = baseMap.rgb * _Color.rgb;
                half alpha = baseMap.a * _Color.a;

                #ifdef _ALPHATEST_ON
                    clip(alpha - _Cutoff);
                #endif
                
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, alpha), half3(_Metallic, _Smothness, _OcclusionMap), KefirSampleNormalTS(InitializeSampleNormalData(i.uv)));
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }

        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #pragma target 2.0

            // -------------------------------------
            // Shader Stages
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment

            // This is used during shadow map generation to differentiate between directional and punctual light shadows, as they use different formulas to apply Normal Bias
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW

            #pragma multi_compile_fragment _ LOD_FADE_CROSSFADE
            #pragma shader_feature_local _ALPHATEST_ON

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #if defined(LOD_FADE_CROSSFADE)
                #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #endif
            
            float3 _LightDirection;
            float3 _LightPosition;
            float4 _ShadowBias;
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                #ifdef _ALPHATEST_ON
                        float2 uv : TEXCOORD0;
                #endif
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                #ifdef _ALPHATEST_ON
                        float2 uv           : TEXCOORD0;
                #endif
            };
            
            float3 ApplyShadowBias(float3 positionWS, float3 normalWS, float3 lightDirection)
            {
                float invNdotL = 1.0 - saturate(dot(lightDirection, normalWS));
                float scale = invNdotL * _ShadowBias.y;

                // normal bias is negative since we want to apply an inset normal offset
                positionWS = lightDirection * _ShadowBias.xxx + positionWS;
                positionWS = normalWS * scale.xxx + positionWS;
                return positionWS;
            }
            
            float4 GetShadowPositionHClip(Attributes input)
            {
                float3 limitedPositionOS = ProjectPointOntoPlane(input.positionOS.xyz, TransformWorldToObject(_HeightLimitPositionWS), TransformWorldToObjectNormal(_HeightLimitNormalWS));
                float3 finalPositionOS = lerp(input.positionOS.xyz, limitedPositionOS, _HeightLimitEvaluate);
                float3 positionWS = TransformObjectToWorld(finalPositionOS);
                float3 normalWS = TransformObjectToWorldNormal(input.normalOS);

                #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                        float3 lightDirectionWS = normalize(_LightPosition - positionWS);
                #else
                        float3 lightDirectionWS = _LightDirection;
                #endif

                float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));

                #if UNITY_REVERSED_Z
                        positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #else
                        positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #endif

                return positionCS;
            }

            Varyings ShadowPassVertex(Attributes input)
            {
                Varyings output;
                output.positionCS = GetShadowPositionHClip(input);
                #ifdef _ALPHATEST_ON
                        output.uv = input.uv;
                #endif
                return output;
            }

            half4 ShadowPassFragment(Varyings input) : SV_TARGET
            {
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(input.positionCS);
                #endif
                
                #ifdef _ALPHATEST_ON
                    float alpha = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uv).a;
                    clip(alpha - _Cutoff);
                #endif
                
                return 0;
            }
            
            ENDHLSL
        }

        Pass
        {
            Name "Object Motion Vectors"

            Tags
            {
                "LightMode" = "MotionVectors"
            }

            HLSLPROGRAM
            #define _ALLOW_CHARACTER_DITHERING 1
            #include_with_pragmas"Assets/Content/ShaderLibrary/Passes/ObjectMotionVectors.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "TransparentDepthPostpass"
            Tags
            {
                "LightMode" = "TransparentDepthPostpass"
            }

            Cull Back
            ZTest LEqual
            ZWrite On
            ColorMask R
            HLSLPROGRAM
            #define _ALLOW_CHARACTER_DITHERING 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthOnly.hlsl"
            ENDHLSL
        }
    }
}