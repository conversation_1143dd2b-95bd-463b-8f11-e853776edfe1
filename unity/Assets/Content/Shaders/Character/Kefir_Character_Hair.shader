Shader "Kefir/Kefir_Character_Hair"
{
    Properties
    { 
        [Header(Hair Colors)]
        _HairColorR ("Hair Base Color 1 (R)", Color) = (1,1,1,1)
        _HairColorG ("Hair Base Color 2 (G)", Color) = (1,1,1,1)
        _HairColorB ("Hair Specular Color 1 (R)", Color) = (1,1,1,1)
        _HairColorA ("Hair Specular Color 2 (G)", Color) = (1,1,1,1)
        
        [Header(Textures)]
        [NoScaleOffset]_BaseMap ("Base Map", 2D) = "white" {}
        [NoScaleOffset]_HairMap ("Hair Map", 2D) = "black" {}
        [NoScaleOffset]_MaskMap ("MSA Map", 2D) = "white" {}
        [NoScaleOffset]_BumpMap ("Normal Map", 2D) = "bump" {}
        
        [Header(Anisotropy)]
        _Anisotropic_Value ("Anisotropic Value", Range(-20,1)) = 0
        _Anisotropic_Smoothness ("Anisotropic Smoothness", Range(0,1)) = 0
        
        [Header(Dithering)]
        [Toggle] _IsCharacterDithering("Is Character Dithering", Float) = 0
        _DitherAlpha ("Dither Alpha", Range(0,1)) = 1

        [HideInInspector]_AmbientProbeBlendFactor("AmbientProbeBlendFactor", Float) = 0
        [HideInInspector] _Custom_SHAr ("_Custom_SHAr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAg ("_Custom_SHAg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAb ("_Custom_SHAb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBr ("_Custom_SHBr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBg ("_Custom_SHBg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBb ("_Custom_SHBb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHC ("_Custom_SHC", Vector) = (0, 0, 0, 0)
        
        [HideInInspector] _HeightLimitPositionWS("Height Limit Position", Vector) = (0, 0, 0, 0)
        [HideInInspector] _HeightLimitNormalWS("Height Limit Normal", Vector) = (0, 0, 0, 0)
        [HideInInspector] _HeightLimitEvaluate("Height Limit Evaluate", Float) = 0
    }
    
    SubShader
    {
        Tags { "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Transparent" }

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_MaskMap);
        SAMPLER(sampler_MaskMap);
        TEXTURE2D(_HairMap);
        SAMPLER(sampler_HairMap);
        
        CBUFFER_START(UnityPerMaterial)
            float4 _Custom_SHAr;
            float4 _Custom_SHAg;
            float4 _Custom_SHAb;
            float4 _Custom_SHBr;
            float4 _Custom_SHBg;
            float4 _Custom_SHBb;
            float4 _Custom_SHC;

            float3 _HeightLimitPositionWS;
            float3 _HeightLimitNormalWS;
        
            float _HeightLimitEvaluate;
        
            half4 _HairColorR;
            half4 _HairColorG;
            half4 _HairColorB;
            half4 _HairColorA;

            half _Anisotropic_Value;
            half _Anisotropic_Smoothness;

            half _IsCharacterDithering;
            half _DitherAlpha;

            half _AmbientProbeBlendFactor;
        CBUFFER_END

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            return UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv));
        }
        
        ENDHLSL
        
        Pass
        {
            ZWrite On
            Blend SrcAlpha OneMinusSrcAlpha
            
            Stencil
            {
                Ref 128
                Comp Always
                Pass Replace
                WriteMask 128
            }
            
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            #pragma multi_compile _ _REFLECTION_PROBES_ROTATED

            #if defined(_REFLECTION_PROBES_ROTATED)
                #pragma require cubearray 
            #endif

            #define _LIGHT_PROBES_AMBIENT_AND_CUSTOM_BLEND 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Debugging.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            #include "Assets/Content/ShaderLibrary/BRDF.hlsl"
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS: TEXCOORD1;
                DECLARE_SH(sh, 4)
            };

            half4 Kefir_ColorVertexMask(half4 mask, half4 colorR, half4 colorG)
            {
                half4 cr = colorR * mask.r;
                half4 cg = colorG * mask.g;
                half4 a = cr + cg;
                return saturate(lerp(a, 0, mask.a));
            }
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                float3 limitedPositionOS = ProjectPointOntoPlane(i.positionOS.xyz, TransformWorldToObject(_HeightLimitPositionWS), TransformWorldToObjectNormal(_HeightLimitNormalWS));
                float3 finalPositionOS = lerp(i.positionOS.xyz, limitedPositionOS, _HeightLimitEvaluate);

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(finalPositionOS);
                o.positionCS = vertexInputs.positionCS;
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.uv = i.uv;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                float2 positionNDC = GetNormalizedScreenSpaceUV(i.positionCS.xy);
                
                if (_IsCharacterDithering)
                {
                    Dither(_DitherAlpha, positionNDC);
                }

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(i.uv));
                
                half3 tangentWS = tbn[0];
                half3 bitangentWS = tbn[1];
                half3 normalWS = tbn[2];
                
                UnpackNormalAnisotropic(normalTS, normalWS, tangentWS, bitangentWS);
                
                half3 msa = SAMPLE_TEXTURE2D(_MaskMap, sampler_MaskMap, i.uv).rgb;
                half metallic = msa.r;
                half smoothness = msa.g;
                half ao = CalculateAmbientOcclusionFactor(positionNDC, positionWS, msa.b, _AmbientProbeBlendFactor);

                half4 hairMask = SAMPLE_TEXTURE2D(_HairMap, sampler_HairMap, i.uv);
                
                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv);
                half4 baseColor = Kefir_ColorVertexMask(hairMask, _HairColorR, _HairColorG) * baseMap;

                half4 specularColor = Kefir_ColorVertexMask(hairMask, _HairColorB, _HairColorA);
                
                // Init BRDFData
                BRDFData brdfData = (BRDFData)0;
                InitializeBRDFData(baseColor.rgb, metallic, specularColor.rgb, smoothness, baseColor.a, brdfData);

                #if defined(DEBUG_DISPLAY)
                    half4 debugColor;
                    if (CanDebugOverrideOutputColor(baseColor, metallic, smoothness, specularColor, ao, 0, baseMap.a, normalTS, normalWS, brdfData, debugColor))
                    {
                        return debugColor;
                    }
                #endif
                
                // Direct Specular (BRDF)
                Light mainLight = GetMainLight(TransformWorldToShadowCoord(positionWS), positionWS, 0);
                half3 viewDirectionWS = normalize(_WorldSpaceCameraPos.xyz - positionWS.xyz);
                
                half3 direct = AnisotropicBRDF(baseColor.rgb, specularColor.rgb, _Anisotropic_Smoothness, normalWS, tbn, _Anisotropic_Value, 0, viewDirectionWS,mainLight);
 
                // Direct Specular for Additional Lights
                uint pixelLightCount = GetAdditionalLightsCount();
                uint meshRenderingLayers = GetMeshRenderingLayer();
                LIGHT_LOOP_BEGIN(pixelLightCount)
                        Light additionalLight = GetAdditionalLight(lightIndex, positionWS, 0);
                
                #ifdef _LIGHT_LAYERS
                        if (IsMatchingLightLayer(additionalLight.layerMask, meshRenderingLayers))
                #endif
                        {
                            direct += AnisotropicBRDF(baseColor.rgb, specularColor.rgb, _Anisotropic_Smoothness, normalWS, tbn, _Anisotropic_Value, 0, viewDirectionWS, additionalLight);
                        }
                LIGHT_LOOP_END
                
                // Indirect Specular+Diffuse (GI)
                half3 bakedGI = COMBINE_VERTEX_FRAGMENT_SH(normalWS, i.sh);
                MixRealtimeAndBakedGI(mainLight, normalWS, bakedGI,ao);
                half3 gi = GlobalIllumination(brdfData, bakedGI, ao, positionWS, normalWS, viewDirectionWS);

                // Artistic tweaks
                half3 radiance = brdfData.diffuse * mainLight.shadowAttenuation * mainLight.color;
                
                half3 model = (direct + gi + bakedGI * radiance) * ao;
                
                return half4(model, baseMap.a);
                
            }
            ENDHLSL
        }

        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #pragma target 2.0

            // -------------------------------------
            // Shader Stages
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment

            // This is used during shadow map generation to differentiate between directional and punctual light shadows, as they use different formulas to apply Normal Bias
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW

            #pragma multi_compile_fragment _ LOD_FADE_CROSSFADE
            #pragma shader_feature_local _ALPHATEST_ON

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #if defined(LOD_FADE_CROSSFADE)
                #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #endif
            
            float3 _LightDirection;
            float3 _LightPosition;
            float4 _ShadowBias;
            
            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                #ifdef _ALPHATEST_ON
                        float2 uv : TEXCOORD0;
                #endif
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                #ifdef _ALPHATEST_ON
                        float2 uv           : TEXCOORD0;
                #endif
            };
            
            float3 ApplyShadowBias(float3 positionWS, float3 normalWS, float3 lightDirection)
            {
                float invNdotL = 1.0 - saturate(dot(lightDirection, normalWS));
                float scale = invNdotL * _ShadowBias.y;

                // normal bias is negative since we want to apply an inset normal offset
                positionWS = lightDirection * _ShadowBias.xxx + positionWS;
                positionWS = normalWS * scale.xxx + positionWS;
                return positionWS;
            }
            
            float4 GetShadowPositionHClip(Attributes input)
            {
                float3 limitedPositionOS = ProjectPointOntoPlane(input.positionOS.xyz, TransformWorldToObject(_HeightLimitPositionWS), TransformWorldToObjectNormal(_HeightLimitNormalWS));
                float3 finalPositionOS = lerp(input.positionOS.xyz, limitedPositionOS, _HeightLimitEvaluate);
                float3 positionWS = TransformObjectToWorld(finalPositionOS);
                float3 normalWS = TransformObjectToWorldNormal(input.normalOS);

                #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                        float3 lightDirectionWS = normalize(_LightPosition - positionWS);
                #else
                        float3 lightDirectionWS = _LightDirection;
                #endif

                float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));

                #if UNITY_REVERSED_Z
                        positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #else
                        positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #endif

                return positionCS;
            }

            Varyings ShadowPassVertex(Attributes input)
            {
                Varyings output;
                output.positionCS = GetShadowPositionHClip(input);
                #ifdef _ALPHATEST_ON
                        output.uv = input.uv;
                #endif
                return output;
            }

            half4 ShadowPassFragment(Varyings input) : SV_TARGET
            {
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(input.positionCS);
                #endif
                
                #ifdef _ALPHATEST_ON
                    float alpha = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uv).a;
                    clip(alpha - _Cutoff);
                #endif
                
                return 0;
            }
            
            ENDHLSL
        }
    }
}
