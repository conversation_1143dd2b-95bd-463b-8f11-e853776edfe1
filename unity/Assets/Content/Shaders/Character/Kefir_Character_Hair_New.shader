Shader "Kefir/Kefir_Character_Hair_New"
{
    Properties
    { 
        [Header(Hair Colors)]
        _HairColorR ("Hair Base Color 1 (R)", Color) = (1,1,1,1)
        _HairColorG ("Hair Base Color 2 (G)", Color) = (1,1,1,1)
        _HairColorB ("Hair Specular Color 1 (R)", Color) = (1,1,1,1)
        _HairColorA ("Hair Specular Color 2 (G)", Color) = (1,1,1,1)
        _HairPaintOption("Hair Paint (0 — Hair Map | 1 — Vertex Color G", Range(0, 1)) = 1
        
        [Space()]
        [Header(MatCap)]
        [NoScaleOffset]_MatCapMap ("MatCap Map(RGB)", 2D) = "black" {}
        _MatCapMapStrength ("MatCap Strength", Range(0.5,5)) = 4
        _MatCapMapPower("MatCap Power", Float) = 2
        
        [Header(Textures)]
        [NoScaleOffset]_BaseMap ("Base Map", 2D) = "white" {}
        [NoScaleOffset]_MaskMap ("(RGB) MSA Map (A) Hair Map", 2D) = "white" {}
        [NoScaleOffset]_BumpMap ("Normal Map", 2D) = "bump" {}
        
        [Space()]
        [Header(SSS)]
        _SSSMap("SSS Map", 2D) = "white" {}
        _SSSFactor("SSS Factor", Range(0, 1)) = 0
        _SSSPower("SSS Power", Float) = 1
        _ShadowmapIntensity("Shadowmap Intencity", Range(0.6,1)) = 1
        
        [Header(Anisotropy)]
        _AnisotropicSmoothnessMultiplier("Anisotropic Smoothness Multiplier", Range(0,2)) = 1
        _AnisotropicRotation("Anisotropic Rotaiton", Range(0,1)) = 0.25
        _AnisotropicPrimaryShift("First Highlight Shift", Range(-2,2)) = 0.5
        _AnisotropicPrimaryBrightnessMultiplier("First Highlight Brightness Multiplier", Range(0,5)) = 1
        _AnisotropicPrimarySmoothnessOffset("First Hightlight Smoothness Offset", Range(0,1)) = 0
        _AnisotropicSecondaryShift("Second Highlight Shift", Range(-2,2)) = -0.5
        _AnisotropicSecondaryBrightnessMultiplier("Second Highlight Brightness Multiplier", Range(0,5)) = 1
        _AnisotropicSecondarySmoothnessOffset("Second Hightlight Smoothness Offset", Range(0,1)) = 0
        
        [Header(PBR Constrains)]
        _HairAlbedoMinBrightness("Hair Albedo Min Brightness", Range(0, 1)) = 0.01176
        
        [Header(Alpha Clipping)]
        [Toggle(_ALPHATEST_ON)] _AlphatestOn("Alpha Clipping", Float) = 0
        _Cutoff("Alpha Clip Threshold", Range(0,1)) = 0.5
        
        [Header(Dithering)]
        [Toggle] _IsCharacterDithering("Is Character Dithering", Float) = 0
        _DitherAlpha ("Dither Alpha", Range(0,1)) = 1

        [HideInInspector]_AmbientProbeBlendFactor("AmbientProbeBlendFactor", Float) = 0
        [HideInInspector] _Custom_SHAr ("_Custom_SHAr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAg ("_Custom_SHAg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAb ("_Custom_SHAb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBr ("_Custom_SHBr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBg ("_Custom_SHBg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBb ("_Custom_SHBb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHC ("_Custom_SHC", Vector) = (0, 0, 0, 0)
    }
    
    SubShader
    {
        Tags { "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry-68" }
        
        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_MaskMap);
        SAMPLER(sampler_MaskMap);
        TEXTURE2D(_MatCapMap);
        SAMPLER(sampler_MatCapMap);
        TEXTURE2D(_SSSMap);
        SAMPLER(sampler_SSSMap);

        float3 _CharacterSecondLightDirection;
        float _CharacterSecondLightIntensity;
        float3 _CharacterSecondLightColor;
        
        CBUFFER_START(UnityPerMaterial)
            float4 _Custom_SHAr;
            float4 _Custom_SHAg;
            float4 _Custom_SHAb;
            float4 _Custom_SHBr;
            float4 _Custom_SHBg;
            float4 _Custom_SHBb;
            float4 _Custom_SHC;
        
            half4 _HairColorR;
            half4 _HairColorG;
            half4 _HairColorB;
            half4 _HairColorA;
            half _HairPaintOption;  

            half _MatCapMapStrength;
            half _MatCapMapPower;
        
            half _SSSFactor;
            half _SSSPower;
            half _ShadowmapIntensity;

            half _AnisotropicSmoothnessMultiplier;
            half _AnisotropicRotation;
            half _AnisotropicPrimaryShift;
            half _AnisotropicPrimaryBrightnessMultiplier;
            half _AnisotropicPrimarySmoothnessOffset;
            half _AnisotropicSecondaryShift;
            half _AnisotropicSecondaryBrightnessMultiplier;
            half _AnisotropicSecondarySmoothnessOffset;

            half _HairAlbedoMinBrightness;

            half _IsCharacterDithering;
            half _DitherAlpha;

            half _Cutoff;

            half _AmbientProbeBlendFactor;
        CBUFFER_END

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            float4 normalMap = SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv);
            return UnpackNormal(normalMap);
        }
        ENDHLSL
        
        Pass
        {
            Cull Off
            ZWrite On
            Blend SrcAlpha OneMinusSrcAlpha
            
            Stencil
            {
                Ref 128
                Comp Always
                Pass Replace
                WriteMask 128
            }
            
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            #pragma shader_feature_local_fragment _ALPHATEST_ON

            #define _LIGHT_PROBES_AMBIENT_AND_CUSTOM_BLEND 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/BRDF.hlsl"
            #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
            #include "Assets/Content/ShaderLibrary/Debugging.hlsl"
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                half4 vertexColor : COLOR;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS: TEXCOORD1;
                DECLARE_SH(sh, 4)
                half4 vertexColor : COLOR;
            };

            half4 Kefir_ColorVertexMask(half4 mask, half4 colorR, half4 colorG)
            {
                half4 cr = colorR * mask.r;
                half4 cg = colorG * mask.g;
                half4 a = cr + cg;
                return saturate(lerp(a, 0, mask.a));
            }
            
            Varyings vert(Attributes i)
            {
                Varyings o;
                
                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.uv = i.uv;

                o.vertexColor = i.vertexColor;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }
            
            half4 frag(Varyings i, bool facing : SV_IsFrontFace) : SV_Target
            {
                float2 positionNDC = GetNormalizedScreenSpaceUV(i.positionCS.xy);
                
                if (_IsCharacterDithering)
                {
                    Dither(_DitherAlpha, positionNDC);
                }

                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv);
                #ifdef _ALPHATEST_ON
                    clip(baseMap.a - _Cutoff);
                #endif
                
                baseMap.rgb *= i.vertexColor.r;

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                float isFrontFace = facing * 2 - 1; 
                tbn[2] *= isFrontFace;
                tbn[0] *= isFrontFace;
                
                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(i.uv));
                float3 normalWS = TransformUnpackedToWorldNormal(normalTS, tbn);
                
                half4 msa = SAMPLE_TEXTURE2D(_MaskMap, sampler_MaskMap, i.uv);
                half metallic = msa.r;
                half smoothness = msa.g * _AnisotropicSmoothnessMultiplier;
                half ao = CalculateAmbientOcclusionFactor(positionNDC, positionWS, msa.b, _AmbientProbeBlendFactor);
                half hairMask = lerp(msa.a, i.vertexColor.g, _HairPaintOption);
                
                half sssMap = SAMPLE_TEXTURE2D(_SSSMap, sampler_SSSMap, i.uv).r;
                half sssfactor = lerp(1, _SSSFactor, sssMap);
                
                half4 baseColor = lerp(_HairColorR, _HairColorG, hairMask) * baseMap;
                baseColor = max(_HairAlbedoMinBrightness, baseColor);

                half4 specularColor = lerp(_HairColorB, _HairColorA, hairMask);

                // Init BRDFData
                BRDFData brdfData = (BRDFData)0;
                InitializeBRDFData(baseColor.rgb, metallic, specularColor.rgb, smoothness, baseColor.a, brdfData);

                #if defined(DEBUG_DISPLAY)
                    half4 debugColor;
                    if (CanDebugOverrideOutputColor(baseColor, metallic, smoothness, specularColor, ao, 0, baseMap.a, normalTS, normalWS, brdfData, debugColor))
                    {
                        return debugColor;
                    }
                #endif
                
                // Direct Specular (BRDF)
                Light mainLight = GetMainLight(TransformWorldToShadowCoord(positionWS), positionWS, 0);
                half3 viewDirectionWS = normalize(_WorldSpaceCameraPos.xyz - positionWS.xyz);
                float3 tangentWS = mul(float3(0, -1, 0), tbn);
                
                half3 direct = LightningKajiyaKay(brdfData,
                    mainLight,
                    normalWS,
                    viewDirectionWS,
                    tangentWS,
                    sssfactor,
                    _SSSPower,
                    _AnisotropicPrimaryShift,
                    _AnisotropicPrimaryBrightnessMultiplier,
                    _AnisotropicPrimarySmoothnessOffset,
                    _AnisotropicSecondaryShift,
                    _AnisotropicSecondaryBrightnessMultiplier,
                    _AnisotropicSecondarySmoothnessOffset,
                    specularColor.rgb,
                    _ShadowmapIntensity);
                
                // Direct Specular for Additional Lights
                uint pixelLightCount = GetAdditionalLightsCount();
                uint meshRenderingLayers = GetMeshRenderingLayer();
                LIGHT_LOOP_BEGIN(pixelLightCount)
                        Light additionalLight = GetAdditionalLight(lightIndex, positionWS, 0);
                
                #ifdef _LIGHT_LAYERS
                        if (IsMatchingLightLayer(additionalLight.layerMask, meshRenderingLayers))
                #endif
                        {
                            direct += LightningKajiyaKay(brdfData,
                                additionalLight,
                                normalWS,
                                viewDirectionWS,
                                tangentWS,
                                sssfactor,
                                _SSSPower,
                                _AnisotropicPrimaryShift,
                                _AnisotropicPrimaryBrightnessMultiplier,
                                _AnisotropicPrimarySmoothnessOffset,
                                _AnisotropicSecondaryShift,
                                _AnisotropicSecondaryBrightnessMultiplier,
                                _AnisotropicSecondarySmoothnessOffset,
                                specularColor.rgb,
                                _ShadowmapIntensity);
                        }
                LIGHT_LOOP_END
                
                // Indirect Specular+Diffuse (GI)
                float3 normalVS = TransformWorldToViewNormal(normalWS);
                float2 muv = float2(normalVS.x, normalVS.y) * 0.5f + float2(0.5f, 0.5f);
                half3 matcapSpecular = MatcapGlossyEnvironmentReflection(muv, _MatCapMap, sampler_MatCapMap, brdfData.perceptualRoughness, smoothness, mainLight.shadowAttenuation) * _MatCapMapStrength;
                matcapSpecular = PositivePow(matcapSpecular, _MatCapMapPower);

                half3 bakedGI = COMBINE_VERTEX_FRAGMENT_SH(normalWS, i.sh);
                MixRealtimeAndBakedGI(mainLight, normalWS, bakedGI, ao);
                half3 giColor = GlobalIllumination(brdfData, bakedGI, ao, positionWS, normalWS, viewDirectionWS, matcapSpecular);
                
                half3 model = direct * ao + giColor;
                
                #ifdef _ALPHATEST_ON
                    return half4(model, 1);
                #endif

                return half4(model, baseMap.a);
            }
            ENDHLSL
        }

        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
        
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On
            Cull Off
            HLSLPROGRAM
            #define _ALLOW_CHARACTER_DITHERING 1
            #define _USE_NORMAL_MAP 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }
        
        Pass
        {
            Name "Object Motion Vectors"
            
            Tags{ "LightMode" = "MotionVectors" }

            HLSLPROGRAM
            #define _ALLOW_CHARACTER_DITHERING 1
            #include_with_pragmas"Assets/Content/ShaderLibrary/Passes/ObjectMotionVectors.hlsl"
            ENDHLSL
        }
    }
}
