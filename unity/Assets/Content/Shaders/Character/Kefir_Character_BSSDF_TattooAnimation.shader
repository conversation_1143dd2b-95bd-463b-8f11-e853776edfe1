Shader "Kefir/Kefir_Character_BSSDF_TattooAnimation"
{
       Properties
    {
        [Header(MatCap)]
        [NoScaleOffset]_MatCapMap ("MatCap Map(RGB)", 2D) = "black" {}
        _MatCapMapStrength ("MatCap Strength", Range(0.5,5)) = 4
        _MatCapMapPower("MatCap Power", Float) = 2
        _MatCapMapSkinSmoothnessMultiplier("MatCap Skin Smoothness Multiplier", Float) = 1
        _MatCapMapClothSmoothnessMultiplier("MatCap Cloth Smoothness Multiplier", Float) = 1

        [KeywordEnum(Body, Head)]_TYPE("Type", Float) = 0
        [Header(Colors)]
        _SkinColor ("Skin Color", Color) = (1,1,1,1)
        _MakeupColor0 ("Makeup Color 0 (R)", Color) = (1,1,1,1)
        _MakeupColor1 ("Makeup Color 1 (G)", Color) = (1,1,1,1)
        _MakeupColor2 ("Makeup Color 2 (B)", Color) = (1,1,1,1)
        
        [Header(Textures)]
        [NoScaleOffset]_BaseMap ("Base Map", 2D) = "white" {}
        [NoScaleOffset]_MaskMap ("Mask Map (M, S, AO, Skin)", 2D) = "white" {}
        [NoScaleOffset]_BumpMap ("Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_MakeupMap ("Makeup Map", 2D) = "black" {}
        [NoScaleOffset]_DetailMap ("Detail Map", 2D) = "black" {}
        [NoScaleOffset]_DetailNormalMap ("Detail Normal Map", 2D) = "bump" {}
        
        [Header(Tatoo)]
        [NoScaleOffset]_TattooBaseMap("Tatoo Base Map", 2D) = "black" {}
        _TattooCurrentSegment("Tatoo Current Segment", Float) = 0
        
        [Space()]
        [Header(Tattoo Animation)]
        [NoScaleOffset]_TattooAnimationMask("Tatoo Animation Mask", 2D) = "white" {}
        _TattooAnimation("Tattoo Animation", Range(0, 1)) = 0
        _TattooEdgeWidth("Tattoo Egde Width", Range(0, 1)) = 0.1
        _TattooBaseMapMipMapsBiasOffset("Tattoo BaseMap MipMaps Bias Offset", Range(-2, 0)) = -1
        
        [Header(SSS)]
        [NoScaleOffset]_SSSMap ("SSS Map (need to be sRGB on)", 2D) = "gray" {}
        _SSSFactor("SSS Factor", Range(0.5,1)) = 0.95
        _SSSPower ("SSS Power", Float) = 1
        
        [Header(Dirt)]
        _DirtBaseColor("Dirt Base Color", Color) = (1,1,1,1)
        _DirtAmount("Dirt Amount", Range(0,1)) = 0
        _DirtBaseHeight("Dirt Base Heigth", Float) = 1.8
        [NoScaleOffset]_DirtBaseMap("Dirt Base Map", 2D) = "white" {}
        
        [Header(Water)]
        _WaterDarkening("Water Darkening", Range(0,1)) = 0.2
        _WaterAmount("Water Amount", Range(0,1)) = 0
        _WaterBaseHeight("Water Base Heigth", Float) = 1.8
        [NoScaleOffset]_WaterNoiseMap("Water Noise Map", 2D) = "white" {}
        _WaterNoiseMinLevel("Water Noise Min Level", Range(0,1)) = 0
        _WaterNoiseMidPointLevel("Water Noise Mid Point Level", Range(0,5)) = 0.5
        _WaterNoiseMaxLevel("Water Noise Max Level", Range(0,5)) = 1
        _WaterGradientPower("Water Gradient Power",Range(0,5)) = 1
        _WaterGradientMultiplier("Water Gradient Multiplier",Range(0,5)) = 1
        
        [Header(Dithering)]
        [Toggle] _IsCharacterDithering("Is Character Dithering", Float) = 0
        _DitherAlpha ("Dither Alpha", Range(0,1)) = 1

        [HideInInspector]_AmbientProbeBlendFactor("AmbientProbeBlendFactor", Float) = 0
        [HideInInspector] _Custom_SHAr ("_Custom_SHAr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAg ("_Custom_SHAg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHAb ("_Custom_SHAb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBr ("_Custom_SHBr", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBg ("_Custom_SHBg", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHBb ("_Custom_SHBb", Vector) = (0, 0, 0, 0)
        [HideInInspector] _Custom_SHC ("_Custom_SHC", Vector) = (0, 0, 0, 0)
    }
    
    SubShader
    {
        Tags { "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry-69" }        

        ZWrite On

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_MaskMap);
        TEXTURE2D(_MakeupMap);
        TEXTURE2D(_DetailMap);
        SAMPLER(sampler_DetailMap);
        TEXTURE2D(_DetailNormalMap);
        SAMPLER(sampler_DetailNormalMap);
        TEXTURE2D(_SSSLookUpMap);
        SAMPLER(sampler_SSSLookUpMap);
        TEXTURE2D(_DirtBaseMap);
        TEXTURE2D(_WaterNoiseMap);
        SAMPLER(sampler_WaterNoiseMap);
        TEXTURE2D(_SSSMap);
        SAMPLER(sampler_SSSMap);
        TEXTURE2D(_MatCapMap);
        SAMPLER(sampler_MatCapMap);

        TEXTURE2D(_TattooBaseMap);
        TEXTURE2D(_TattooAnimationMask);
        
        float3 _CharacterSecondLightDirection;
        float _CharacterSecondLightIntensity;
        float3 _CharacterSecondLightColor;
        
        CBUFFER_START(UnityPerMaterial)
        float4 _Custom_SHAr;
            float4 _Custom_SHAg;
            float4 _Custom_SHAb;
            float4 _Custom_SHBr;
            float4 _Custom_SHBg;
            float4 _Custom_SHBb;
            float4 _Custom_SHC;

            float _TattooCurrentSegment;
            float _TattooBaseMapMipMapsBiasOffset;
            float _TattooAnimation;
            float _TattooEdgeWidth;

            half4 _SkinColor;
            half4 _MakeupColor0;
            half4 _MakeupColor1;
            half4 _MakeupColor2;

            half4 _DirtBaseColor;
        
            half _MatCapMapStrength;
            half _MatCapMapPower;
            half _MatCapMapSkinSmoothnessMultiplier;
            half _MatCapMapClothSmoothnessMultiplier;
        
            half _SSSFactor;
            half _SSSPower;
        
            half _DirtAmount;
            half _DirtBaseHeight;

            half _WaterDarkening;
            half _WaterAmount;
            half _WaterBaseHeight;
            half _WaterNoiseMinLevel;
            half _WaterNoiseMidPointLevel;
            half _WaterNoiseMaxLevel;
            half _WaterGradientPower;
            half _WaterGradientMultiplier;

            half _IsCharacterDithering;
            half _DitherAlpha;

            half _AmbientProbeBlendFactor;
        CBUFFER_END
        
        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            half4 detailMap = SAMPLE_TEXTURE2D(_DetailMap, sampler_DetailMap, snData.uv);
            half3 normalTS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv));
            half3 normalMapDetail = UnpackNormal(SAMPLE_TEXTURE2D(_DetailNormalMap, sampler_DetailNormalMap, snData.uv));
            return lerp(normalTS, normalMapDetail, detailMap.a);
        }
        
        ENDHLSL
        
        Pass
        {
            Stencil
            {
                Ref 128
                Comp Always
                Pass Replace
                WriteMask 128
            }
            
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            #pragma multi_compile _ _REFLECTION_PROBES_ROTATED

            #if defined(_REFLECTION_PROBES_ROTATED)
                #pragma require cubearray 
            #endif

            #define _LIGHT_PROBES_AMBIENT_AND_CUSTOM_BLEND 1
            
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Debugging.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            #include "Assets/Content/ShaderLibrary/BRDF.hlsl"

            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv0 : TEXCOORD0;
                float2 uv1 : TEXCOORD1;
                float2 uv2 : TEXCOORD2; 
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float4 uv : TEXCOORD0;
                float2 uv2 : TEXCOORD1;
                float4x3 tbn_positionWS: TEXCOORD2;
                DECLARE_SH(sh, 5)
            };

            half4 Kefir_CharacterDetail(half4 detailMap, half4 skinColor, half4 baseColor)
            {
                half4 color = detailMap * skinColor;
                color = lerp(baseColor, color, detailMap.a);
                return color;
            }

            half4 Kefir_CharacterMakeup(half4 makeupMap, half4 makeupColor0, half4 makeupColor1, half4 makeupColor2, half4 baseColor)
            {
                half4 r = makeupMap.r * makeupColor0;
                half4 g = makeupMap.g * makeupColor1;
                half4 b = makeupMap.b * makeupColor2;
                half4 color = r + g + b;
                color = lerp(baseColor, color, makeupMap.a);
                return color;
            }
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);

                o.uv.xy = i.uv0;
                o.uv.zw = i.uv1;
                o.uv2 = i.uv2;

                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                float2 positionNDC = GetNormalizedScreenSpaceUV(i.positionCS.xy);
                
                if (_IsCharacterDithering)
                {
                    Dither(_DitherAlpha, positionNDC);
                }

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                half4 msa = SAMPLE_TEXTURE2D(_MaskMap, sampler_BaseMap, i.uv.xy);
                half metallic = msa.r;
                half smoothness = msa.g;
                half ao = CalculateAmbientOcclusionFactor(positionNDC, positionWS, msa.b, _AmbientProbeBlendFactor);
                half skin = msa.a;
                half oneMinusSkin = 1 - skin;
                
                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv.xy);
                half4 baseColor = _SkinColor * baseMap;
                baseColor = lerp(baseMap, baseColor, skin);
                
                half sssMap = SAMPLE_TEXTURE2D(_SSSMap, sampler_SSSMap, i.uv.xy).r;
                half sssfactor = lerp(1, _SSSFactor, skin);
                sssfactor = lerp(1, sssfactor, sssMap);
                half ssspower = lerp(1, _SSSPower, skin);
                half4 detailMap = SAMPLE_TEXTURE2D(_DetailMap, sampler_DetailMap, i.uv.xy);
                half4 makeupMap = SAMPLE_TEXTURE2D(_MakeupMap, sampler_BaseMap, i.uv.xy);

                half eyeColorMask = (1 - makeupMap.g) * (1 - makeupMap.b);
                half4 skinColor = lerp(_SkinColor, 1, eyeColorMask);
                half4 makeupColor0 = lerp(_MakeupColor0, detailMap, eyeColorMask * detailMap.a);
                
                baseColor = Kefir_CharacterDetail(detailMap, skinColor, baseColor);
                baseColor = Kefir_CharacterMakeup(makeupMap, makeupColor0, _MakeupColor1, _MakeupColor2, baseColor);

                // Tatoo
                float tattooStep = 0.03125;
                float tattooValue = tattooStep * floor(_TattooCurrentSegment);
                half4 tattooBaseMap = SAMPLE_TEXTURE2D_BIAS(_TattooBaseMap, sampler_BaseMap, i.uv2, _TattooBaseMapMipMapsBiasOffset);
                half4 tattooBaseColor = half4(tattooBaseMap.rgb, 1);
                float tattooValueReference = saturate(tattooBaseMap.a - tattooStep);
                float tattooMask = (tattooValue > tattooValueReference) * skin;

                // Tattoo animation
                float tattooPreviousValue = saturate(tattooStep * floor(_TattooCurrentSegment - 1));
                float tattooPreviousMask = (tattooPreviousValue > tattooValueReference) * skin;
                tattooMask -= tattooPreviousMask;
                
                float tattooAnimationValueRaw = SAMPLE_TEXTURE2D(_TattooAnimationMask, sampler_BaseMap, i.uv2).r;
                float tattooAnimationValue = _TattooAnimation - tattooAnimationValueRaw;
                float tattooAnimationMask = (tattooAnimationValue > 0) * tattooMask;

                float tattooEdgeWidthValue = _TattooAnimation - lerp(_TattooEdgeWidth,0,_TattooAnimation);
                float tattooEdgeAnimationValue = tattooEdgeWidthValue - tattooAnimationValueRaw;
                float tattooEdgeAnimationMask = (tattooEdgeAnimationValue > 0) * tattooMask;

                float edge = tattooAnimationMask - tattooEdgeAnimationMask;

                baseColor = lerp(baseColor, baseColor * tattooBaseColor, tattooPreviousMask);
                baseColor *= 1 - edge;
                baseColor = lerp(baseColor, baseColor * tattooBaseColor, tattooAnimationMask);

                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(i.uv.xy));
                float3 normalWS = TransformUnpackedToWorldNormal(normalTS, tbn);
                
                half3 specularColor = half3(0.5, 0.5, 0.5);

                // Dirt
                half dirtBaseMap = SAMPLE_TEXTURE2D(_DirtBaseMap, sampler_BaseMap, i.uv.xy).r;
                half dirtMask = (1 - i.uv.w) * dirtBaseMap * ao * oneMinusSkin;
                
                dirtMask = saturate(dirtMask);
                dirtMask *= _DirtAmount;

                half4 dirtBaseColor = _DirtBaseColor * dirtBaseMap;
                half dirtSmothness = 0;
                half dirtMetallic = 0;

                baseColor = lerp(baseColor, dirtBaseColor, dirtMask);
                smoothness = lerp(smoothness, dirtSmothness, dirtMask);
                metallic = lerp(metallic, dirtMetallic, dirtMask);

                // Water
                half waterNoise = 1 - SAMPLE_TEXTURE2D(_WaterNoiseMap, sampler_WaterNoiseMap, i.uv.xy).r;
                waterNoise = Levels(waterNoise, _WaterNoiseMinLevel, _WaterNoiseMidPointLevel, _WaterNoiseMaxLevel);
                
                float waterMask = saturate(_WaterGradientMultiplier * (1 - i.uv.w) - (1 - _WaterAmount));
                
                waterMask = PositivePow(waterMask, _WaterGradientPower);
                
                waterMask -= waterNoise;
                waterMask = saturate(waterMask);
                waterMask = sqrt(waterMask); // TODO: избавиться от этого
                
                half waterSmoothnessMask = waterMask * oneMinusSkin;
                
                half4 waterBaseColor = baseColor * _WaterDarkening;
                half waterSmoothness = 1;
                
                baseColor = lerp(baseColor, waterBaseColor, waterSmoothnessMask);
                smoothness = lerp(smoothness, waterSmoothness, waterMask);
                
                // Init BRDFData
                BRDFData brdfData = (BRDFData)0;
                half alpha = 1;
                InitializeBRDFData(baseColor.rgb, metallic, specularColor, smoothness, alpha, brdfData);

                #if defined(DEBUG_DISPLAY)
                    half4 debugColor;
                    if (CanDebugOverrideOutputColor(baseColor, metallic, smoothness, specularColor, ao, 0, alpha, normalTS, normalWS, brdfData, debugColor))
                    {
                        return debugColor;
                    }
                #endif
                
                // Direct Specular + Diffuse (BSSDF)
                Light mainLight = GetMainLight(TransformWorldToShadowCoord(positionWS), positionWS, 0);
                float3 viewDirectionWS = GetWorldSpaceNormalizeViewDir(positionWS);
                
                half3 lightingColor = LightingPhysicallyBasedSkin(brdfData, mainLight, normalWS, viewDirectionWS, sssfactor, ssspower, skin);
                
                // Direct Specular + Diffuse (BSSDF) for Additional Lights
                uint pixelLightCount = GetAdditionalLightsCount();
                uint meshRenderingLayers = GetMeshRenderingLayer();
                LIGHT_LOOP_BEGIN(pixelLightCount)
                        Light additionalLight = GetAdditionalLight(lightIndex, positionWS, 0);
                
                #ifdef _LIGHT_LAYERS
                        if (IsMatchingLightLayer(additionalLight.layerMask, meshRenderingLayers))
                #endif
                        {
                            lightingColor += LightingPhysicallyBasedSkin(brdfData, additionalLight, normalWS, viewDirectionWS, sssfactor, ssspower, skin);
                        }
                LIGHT_LOOP_END
                
                // Indirect Specular + Diffuse (GI)
                half3 bakedGI = COMBINE_VERTEX_FRAGMENT_SH(normalWS, i.sh);
                MixRealtimeAndBakedGI(mainLight, normalWS, bakedGI, ao);
                
                float3 normalVS = TransformWorldToViewNormal(normalWS);
                float2 muv = float2(normalVS.x, normalVS.y) * 0.5f + float2(0.5f, 0.5f);
                half matcapSmoothness = saturate(lerp(smoothness * _MatCapMapClothSmoothnessMultiplier, smoothness * _MatCapMapSkinSmoothnessMultiplier, skin));
                float3 matcapSpecular = MatcapGlossyEnvironmentReflection(muv, _MatCapMap, sampler_MatCapMap, brdfData.perceptualRoughness, matcapSmoothness, mainLight.shadowAttenuation) * _MatCapMapStrength;
                matcapSpecular = PositivePow(matcapSpecular, _MatCapMapPower);

                half3 giColor = GlobalIllumination(brdfData, bakedGI, ao, positionWS, normalWS, viewDirectionWS, matcapSpecular);

                //return half4(tattooAnimationValue, tattooAnimationValue, tattooAnimationValue, 1);
                return half4(lightingColor + giColor, 1);
            }
            ENDHLSL
        }

        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }

            ZWrite On

            HLSLPROGRAM
            #define _ALLOW_CHARACTER_DITHERING 1
            #define _USE_NORMAL_MAP 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "Object Motion Vectors"
            
            Tags{ "LightMode" = "MotionVectors" }

            HLSLPROGRAM
            #define _ALLOW_CHARACTER_DITHERING 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ObjectMotionVectors.hlsl"
            ENDHLSL
        }
    }
}
