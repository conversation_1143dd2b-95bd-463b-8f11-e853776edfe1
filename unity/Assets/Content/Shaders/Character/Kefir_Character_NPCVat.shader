Shader "Kefir/Kefir_Character_NPCVat"
{
    Properties
    {
        _BaseMap ("Texture", 2D) = "white" {}
        [NoScaleOffset]_BumpMap ("Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_MaskMap("MSA Map", 2D) = "black" {}
        _PositionTexture ("Position Texture", 2D) = "black" {}
        _RotationTexture ("Rotation Texture", 2D) = "black" {}
        _TotalFrames ("Total Frames", Float) = 30
        _A_AnimationFrameCount("Animation A Frame Count", Float) = 10
        _A_AnimationOffset("Animation A Offset", Float) = 0
        _A_StartTime("Start Time Animation A", Float) = 0
        
        [Toggle(_BLEND_MODE)] _BlendMode("Blend Mode", Float) = 0
        _BlendSpeed("Blend Speed", Float) = 0
        _B_AnimationFrameCount("Animation B Frame Count", Float) = 10
        _B_AnimationOffset("Animation B Offset", Float) = 0
        _B_StartTime("Start Time Animation B", Float) = 0
        
        _BoneCount("Bone Count", Float) = 0
        [Toggle(_USE_DEBUG_TIME)] _UseDebugTime("Use Debug Time", Float) = 0
        _DebugProgress ("Debug Progress", Range(0, 47)) = 0
        _TimeSpeed("Time Speed", Float) = 7
    }
    SubShader
    {
        Tags
        {
            "RenderPipeline" = "UniversalPipeline" "RenderType" = "Opaque" "Queue" = "Geometry"
        }
 
        HLSLINCLUDE
        
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

        #pragma multi_compile _ _BLEND_MODE
        #pragma shader_feature _ _USE_DEBUG_TIME
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_MaskMap);
        SAMPLER(sampler_MaskMap);
        TEXTURE2D(_RotationTexture);
        SAMPLER(sampler_RotationTexture);
        TEXTURE2D(_PositionTexture);
        SAMPLER(sampler_PositionTexture);
        CBUFFER_START(UnityPerMaterial)
            float _Frame;
            float _TotalFrames;
            float _BoneCount;
            float _DebugProgress;
            float _TimeSpeed;
            float _A_StartTime;
            float _A_AnimationOffset;
            float _A_AnimationFrameCount;
            float _B_AnimationFrameCount;
            float _B_AnimationOffset;
            float _B_StartTime;
            float _BlendSpeed;
        CBUFFER_END

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            return UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv));
        }

        float3 RotateByQuaternion(float3 pos, float4 quat)
        {
            float3 t = 2 * cross(quat.xyz, pos);
            return pos + quat.w * t + cross(quat.xyz, t);
        }

        float4 InverseQuat(float4 q)
        {
            return float4(-q.xyz, q.w);
        }

        float4 MulQuat(float4 q1, float4 q2)
        {
            return float4(
                q1.w*q2.x + q1.x*q2.w + q1.y*q2.z - q1.z*q2.y,
                q1.w*q2.y - q1.x*q2.z + q1.y*q2.w + q1.z*q2.x,
                q1.w*q2.z + q1.x*q2.y - q1.y*q2.x + q1.z*q2.w,
                q1.w*q2.w - q1.x*q2.x - q1.y*q2.y - q1.z*q2.z
            );
        }   

        float GetProgressA()
        {
            float progress = _Time.y;
            #if defined(_USE_DEBUG_TIME)
                progress = _DebugProgress;
            #endif
            progress -= _A_StartTime;
            progress *= _TimeSpeed;
            progress %= (_A_AnimationFrameCount-1);
            
            return progress;
        }

        float GetProgressB()
        {
            float progress = _Time.y;
            #if defined(_USE_DEBUG_TIME)
                progress = _DebugProgress;
            #endif
            progress -= _B_StartTime;
            progress *= _TimeSpeed;
            progress %= (_B_AnimationFrameCount-1);
            
            return progress;
        }

        float GetBlendProgress()
        {
            float progress = _Time.y - _B_StartTime;
            #if defined(_USE_DEBUG_TIME)
                progress = _DebugProgress;
            #endif
            progress = (progress-_B_StartTime) * _BlendSpeed;
            return progress;
        }

        struct PositionNormal
        {
            float3 position;
            float3 normal;
        };

        PositionNormal GetAnimatedPositionNormal(float3 positionOS, float4 vertexColor, float2 uv1, float2 uv2, float3 normalOS, float currentClipFrame, float animationOffset)
        {
            float absoluteFrameIndex = animationOffset + currentClipFrame;

            float frameNorm = (absoluteFrameIndex + 0.5) / _TotalFrames;
            frameNorm = frameNorm - floor(frameNorm);

            float3 finalPosition = 0;
            float3 finalNormal = 0;

            float4 boneIndexes = vertexColor;

            float4 weights = float4(uv1.x, uv1.y, uv2.x, uv2.y);

            for (int i = 0; i < 4; i++)
            {       
                float boneIndex = boneIndexes[i];

                float pixel = 1.0 / _BoneCount;
                float bonePixelIdx = round(boneIndex * (_BoneCount));

                float U = (bonePixelIdx + 0.5) * pixel;  
                float2 texUV = float2(U, frameNorm);
                float2 tPoseUV = float2(U, 0);

                float boneWeight = weights[i];

                float3 tPosePos = SAMPLE_TEXTURE2D_LOD(_PositionTexture,sampler_PositionTexture, tPoseUV, 0).rgb;
                float3 bonePos = SAMPLE_TEXTURE2D_LOD(_PositionTexture,sampler_PositionTexture, texUV, 0).rgb;

                float3 vertOffset = positionOS - tPosePos;

                float4 tPoseRotation = SAMPLE_TEXTURE2D_LOD(_RotationTexture,sampler_PositionTexture, tPoseUV, 0);
                float4 boneRotation = SAMPLE_TEXTURE2D_LOD(_RotationTexture,sampler_PositionTexture, texUV, 0);

                float4 finalRotation = MulQuat(boneRotation, InverseQuat(tPoseRotation));

                float3 rotatedVertex = RotateByQuaternion(vertOffset, finalRotation);
                finalPosition += (rotatedVertex + bonePos + tPosePos) * boneWeight;
                
                float3 rotatedNormal = RotateByQuaternion(normalOS, finalRotation);
                finalNormal += rotatedNormal * boneWeight;
            }
            PositionNormal result;
            result.position = finalPosition;
            result.normal = finalNormal;
            return result;
        }

        PositionNormal GetFinalPositionAndNormalOS(float3 positionOS, float4 vertexColor, float2 uv1, float2 uv2, float3 normalOS)
        {
            PositionNormal animatedPosNormals_A = GetAnimatedPositionNormal(positionOS, vertexColor, uv1, uv2, normalOS, GetProgressA(), _A_AnimationOffset);
            PositionNormal result;
            #if defined(_BLEND_MODE)
                PositionNormal animatedPosNormals_B = GetAnimatedPositionNormal(positionOS, vertexColor, uv1, uv2, normalOS, GetProgressB(), _B_AnimationOffset);

                float blendProgress = saturate(GetBlendProgress());
                result.position = lerp(animatedPosNormals_A.position, animatedPosNormals_B.position, blendProgress);
                result.normal = lerp(animatedPosNormals_A.normal, animatedPosNormals_B.normal, blendProgress);
            #else
                result.position = animatedPosNormals_A.position;
                result.normal = animatedPosNormals_A.normal;
            #endif
            return result;
        }
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            #pragma vertex LitPassVertex
            #pragma fragment LitPassFragment
            

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
                float2 boneUV1 : TEXCOORD1;
                float2 boneUV2 : TEXCOORD2;
                float4 vertexColor : COLOR;
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS: TEXCOORD1;
                DECLARE_SH(sh, 4)
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            Varyings LitPassVertex(Attributes i)
            {
                Varyings o;
                o.uv = i.uv;
                PositionNormal positionAndNormalOS = GetFinalPositionAndNormalOS(i.positionOS.xyz, i.vertexColor, i.boneUV1, i.boneUV2, i.normalOS);

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(positionAndNormalOS.position);
                o.positionCS = vertexInputs.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(positionAndNormalOS.normal, i.tangentOS);

                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                
                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)   
                return o;
            }

            half4 LitPassFragment(Varyings i) : SV_Target
            {
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                half3 albedo = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, i.uv).rgb;
                half3 msa = SAMPLE_TEXTURE2D(_MaskMap, sampler_MaskMap, i.uv);
                
                SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, 1), msa, KefirSampleNormalTS(InitializeSampleNormalData(i.uv)));
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #pragma target 2.0
            
            // -------------------------------------
            // Shader Stages
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment
            
            // This is used during shadow map generation to differentiate between directional and punctual light shadows, as they use different formulas to apply Normal Bias
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW
            
            // Shadow Casting Light geometric parameters. These variables are used when applying the shadow Normal Bias and are set by UnityEngine.Rendering.Universal.ShadowUtils.SetupShadowCasterConstantBuffer in com.unity.render-pipelines.universal/Runtime/ShadowUtils.cs
            // For Directional lights, _LightDirection is used when applying shadow Normal Bias.
            // For Spot lights and Point lights, _LightPosition is used to compute the actual light direction because it is different at each shadow caster geometry vertex.
            float3 _LightDirection;
            float3 _LightPosition;
            float4 _ShadowBias;
            
            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS     : NORMAL;
                float4 vertexColor : COLOR;
                float2 uv0 : TEXCOORD0;
                float2 boneUV1 : TEXCOORD1;
                float2 boneUV2 : TEXCOORD2;
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
            };
               
            float3 ApplyShadowBias(float3 positionWS, float3 normalWS, float3 lightDirection)
            {
                float invNdotL = 1.0 - saturate(dot(lightDirection, normalWS));
                float scale = invNdotL * _ShadowBias.y;

                // normal bias is negative since we want to apply an inset normal offset
                positionWS = lightDirection * _ShadowBias.xxx + positionWS;
                positionWS = normalWS * scale.xxx + positionWS;
                return positionWS;
            }
            

            Varyings ShadowPassVertex(Attributes input)
            {
                Varyings output;

                PositionNormal positionAndNormalOS = GetFinalPositionAndNormalOS(input.positionOS.xyz, input.vertexColor, input.boneUV1, input.boneUV2, input.normalOS);

                float3 positionWS = TransformObjectToWorld(positionAndNormalOS.position);
                float3 normalWS = TransformObjectToWorldNormal(positionAndNormalOS.normal);

                #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                    float3 lightDirectionWS = normalize(_LightPosition - positionWS);
                #else
                    float3 lightDirectionWS = _LightDirection;
                #endif

                    float4 positionCS = TransformWorldToHClip(ApplyShadowBias(positionWS, normalWS, lightDirectionWS));

                #if UNITY_REVERSED_Z
                    positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #else
                    positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #endif

                output.positionCS = positionCS;
                return output;
            }

            half4 ShadowPassFragment(Varyings input) : SV_TARGET
            {
                return 0;
            }
            ENDHLSL
        }
        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }
            
            ZWrite On

            HLSLPROGRAM
            #pragma target 2.0
            
            #pragma vertex DepthNormalsVertex
            #pragma fragment DepthNormalsFragment
            
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            
            struct Attributes
            {
                float4 positionOS     : POSITION;
                float3 normalOS       : NORMAL;
                float4 tangentOS : TANGENT;
                half4 vertexColor : COLOR;
                float2 uv0 : TEXCOORD0;
                float2 boneUV1 : TEXCOORD1;
                float2 boneUV2 : TEXCOORD2;
            };

            struct Varyings
            {
                float4 positionCS   : SV_POSITION;
                float2 uv0 : TEXCOORD0;
                half4 vertexColor : TEXCOORD2;
                float3x3 tbn : TEXCOORD3;
            };
                   
            Varyings DepthNormalsVertex(Attributes input)
            {
                Varyings output = (Varyings)0;

                PositionNormal positionAndNormalOS = GetFinalPositionAndNormalOS(input.positionOS.xyz, input.vertexColor, input.boneUV1, input.boneUV2, input.normalOS);

                VertexPositionInputs vertexInput = GetVertexPositionInputs(positionAndNormalOS.position);
                output.positionCS = vertexInput.positionCS;
                VertexNormalInputs normalInput = GetVertexNormalInputs(positionAndNormalOS.normal, input.tangentOS);
                output.uv0 = input.uv0;
                output.vertexColor = input.vertexColor;
                output.tbn = float3x3(
                    normalInput.tangentWS,
                    normalInput.bitangentWS,
                    normalInput.normalWS);
                return output;
            }

            void DepthNormalsFragment(
                Varyings input
                , out half4 outNormalWS : SV_Target0
            #ifdef _WRITE_RENDERING_LAYERS
                , out float4 outRenderingLayers : SV_Target1
            #endif
            )
            {
                float baseColorAlpha = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uv0).a;
                float epsilon = 1e-5;
                bool baseColorClipEqual = abs(baseColorAlpha - 0.2) > epsilon;
                clip(baseColorClipEqual - 0.5);

                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(input.uv0));
                outNormalWS = half4(TransformUnpackedToWorldNormal(normalTS, input.tbn), 0.0);

                #ifdef _WRITE_RENDERING_LAYERS
                    uint renderingLayers = GetMeshRenderingLayer();
                    outRenderingLayers = float4(EncodeMeshRenderingLayer(renderingLayers), 0, 0, 0);
                #endif
            }
            ENDHLSL
        }

        Pass
        {
            Name "Object Motion Vectors"
            Tags
            {
                "LightMode" = "MotionVectors"
            }

            HLSLPROGRAM
            #ifndef OBJECT_MOTION_VECTORS_WITH_DITHERING_INCLUDED
            #define OBJECT_MOTION_VECTORS_WITH_DITHERING_INCLUDED

            #pragma multi_compile_fragment _ _FOVEATED_RENDERING_NON_UNIFORM_RASTER
            #pragma never_use_dxc metal

            #pragma exclude_renderers d3d11_9x
            #pragma target 3.5

            #pragma vertex vert
            #pragma fragment frag

            // -------------------------------------
            // Includes
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/UnityInput.hlsl"
            #include "Assets/Content/ShaderLibrary/Dithering.hlsl"
            #ifndef HAVE_VFX_MODIFICATION
            #pragma multi_compile _ DOTS_INSTANCING_ON
            #if UNITY_PLATFORM_ANDROID || UNITY_PLATFORM_WEBGL || UNITY_PLATFORM_UWP
                    #pragma target 3.5 DOTS_INSTANCING_ON
            #else
            #pragma target 4.5 DOTS_INSTANCING_ON
            #endif
            #endif // HAVE_VFX_MODIFICATION
            #if defined(_FOVEATED_RENDERING_NON_UNIFORM_RASTER)
                            #include "Packages/com.unity.render-pipelines.core/ShaderLibrary/FoveatedRendering.hlsl"
            #endif

            // -------------------------------------
            // Structs
            struct Attributes
            {
                float4 position : POSITION;
                float4 vertexColor : COLOR;
                float3 positionOld : TEXCOORD4;
                float2 boneUV1 : TEXCOORD1;
                float2 boneUV2 : TEXCOORD2;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float4 positionCSNoJitter : TEXCOORD0;
                float4 previousPositionCSNoJitter : TEXCOORD1;
                UNITY_VERTEX_INPUT_INSTANCE_ID
                UNITY_VERTEX_OUTPUT_STEREO
            };

            // -------------------------------------
            // Vertex
            Varyings vert(Attributes input)
            {
                Varyings output = (Varyings)0;

                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_TRANSFER_INSTANCE_ID(input, output);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

                PositionNormal positionAndNormalOS = GetFinalPositionAndNormalOS(input.position.xyz, input.vertexColor, input.boneUV1, input.boneUV2, float3(0, 0, 0));

                VertexPositionInputs vertexInput = GetVertexPositionInputs(positionAndNormalOS.position);
                output.positionCS = vertexInput.positionCS;
                // Jittered. Match the frame.

                // This is required to avoid artifacts ("gaps" in the _MotionVectorTexture) on some platforms
                #if defined(UNITY_REVERSED_Z)
                output.positionCS.z -= unity_MotionVectorsParams.z * output.positionCS.w;
                #else
                                output.positionCS.z += unity_MotionVectorsParams.z * output.positionCS.w;
                #endif

                output.positionCSNoJitter = mul(_NonJitteredViewProjMatrix, mul(UNITY_MATRIX_M, input.position));

                const float4 prevPos = (unity_MotionVectorsParams.x == 1) ? float4(input.positionOld, 1) : input.position;
                output.previousPositionCSNoJitter = mul(_PrevViewProjMatrix, mul(UNITY_PREV_MATRIX_M, prevPos));

                return output;
            }

            #if defined(_FOVEATED_RENDERING_NON_UNIFORM_RASTER)
                            // Non-uniform raster needs to keep the posNDC values in float to avoid additional conversions
                            // since uv remap functions use floats
                            #define POS_NDC_TYPE float2 
            #else
            #define POS_NDC_TYPE half2
            #endif

            // -------------------------------------
            // Fragment
            half4 frag(Varyings input) : SV_Target
            {
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_SETUP_STEREO_EYE_INDEX_POST_VERTEX(input);

                // Note: unity_MotionVectorsParams.y is 0 is forceNoMotion is enabled
                bool forceNoMotion = unity_MotionVectorsParams.y == 0.0;
                if (forceNoMotion)
                {
                    return half4(0.0, 0.0, 0.0, 0.0);
                }

                // Calculate positions
                float4 posCS = input.positionCSNoJitter;
                float4 prevPosCS = input.previousPositionCSNoJitter;

                POS_NDC_TYPE posNDC = posCS.xy * rcp(posCS.w);
                POS_NDC_TYPE prevPosNDC = prevPosCS.xy * rcp(prevPosCS.w);

                #if defined(_FOVEATED_RENDERING_NON_UNIFORM_RASTER)
                                // Convert velocity from NDC space (-1..1) to screen UV 0..1 space since FoveatedRendering remap needs that range.
                                half2 posUV = RemapFoveatedRenderingResolve(posNDC * 0.5 + 0.5);
                                half2 prevPosUV = RemapFoveatedRenderingPrevFrameResolve(prevPosNDC * 0.5 + 0.5);
                                
                                // Calculate forward velocity
                                half2 velocity = (posUV - prevPosUV);
                #if UNITY_UV_STARTS_AT_TOP
                                    velocity.y = -velocity.y;
                #endif
                #else
                // Calculate forward velocity
                half2 velocity = (posNDC.xy - prevPosNDC.xy);
                #if UNITY_UV_STARTS_AT_TOP
                velocity.y = -velocity.y;
                #endif

                // Convert velocity from NDC space (-1..1) to UV 0..1 space
                // Note: It doesn't mean we don't have negative values, we store negative or positive offset in UV space.
                // Note: ((posNDC * 0.5 + 0.5) - (prevPosNDC * 0.5 + 0.5)) = (velocity * 0.5)
                velocity.xy *= 0.5;
                #endif
                return half4(velocity, 0, 0);
            }
            #endif

            ENDHLSL
        }
    }
}
