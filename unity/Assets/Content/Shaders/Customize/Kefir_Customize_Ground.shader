Shader "Kefir/Kefir_Customize_Ground"
{
       Properties
    { 
        [Header(Base)]
        [NoScaleOffset]_BaseMap("Base Color Map",2D) = "white" {}
        [NoScaleOffset]_BackgroundMap("Background Map", 2D) = "black" {}
        [NoScaleOffset]_AmbientOcclusionMap("Ambient Occlusion Map", 2D) = "white" {}
        [NoScaleOffset]_BumpMap("Normal Map",2D) = "bump" {}
        [NoScaleOffset]_RoughnessMap("Roughness Map",2D) = "white" {}
        _UVScaleOffset("UV Scale Offset", Vector) = (1,1,0,0)
        _Metallic("Metallic",Range(0,1)) = 0
        _SpecularPower("Specular Power", Float) = 20
        _Specularity("Specularity", Float) = 1
        
        [Space()]
        [Header(Transparency)]
        _TransparencyHorizonLevel("Trasparency Horizon Level", Range(0,1)) = 0.5
        _TransparencyPower("Transparency Power", Float) = 1
        
        [Space()]
        [Header(Reflection)]
        [NoScaleOffset]_ReflectTex("Reflection Texture", 2D) = "black" {}
        _ReflectionHorizonLevel("Reflection Horizon Level", Range(0,1)) = 0.5
        _HorizonShift("Reflection Horizon Shift",Range(-1,1)) = 0
        _ReflectBrightness("Reflect Brightness", Range(0,5)) = 1 
    }
    
    SubShader
    {
        Tags { "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry" }

        Blend SrcAlpha OneMinusSrcAlpha
        ZWrite On

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);
        TEXTURE2D(_BackgroundMap);
        SAMPLER(sampler_BackgroundMap);
        TEXTURE2D(_AmbientOcclusionMap);
        SAMPLER(sampler_AmbientOcclusionMap);
        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);
        TEXTURE2D(_RoughnessMap);
        SAMPLER(sampler_RoughnessMap);
        TEXTURE2D(_ReflectTex);
        SAMPLER(sampler_ReflectTex);
        
        CBUFFER_START(UnityPerMaterial)
            float4 _UVScaleOffset;
            half _Metallic;
            half _SpecularPower;
            half _Specularity;

            half _TransparencyHorizonLevel;
            half _TransparencyPower;
        
            half _ReflectionHorizonLevel;
            half _HorizonShift;
            half _ReflectBrightness;
        CBUFFER_END

        half3 KefirSampleNormalTS(SampleNormalData snData)
        {
            return UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, snData.uv));
        }
        
        ENDHLSL
        
        Pass
        {
            HLSLPROGRAM
            
            #pragma vertex vert
            #pragma fragment frag
            
            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            struct Attributes
            {
                float4 positionOS   : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
            };

            struct Varyings
            {
                float4 positionCS  : SV_POSITION;
                float2 uv : TEXCOORD0;
                float4x3 tbn_positionWS: TEXCOORD1;
            };
            
            Varyings vert(Attributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInputs = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInputs.positionCS;
                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);
                
                PackTBNAndPositionWS(vertexInputs, normalInputs, o.tbn_positionWS);
                
                o.uv = i.uv;
                return o;
            }
            
            half4 frag(Varyings i) : SV_Target
            {
                float alpha = max(1 - i.uv.y, 0.001);
                alpha *= 1 / max(0.001, 1 - _TransparencyHorizonLevel);
                alpha = saturate(pow(alpha, _TransparencyPower));

                float2 uv = i.uv * _UVScaleOffset.xy + _UVScaleOffset.zw;
                half3 albedo = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uv).rgb;
                
                half metallic = _Metallic;
                half smoothness = 1 - SAMPLE_TEXTURE2D(_RoughnessMap, sampler_RoughnessMap, uv).r;
                half ao = SAMPLE_TEXTURE2D(_AmbientOcclusionMap, sampler_AmbientOcclusionMap, uv).r;

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);
                
                half3 normalTS = KefirSampleNormalTS(InitializeSampleNormalData(i.uv));
                float3 normalWS = TransformUnpackedToWorldNormal(normalTS, tbn);
                
                // Init BRDFData
                BRDFData brdfData = (BRDFData)0;
                InitializeBRDFData(albedo, metallic, 0, smoothness, alpha, brdfData);
                
                // Direct Specular + Diffuse
                half3 viewDirectionWS = normalize(_WorldSpaceCameraPos.xyz - positionWS);
                Light mainLight = GetMainLight(TransformWorldToShadowCoord(positionWS), positionWS, 0);
                
                half3 direct = LightingPhysicallyBased(brdfData, mainLight, normalWS, viewDirectionWS);
                
                uint pixelLightCount = GetAdditionalLightsCount();
                uint meshRenderingLayers = GetMeshRenderingLayer();
                LIGHT_LOOP_BEGIN(pixelLightCount)
                        Light additionalLight = GetAdditionalLight(lightIndex, positionWS, 0);
                
                #ifdef _LIGHT_LAYERS
                        if (IsMatchingLightLayer(additionalLight.layerMask, meshRenderingLayers))
                #endif
                        {
                            half NdotL = saturate(dot(normalWS, additionalLight.direction));
                            half3 radiance = additionalLight.shadowAttenuation * additionalLight.distanceAttenuation * NdotL;
                            direct += radiance;
                        }
                LIGHT_LOOP_END
                
                
                half3 reflectVector = reflect(-viewDirectionWS, normalWS);
                half NoV = saturate(dot(normalWS, viewDirectionWS));
                half fresnelTerm = Pow4(1.0 - NoV);
                
                half3 indirectDiffuse = 0;
                
                half3 indirectSpecular;
                #if !defined(_ENVIRONMENTREFLECTIONS_OFF)
                    half3 irradiance;
                
                    half mip = brdfData.perceptualRoughness * (1.7 - 0.7 * brdfData.perceptualRoughness) * 6;
                    half4 encodedIrradiance = half4(SAMPLE_TEXTURECUBE_LOD(unity_SpecCube0, samplerunity_SpecCube0, reflectVector, mip));

                    float2 positionNDC = GetNormalizedScreenSpaceUV(i.positionCS.xy);
                    float2 reflectUV = positionNDC;
                    reflectUV.y = _ReflectionHorizonLevel - reflectUV.y + _ReflectionHorizonLevel;
                    reflectUV.x += _HorizonShift;
                
                    half4 reflectTex = SAMPLE_TEXTURE2D(_ReflectTex, sampler_ReflectTex, reflectUV);
                    reflectTex = saturate(reflectTex);
                    encodedIrradiance = lerp(encodedIrradiance, encodedIrradiance + reflectTex, _ReflectBrightness);
            
                    half irradianceAlpha = max(unity_SpecCube0_HDR.w * (encodedIrradiance.a - 1.0) + 1.0, 0.0);
                    irradiance = unity_SpecCube0_HDR.x * PositivePow(irradianceAlpha, unity_SpecCube0_HDR.y) * encodedIrradiance.rgb;
                

                    indirectSpecular = irradiance * ao;
                #else
                    indirectSpecular = _GlossyEnvironmentColor.rgb * occlusion;
                #endif 
                
                half3 color = indirectDiffuse * brdfData.diffuse;
                half surfaceReduction = 1.0 / (brdfData.roughness2 + 1.0);
                color += indirectSpecular * half3(surfaceReduction * lerp(brdfData.specular, brdfData.grazingTerm, fresnelTerm));
                
                half3 gi = color * ao;
                
                // Combining
                half3 model = direct + gi;
                half3 background = SAMPLE_TEXTURE2D(_BackgroundMap, sampler_BackgroundMap, positionNDC).rgb;
                model = lerp(background, model, alpha);
                
                return half4(model, alpha);
            }
            ENDHLSL
        }
        
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
        
        Pass
        {
            Name "DepthOnly"
            Tags
            {
                "LightMode" = "DepthOnly"
            }

            Cull Back
            ZTest LEqual
            ZWrite On
            ColorMask R

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthOnly.hlsl"
            ENDHLSL
        }

        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }

            Cull Back
            ZTest LEqual
            ZWrite On

            HLSLPROGRAM
            #define _USE_NORMAL_MAP 1
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/DepthNormals.hlsl"
            ENDHLSL
        }
    }
}
