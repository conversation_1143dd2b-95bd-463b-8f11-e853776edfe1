#ifndef KEFIR_DECOR_COVERAGE_LAYERS_INCLUDED
#define KEFIR_DECOR_COVERAGE_LAYERS_INCLUDED

void LayerBlending_float(float3 originalColor, float3 originalNormal, float2 originalMetalSmoothness, float4 coverageTextureValue,
    float4 coverageTextureNoiseValue, float4 coverageTextureNoisePower, float2 uv, UnitySamplerState reusedSampler, UnitySamplerState reusedSamplerAniso,
    float layerRScale, float4 layerRColor, UnityTexture2D layerRColorTexture, UnityTexture2D layerRNormalTexture, UnityTexture2D layerRMetalSmoothnessTexture,
    float layerGScale, float4 layerGColor, UnityTexture2D layerGColorTexture, UnityTexture2D layerGNormalTexture, UnityTexture2D layerGMetalSmoothnessTexture,
    float layerBScale, float4 layerBColor, UnityTexture2D layerBColorTexture, UnityTexture2D layerBNormalTexture, UnityTexture2D layerBMetalSmoothnessTexture,
    float layerAScale, float4 layerAColor, UnityTexture2D layerAColorTexture, UnityTexture2D layerANormalTexture, UnityTexture2D layerAMetalSmoothnessTexture,
    out float3 resultColor, out float3 resultNormal, out float2 resultMetalSmoothness)
{
    resultColor = originalColor;
    resultNormal = originalNormal;
    resultMetalSmoothness = originalMetalSmoothness;
    
    #if defined(_COVERAGEBRUSHLAYERS_NONE)
        return;
    #endif

    // Using inline sampler because we sample more than 16 textures and can have separate sampler for each. But inline sampler can't
    // know about aniso setting, so we select correct inline sampler based on keyword.
    #if defined(_ANISOTROPIC_FILTERING_FORCED_ON)
        UnitySamplerState activeSampler = reusedSamplerAniso;
    #else
        UnitySamplerState activeSampler = reusedSampler;
    #endif
    
    coverageTextureValue += lerp(float4(1, 1, 1, 1), coverageTextureNoiseValue, coverageTextureNoisePower) * coverageTextureValue;
    coverageTextureValue = saturate(coverageTextureValue);

    // Use transparency of base texture to decrease weight, so we can correctly blend between combined layer textures and base texture.
    float4 coverageTextureValueWithNoiseAndTransparency = coverageTextureValue;
    
    // Blending of layers similar to TerrainSplatmapCommon.cginc from builtin shaders.
    half weight = dot(coverageTextureValue, float4(1, 1, 1, 1));
    coverageTextureValue /= (weight + 1e-3f);
    
    float2 layerUV = uv * layerRScale;
    float4 layerColorFromTexture = SAMPLE_TEXTURE2D(layerRColorTexture.tex, activeSampler.samplerstate, layerUV);
    float layerFactor = coverageTextureValue.r * layerColorFromTexture.a;
    coverageTextureValueWithNoiseAndTransparency.r *= layerColorFromTexture.a;
    float3 combinedLayersColor = layerColorFromTexture.rgb * layerRColor.rgb * layerFactor;
    float3 combinedLayersNormal = UnpackNormal(SAMPLE_TEXTURE2D(layerRNormalTexture.tex, activeSampler.samplerstate, layerUV)) * layerFactor;
    float2 combinedLayersMetalSmoothness = SAMPLE_TEXTURE2D(layerRMetalSmoothnessTexture.tex, activeSampler.samplerstate, layerUV).rg * layerFactor;
    
    #if defined(_COVERAGEBRUSHLAYERS_TWO) || defined(_COVERAGEBRUSHLAYERS_THREE) || defined(_COVERAGEBRUSHLAYERS_FOUR)
        layerUV = uv * layerGScale;
        layerColorFromTexture = SAMPLE_TEXTURE2D(layerGColorTexture.tex, activeSampler.samplerstate, layerUV);
        layerFactor = coverageTextureValue.g * layerColorFromTexture.a;
        coverageTextureValueWithNoiseAndTransparency.g *= layerColorFromTexture.a;
        combinedLayersColor += layerColorFromTexture.rgb * layerGColor.rgb * layerFactor;
        combinedLayersNormal += UnpackNormal(SAMPLE_TEXTURE2D(layerGNormalTexture.tex, activeSampler.samplerstate, layerUV)) * layerFactor;
        combinedLayersMetalSmoothness += SAMPLE_TEXTURE2D(layerGMetalSmoothnessTexture.tex, activeSampler.samplerstate, layerUV).rg * layerFactor;
    #endif
    
    #if defined(_COVERAGEBRUSHLAYERS_THREE) || defined(_COVERAGEBRUSHLAYERS_FOUR)
        layerUV = uv * layerBScale;
        layerColorFromTexture = SAMPLE_TEXTURE2D(layerBColorTexture.tex, activeSampler.samplerstate, layerUV);
        layerFactor = coverageTextureValue.b * layerColorFromTexture.a;
        coverageTextureValueWithNoiseAndTransparency.b *= layerColorFromTexture.a;
        combinedLayersColor += layerColorFromTexture.rgb * layerBColor.rgb * layerFactor;
        combinedLayersNormal += UnpackNormal(SAMPLE_TEXTURE2D(layerBNormalTexture.tex, activeSampler.samplerstate, layerUV)) * layerFactor;
        combinedLayersMetalSmoothness += SAMPLE_TEXTURE2D(layerBMetalSmoothnessTexture.tex, activeSampler.samplerstate, layerUV).rg * layerFactor;
    #endif
    
    #if defined(_COVERAGEBRUSHLAYERS_FOUR)
        layerUV = uv * layerAScale;
        layerColorFromTexture = SAMPLE_TEXTURE2D(layerAColorTexture.tex, activeSampler.samplerstate, layerUV);
        layerFactor = coverageTextureValue.a * layerColorFromTexture.a;
        coverageTextureValueWithNoiseAndTransparency.a *= layerColorFromTexture.a;
        combinedLayersColor += layerColorFromTexture.rgb * layerAColor.rgb * layerFactor;
        combinedLayersNormal += UnpackNormal(SAMPLE_TEXTURE2D(layerANormalTexture.tex, activeSampler.samplerstate, layerUV)) * layerFactor;
        combinedLayersMetalSmoothness += SAMPLE_TEXTURE2D(layerAMetalSmoothnessTexture.tex, activeSampler.samplerstate, layerUV).rg * layerFactor;
    #endif

    // To avoid nan after normalizing.
    combinedLayersNormal.z += 1e-5f;
    
    float blend = saturate(dot(coverageTextureValueWithNoiseAndTransparency, float4(1, 1, 1, 1)));
    resultColor = lerp(resultColor, combinedLayersColor, blend);
    resultNormal = lerp(resultNormal, normalize(combinedLayersNormal), blend);
    resultMetalSmoothness = lerp(resultMetalSmoothness, combinedLayersMetalSmoothness, blend);
}
#endif
