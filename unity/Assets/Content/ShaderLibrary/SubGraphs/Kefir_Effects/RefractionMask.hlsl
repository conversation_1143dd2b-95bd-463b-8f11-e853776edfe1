#ifndef REFRACTIONMASK_INCLUDED
#define REFRACTIONMASK_INCLUDED

#if !defined(SHADERGRAPH_PREVIEW)
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Input.hlsl"
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareDepthTexture.hlsl"
#endif

SamplerState sampler_ScreenTextures_linear_clamp;
float4 _CameraDepthTexture_TexelSize;

float GetAccurateDepth(half2 screenSpaceUV)
{
    float rawD = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, sampler_ScreenTextures_linear_clamp, screenSpaceUV);
    #if !SHADER_API_MOBILE
    float2 texelSize = _CameraDepthTexture_TexelSize.xy;
    float rawD1 = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, sampler_ScreenTextures_linear_clamp, screenSpaceUV + float2(1.0, 0.0) * texelSize);
    float rawD2 = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, sampler_ScreenTextures_linear_clamp, screenSpaceUV + float2(-1.0, 0.0) * texelSize);
    float rawD3 = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, sampler_ScreenTextures_linear_clamp, screenSpaceUV + float2(0.0, 1.0) * texelSize);
    float rawD4 = SAMPLE_DEPTH_TEXTURE(_CameraDepthTexture, sampler_ScreenTextures_linear_clamp, screenSpaceUV + float2(0.0, -1.0) * texelSize);

    // Search for farthest depth, this prevents MSAA artifacts similar to described in this thread:
    // https://forum.unity.com/threads/fixing-screen-space-directional-shadows-and-anti-aliasing.379902/
    rawD = max(rawD, max(max(rawD1, rawD2), max(rawD3, rawD4)));
    #endif
    return rawD;
}

void RefractionMask_float (float3 worldPosPixel, float4 screenPosRefracted, out float mask)
{
    #if !defined(SHADERGRAPH_PREVIEW)
        float screenPosRefractedDepth01 = GetAccurateDepth(screenPosRefracted.xy);
        float3 screenPosRefractedWorldPos = ComputeWorldSpacePosition(screenPosRefracted.xy, screenPosRefractedDepth01, UNITY_MATRIX_I_VP);
        mask = saturate(worldPosPixel.y - screenPosRefractedWorldPos.y);
    
    #else
        mask = 0;

    #endif
}
#endif
