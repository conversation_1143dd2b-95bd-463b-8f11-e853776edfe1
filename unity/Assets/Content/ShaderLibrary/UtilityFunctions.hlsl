#ifndef UTILITY_FUNCTIONS_INCLUDED
#define UTILITY_FUNCTIONS_INCLUDED

#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/ShaderVariablesFunctions.hlsl"
#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/SpaceTransforms.hlsl"

// Packing
void PackTBNAndPositionWS(VertexPositionInputs positionInputs, VertexNormalInputs normalInputs, out float4x3 tbn_positionWS)
{
    tbn_positionWS = float4x3(
    float4(normalInputs.tangentWS, normalInputs.bitangentWS.x),
    float4(normalInputs.bitangentWS.yz, normalInputs.normalWS.xy),
    float4(normalInputs.normalWS.z, positionInputs.positionWS)
    );
}

void PackTBNAndPositionWS(float3 positionWS, VertexNormalInputs normalInputs, out float4x3 tbn_positionWS)
{
    tbn_positionWS = float4x3(
    float4(normalInputs.tangentWS, normalInputs.bitangentWS.x),
    float4(normalInputs.bitangentWS.yz, normalInputs.normalWS.xy),
    float4(normalInputs.normalWS.z, positionWS)
    );
}

void UnpackTBNAndPositionWS(float4x3 tbn_positionWS, out half3x3 tbn, out float3 positionWS)
{
    tbn[0] = tbn_positionWS[0];
    tbn[1] = tbn_positionWS[1];
    tbn[2] = tbn_positionWS[2];
    positionWS = tbn_positionWS[3];
}

struct SampleNormalData
{
    float2 uv;
    float2 uv1;
    float3 positionWS;
    half3 vertexColor;
};

SampleNormalData InitializeSampleNormalData(
    float2 uv = 0,
    float2 uv1 = 0,
    float3 positionWS = 0,
    half3 vertexColor = 0)
{
    SampleNormalData o;
    o.uv = uv;
    o.uv1 = uv1;
    o.positionWS = positionWS;
    o.vertexColor = vertexColor;
    return o;
}

float3 TransformUnpackedToWorldNormal(float3 normalFromTexture, float3x3 tbn)
{
    float3 normalWS = TransformTangentToWorld(normalFromTexture, tbn);
    normalWS = NormalizeNormalPerPixel(normalWS);
    return normalWS;
}

// Remaps
float Remap1(float x, float in_min, float in_max, float out_min, float out_max)
{
    return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

float2 Remap2(float2 x, float2 in_min, float2 in_max, float2 out_min, float2 out_max)
{
    return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

float3 Remap3(float3 x, float3 in_min, float3 in_max, float3 out_min, float3 out_max)
{
    return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

// Compare functions
float Greater(float a, float b)
{
    float x = (a - b) * 1000;
    return clamp(x, 0, 1);
}

float Lesser(float a, float b)
{
    float x = -(a - b) * 1000;
    return clamp(x, 0, 1);
}

// Lerps
float InverseLerp(float a, float b, float t)
{
    return (t - a) / (b - a);
}

// Blends
float3 BlendOverlay(float3 Base, float3 Blend, float Opacity)
{
    float3 result1 = 1.0 - 2.0 * (1.0 - Base) * (1.0 - Blend);
    float3 result2 = 2.0 * Base * Blend;
    float3 zeroOrOne = step(Base, 0.5);
    float3 ret = result2 * zeroOrOne + (1 - zeroOrOne) * result1;
    ret = lerp(Base, ret, Opacity);
    return ret;
}

float3 BlendMultiply(float3 Base, float3 Blend, float Opacity)
{
    float3 ret = Base * Blend;
    ret = lerp(Base, ret, Opacity);
    return ret;
}

// Fade Transition
float FadeTransition(float noiseValue, float fadeValue, float fadeContrast)
{
    return saturate(fadeValue * (fadeContrast + 1) + (noiseValue - 1) * fadeContrast);
}

// Gamma Correction
float GammaCorrect(float input, float gamma)
{
    return pow(input, 1.0 / gamma);
}

// Levels
float Levels(float input, float minInput, float midPoint, float maxInput)
{
    return PositivePow(Remap1(input, 0, 1, minInput, maxInput), midPoint);
}

// Triplanar
half4 Triplanar(Texture2D tex, SamplerState state, float3 position, float3 normal, float scale, float blend)
{
    float3 Node_UV = position * scale;
    float3 Node_Blend = pow(abs(normal), blend);
    Node_Blend /= dot(Node_Blend, 1.0);
    half4 Node_X = SAMPLE_TEXTURE2D(tex, state, Node_UV.zy);
    half4 Node_Y = SAMPLE_TEXTURE2D(tex, state, Node_UV.xz);
    half4 Node_Z = SAMPLE_TEXTURE2D(tex, state, Node_UV.xy);
    return Node_X * Node_Blend.x + Node_Y * Node_Blend.y + Node_Z * Node_Blend.z;
}

half4 TriplanarNormal(sampler2D tex, float3 position, float3 normal, float scale, float blend)
{
    float3 Node_UV = position * scale;
    float3 Node_Blend = max(pow(abs(normal), blend), 0);
    Node_Blend /= (Node_Blend.x + Node_Blend.y + Node_Blend.z ).xxx;
    half3 Node_X = UnpackNormalmapRGorAG(tex2D(tex, Node_UV.zy));
    half3 Node_Y = UnpackNormalmapRGorAG(tex2D(tex, Node_UV.xz));
    half3 Node_Z = UnpackNormalmapRGorAG(tex2D(tex, Node_UV.xy));
    Node_X = half3(Node_X.xy + normal.zy, abs(Node_X.z) * normal.x);
    Node_Y = half3(Node_Y.xy + normal.xz, abs(Node_Y.z) * normal.y);
    Node_Z = half3(Node_Z.xy + normal.xy, abs(Node_Z.z) * normal.z);
    return half4(normalize(Node_X.zyx * Node_Blend.x + Node_Y.xzy * Node_Blend.y + Node_Z.xyz * Node_Blend.z), 1);
}

// Fresnel
float FresnelEffect(float3 normal, float3 viewDir, float power)
{
    return pow(1.0 - saturate(dot(normalize(normal), normalize(viewDir))), power);
}

float3 RotateAboutAxis(float3 axis, float angle, float3 vec)
{
    axis = normalize(axis);
    float cosTheta = cos(angle);
    float sinTheta = sin(angle);
                
    float3 rotatedVector = (cosTheta * vec) + 
                           (sinTheta * cross(axis, vec)) + 
                           ((1.0 - cosTheta) * dot(axis, vec) * axis);
                
    return rotatedVector;
}

float RandomValue(float seed, float minValue, float maxValue)
{
    float r = frac(sin(seed) * 10000);
    return lerp(minValue, maxValue, r);
}

//Random value between -1 to 1
float RandomRange(float seed)
{
    return frac(sin(seed) * 10000) * 2 - 1;
}

// Input
float3 GetObjectPositionWS()
{
    return GetObjectToWorldMatrix()._m03_m13_m23;
}

float2 GetFlipbookUV(float2 uv, float rowsCount, float tilesCount, float progress)
{
    float tilesProgress = floor(frac(progress) * (tilesCount + 0.00001));
    float tileSize = 1 / rowsCount;
    float base = floor((tilesProgress + 0.5) * tileSize);
    float tileX = tilesProgress - rowsCount * base;
    float tileY = rowsCount - (base + 1);
    return (uv + float2(tileX, tileY)) * tileSize;
}

float3 ProjectPointOntoPlane(float3 pointPositionOS, float3 planePositionOS, float3 planeNormalOS)
{
    float signedDistance = dot(planeNormalOS, pointPositionOS - planePositionOS);
    float3 limitedPositionOS = pointPositionOS - (signedDistance * planeNormalOS);
    return signedDistance < 0 ? limitedPositionOS : pointPositionOS;
}

#endif