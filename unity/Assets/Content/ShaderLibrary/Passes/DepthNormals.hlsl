#ifndef DEPTH_NORMALS_WITH_DITHERING_INCLUDED
#define DEPTH_NORMALS_WITH_DITHERING_INCLUDED

#pragma target 2.0

#pragma vertex DepthNormalsVertex
#pragma fragment DepthNormalsFragment
#pragma multi_compile_fragment _ LOD_FADE_CROSSFADE

#ifdef _ALLOW_CHARACTER_DITHERING
    #include "Assets/Content/ShaderLibrary/Dithering.hlsl"   
#endif

#pragma shader_feature_local _ALPHATEST_ON

#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

#ifdef _USE_NORMAL_MAP
    #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
#endif

#if defined(LOD_FADE_CROSSFADE)
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
#endif

struct Attributes
{
    float4 positionOS : POSITION;
    
    #if defined(_ALPHATEST_ON) || defined(_USE_NORMAL_MAP)
        float2 uv : TEXCOORD0;
    #endif

    float3 normalOS : NORMAL;

    #if defined(_USE_NORMAL_MAP)
        float4 tangentOS : TANGENT;

        #ifdef _USE_UV1
            float2 uv1 : TEXCOORD1;
        #endif

        #ifdef _USE_VERTEX_COLOR
            half4 vertexColor : COLOR;
        #endif
    #endif
};

struct Varyings
{
    float4 positionCS : SV_POSITION;

    #if defined(_ALPHATEST_ON) || defined(_USE_NORMAL_MAP)
        float2 uv : TEXCOORD0;
    #endif

    #ifdef _USE_NORMAL_MAP
        #ifdef _USE_UV1
            float2 uv1 : TEXCOORD1;
        #endif

        #ifdef _USE_VERTEX_COLOR
            half4 vertexColor : TEXCOORD2;
        #endif

        #ifdef _USE_POSITION_WS
            float3 positionWS : TEXCOORD3;
        #endif
        
        float3x3 tbn : TEXCOORD4;
    #else
        half3 normalWS : TEXCOORD4;
    #endif
    
};

Varyings DepthNormalsVertex(Attributes input)
{
    Varyings output = (Varyings)0;

    output.positionCS = TransformObjectToHClip(input.positionOS.xyz);
    
    #if defined(_ALPHATEST_ON) || defined(_USE_NORMAL_MAP)
        output.uv = input.uv;
    #endif

    #ifdef _USE_NORMAL_MAP
        #ifdef _USE_UV1
            output.uv1 = input.uv1;
        #endif

        #ifdef _USE_VERTEX_COLOR
            output.vertexColor = input.vertexColor;
        #endif

        #ifdef _USE_POSITION_WS
            output.positionWS = TransformObjectToWorld(input.positionOS.xyz);
        #endif
    
        VertexNormalInputs normalInput = GetVertexNormalInputs(input.normalOS, input.tangentOS);
        output.tbn = float3x3(
            normalInput.tangentWS,
            normalInput.bitangentWS,
            normalInput.normalWS
            );
    
    #else
        output.normalWS = TransformObjectToWorldNormal(input.normalOS);
    #endif
    
    return output;
}

void DepthNormalsFragment(
    Varyings input,
    
    #ifdef _FLIP_BACKFACE_NORMALS
        bool facing : FRONT_FACE_SEMANTIC,
    #endif
    
    out half4 outNormalWS : SV_Target0
    
    #ifdef _WRITE_RENDERING_LAYERS
            , out float4 outRenderingLayers : SV_Target1
    #endif
)
{
    #if defined(_ALPHATEST_ON) && !defined(_NO_BASE_MAP)
        float alpha = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uv).a;
        clip(alpha - _Cutoff);
    #endif
    
    #if defined(LOD_FADE_CROSSFADE)
        LODFadeCrossFade(input.positionCS);
    #endif

    #ifdef _ALLOW_CHARACTER_DITHERING
        if (_IsCharacterDithering)
        {
            Dither(_DitherAlpha, GetNormalizedScreenSpaceUV(input.positionCS.xy));
        }
    #endif
    
    #ifdef _USE_NORMAL_MAP
    
        SampleNormalData snData = InitializeSampleNormalData(input.uv);
    
        #ifdef _FLIP_BACKFACE_NORMALS
            float isFrontFace =  IS_FRONT_VFACE(facing, 1, -1);
            snData.tbn[0] *= isFrontFace;
            snData.tbn[2] *= isFrontFace;
        #endif
    
        #ifdef _USE_UV1
            snData.uv1 = input.uv1;
        #endif
    
        #ifdef _USE_VERTEX_COLOR
            snData.vertexColor = input.vertexColor.rgb;
        #endif
    
        #ifdef _USE_POSITION_WS
            snData.positionWS = input.positionWS;
        #endif

        #if defined(KEFIR_SAMPLE_NORMAL_WS)
            outNormalWS = half4(KefirSampleNormalWS(snData, input.tbn), 0.0);
        #else
            half3 normalTS = KefirSampleNormalTS(snData);
            outNormalWS = half4(TransformUnpackedToWorldNormal(normalTS, input.tbn), 0.0);
        #endif

    #else
    
        outNormalWS = half4(input.normalWS, 0.0);
        #ifdef _FLIP_BACKFACE_NORMALS
            float isFrontFace =  IS_FRONT_VFACE(facing, 1, -1);
            outNormalWS *= isFrontFace; 
        #endif
    
    #endif
    
    #ifdef _WRITE_RENDERING_LAYERS
                uint renderingLayers = GetMeshRenderingLayer();
                outRenderingLayers = float4(EncodeMeshRenderingLayer(renderingLayers), 0, 0, 0);
    #endif
}

#endif
