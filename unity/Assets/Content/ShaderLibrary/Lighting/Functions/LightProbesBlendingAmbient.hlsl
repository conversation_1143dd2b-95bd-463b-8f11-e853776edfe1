#ifndef LIGHT_PROBES_BLENDING_AMBIENT_INCLUDED
#define LIGHT_PROBES_BLENDING_AMBIENT_INCLUDED
float _ForceAmbientProbeWhenNotInPlaymode;
real4 _Ambient_SHAr;
real4 _Ambient_SHAg;
real4 _Ambient_SHAb;
real4 _Ambient_SHBr;
real4 _Ambient_SHBg;
real4 _Ambient_SHBb;
real4 _Ambient_SHC;

// Samples SH L0, L1 and L2 terms
half3 SampleSH(half3 normalWS)
{
    // LPPV is not supported in Ligthweight Pipeline
    real4 SHCoefficients[7];
    SHCoefficients[0] = unity_SHAr;
    SHCoefficients[1] = unity_SHAg;
    SHCoefficients[2] = unity_SHAb;
    SHCoefficients[3] = unity_SHBr;
    SHCoefficients[4] = unity_SHBg;
    SHCoefficients[5] = unity_SHBb;
    SHCoefficients[6] = unity_SHC;
    half3 interpolatedLightProbeColor = max(half3(0, 0, 0), SampleSH9(SHCoefficients, normalWS));
    
    SHCoefficients[0] = _Ambient_SHAr;
    SHCoefficients[1] = _Ambient_SHAg;
    SHCoefficients[2] = _Ambient_SHAb;
    SHCoefficients[3] = _Ambient_SHBr;
    SHCoefficients[4] = _Ambient_SHBg;
    SHCoefficients[5] = _Ambient_SHBb;
    SHCoefficients[6] = _Ambient_SHC;
    half3 ambientLightProbeColor = max(half3(0, 0, 0), SampleSH9(SHCoefficients, normalWS));
    
    return lerp(interpolatedLightProbeColor, ambientLightProbeColor, _AmbientProbeBlendFactor + _ForceAmbientProbeWhenNotInPlaymode);
}

// SH Vertex Evaluation. Depending on target SH sampling might be
// done completely per vertex or mixed with L2 term per vertex and L0, L1
// per pixel. See SampleSHPixel
half3 SampleSHVertex(half3 normalWS)
{
    #if defined(EVALUATE_SH_VERTEX)
    return SampleSH(normalWS);
    #elif defined(EVALUATE_SH_MIXED)
    // no max since this is only L2 contribution
    half3 interpolatedLightProbeTermL2 = SHEvalLinearL2(normalWS, unity_SHBr, unity_SHBg, unity_SHBb, unity_SHC);
    half3 ambientLightProbeTermL2 = SHEvalLinearL2(normalWS, _Ambient_SHBr, _Ambient_SHBg, _Ambient_SHBb, _Ambient_SHC);
    return lerp(interpolatedLightProbeTermL2, ambientLightProbeTermL2, _AmbientProbeBlendFactor + _ForceAmbientProbeWhenNotInPlaymode);
    #endif

    // Fully per-pixel. Nothing to compute.
    return half3(0.0, 0.0, 0.0);
}

// SH Pixel Evaluation. Depending on target SH sampling might be done
// mixed or fully in pixel. See SampleSHVertex
half3 SampleSHPixel(half3 L2Term, half3 normalWS)
{
    #if defined(EVALUATE_SH_VERTEX)
    return L2Term;
    #elif defined(EVALUATE_SH_MIXED)
    half3 interpolatedLightProbeTermL1L0 = SHEvalLinearL0L1(normalWS, unity_SHAr, unity_SHAg, unity_SHAb);
    half3 ambientLightProbeTermL1L0 = SHEvalLinearL0L1(normalWS, _Ambient_SHAr, _Ambient_SHAg, _Ambient_SHAb);
    half3 res = L2Term + lerp(interpolatedLightProbeTermL1L0, ambientLightProbeTermL1L0, _AmbientProbeBlendFactor + _ForceAmbientProbeWhenNotInPlaymode);
    #ifdef UNITY_COLORSPACE_GAMMA
    res = LinearToSRGB(res);
    #endif
    return max(half3(0, 0, 0), res);
    #endif

    // Default: Evaluate SH fully per-pixel
    return SampleSH(normalWS);
}
#endif
