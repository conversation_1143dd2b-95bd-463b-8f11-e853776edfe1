#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
half4x4 _WaterCausticsMainLightDir;

float2 CausticUVs(float causticsSize, float2 rawUV, float2 offset)
{
    float2 uv = rawUV * causticsSize;
    return uv + offset * 0.1;
}

half3 CalculateCaustics(float causticsSize, float blendDistanceY, float waterLevelY, float3 reconstructedPositionWS, Texture2D causticsTexture, SamplerState sampler_CausticsTexture)
{
    float time = _Time.x;

    // Read wave texture for noise to offset cautics UVs.
    float2 uv = reconstructedPositionWS.xz * 0.025 + time * 0.25;
    half waveOffset = SAMPLE_TEXTURE2D(causticsTexture, sampler_CausticsTexture, uv).w - 0.5;\
    
    // Get light direction and use it to rotate the world position.
    float3 lightUVs = mul(float4(reconstructedPositionWS, 1), _WaterCausticsMainLightDir).xyz;
    //return lightUVs;
    float2 causticUV = CausticUVs(causticsSize, lightUVs.xy, waveOffset);
    //return float3(causticUV,0);

    float distanceFactor = smoothstep(25, 100, distance(_WorldSpaceCameraPos, reconstructedPositionWS));
    //return distanceFactor;
    float lodLevel = abs(reconstructedPositionWS.y - waterLevelY) * 4 / blendDistanceY;
    // Hide caustics if we far from surface with caustics.
    lodLevel = lerp(lodLevel, 5, distanceFactor);
    half4 causticsA = SAMPLE_TEXTURE2D_LOD(causticsTexture, sampler_CausticsTexture, causticUV + time, lodLevel);
    half4 causticsB = SAMPLE_TEXTURE2D_LOD(causticsTexture, sampler_CausticsTexture, causticUV * 2.0, lodLevel);
    
    float causticsDriver = (causticsA.z * causticsB.z) * 10 + causticsA.z + causticsB.z;
    //return  abs(reconstructedPositionWS.y - waterLevelY) * 4 / blendDistanceY;
    
    // Mask caustics from above water and fade below.
    half upperMask = saturate(-reconstructedPositionWS.y + waterLevelY);
    half lowerMask = saturate((reconstructedPositionWS.y - waterLevelY)/ blendDistanceY + blendDistanceY);
    causticsDriver *= min(upperMask, lowerMask);
    
    // Fake light dispersion.
    Light mainLight = GetMainLight(TransformWorldToShadowCoord(reconstructedPositionWS), reconstructedPositionWS, 0);
    half3 caustics = causticsDriver * half3(causticsA.w * 0.5, causticsB.w * 0.75, causticsB.x) * mainLight.color;
    caustics *= mainLight.shadowAttenuation;

    return caustics;
}


