using Core.Updater;
using Game.Common;
using Game.Global;
using Game.Global.Clan.ClanSettings;
using Game.Global.ModalDialog;
using Game.Shared;
using Inputs;
using System.Collections.Generic;
using UI;
using UnityEngine;
using UnityEngine.InputSystem;
using Event = AK.Wwise.Event;

namespace Game.MainMenu
{
    public class MainMenuInputActionUpdater : IInputHandler
    {
        private const int _citiesInClusterCount = 1;

        private readonly ModalDialogModel _modalDialogModel;
        private readonly GlobalScreenContent _globalScreenContent;
        private readonly PlayerInput _playerInput;
        private readonly IUpdater _updater;
        private readonly Event _mainClick;
        private readonly GameObject _objectEventView;
        private readonly DropdownView _dropdown;

        private readonly CompositeButtonView _findCityButtonView;
        private readonly InputFieldWithErrorView _textFieldCoinsViewWithError;

        public MainMenuInputActionUpdater(GlobalModel globalModel, InputActions inputActions,
            GameObject objectEventView, MainMenuScreenContent mainMenuContent, GlobalScreenContent globalScreenContent)
        {
            _modalDialogModel = globalModel.ModalDialogModel;
            _globalScreenContent = globalScreenContent;
            _mainClick = mainMenuContent.MainClick;
            _objectEventView = objectEventView;

            _updater = new CompositeUpdater(new List<IUpdater>
            {
                new ActionInputUpdater(ShowQuitDialog, inputActions.Battle.Back),
            });
        }

        private void ShowQuitDialog()
        {
            PlayClickSound();

            var screenLocalize = _globalScreenContent.LocalizationKeys.QuitScreenLocalize;
            _modalDialogModel.CreateBuilder()
                .CreateConfirmCancelDialog(screenLocalize.Header, Quit, _modalDialogModel.CloseDialog, new Text(screenLocalize.Description), _globalScreenContent.LocalizationKeys)
                .OpenDialog();
        }

        private void PlayClickSound()
        {
            _mainClick.Post(_objectEventView);
        }

        private void Quit()
        {
            Debug.Log("Quit");
            Application.Quit();
        }

        public IEnumerable<(InputMask, IUpdater)> GetHandlers()
        {
            yield return (InputMask.Main, _updater);
        }

        public InputHandlerPriority Priority => InputHandlerPriority.Common;
    }
}