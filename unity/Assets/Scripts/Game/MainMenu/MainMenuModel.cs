using Content.ContentWrappers;
using Game.Global.SkinCenter.Customization.Models;
using System.Collections.Generic;

namespace Game.MainMenu
{
    public class MainMenuModel
    {
        public MainMenuDialogModel MainMenuDialogModel { get; } = new();
        public EarlyAccessDialogModel EarlyAccessDialogModel { get; } = new();
        public SkinLoadingModel<CustomizationMaleSnapshotModel> MaleLoadingModel { get; } = new();
        public SkinLoadingModel<CustomizationFemaleSnapshotModel> FemaleLoadingModel { get; } = new();
        public HashSet<ILoadedContent> LoadedContents { get; } = new();
    }
}