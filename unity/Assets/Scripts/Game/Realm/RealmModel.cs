using System.Collections.Generic;
using Content.Disposable;
using Game.Battle.Inputs;
using Game.Global.PopupNotificationDialog;
using Game.Realm.BuySettlement;
using Game.Realm.ConquestSector.Model;
using Game.Realm.Map.Camera;
using Game.Realm.Map.HomeMarker;
using Game.Realm.Map.InfoPanel.Model;
using Game.Realm.Map.Ownership;
using Game.Realm.Map.Selection;
using Models.References.Location;
using UnityEngine;

namespace Game.Realm
{
    public class RealmModel
    {
        public RealmSelection Selection { get; set; }
        public RealmSelection HoverSelection { get; set; }
        public RealmSelection AttentionSelection { get; set; }

        public InputModel OpenSideMenuDialogInputModel { get; } = new() { Mode = InputModel.InputMode.Press };
        public InputModel OpenClanShowdownInputModel { get; } = new() { Mode = InputModel.InputMode.Press };
        public InputModel OpenChatInputModel { get; } = new() { Mode = InputModel.InputMode.Press };

        public MapCameraModel MapCameraModel { get; } = new();

        public InfoPanelDialogModel InfoPanelDialogModel { get; } = new();
        public BuySettlementDialogModel BuySettlementDialogModel { get; } = new();
        public ConquestSectorDialogModel ConquestSectorDialogModel { get; } = new();
        public IconNotificationModel TaxesIconNotificationModel { get; } = new();
        public ConfiscationPopupModel ConfiscationPopupModel { get; } = new();
        public RealmMapOwnershipChangesModel RealmMapOwnershipChangesModel { get; } = new();
        public bool IsInfoPanelOpen { get; set; }
        public Rect InfoPanelRect { get; set; }

        public List<(LocationDescription locationDescription, IDisposableContent<RealmIconLocationMarkerView> content)> IconLocationContent = new();
        public IDisposableContent<RealmIconLocationMarkerView> PlayerContent;
    }
}