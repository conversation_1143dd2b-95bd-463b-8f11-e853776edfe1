using System.Collections.Generic;
using Game.Realm.Map.Camera;
using Game.Realm.Map.Selection;
using Models.References;
using Models.References.Location;
using Models.References.Realm;
using UnityEngine;
using Utils.ColliderDesigner.Scripts.Realm;

namespace Game.Realm.Sectors
{
    public class RealmSectorsView : MonoBeh<PERSON>our, ISerializationCallbackReceiver
    {
        [field: SerializeField] public RealmSelectionCellView RealmSelectionCellView { get; private set; }
        [field: SerializeField] public RealmSelectionCellView RealmHoverCellView { get; private set; }
        [field: SerializeField] public List<RealmSectorView> RealmSectorViews { get; private set; } = new();
        [field: SerializeField] public List<RealmDottedLineView> RealmDottedLineViews { get; private set; } = new();

        [field: SerializeField] public RealmMapCanvas ZoomInCanvas { get; private set; }
        [field: SerializeField] public RealmMapCanvas ZoomOutCanvas { get; private set; }

        [field: SerializeField] public float RightOffsetNormalized { get; private set; }
        [SerializeField] private Camera _camera;
        
        [field: SerializeField] public List<RealmLocation> RealmLocations { get; private set; } = new();

        [field: SerializeField]
        [field: HideInInspector]
        public List<RealmWorldCell> WorldCells { get; private set; } = new();

        [field: SerializeField]
        [field: HideInInspector]
        public List<RealmWorldCell> LocationWorldCells { get; private set; } = new();

        private readonly Dictionary<Vector2Int, int[]> _sectorZones = new();
        private readonly Dictionary<Vector2Int, int[]> _locationZones = new();
        private readonly Dictionary<LocationDescription, RealmSectorView> RealmLocationViews = new();

        public RealmSectorView GetSector(SectorDescription description)
        {
            return RealmSectorViews[description.Index];
        }

        public RealmSectorView GetLocation(LocationDescription description)
        {
            return RealmLocationViews.GetValueOrDefault(description);
        }

        public RealmSelection GetNearestCameraSector(RealmSelection prioryRealmSelection)
        {
            var scenePosition = MapCameraUtils.ScreenToScenePosition(_camera, new Vector2(Screen.width, Screen.height) / 2);
            var worldCoords = SectorUtils.PositionToCoords(scenePosition);

            if (_sectorZones.TryGetValue(worldCoords, out var sectorIndexes))
            {
                if (prioryRealmSelection is RealmSelection.Sector sector)
                {
                    foreach (var index in sectorIndexes)
                    {
                        if (index == sector.SectorDescription.Index)
                        {
                            return prioryRealmSelection;
                        }
                    }
                }

                if (SectorDescription.Enum.TryFromIndex(sectorIndexes[0], out var sectorDescription))
                {
                    return new RealmSelection.Sector(sectorDescription);
                }
            }

            if (_locationZones.TryGetValue(worldCoords, out var indexes))
            {
                if (prioryRealmSelection is RealmSelection.Location location)
                {
                    foreach (var index in indexes)
                    {
                        if (index == location.Description.Index)
                        {
                            return prioryRealmSelection;
                        }
                    }
                }


                if (LocationDescription.Enum.TryFromIndex(indexes[0], out var locationDescription))
                {
                    return new RealmSelection.Location(locationDescription);
                }
            }

            return null;
        }

        public bool TryGetSelection(Vector2 screenPosition, out RealmSelection selection)
        {
            selection = null;
            if (TryGetSettlement(screenPosition, out var sectorDescription, out var settlementDescription))
            {
                selection = new RealmSelection.Settlement(sectorDescription, settlementDescription, LocationDescription.Settlement);
                return true;
            }

            if (TryGetLocation(screenPosition, out var locationDescription))
            {
                selection = new RealmSelection.Location(locationDescription);
                return true;
            }

            return false;
        }

        public void OnBeforeSerialize()
        {
        }

        public void OnAfterDeserialize()
        {
            _sectorZones.Clear();
            foreach (var worldCell in WorldCells)
            {
                _sectorZones.Add(worldCell.Coords, worldCell.NearestSectors);
            }

            _locationZones.Clear();
            foreach (var worldCell in LocationWorldCells)
            {
                _locationZones.Add(worldCell.Coords, worldCell.NearestSectors);
            }

            RealmLocationViews.Clear();
            foreach (var location in RealmLocations)
            {
                if (LocationDescription.Enum.TryFromId(location.Location.Id, out var locationDescription))
                {
                    RealmLocationViews.Add(locationDescription, location.RealmSectorView);
                }
            }
        }

        private bool TryGetSettlement(Vector2 screenPosition, out SectorDescription sectorDescription, out SettlementDescription settlementDescription)
        {
            var worldPosition = MapCameraUtils.ScreenToScenePosition(_camera, screenPosition);
            var worldCoords = SectorUtils.PositionToCoords(worldPosition);

            foreach (var currentSectorDescription in SectorDescription.Enum)
            {
                if (GetSector(currentSectorDescription).TryGetCell(worldCoords, out var cell) && SettlementDescription.Enum.TryFromIndex(cell.SettlementIndex, out settlementDescription))
                {
                    sectorDescription = currentSectorDescription;
                    return true;
                }
            }

            settlementDescription = null;
            sectorDescription = null;
            return false;
        }

        private bool TryGetLocation(Vector2 screenPosition, out LocationDescription sectorDescription)
        {
            var worldPosition = MapCameraUtils.ScreenToScenePosition(_camera, screenPosition);
            var worldCoords = SectorUtils.PositionToCoords(worldPosition);

            foreach (var currentSectorDescription in LocationDescription.Enum)
            {
                var realmSectorView = GetLocation(currentSectorDescription);
                if (realmSectorView != null && realmSectorView.TryGetCell(worldCoords, out _))
                {
                    sectorDescription = currentSectorDescription;
                    return true;
                }
            }

            sectorDescription = null;
            return false;
        }
    }
}