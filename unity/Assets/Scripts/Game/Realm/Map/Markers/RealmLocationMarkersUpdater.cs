using System.Collections.Generic;
using Content;
using Content.ContentWrappers;
using Content.Disposable;
using Core;
using Core.Updater;
using Game.Global;
using Game.Global.UI;
using Game.Realm.Map.Camera;
using Game.Realm.Map.Selection;
using Game.Realm.Sectors;
using Game.Shared.Models;
using Inputs;
using Models.Models.RegionUserHangarModel;
using Models.References.Location;
using UI;
using UnityEngine;

namespace Game.Realm.Map.HomeMarker
{
    public class RealmLocationMarkersUpdater : IUpdater
    {
        private const float _baseArrowAngle = -90;
        private readonly RealmModel _realmModel;
        private readonly RealmSectorsView _realmSectorsView;
        private readonly MapCameraSettingsViewDescription _cameraSettings;
        private readonly UnityEngine.Camera _camera;
        private readonly List<(LocationDescription locationDescription, IDisposableContent<RealmLocationMarkerView> content)> _textLocationContent = new();
        private readonly List<IUpdater> _updaters = new();
        private readonly MapCameraModel _mapCameraModel;
        private readonly IRegionUserHangarModel _hangarModel;
        private readonly LoadingModel _loadingModel;
        private bool _isShowed;
        private readonly DeviceId _deviceId;

        private RealmSelection _selection;
        private RealmSelection _attention;
        private Vector2 _rightUpBorder;
        private Vector2 _leftDownBorder;
        private bool _isInfoPanelOpen;

        public RealmLocationMarkersUpdater(GlobalModel globalModel, RealmModel realmModel, RealmSectorsView realmSectorsView, PooledLoadedComponentContent<RealmIconLocationMarkerView> iconMarkerInstantiate,
            IInstantiateContent<RealmLocationMarkerView> textMarkerInstantiate, MapCameraSettingsViewDescription cameraSettings, UnityEngine.Camera camera, Transform parent3D, Transform parentUI,
            LocationNameViewDescriptions locationDescription,
            IRegisterDisposable registerDisposable)
        {
            _deviceId = globalModel.DeviceModel.CurrentDeviceId;
            _realmModel = realmModel;
            _realmSectorsView = realmSectorsView;
            _cameraSettings = cameraSettings;
            _camera = camera;
            _mapCameraModel = realmModel.MapCameraModel;
            _hangarModel = globalModel.HangarModel;
            _loadingModel = globalModel.LoadingModel;

            _selection = _realmModel.Selection;
            _attention = _realmModel.AttentionSelection;

            _leftDownBorder = Vector2.zero;
            _rightUpBorder = new Vector2(_realmSectorsView.RightOffsetNormalized, 1);

            foreach (var realmLocations in realmSectorsView.RealmLocations)
            {
                if (LocationDescription.Enum.TryFromId(realmLocations.Location.Id, out var description))
                {
                    var location = realmSectorsView.GetLocation(description);
                    if (location)
                    {
                        CreateLocationMarker(textMarkerInstantiate, parent3D, locationDescription, description, location);
                        CreatIconMarker(iconMarkerInstantiate, parentUI, locationDescription, description, location);
                    }
                }
            }

            registerDisposable.Register(Dispose);
        }

        public void Update()
        {
            UpdateView();

            if (_selection != _realmModel.Selection || _attention != _realmModel.AttentionSelection)
            {
                _selection = _realmModel.Selection;
                _attention = _realmModel.AttentionSelection;
            }

            if (!_loadingModel.IsVisible && !_isShowed)
            {
                _isShowed = true;
                UpdateAttention();
            }

            _updaters.Update();
        }

        private void UpdateView()
        {
            foreach (var (locationDescription, content) in _realmModel.IconLocationContent)
            {
                var isSectorSelected = (_realmModel.Selection is RealmSelection.Location selection && selection.Description == locationDescription)
                                       || (_realmModel.HoverSelection is RealmSelection.Location location && location.Description == locationDescription);

                content.Value.State.State = isSectorSelected ? RealmIconLocationMarkerView.IconState.Selected : RealmIconLocationMarkerView.IconState.None;
                UpdateSector(content.Value, locationDescription);
            }
        }

        private void CreatIconMarker(PooledLoadedComponentContent<RealmIconLocationMarkerView> iconMarkerInstantiate, Transform parentUI, LocationNameViewDescriptions locationDescription, LocationDescription description,
            RealmSectorView location)
        {
            var iconContent = iconMarkerInstantiate.Generate(parentUI);
            _realmModel.IconLocationContent.Add((description, iconContent));

            var view = iconContent.Value;
            _updaters.Add(new ClickButtonActionUpdater(() => OnSelect(description), view.CompositeButtonView.ClickButtonView));

            view.MarkerState.State = RealmIconLocationMarkerView.IconMarkerState.Location;
            view.SetPosition(location.MarkerWorldPosition);
            view.SetArrowRotate(_baseArrowAngle);
            view.LocationIcon.SetSprite(locationDescription[description].RealmLocationIcon);
            view.SetImageState(description != LocationDescription.Prison);
            view.State.State = RealmIconLocationMarkerView.IconState.None;
        }

        private void OnSelect(LocationDescription description)
        {
            _realmModel.Selection = new RealmSelection.Location(description);

            var settlementWorldPosition = _realmSectorsView.GetLocation(description).MarkerWorldPosition;
            _mapCameraModel.SetTargetPosition(settlementWorldPosition, _cameraSettings.LocationSpeed);
            _mapCameraModel.SetTargetDistance(_deviceId == DeviceId.KeyboardMouse ? _cameraSettings.BattleDistance : _cameraSettings.BattleTouchDistance, new Vector2(Screen.width / 2f, Screen.height / 2f),
                _cameraSettings.LocationZoomSpeed);

            UpdateAttention();
        }

        private void UpdateAttention()
        {
            foreach (var (description, content) in _realmModel.IconLocationContent)
            {
                content.Value.SetDrawAttention(CalculateTabState(_attention, description));
            }
        }

        private void CreateLocationMarker(IInstantiateContent<RealmLocationMarkerView> textMarkerInstantiate, Transform parent3D, LocationNameViewDescriptions locationDescription, LocationDescription description,
            RealmSectorView location)
        {
            var content = textMarkerInstantiate.Generate(parent3D);
            _textLocationContent.Add((description, content));

            content.Value.SetPosition(location.MarkerWorldPosition);
            content.Value.SetText(locationDescription[description].ShortName);
        }

        private bool CalculateTabState(RealmSelection selection, LocationDescription key)
        {
            if (selection is RealmSelection.Settlement or RealmSelection.Location)
            {
                return selection switch
                {
                    RealmSelection.Location location => key == location.Description,
                    RealmSelection.Settlement settlement => key == LocationDescription.Settlement && settlement.SectorDescription == _hangarModel.SelfSector && settlement.Description == _hangarModel.SelfSettlement,
                };
            }

            return false;
        }

        private void UpdateSector(RealmIconLocationMarkerView view, LocationDescription description)
        {
            var worldPosition = _realmSectorsView.GetLocation(description).MarkerWorldPosition;
            var viewPortNormalizedPosition = _camera.WorldToViewportPoint(worldPosition);

            if (description == LocationDescription.Prison)
            {
                view.SetPosition(viewPortNormalizedPosition);
            }
            else
            {
                Vector2 position = MarkersRules.GetEllipseBorderPosition(viewPortNormalizedPosition, out var insideEllipse, out _);
                var inRectangle = true;
                if (_realmModel.IsInfoPanelOpen)
                {
                    if (_isInfoPanelOpen != _realmModel.IsInfoPanelOpen)
                    {
                        _rightUpBorder = new Vector2(_realmModel.InfoPanelRect.x / Screen.width - _realmSectorsView.RightOffsetNormalized, 1);
                    }

                    position = MarkersRules.GetViewPortNormalizedByRectangle(position, _leftDownBorder, _rightUpBorder, out inRectangle);
                }
                _isInfoPanelOpen = _realmModel.IsInfoPanelOpen;

                var angle = MarkersRules.GetAngle(position, viewPortNormalizedPosition);
                view.SetPosition(position);
                view.SetArrowRotate(insideEllipse && inRectangle ? _baseArrowAngle : angle);
            }
        }

        private void Dispose()
        {
            foreach (var (_, content) in _textLocationContent)
            {
                content.Dispose();
            }

            _textLocationContent.Clear();

            foreach (var (_, content) in _realmModel.IconLocationContent)
            {
                content.Dispose();
            }

            _realmModel.IconLocationContent.Clear();
        }
    }
}