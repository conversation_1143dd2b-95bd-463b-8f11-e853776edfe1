using System;
using System.Collections.Generic;
using Content.Disposable;
using Core;
using Core.Updater;
using Game.Common;
using Game.Global;
using Game.Shared;
using Inputs;
using Network.Connections;
using UnityEngine;

namespace Game.Realm.Map.InfoPanel
{
    public class InfoPanelDialogUpdater : IUpdater
    {
        private readonly IUpdater _updater;

        public InfoPanelDialogUpdater(RealmModel realmModel, GlobalModel globalModel, RealmScreenContent realmScreenContent, GlobalScreenContent globalScreenContent, Transform dialogContainer,
            IRegisterDisposable registerDisposable, InputActions inputActions, InputHandlersManager inputHandlersManager, NetworkStatusModel networkStatusModel, IRoomConnections roomConnections)
        {
            var updaters = new List<IUpdater>();
            _updater = new CompositeUpdater(updaters);

            IDisposableContent<RealmInfoPanelDialogView> content;

            switch (globalModel.DeviceModel.CurrentDeviceId)
            {
                case DeviceId.KeyboardMouse:
                {
                    content = realmScreenContent.KeyboardRealmInfoPanelView.Generate(dialogContainer);
                    break;
                }
                case DeviceId.Touch:
                {
                    content = realmScreenContent.TouchRealmInfoPanelView.Generate(dialogContainer);
                    break;
                }
                default:
                    throw new ArgumentOutOfRangeException();
            }

            var view  = content.Value;
            realmModel.IsInfoPanelOpen = true;
            realmModel.InfoPanelRect = ((RectTransform)view.transform).GetWorldRect();
            
            IInputHandler handler = new InfoPanelDialogInputHandler(globalModel, realmModel, inputActions, networkStatusModel, roomConnections.RegionUser, view, globalScreenContent.LocalizationKeys.ClanLocalization);

            inputHandlersManager.Add(handler);

            updaters.Add(new InfoPanelDialogSelectionsUpdater(globalModel, realmModel, networkStatusModel, view, globalScreenContent, realmScreenContent));

            globalModel.CursorLockModel.Add(CursorLockKey.SubDialog, CursorLockMode.None);

            registerDisposable.Register(() =>
            {
                globalModel.CursorLockModel.Remove(CursorLockKey.SubDialog);
                content.Dispose();
                inputHandlersManager.Remove(handler);
                realmModel.Selection = null;
                realmModel.IsInfoPanelOpen = false;
            });
        }

        public void Update()
        {
            _updater.Update();
        }
    }
}