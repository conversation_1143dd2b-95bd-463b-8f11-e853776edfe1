using UI;
using UnityEngine;
using Utils.TweenAnimation;

namespace Game.Realm.Notifications.Views
{
    public class NewSettlementsUnlockedNotificationView : MonoBeh<PERSON>our, INotificationView
    {
        [SerializeField] private TweenGroup _tweenGroup;

        public bool IsActive => _tweenGroup.IsPlaying();

        public void Show()
        {
            gameObject.SetActive(true);
            _tweenGroup.Play();
        }

        private void Update()
        {
            if (!_tweenGroup.IsPlaying() && gameObject.activeSelf)
            {
                Reset();
            }
        }

        public void Reset()
        {
            gameObject.SetActive(false);
            _tweenGroup.Stop();
        }
    }
}