using Game.Battle.Inventory;
using Models.References.Cargo;
using Models.References.Inventory;
using UnityEngine;

namespace Game.Battle
{
    public class TooltipModel
    {
        public Vector2 Position { get; set; }
        public Vector2 ItemSize { get; set; }

        public bool IsOpen { get; set; }

        public bool IsActionsOpen { get; set; }
        public bool IsDescriptionOpen { get; set; }

        public ScreenIntention Intention { get; set; } = ScreenIntention.Close;

        public ITooltipContext Context { get; private set; }

        public void OpenCargo(CargoDescription description)
        {
            Context = new CargoTooltipContext(description);
        }

        public void OpenInventoryItem(IItemMoveContext description)
        {
            Context = new InventoryItemTooltipContext(description);
        }

        public void OpenMapInventoryItem(InventoryItemDescription description)
        {
            Context = new MapInventoryItemTooltipContext(description);
        }

        public bool CheckInventory(IItemMoveContext context)
        {
            return Context is InventoryItemTooltipContext inventoryItemTooltipContext && inventoryItemTooltipContext.Context == context;
        }

        public enum ScreenIntention
        {
            None,
            OpenDescription,
            OpenActions,
            Close,
        }

        public void Clear()
        {
            Context = null;
            IsOpen = false;
            IsActionsOpen = false;
            IsDescriptionOpen = false;
            Intention = ScreenIntention.None;
            ItemSize = Vector2.zero;
            Position = Vector2.zero;
        }
    }
}