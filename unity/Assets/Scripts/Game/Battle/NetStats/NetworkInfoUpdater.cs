using ClientCore.Prediction.BattleInputDataModel;
using Core;
using Core.Updater;
using Game.Global.Settings;
using Game.Global.Settings.PerformanceInfo;
using Network.Connection;

namespace Game.Battle.NetStats
{
    public class NetworkInfoUpdater : IUpdater
    {
        private readonly NetStatsView _netStatsView;
        private readonly NetGraphView _netGraphView;
        private readonly GameSettingsModel _gameSettingModel;
        private readonly IUpdater _netStatsUpdater;
        private readonly IUpdater _netGraphUpdater;

        public NetworkInfoUpdater(NetStatsView netStatsView, NetGraphView netGraphView, IBattleInputDataModel battleInputModel, BattleModel battleModel, IRoomConnection connection,
            GameSettingsModel gameSettingModel, IRegisterDisposable registerDisposable)
        {
            _netStatsView = netStatsView;
            _netGraphView = netGraphView;
            _gameSettingModel = gameSettingModel;
            _netStatsUpdater = new NetStatsUpdater(netStatsView, battleModel, connection);
            _netGraphUpdater = new NetGraphUpdater(netGraphView, battleModel, battleInputModel, registerDisposable);
        }

        public void Update()
        {
            bool isDefaultEnabled = _gameSettingModel.PerformanceInfoSettings.CurrentValue == PerformanceInfoSettings.Default;
            bool isNetStatsEnabled = !isDefaultEnabled && _gameSettingModel.PerformanceNetStats.CurrentValue;
            bool isNetGraphEnabled = !isDefaultEnabled && _gameSettingModel.PerformanceNetGraph.CurrentValue;

            _netStatsView.SetEnabled(isNetStatsEnabled);
            _netGraphView.SetEnabled(isNetGraphEnabled);

            if (isNetStatsEnabled)
            {
                _netStatsUpdater.Update();
            }

            if (isNetGraphEnabled)
            {
                _netGraphUpdater.Update();
            }
        }
    }
}