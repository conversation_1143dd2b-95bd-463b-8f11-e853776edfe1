using Game.Battle.Markers.Views;
using UnityEngine;
using Utils.UiState;

namespace Game.Battle.Markers.Markers3D.View
{
    public class CraftWorkbenchMarker3DView : Marker3DView
    {
        [field: SerializeField] public MarkerProgressView ProgressView { get; private set; }
        [field: SerializeField] public MarkerTextView ProgressTextView { get; private set; }
        [Space]
        [SerializeField, EnumInfo(typeof(CraftWorkbenchMarkerState))] private EnumStateElement<CraftWorkbenchMarkerState> _stateGroup;

        public void SetState(CraftWorkbenchMarkerState state)
        {
            _stateGroup.State = state;
        }

        protected override void ResetContent()
        {
            ProgressView.SetProgress(0);
            AlphaView.SetAlpha(0);
        }

        public enum CraftWorkbenchMarkerState
        {
            Idle,
            InProgress
        }
    }
}