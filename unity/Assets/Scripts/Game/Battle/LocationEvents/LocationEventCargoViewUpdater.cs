using Core;
using Core.Updater;
using Game.Battle.Entities.Cargo.Updaters;
using Game.Battle.EntityModel.Cargo;
using Game.Battle.EntityModel.LocationEventCargo;
using Game.Battle.Target;
using Game.Global;
using Game.Targets;
using Models.Models.CrouchStandUpModel;
using Models.Models.LocationEventCargoSpawnPointModel;
using Models.Models.PositionModel;
using Models.References;
using Models.References.Cargo;
using UnityEngine;
using Utils.TypeCastExtensions;

namespace Game.Battle.LocationEvents
{
    public class LocationEventCargoViewUpdater: IUpdater
    {
        private readonly IUpdater _updater;
        private readonly ILocationEventCargoSpawnPointModel _locationEventCargoModel;
        private readonly CargoView _view;
        private readonly BattleModel _battleModel;
        private readonly TargetSubsystem<Collider, UnityRaycastTargetOptions> _targetSubsystem;
        private readonly PingMarkerCollidersModel _pingMarkerCollidersModel;
        private readonly LocationEventCargoTargetActions _targetActions;
        private readonly LocationEventCargoTargetProvider _targetProvider;
        private PlayerRoleDescription _playerRole;
        private bool _isActive;

        public LocationEventCargoViewUpdater(LocationEventCargoSpawnPointEntityModel entityModel, CargoView view, IRegisterDisposable registerDisposable, BattleModel battleModel, BattleScreenContent battleScreenContent, GlobalModel globalModel,
            TargetSubsystem<Collider, UnityRaycastTargetOptions> targetSubsystem, ICrouchStandUpModel crouchStandUpModel)
        {
            _locationEventCargoModel = entityModel.ServerModel;
            _view = view;
            _battleModel = battleModel;
            _targetSubsystem = targetSubsystem;
            _pingMarkerCollidersModel = battleModel.PingMarkerCollidersModel;

            _targetActions = new LocationEventCargoTargetActions(battleModel, globalModel.PersonalNotificationModel, globalModel.UISoundModel, entityModel.Id, entityModel.ServerModel.Description.CargoDescription, battleScreenContent);
            _targetProvider = new LocationEventCargoTargetProvider(entityModel.Id, _targetActions, battleModel.BattleEntitiesModel, crouchStandUpModel, battleScreenContent.LocalizationKeys.TargetActions.UnavailableKeys);

            IPositionModel positionModel = new PositionModel(entityModel.ServerModel.Description.InteractionPoint);
            var rotation = Quaternion.identity;
            if (_battleModel.LocationDescription.TryGetLocationEventCargo(entityModel.Id, out LocationEventCargoSpawnPointDescription locationEventCargoDescription))
            {
                rotation = Quaternion.Euler(locationEventCargoDescription.Rotation.ToVector3());
            }
            _updater = new CargoPositionViewUpdater(positionModel, rotation, Vector3.zero, view);

            view.SetVisibleView(false);
            view.SetCameraCollisionsEnabled(false);
            SetActiveState();

            registerDisposable.Register(() =>
            {
                RemoveInteractions();
                view.SetVisibleView(true);
                battleScreenContent.CargoViewDescriptions.DropEvent.Stop(_view.gameObject);
            });
        }

        public void Update()
        {
            _updater.Update();
            SetActiveState();
        }

        private void SetActiveState()
        {
            if (_battleModel.BattleEntitiesModel.Player is { } player)
            {
                bool isActive = _locationEventCargoModel.IsActive;
                var playerRole = player.ServerModel.Role.Value;

                if (_isActive != isActive || _playerRole != playerRole)
                {
                    _isActive = isActive;
                    _playerRole = playerRole;

                    bool isInteractable = isActive && player.ServerModel.Role.Value.CanTakeCargo;
                    if (isInteractable)
                    {
                        AddInteractions();
                    }
                    else
                    {
                        RemoveInteractions();
                    }
                    _view.SetActive(isActive);
                }
            }
        }

        private void AddInteractions()
        {
            _pingMarkerCollidersModel.AddWithDefaultOffset(_view.Collider, PingMarkTypeDescription.Loot);
            _targetSubsystem.Add(_view.Collider, _targetProvider);
            _battleModel.TargetHintModel.Add(_view.Collider, _view.TargetHintPositionView, null, _targetActions.ActionSet);
        }

        private void RemoveInteractions()
        {
            _pingMarkerCollidersModel.Remove(_view.Collider);
            _targetSubsystem.Remove(_view.Collider);
            _battleModel.TargetHintModel.Remove(_view.Collider);
        }
    }
}