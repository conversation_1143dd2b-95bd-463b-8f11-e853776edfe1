using ColorTheme;
using Content.ContentWrappers;
using Content.PoolCallbackReceiver;
using Core;
using Game.Battle.EntityModel.Cargo;
using Game.Battle.EntityModel.CargoExchanger;
using Game.Battle.EntityModel.Plot;
using Models.References;
using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Localization;
using Event = AK.Wwise.Event;

namespace Game.Battle.LocationEvents
{
    public class LocationEventsViewDescription : ScriptableObject, ISerializationCallbackReceiver
    {
        [SerializeField] private AssetReference _takeCargoPrefab;
        [field: SerializeField] public LocalizedString TakeCargoDescription { get; set; }
        [field: SerializeField] public Event TakeCargoEvent { get; private set; }
        [SerializeField] private AssetReference _takeCashPrefab;
        [field: SerializeField] public PaletteColor PrepareColor { get; private set; }
        [field: SerializeField] public PaletteColor ActiveColor {get; private set;}
        [SerializeField] private AssetReference _interacCargoPointPrefab;
        [Tooltip("Время указанно в милисекундах(1сек = 1000мс)")]
        [field: SerializeField] public long EndEventTimeDuration { get; private set; } = 3000;
        [field: SerializeField] public LocationEventViewDescription[] Events { get; set; }
        [field: SerializeField] public LocalizedString NoRobberEventsMessage { get; set; }
        [field: SerializeField] public LocalizedString GetRobberRoleMessage { get; set; }
        private readonly Dictionary<LocationEventDescription, LocationEventViewDescription> _events = new Dictionary<LocationEventDescription, LocationEventViewDescription>();
        
        public LocationEventViewDescription this[LocationEventDescription eventDescription] => _events[eventDescription];
        
        public PooledLoadedComponentContent<CargoExchangerView> TakeCargo { get; private set; }
        public PooledLoadedComponentContent<CashHeapView> TakeCash { get; private set; }
        public PooledLoadedComponentContent<CargoView> InteractCargoPoint { get; private set; }

        public void OnBeforeSerialize()
        {
        }

        public void OnAfterDeserialize()
        {
            _events.Clear();
            foreach (var eventViewDescription in Events)
            {
                _events.Add(LocationEventDescription.Enum[eventViewDescription.Event.Id], eventViewDescription);
            }
            TakeCargo = new PooledLoadedComponentContent<CargoExchangerView>(_takeCargoPrefab, 0, PoolHideCallbackReceiver.Instance);
            TakeCash = new PooledLoadedComponentContent<CashHeapView>(_takeCashPrefab, 0, PoolHideCallbackReceiver.Instance);
            InteractCargoPoint = new PooledLoadedComponentContent<CargoView>(_interacCargoPointPrefab, 0, PoolHideCallbackReceiver.Instance);
        }
    }

    [Serializable]
    public class LocationEventViewDescription
    {
        [field: SerializeField] public IdentifiedProperty<LocationEventDescription> Event { get; set; }
        [field: SerializeField] public LocalizedString PrepareDescription { get; set; }
        [field: SerializeField] public LocalizedString Description { get; set; }
        [field: SerializeField] public LocalizedString Title { get; set; }
        [field: SerializeField] public Sprite Icon { get; set; }
        [field: SerializeField] public Vector3 EntityMarkerPositionOffset { get; set; }
    }
}