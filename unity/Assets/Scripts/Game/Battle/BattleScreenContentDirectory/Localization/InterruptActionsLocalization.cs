using UnityEngine;
using UnityEngine.Localization;

namespace Game.Battle
{
    [CreateAssetMenu(fileName = "InterruptActionsLocalization", menuName = "ScriptableObjects/Localizations/InterruptActionsLocalization")]
    public class InterruptActionsLocalization : ScriptableObject
    {
        [field: SerializeField] public LocalizedString TakeCargo { get; private set; }
        [field: SerializeField] public LocalizedString TakeLootObject { get; private set; }
        [field: SerializeField] public LocalizedString HideInObject { get; private set; }
        [field: SerializeField] public LocalizedString SecurityAlarmCommand { get; private set; }
        [field: SerializeField] public LocalizedString Unlock { get; private set; }
        [field: SerializeField] public LocalizedString Arrest { get; private set; }
        [field: SerializeField] public LocalizedString InterruptArrested { get; private set; }
        [field: SerializeField] public LocalizedString ReviveVigilantNpc { get; private set; }
        [field: SerializeField] public LocalizedString TakeLootNode { get; private set; }
        [field: SerializeField] public LocalizedString ReviveCharacter { get; private set; }
        [field: SerializeField] public LocalizedString ArrestLoot { get; private set; }
        [field: SerializeField] public LocalizedString RefillEquip { get; private set; }
        [field: SerializeField] public LocalizedString SetDrill { get; private set; }
        [field: SerializeField] public LocalizedString RemoveDrill { get; private set; }
        [field: SerializeField] public LocalizedString TakeMoneyWorkbench { get; private set; }
        [field: SerializeField] public LocalizedString BecomeRobber { get; private set; }
        [field: SerializeField] public LocalizedString CopEquip { get; private set; }
        [field: SerializeField] public LocalizedString SetHandcuff { get; private set; }
        [field: SerializeField] public LocalizedString RepairBreach { get; private set; }
        [field: SerializeField] public LocalizedString ChargeCumulative { get; private set; }
    }
}