using Game.Global.Ui;
using UI;
using UnityEngine;
using UnityEngine.Localization.Components;
using Utils.UiState;

namespace Game.Battle.Ui.HUD
{
    public class ClanBattleQuestPanelView : MonoBehaviour
    {
        [SerializeField] private StateGroup _state;
        [SerializeField] private StateGroup _typeStateGroup;
        [SerializeField] private TextLabel<int, int> _timerLabel;
        [field: SerializeField] public LocalizeStringEvent Phase { get; private set; }
        [field: SerializeField] public InputTextView InputTextView { get; private set; }

        public void SetTimer(long milliseconds)
        {
            var seconds = Mathf.CeilToInt(milliseconds / 1000f);
            _timerLabel.Set(seconds / 60, seconds % 60);
        }

        public void SetPhase(PhaseState state)
        {
            _state.State = (int)state;
        }

        public void SetType(TypeState state)
        {
            _typeStateGroup.State = (int)state;
        }

        public enum PhaseState
        {
            Preparation,
            Process,
            FinishingProcess,
            Completion,
        }

        public enum TypeState
        {
            Attacker,
            Defender,
            Neutral,
        }
    }
}