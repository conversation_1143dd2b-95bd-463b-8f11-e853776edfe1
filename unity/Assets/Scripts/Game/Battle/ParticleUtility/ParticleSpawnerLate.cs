using System.Collections.Generic;
using UnityEngine;

namespace Game.Battle.ParticleUtility
{
    public class ParticleSpawnerLate : MonoBehaviour
    {
        private readonly Dictionary<ParticleSystem, float> _playingParticles = new();
        private readonly HashSet<ParticleSystem> _stoppingParticles = new();
        private readonly List<(ParticleSystem, Transform)> _lateEmitted = new();

        public void Play(ParticleSystem particleSystem)
        {
            ResetParticleSystem(particleSystem);
            float ts = Time.time + particleSystem.main.duration;
            _playingParticles[particleSystem] = ts;
        }

        public void Stop(ParticleSystem particleSystem)
        {
            if (_playingParticles.ContainsKey(particleSystem))
            {
                _stoppingParticles.Add(particleSystem);
            }
        }

        public void Emit(ParticleSystem particleSystem, Transform parent)
        {
            _lateEmitted.Add((particleSystem, parent));
        }

        public void Clear()
        {
            _playingParticles.Clear();
            _lateEmitted.Clear();
        }

        private void Update()
        {
            foreach (var (particleSystem, ts) in _playingParticles)
            {
                bool isFinished = ts <= Time.time;
                if (isFinished)
                {
                    _stoppingParticles.Add(particleSystem);
                }
            }

            foreach (var particleSystem in _stoppingParticles)
            {
                _playingParticles.Remove(particleSystem);
                ResetParticleSystem(particleSystem);
            }
            _stoppingParticles.Clear();
        }

        private void LateUpdate()
        {
            foreach (var (particleSystem, _) in _playingParticles)
            {
                SimulateParticleSystem(particleSystem);
            }

            foreach (var (particleSystem, parent) in _lateEmitted)
            {
                particleSystem.transform.position = Vector3.zero;
                particleSystem.Emit(new ParticleSystem.EmitParams
                {
                    position = parent.position,
                    velocity = parent.forward * particleSystem.main.startSpeed.Evaluate(Random.value)
                }, 1);
            }
            _lateEmitted.Clear();
        }

        private void ResetParticleSystem(ParticleSystem particleSystem)
        {
            particleSystem.Simulate(0, true, true);
        }

        private void SimulateParticleSystem(ParticleSystem particleSystem)
        {
            particleSystem.Simulate(Time.deltaTime, true, false);
        }
    }
}
