using Core;
using Core.Updater;
using Game.Global.Ui;
using UI;
using UnityEngine;

namespace Game.Battle.Map
{
    internal class KeyboardMouseCargoTooltipInputUpdater: IUpdater
    {
        private readonly PointerButtonsCargoModel _pointerButtonsCargoModel;
        private readonly TooltipModel _tooltipModel;
        private PointerButtonView _view;

        public KeyboardMouseCargoTooltipInputUpdater(PointerButtonsCargoModel pointerButtonsCargoModel, TooltipModel tooltipModel)
        {
            _pointerButtonsCargoModel = pointerButtonsCargoModel;
            _tooltipModel = tooltipModel;
        }

        public void Update()
        {
            foreach (var (view, item) in _pointerButtonsCargoModel.Dictionary)
            {
                if (view.IsHover
                    && !view.IsTouch
                    && view.HoverTime > TooltipRules.TooltipEnterTime
                    && !_tooltipModel.IsOpen)
                {
                    var transform = (RectTransform) view.transform;
                    _tooltipModel.Position = transform.GetWorldPosition();
                    _tooltipModel.Intention = TooltipModel.ScreenIntention.OpenDescription;
                    _tooltipModel.OpenCargo(item);
                    _tooltipModel.ItemSize = transform.GetWorldSize();
                    _view = view;
                }
            }

            if (_view != null
                && !_view.IsHover
                && _tooltipModel.IsOpen 
                && !_tooltipModel.IsActionsOpen)
            {
                _view = null;
                _tooltipModel.Intention = TooltipModel.ScreenIntention.Close;
            }
        }
    }
}