using System.Collections.Generic;
using Game.Global;
using UnityEngine;
using Utils.UiState;

namespace Game.Battle.Map
{
    public class MapObjectivesPanelView : MonoBehaviour
    {
        [field: SerializeField, EnumInfo(typeof(ObjectivesPanelVisibility))] public EnumStateElement<ObjectivesPanelVisibility> PanelState { get; private set; }
        [field: SerializeField, EnumInfo(typeof(ObjectivesTab))] public EnumStateElement<ObjectivesTab> TabsState { get; private set; }
        [field: SerializeField] public List<TabView<ObjectivesTab>> ObjectivesTabs { get; private set; }
        
        [field: SerializeField] public MapQuestPanelView QuestPanel { get; private set; }
        [field: SerializeField] public List<MapCollectionPanelView> CollectionPanels { get; private set; } 
        [field: SerializeField] public MapEventPanelView EventPanel { get; private set; }
        
        public enum ObjectivesTab
        {
            Quests = 0,
            Collections = 1,
            Events = 2,
        }
        
        public enum ObjectivesPanelVisibility
        {
            Collapsed = 0,
            Shown = 1,
        }
    }
}