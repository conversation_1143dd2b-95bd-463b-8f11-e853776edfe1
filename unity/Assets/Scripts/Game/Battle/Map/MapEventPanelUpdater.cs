using Core.Updater;
using Framework.Core.Now;
using Game.Battle.LocationEvents;
using Game.Locations;
using Game.Locations.BuildingDifficulty;
using Models.References;
using Models.References.Building;
using Core;

namespace Game.Battle.Map
{
    public class MapEventPanelUpdater : IUpdater
    {
        private readonly MapEventPanelView _mapEventPanelView;
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly LocationScreenViewModel _locationScreenViewModel;
        private readonly LocationEventModel _locationEventModel;
        private readonly LocationEventsViewDescription _locationEventsViewDescription;
        private readonly INow _time;
        private PlayerRoleDescription _currentRole = null;
        private bool _hasEvent = false;
        private bool _isPreparing = false;

        public MapEventPanelUpdater(MapEventPanelView mapEventPanelView, IBattleEntitiesModel battleEntitiesModel, LocationScreenViewModel locationScreenViewModel, LocationEventModel locationEventModel, LocationEventsViewDescription locationEventsViewDescription, INow time)
        {
            _mapEventPanelView = mapEventPanelView;
            _battleEntitiesModel = battleEntitiesModel;
            _locationScreenViewModel = locationScreenViewModel;
            _locationEventModel = locationEventModel;
            _locationEventsViewDescription = locationEventsViewDescription;
            _time = time;
        }
        public void Update()
        {
            if (_battleEntitiesModel.Player is { } player)
            {
                var playerRole = player.ServerModel.Role.Value;
                bool roleChanged = false;
                if (playerRole != _currentRole)
                {
                    _currentRole = playerRole;
                    roleChanged = true;
                    _hasEvent = false;
                    if (playerRole != PlayerRoleDescription.Robber)
                    {
                        _mapEventPanelView.SetNoEventDescription(_locationEventsViewDescription.GetRobberRoleMessage);
                    }
                    else
                    {
                        if (!_locationEventModel.HasEvent)
                        {
                            _mapEventPanelView.SetNoEventDescription(_locationEventsViewDescription.NoRobberEventsMessage);
                        }
                    }
                }

                if (playerRole == PlayerRoleDescription.Robber || roleChanged)
                {
                    if (_hasEvent != _locationEventModel.HasEvent)
                    {
                        _hasEvent = _locationEventModel.HasEvent;
                        if (_hasEvent)
                        {
                            var time = _time.Get();
                            var viewData = _locationEventsViewDescription[_locationEventModel.Description];
                            if (_locationEventModel.EndPrepareTime >= time)
                            {
                                _mapEventPanelView.SetEventDescription(viewData.Title, viewData.PrepareDescription);
                                _isPreparing = true;
                            }
                            else
                            {
                                _mapEventPanelView.SetEventDescription(viewData.Title, viewData.Description);
                                _isPreparing = false;
                            }

                            SetCustomEventParams();
                        }
                        else
                        {
                            _mapEventPanelView.SetNoEventDescription(_locationEventsViewDescription.NoRobberEventsMessage);
                        }
                    }

                    if (_hasEvent)
                    {
                        long currentTime = _time.Get();
                        long timeDelta = currentTime <= _locationEventModel.EndPrepareTime ? _locationEventModel.EndPrepareTime - currentTime : _locationEventModel.EndEventTime - currentTime;
                        _mapEventPanelView.UpdateTimer(timeDelta);
                        if (_isPreparing && currentTime > _locationEventModel.EndPrepareTime)
                        {
                            var viewData = _locationEventsViewDescription[_locationEventModel.Description];
                            _mapEventPanelView.SetEventDescription(viewData.Title, viewData.Description);
                            _isPreparing = false;
                        }
                    }
                }
            }
        }
        
        private void SetCustomEventParams()
        {
            if (_locationEventModel.Description == LocationEventDescription.UnlockContainer)
            {
                if (_locationEventModel.BuildingOwnerDescription != null)
                {
                    if (TryGetMapBuildingDescription(_locationEventModel.BuildingOwnerDescription, out MapBuildingDescription mapBuildingDescription))
                    {
                        _mapEventPanelView.AddEventDescriptionAdditiveParam(mapBuildingDescription.Title);
                    }
                }
            }
        }
        
        private bool TryGetMapBuildingDescription(LocationBuildingDescription description, out MapBuildingDescription mapBuildingDescription)
        {
            mapBuildingDescription = null;
            foreach (var building in _locationScreenViewModel.LocationMapBuildings)
            {
                if (building.View.Building.Id == description.Id)
                {
                    mapBuildingDescription = building.View.MapBuildingViewDescription;
                    return true;
                }
            }
            return false;
        }
    }
}