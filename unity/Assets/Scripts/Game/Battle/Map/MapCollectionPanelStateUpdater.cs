using Core.Updater;
using Models.Models.GatherHangarCollectionsModel;
using Models.References.Collection;

namespace Game.Battle.Map
{
    public class MapCollectionPanelStateUpdater : IUpdater
    {
        private readonly MapCollectionPanelView _collectionPanelView;
        private readonly MapObjectivesModel _mapObjectivesModel;
        
        private readonly bool _isLockedSlot;
        private readonly bool _isCollectionAvailableInThisSlot;
        private readonly int _slotIndex;
        
        public MapCollectionPanelStateUpdater(MapCollectionPanelView collectionPanelView, MapDialogModel mapDialogModel, IGatherHangarCollectionsModel gatherHangarCollection,
            CollectionTableSlotDescription slotDescription, int lockSlotIndex)
        {
            _collectionPanelView = collectionPanelView;
            _mapObjectivesModel = mapDialogModel.MapObjectivesModel;
            
            _isLockedSlot = lockSlotIndex == slotDescription.Index;
            _isCollectionAvailableInThisSlot = gatherHangarCollection.Slots[slotDescription].CollectionDescription != null;
            _slotIndex = slotDescription.Index;
        }
        
        public void Update()
        {
            MapCollectionPanelView.CollectionPanelState state = GetState();
            _collectionPanelView.SetCollectionState(state);
        }
        
        private MapCollectionPanelView.CollectionPanelState GetState()
        {
            bool isCollapsed = _mapObjectivesModel.CollectionPanelIsCollapsed[_slotIndex];
            return (_isCollectionAvailableInThisSlot, _isLockedSlot, isCollapsed) switch
            {
                (false, false, _) => MapCollectionPanelView.CollectionPanelState.CompletelyHidden,
                (_, _, true) => MapCollectionPanelView.CollectionPanelState.Collapsed,
                (false, true, _) => MapCollectionPanelView.CollectionPanelState.ShouldBeUnlockedHint,
                (true, _, _) => MapCollectionPanelView.CollectionPanelState.Available,
            };
        }
    }
}