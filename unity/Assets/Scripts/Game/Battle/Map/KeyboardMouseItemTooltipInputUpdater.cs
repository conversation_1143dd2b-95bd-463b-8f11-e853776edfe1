using Core;
using Core.Updater;
using Game.Battle.Inventory;
using Game.Global.Ui;
using UI;
using UnityEngine;

namespace Game.Battle.Map
{
    internal class KeyboardMouseItemTooltipInputUpdater : IUpdater
    {
        private readonly PointerButtonsItemModel _pointerButtonsItemModel;
        private readonly TooltipModel _tooltipModel;
        private PointerButtonView _view;

        public KeyboardMouseItemTooltipInputUpdater(PointerButtonsItemModel pointerButtonsItemModel, TooltipModel tooltipModel)
        {
            _pointerButtonsItemModel = pointerButtonsItemModel;
            _tooltipModel = tooltipModel;
        }

        public void Update()
        {
            foreach (var (view, item) in _pointerButtonsItemModel.Dictionary)
            {
                if (view.IsHover
                    && !view.IsTouch
                    && view.HoverTime > TooltipRules.TooltipEnterTime
                    && !_tooltipModel.IsOpen)
                {
                    var transform = (RectTransform) view.transform;
                    _tooltipModel.Position = transform.GetWorldPosition();
                    _tooltipModel.Intention = TooltipModel.ScreenIntention.OpenDescription;
                    _tooltipModel.ItemSize = transform.GetWorldSize();
                    _tooltipModel.OpenMapInventoryItem(item);
                    _view = view;
                }
            }

            if (_view != null
                && !_view.IsHover
                && _tooltipModel.IsOpen
                && !_tooltipModel.IsActionsOpen)
            {
                _view = null;
                _tooltipModel.Intention = TooltipModel.ScreenIntention.Close;
            }
        }
    }
}