using TMPro;
using UI;
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Components;
using Utils.UiState;

namespace Game.Battle.Map
{
    public class MapEventPanelView : MonoBehaviour
    {
        [SerializeField] private LocalizeStringEvent _noEventDescription;
        [SerializeField] private LocalizeStringEvent _eventNameText;
        [SerializeField] private LocalizeStringEvent _eventDescriptionText;
        [SerializeField] private TMP_Text _eventDescriptionTMPText;
        [SerializeField] private TextMeshProUGUI _eventTimerText;
        [SerializeField] private StateGroup _eventState;
        private LocalizedString _eventDescriptionAdditiveParam;
        
        private void Awake()
        {
            _eventDescriptionText.OnUpdateString.AddListener(TryUpdateEventDescriptionWithLocalizedParam);
        }

        public void SetNoEventDescription(LocalizedString noEventDescription)
        {
            _eventState.State = (int)State.Empty;
            _noEventDescription.StringReference = noEventDescription;
        }

        public void SetEventDescription(LocalizedString eventName, LocalizedString eventDescription)
        {
            _eventState.State = (int)State.InProgress;
            _eventDescriptionAdditiveParam = null;
            _eventNameText.StringReference = eventName;
            _eventDescriptionText.StringReference = eventDescription;
        }
        
        public void UpdateTimer(long time)
        {
            _eventTimerText.text = TimeSetDescription.GetTimerText(time, TimeSetDescription.TimeFormat.Dynamic);
        }
        
        public void AddEventDescriptionAdditiveParam(LocalizedString eventDescriptionAdditiveParam)
        {
            _eventDescriptionAdditiveParam = eventDescriptionAdditiveParam;
            TryUpdateEventDescriptionWithLocalizedParam(null);
        }
        
        private void TryUpdateEventDescriptionWithLocalizedParam(string arg0)
        {
            if (_eventDescriptionAdditiveParam != null)
            {
                var descriptionLocalizedParam = _eventDescriptionAdditiveParam.GetLocalizedString();
                _eventDescriptionTMPText.text = _eventDescriptionText.StringReference.GetLocalizedString(descriptionLocalizedParam);
            }
        }

        private void OnDestroy()
        {
            _eventDescriptionText.OnUpdateString.RemoveListener(TryUpdateEventDescriptionWithLocalizedParam);
        }

        private enum State
        {
            Empty = 0,
            InProgress = 1,
        }
    }
}