using System;
using Core.Updater;
using Framework.Core.Either;
using Framework.Value.Values;
using Models.Messages;
using Models.Models.PhysicsDebug.Models.PhysicsDebugEnabledModel;
using Network.Connection;
using PhysicsDebugModel = Game.Battle.PhysicsDebug.Model.PhysicsDebugModel;

namespace Game.Battle.PhysicsDebug.Updater
{
    public class PhysicsDebugSendEnableUpdater : IUpdater
    {
        private readonly PhysicsDebugModel _model;
        private readonly IPhysicsDebugEnabledModel _physicsDebugEnabledModel;
        private readonly IRoomConnection _roomConnection;

        private bool _lastEnabledState;

        public PhysicsDebugSendEnableUpdater(PhysicsDebugModel model, IPhysicsDebugEnabledModel physicsDebugEnabledModel, IRoomConnection roomConnection)
        {
            _model = model;
            _physicsDebugEnabledModel = physicsDebugEnabledModel;
            _roomConnection = roomConnection;
        }
        
        public void Update()
        {
            bool enabled = _model.IsEnabled;
            if (enabled != _lastEnabledState)
            {
                SendSetEnabled(enabled);
                _lastEnabledState = enabled;
            }
        }
        
        private async void SendSetEnabled(bool isEnabled)
        {
            IEither<IValue, IValue> sendResult = await _roomConnection.SendMessage(new CheatSetEnabledPhysicsDebugMessage(isEnabled));

            string result = sendResult.IsLeft ? "fail" : "success";
            IValue value = sendResult.IsLeft ? sendResult.AsLeft : sendResult.AsRight;
            Console.WriteLine($"CheatSetEnabledPhysicsDebugMessage set enable={isEnabled} - {result}: {value}");

            _physicsDebugEnabledModel.IsEnabled = isEnabled;
        }
    }
}