using Core;
using Core.Updater;
using Game.Battle.BattleScreenContentDirectory.Localization;
using Game.Battle.BecomeRobber;
using Game.Battle.Inventory;
using Game.Global.Ui;
using Game.Shared.Models;
using Game.Shared.Models.Screens;
using Models.Models.CargoInventorySlotModel;
using Models.References.Cargo;
using Models.References.Input;
using SharedType;
using UI;
using UnityEngine;

namespace Game.Battle.Trunk.Cargo
{
    public class KeyboardCargoSlotInputUpdater : IUpdater
    {
        private readonly CargoInventorySlotView _slotView;
        private readonly ICargoInventorySlotModel _slotModel;
        private readonly GlobalUISoundModel _uiSoundModel;
        private readonly InventoryDialogModel _inventoryDialogModel;
        private readonly BecomeRobberNotificationModel _becomeRobberNotificationModel;
        private readonly UnavailableTargetActionsKeys _unavailableKeys;
        private readonly PersonalNotificationModel _personalNotificationModel;
        private readonly BattleModel _battleModel;

        private CargoDescription _cargo;
        private bool _isSelectedForTooltip;
        private readonly TooltipModel _tooltipModel;

        public KeyboardCargoSlotInputUpdater(CargoInventorySlotView slotView, ICargoInventorySlotModel slotModel, GlobalUISoundModel uiSoundModel, InventoryDialogModel inventoryDialogModel,
            BecomeRobberNotificationModel becomeRobberNotificationModel, UnavailableTargetActionsKeys unavailableKeys, PersonalNotificationModel personalNotificationModel, BattleModel battleModel)
        {
            _slotView = slotView;
            _slotModel = slotModel;
            _uiSoundModel = uiSoundModel;
            _inventoryDialogModel = inventoryDialogModel;
            _becomeRobberNotificationModel = becomeRobberNotificationModel;
            _unavailableKeys = unavailableKeys;
            _personalNotificationModel = personalNotificationModel;
            _battleModel = battleModel;
            _tooltipModel = _battleModel.TooltipModel;
        }

        public void Update()
        {
            UpdateCargoTakeButton();
            UpdateCargoTooltipOpen();
            UpdateCargoTooltipClose();
        }

        private void UpdateCargoTakeButton()
        {
            var player = _battleModel.BattleEntitiesModel.Player; 
            
            if (_slotModel.Cargo != null && _slotView.ButtonTake.IsClicked)
            {
                _uiSoundModel.PlaySound(GlobalUISoundType.ButtonClick);
                
                if (!player.ServerModel.Role.Value.CanTakeCargo)
                {
                    if (player.ServerModel.Role.Value.CanBecomeRobber)
                    {
                        _becomeRobberNotificationModel.HasNewNotification = true;
                    }

                    _personalNotificationModel.Enqueue(_unavailableKeys.ChangeToRobberNotification.GetLocalizedString());
                }
                else if (player.ServerModel.InVehicleModel.IsInsideVehicle)
                {
                    _personalNotificationModel.Enqueue(_unavailableKeys.UnavailableInVehicle.GetLocalizedString());
                }
                else
                {
                    TakeCargo();
                }
            }
        }

        private void TakeCargo()
        {
            var slotId = _battleModel.BattleEntitiesModel.WorldEntitiesModel.CargoInventorySlotIds[_slotModel];
            _battleModel.BattleInputModel.UseEntityCommandInput = UseEntityCommandDescription.TakeCargoStart;
            _battleModel.BattleInputModel.UseEntityId = slotId;
            
            _inventoryDialogModel.Intention = IntentionScreen.Close;
        }

        private void UpdateCargoTooltipOpen()
        {
            PointerButtonView button = _slotView.PointerButtonView;

            bool isTooltipShown = !_slotModel.IsEmpty
                                  && button.IsHover
                                  && !button.IsTouch
                                  && button.HoverTime > TooltipRules.TooltipEnterTime
                                  && !_tooltipModel.IsOpen;

            if (isTooltipShown)
            {
                RectTransform rectTransform = (RectTransform) button.transform;
                Vector2 itemPosition = rectTransform.GetWorldPosition();

                _isSelectedForTooltip = true;

                _tooltipModel.Position = itemPosition;
                _tooltipModel.Intention = TooltipModel.ScreenIntention.OpenDescription;
                _tooltipModel.ItemSize = _slotView.RectTransform.GetWorldSize();
                _tooltipModel.OpenCargo(_slotModel.Cargo);

            }
        }

        private void UpdateCargoTooltipClose()
        {
            PointerButtonView button = _slotView.PointerButtonView;

            bool isTooltipClosed = !button.IsHover
                                   && _isSelectedForTooltip
                                   && _tooltipModel.IsOpen
                                   && !_tooltipModel.IsActionsOpen;

            if (isTooltipClosed)
            {
                _tooltipModel.Intention = TooltipModel.ScreenIntention.Close;
                _isSelectedForTooltip = false;
            }
        }
    }
}