using System;
using System.Collections.Generic;
using Content.Disposable;
using Core;
using Core.Updater;
using Game.Battle.Inventory;
using Game.Battle.PlanningBoard.PlanningSettlement;
using Game.Battle.UiUpdater;
using Game.Common;
using Game.Global;
using Game.Global.Sounds;
using Game.Shared.Models;
using Inputs;
using Models.References;
using Network.Connection;
using SharedType;
using UnityEngine;
using Utils.TypeCastExtensions;

namespace Game.Battle.PlanningBoard
{
    public class PlanningBoardDialogUpdater : IUpdater
    {
        private readonly IUpdater _updaters;
        private readonly IUpdater _planningBoardSettlementsUpdater;

        public PlanningBoardDialogUpdater(GlobalModel globalModel, BattleModel battleModel, InputActions inputActions, BattleScreenContent battleScreenContent,
            Transform dialogContainer, InputHandlersManager inputHandlersManager, GlobalScreenContent globalScreenContent, IRoomConnection roomConnectionBattle, IRegisterDisposable disposable)
        {
            var dialogModel = battleModel.PlanningBoardDialogModel;
            var updaters = new List<IUpdater>();

            IDisposableContent<PlanningBoardDialogView> content;
            ItemTooltipView tooltipView;
            IDisposable tooltipContent;

            updaters.Add(new TooltipClearUpdater(battleModel.TooltipModel));
            switch (globalModel.DeviceModel.CurrentDeviceId)
            {
                case DeviceId.KeyboardMouse:
                    content = battleScreenContent.KeyboardMousePlanningBoardDialogView.Generate(dialogContainer);
                    _planningBoardSettlementsUpdater = new PlanningBoardSettlementsUpdater(globalModel.SettlementOwnersModel, content.Value.PlanningSettlementView,
                        battleScreenContent.KeyboardSettlementCardView, globalScreenContent, globalModel.RegionUserTimeModel.ServerNow, disposable, battleScreenContent.SettlementBuildingViewDescriptions);
                    var keyboardTooltipContent = battleScreenContent.KeyboardMouseItemTooltipView.Generate(dialogContainer);
                    tooltipView = keyboardTooltipContent.Value;
                    tooltipContent = keyboardTooltipContent;

                    updaters.Add(new KeyboardTooltipIntentionUpdater(battleModel.TooltipModel, battleModel.InventoryModels));
                    break;
                case DeviceId.Touch:
                    content = battleScreenContent.TouchPlanningBoardDialogView.Generate(dialogContainer);
                    _planningBoardSettlementsUpdater = new PlanningBoardSettlementsUpdater(globalModel.SettlementOwnersModel, content.Value.PlanningSettlementView,
                        battleScreenContent.TouchSettlementCardView, globalScreenContent, globalModel.RegionUserTimeModel.ServerNow, disposable, battleScreenContent.SettlementBuildingViewDescriptions);
                    var touchTooltipContent = battleScreenContent.TouchItemTooltipView.Generate(dialogContainer);
                    tooltipView = touchTooltipContent.Value;
                    tooltipContent = touchTooltipContent;
                    updaters.Add(new TouchTooltipIntentionUpdater(battleModel.TooltipModel, battleModel.InventoryModels));
                    break;
                default:
                    throw new Exception("unknown device");
            }

            var view = content.Value;
            updaters.Add(new TooltipVisibleViewUpdater(tooltipView, battleModel.UISoundModel, battleModel.TooltipModel));
            updaters.Add(new TooltipViewUpdater(tooltipView, battleScreenContent, battleModel.TooltipModel, battleModel.BattleEntitiesModel, inputActions.Battle.SplitOnDrop, disposable));
            updaters.Add(new TooltipVisiblePositionUpdater(tooltipView, battleModel.TooltipModel, disposable));

            updaters.Add(new PlanningBoardContentVisibleUpdater(view, dialogModel.CurrentTab));
            updaters.Add(new ResetDialogByDistanceUpdater(battleModel.BattleEntitiesModel.Player.ServerModel, dialogModel, dialogModel.EntityPosition.ToSystemVector3(), BattleDistancesDescription.DefaultInteractSqrDistance));
            _updaters = new CompositeUpdater(updaters);

            var inputHandler = new PlanningBoardInputHandler(globalModel, dialogModel, inputActions, view);

            inputHandlersManager.Add(inputHandler);
            globalModel.CursorLockModel.Add(CursorLockKey.Dialog, CursorLockMode.None);
            battleModel.UISoundModel.PlaySound(BattleUISoundType.OpenMap);

            disposable.Register(() =>
            {
                globalModel.UISoundModel.PlaySound(GlobalUISoundType.CloseDialog);
                inputHandlersManager.Remove(inputHandler);
                globalModel.CursorLockModel.Remove(CursorLockKey.Dialog);
                content.Dispose();
                dialogModel.Reset();
                battleModel.TooltipModel.Clear();
                tooltipContent.Dispose();
            });
        }

        public void Update()
        {
            _updaters.Update();
            _planningBoardSettlementsUpdater.Update();
        }
    }
}