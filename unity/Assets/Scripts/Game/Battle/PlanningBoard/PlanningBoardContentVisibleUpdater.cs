using Core.Updater;
using Game.Global;

namespace Game.Battle.PlanningBoard
{
    public class PlanningBoardContentVisibleUpdater : IUpdater
    {
        private readonly PlanningBoardDialogView _view;
        private readonly TabData<PlanningBoardTabs> _dialogModelCurrentTab;

        public PlanningBoardContentVisibleUpdater(PlanningBoardDialogView view, TabData<PlanningBoardTabs> dialogModelCurrentTab)
        {
            _view = view;
            _dialogModelCurrentTab = dialogModelCurrentTab;
        }

        public void Update()
        {
            _view.SetTabActive(_dialogModelCurrentTab.Value);
        }
    }
}