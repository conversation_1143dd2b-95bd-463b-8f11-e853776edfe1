using Game.Battle.Inventory.Slot;
using UnityEngine;

namespace Game.Battle.Inventory
{
    public class InventoryUiView : MonoBehaviour
    {
        [field: SerializeField] public PocketsUiView PocketsUiView { get; private set; }
        [field: SerializeField] public EquipsUiView EquipsUiView { get; private set; }
        [field: SerializeField] public ScrolledCellsUiView InventoryCellsUiView { get; private set; }
        [field: SerializeField] public SlotDropInfoView SlotDropInfoView { get; private set; }
    }
}