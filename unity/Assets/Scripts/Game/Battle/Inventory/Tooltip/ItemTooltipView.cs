using Core;
using Game.Battle.Inventory.Slot;
using Game.Battle.TooltipHintButton;
using Game.Global.Rare;
using System.Collections.Generic;
using Models.References.Inventory;
using SharedType;
using TMPro;
using UI;
using UnityEngine;
using UnityEngine.Localization;
using Utils.UiState;

namespace Game.Battle.Inventory
{
    public class ItemTooltipView : MonoBehaviour, IItemRareView
    {
        private readonly Vector3[] _corners = new Vector3[4];

        [SerializeField] private TextMeshProUGUI _itemName;
        [SerializeField] private TextMeshProUGUI _itemDescription;
        [SerializeField] private TextMeshProUGUI _itemType;
        [SerializeField] private RectTransform _transform;
        [SerializeField] private RareView _imageRare;
        [SerializeField] private RareView _decorRare;
        [SerializeField] private RareView _nameRare;
        [SerializeField] private RectTransform[] _transformsForCorners;
        [SerializeField] private GameObject _itemTopStatsContainer;
        [SerializeField] private GameObject _itemLockOnLocationLevelHint;
        [SerializeField] private TextLabel<int> _itemLockOnLocationTextLabel;
        [SerializeField] private EnumStateElement<MoneyType> _moneyState;

        [field: SerializeField] public ClickButtonView UseButton { get; private set; }
        [field: SerializeField] public ClickButtonView EquipButton { get; private set; }
        [field: SerializeField] public ClickButtonView UnequipButton { get; private set; }
        [field: SerializeField] public ClickButtonView SplitButton { get; private set; }
        [field: SerializeField] public ClickButtonView DropButton { get; private set; }
        [field: SerializeField] public ClickButtonView SellButton { get; private set; }
        [field: SerializeField] public ClickButtonView MoveButton { get; private set; }
        [field: SerializeField] public ClickButtonView InstallButton { get; private set; }
        [field: SerializeField] public ClickButtonView BuyButton { get; private set; }
        [field: SerializeField] public ClickButtonView UnloadButton { get; private set; }
        [field: SerializeField] public ClickButtonView ReloadButton { get; private set; }
        [field: SerializeField] public ClickButtonView FindButton { get; private set; }
        [field: SerializeField] public ClickButtonView RepairButton { get; private set; }
        [field: SerializeField] public ClickButtonView CollectButton { get; private set; }
        [field: SerializeField] public List<ItemStatView> ItemStatViews { get; private set; }
        [field: SerializeField] public DamageDistanceGraphView DamageGraph { get; private set; }
        [field: SerializeField] public ArmorStatView ArmorStatView { get; private set; }
        [field: SerializeField] public TooltipCumulativeProgressView CumulativeProgressView { get; private set; }
        [field: SerializeField] public ItemStatView DurabilityStatView { get; private set; }
        [field: SerializeField] public TooltipPriceStatView CostPriceStatView { get; private set; }
        [field: SerializeField] public GameObject PriceHolder { get; private set; }
        [field: SerializeField] public TooltipRequirementView RequirementView { get; private set; }
        [field: SerializeField] public TooltipHintButtonView TooltipHintButtonView { get; private set; }
        [field: SerializeField] public TooltipVehicleView VehicleView { get; private set; }
        [field: SerializeField] public TooltipCarUpgradeView VehicleUpgradeView { get; private set; }
        [field: SerializeField] public RectChangeListener RectChangeListener { get; private set; }

        [SerializeField] private GameObject _actionsContainer;
        [SerializeField] private GameObject _descriptionContainer;

        private GameObject _gameObject;
        private GameObject CachedGameObject => _gameObject ??= gameObject;
        private int _lastLockLevelIndex = -1;

        public TextMeshProUGUI ItemName => _itemName;
        
        public void SetActive(bool isActive, bool isActionContainerActive, bool isDescriptionContainerActive)
        {
            CachedGameObject.SetActive(isActive);
            
            if (isActive)
            {
                if (_actionsContainer.activeSelf != isActionContainerActive)
                {
                    _actionsContainer.SetActive(isActionContainerActive);
                }
            
                if (_descriptionContainer.activeSelf != isDescriptionContainerActive)
                {
                    _descriptionContainer.SetActive(isDescriptionContainerActive);
                }
            }
        }

        public void SetTopStatsActive(bool isActive)
        {
            if (_itemTopStatsContainer.activeSelf != isActive)
            {
                _itemTopStatsContainer.SetActive(isActive);
            }
        }

        public Vector2 GetCurrentSize()
        {
            var corners = GetCorners();
            return new Vector2(corners.Item2.x - corners.Item1.x, corners.Item2.y - corners.Item1.y);
        }

        public (Vector2, Vector2) GetCorners()
        {
            foreach (var rect in _transformsForCorners)
            {
                if (rect.gameObject.activeSelf)
                {
                    rect.GetWorldCorners(_corners);
                    return (_corners[0], _corners[2]);
                }
            }

            return default;
        }

        public void SetPosition(Vector2 position)
        {
            _transform.SetWorlPositionIgnorePivot(position);
        }
        
        public void SetName(LocalizedString title)
        {
            _itemName.text = title.GetLocalizedString();
        }

        public void SetDescription(string description)
        {
            _itemDescription.text = description;
        }

        public void SetType(string itemType)
        {
            _itemType.text = itemType;
        }

        public void SetRarity(RarityItem rareItem)
        {
            _imageRare.SetRarity(rareItem);
            _decorRare.SetRarity(rareItem);
            _nameRare.SetRarity(rareItem);
        }

        public void SetLockOnLocationHint(bool active, int level)
        {
            if (_itemLockOnLocationLevelHint.activeSelf != active)
            {
                _itemLockOnLocationLevelHint.SetActive(active);
            }

            if (_lastLockLevelIndex != level)
            {
                _itemLockOnLocationTextLabel.Set(level);
                _lastLockLevelIndex = level;
            }
        }

        public void SetMonetState(MoneyType state)
        {
            _moneyState.State = state;
        }
    }
}