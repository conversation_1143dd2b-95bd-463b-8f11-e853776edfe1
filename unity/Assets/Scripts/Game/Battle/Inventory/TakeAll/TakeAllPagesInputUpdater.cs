using System.Collections.Generic;
using Core.Updater;
using Game.Battle.BattleScreenContentModels;
using Game.Battle.EntityModel.Player;
using Game.Battle.Inventory.Pages;
using Game.Shared.Models;
using Models.Models.InventoryModel;
using UnityEngine.InputSystem;

namespace Game.Battle.Inventory
{
    public class TakeAllPagesInputUpdater : IUpdater
    {
        private readonly ItemInventoryPaginatorModel _paginatorModel;
        private readonly InputAction _takeAll;
        private readonly ItemMoveModel _itemMoveModel;
        private readonly PersonalNotificationModel _personalNotificationModel;
        private readonly NotificationError _notificationError;
        private readonly InventoryDragAndDropModel _inventoryDragAndDropModel;
        private readonly PlayerClientModel _playerClientModel;

        private readonly List<ExternalItemMoveContext> _takeItems = new();

        public TakeAllPagesInputUpdater(ItemInventoryPaginatorModel paginatorModel, InputAction takeAll, ItemMoveModel itemMoveModel, PersonalNotificationModel personalNotificationModel,
            NotificationError notificationError, InventoryDragAndDropModel inventoryDragAndDropModel, PlayerClientModel playerClientModel)
        {
            _paginatorModel = paginatorModel;
            _takeAll = takeAll;
            _itemMoveModel = itemMoveModel;
            _personalNotificationModel = personalNotificationModel;
            _notificationError = notificationError;
            _inventoryDragAndDropModel = inventoryDragAndDropModel;
            _playerClientModel = playerClientModel;
        }

        public void Update()
        {
            if (_takeAll.WasPerformedThisFrame())
            {
                _takeItems.Clear();
                _inventoryDragAndDropModel.Clear();

                for (int index = _paginatorModel.PageIndex; index < _paginatorModel.TotalPages; index++)
                {
                    FillByPage(_paginatorModel.GetPageModel(index), _paginatorModel.PagesEntitiesIds[index]);
                }

                for (int index = 0; index < _paginatorModel.PageIndex; index++)
                {
                    FillByPage(_paginatorModel.GetPageModel(index), _paginatorModel.PagesEntitiesIds[index]);
                }

                var inventoryIntention = _playerClientModel.InventoryIntention;
                if (inventoryIntention.TakeItems(_takeItems, out var action, out var errorType))
                {
                    _itemMoveModel.NewInput = action;
                }

                if (errorType != NotificationError.Type.None)
                {
                    _personalNotificationModel.Enqueue(_notificationError.GetErrorText(errorType));
                }
            }
        }

        private void FillByPage(IInventoryModel inventoryModel, int entityId)
        {
            var slots = inventoryModel.Slots;
            foreach (var slot in slots.Values)
            {
                var context = new ExternalItemMoveContext(entityId, inventoryModel, slot);
                if (!slot.ItemModel.IsEmpty && !_playerClientModel.BlockedSlotsModel.IsBlocked(context))
                {
                    _takeItems.Add(context);
                }
            }
        }
    }
}