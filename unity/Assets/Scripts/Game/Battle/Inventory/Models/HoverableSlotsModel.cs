using System.Collections.Generic;
using UI;

namespace Game.Battle.Inventory
{
    public class HoverableSlotsModel
    {
        private readonly Dictionary<IItemMoveContext, PointerButtonView> _items = new ();

        public void Add(IItemMoveContext model, PointerButtonView view)
        {
            _items.Add(model, view);
        }

        public void Remove(IItemMoveContext model)
        {
            _items.Remove(model);
        }

        public IReadOnlyDictionary<IItemMoveContext, PointerButtonView> Items => _items;
    }
}