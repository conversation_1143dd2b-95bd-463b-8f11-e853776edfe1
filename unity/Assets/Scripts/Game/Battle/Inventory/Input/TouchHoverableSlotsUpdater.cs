using Core;
using Core.Updater;
using Game.Global.Ui;
using UnityEngine;

namespace Game.Battle.Inventory
{
    public class TouchHoverableSlotsUpdater : IUpdater
    {
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly TooltipMiniModel _tooltipModel;
        private readonly InventoryModels _inventoryModels;

        public TouchHoverableSlotsUpdater(InventoryModels inventoryModels, IBattleEntitiesModel battleEntitiesModel, TooltipMiniModel tooltipModel)
        {
            _battleEntitiesModel = battleEntitiesModel;
            _tooltipModel = tooltipModel;
            _inventoryModels = inventoryModels;
        }

        public void Update()
        {
            var player = _battleEntitiesModel.Player;
            if (player != null)
            {
                foreach (var (context, view) in _inventoryModels.HoverableSlotsModel.Items)
                {
                    if (!_tooltipModel.IsOpen && view.IsTouch && view.TimePressed > TooltipRules.TooltipEnterTime && !player.ClientModel.BlockedSlotsModel.IsBlocked(context))
                    {
                        var targetTransform = view.transform as RectTransform;
                        _tooltipModel.Open(targetTransform.GetWorldPosition(), targetTransform.GetWorldSize(), new TooltipMiniContext.InventoryItem(context), TooltipPositionSettings.MiniInfo);
                    }
                }
            }
        }
    }
}