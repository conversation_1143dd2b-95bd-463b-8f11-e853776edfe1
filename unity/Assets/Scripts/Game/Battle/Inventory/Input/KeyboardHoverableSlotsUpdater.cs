using Core;
using Core.Updater;
using Game.Global.Ui;
using UnityEngine;

namespace Game.Battle.Inventory
{
    public class KeyboardHoverableSlotsUpdater : IUpdater
    {
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly TooltipMiniModel _tooltipModel;
        private readonly InventoryModels _inventoryModels;

        public KeyboardHoverableSlotsUpdater(InventoryModels inventoryModels, BattleModel battleModel, TooltipMiniModel tooltipModel)
        {
            _inventoryModels = inventoryModels;
            _battleEntitiesModel = battleModel.BattleEntitiesModel;
            _tooltipModel = tooltipModel;
        }

        public void Update()
        {
            var player = _battleEntitiesModel.Player;
            if (player != null)
            {
                foreach (var (context, view) in _inventoryModels.HoverableSlotsModel.Items)
                {
                    if (!_tooltipModel.IsOpen && view.IsHover && view.HoverTime > TooltipRules.TooltipEnterTime && !player.ClientModel.BlockedSlotsModel.IsBlocked(context))
                    {
                        _inventoryModels.InventorySelectedModel.SetItemContext(context);
                        _inventoryModels.InventorySelectedModel.NeedHighlightItem = false;

                        var targetTransform = view.transform as RectTransform;
                        _tooltipModel.Open(targetTransform.GetWorldPosition(), targetTransform.GetWorldSize(), new TooltipMiniContext.InventoryItem(context), TooltipPositionSettings.MiniInfo);
                    }

                    if (_tooltipModel.IsOpen && !view.IsHover && _tooltipModel.IsOpenedContext(context))
                    {
                        _tooltipModel.Clear();
                    }
                }
            }
        }
    }
}