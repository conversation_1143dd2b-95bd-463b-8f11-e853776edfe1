using Core;
using Core.Updater;
using Game.Battle.BattleScreenContentModels;
using Game.Battle.Entities.Plot;
using Game.Battle.EntityModel.CargoWorkbenchSlot.Models;
using Game.Battle.EntityModel.CarTrunk.Models;
using Game.Battle.EntityModel.Container;
using Game.Battle.EntityModel.Loot;
using Game.Battle.EntityModel.Plot;
using Game.Battle.EntityModel.PlotContainer;
using Game.Battle.EntityModel.PlotVehicleTrunk.Models;
using Game.Battle.EntityModel.StorageBox.Models;
using Game.Battle.EntityModel.VehicleLoot;
using Game.Global.Ui;
using Game.Shared.Models;
using Models.Models.CarUpgradesHolderModel;
using Models.Models.RegionUserBattleRoomModel;
using Models.Models.VehicleTrunkInventoriesModel;
using SharedType;
using System;
using UI;
using UnityEngine;
using UnityEngine.EventSystems;

namespace Game.Battle.Inventory
{
    public class KeyboardClickableItemsUpdater : IUpdater
    {
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly InputActions.BattleActions _battleActions;
        private readonly PersonalNotificationModel _personalNotificationModel;
        private readonly NotificationError _notificationError;
        private readonly BattleUISoundModel _uiSoundModel;
        private readonly IRegionUserBattleRoomModel _battleRoomModel;
        private readonly TooltipModel _tooltipModel;
        private readonly CurrentInventoryModel _currentInventoryModel;
        private readonly LocalizationKeys _localization;

        private float _lastClickTime;
        private readonly InventoryModels _inventoryModels;

        public KeyboardClickableItemsUpdater(InventoryModels inventoryModels, CurrentInventoryModel currentInventoryModel, BattleModel battleModel, InputActions.BattleActions battleActions,
            PersonalNotificationModel personalNotificationModel, NotificationError notificationError, BattleScreenContent content, IRegionUserBattleRoomModel battleRoomModel)
        {
            _inventoryModels = inventoryModels;
            _currentInventoryModel = currentInventoryModel;
            _battleEntitiesModel = battleModel.BattleEntitiesModel;
            _battleActions = battleActions;
            _personalNotificationModel = personalNotificationModel;
            _notificationError = notificationError;
            _uiSoundModel = battleModel.UISoundModel;
            _battleRoomModel = battleRoomModel;
            _tooltipModel = battleModel.TooltipModel;
            _localization = content.LocalizationKeys;
        }

        public void Update()
        {
            var player = _battleEntitiesModel.Player;
            if (player != null)
            {
                if (_inventoryModels.ItemMoveContext != null)
                {
                    FastMoveOnClick(_inventoryModels.ItemMoveContext);
                    _inventoryModels.ItemMoveContext = null;
                }

                foreach (var (_, view) in _inventoryModels.ClickableItemsModel.Items)
                {
                    if (view.IsClicked)
                    {
                        _lastClickTime = Time.time;
                        break;
                    }
                }

                var lastClickTimePassed = Time.time - _lastClickTime;

                foreach (var (context, view) in _inventoryModels.ClickableItemsModel.Items)
                {
                    if (_inventoryModels.InventoryDragAndDropModel.IsDragStarted || _inventoryModels.InventoryDragAndDropModel.IsScrollStarted)
                    {
                        view.ResetClicked();
                        continue;
                    }

                    if (view.IsClicked)
                    {
                        if (view.InputButton == PointerEventData.InputButton.Middle)
                        {
                            view.ResetClicked();
                        }

                        if (view.ClickCount == 1)
                        {
                            if (view.InputButton == PointerEventData.InputButton.Left && _battleActions.SplitOnDrop.ReadValue<float>() > 0.5f)
                            {
                                _inventoryModels.InventorySelectedModel.SetItemContext(context);
                                _inventoryModels.InventorySelectedModel.NeedHighlightItem = true;

                                if (_battleEntitiesModel.Player.ClientModel.InventoryIntention.TryGetSlotToSplit(context, _inventoryModels.ItemSplitModel.Count, out var toContext, out var errorType) &&
                                    _inventoryModels.InventorySelectedModel.ItemContext.ItemModel.Value.IsStackable && _inventoryModels.InventorySelectedModel.ItemContext.Count > 1)
                                {
                                    InventoryUtils.TrySplit(_inventoryModels.ItemSplitModel, context, toContext);
                                }
                                else
                                {
                                    ShowNotification(errorType);
                                    _inventoryModels.InventorySelectedModel.ClearItemContext();
                                }

                                view.ResetClicked();
                            }

                            if (view.InputButton == PointerEventData.InputButton.Left && !view.NeedWaitForClick(2))
                            {
                                view.ResetClicked();
                            }

                            if (view.InputButton == PointerEventData.InputButton.Right)
                            {
                                if (!player.ClientModel.BlockedSlotsModel.IsBlocked(context)
                                    && !player.ClientModel.InventoryProgressItemModel.IsEqualsSource(context)
                                    && context is not CollectionItemMoveContext)
                                {
                                    _tooltipModel.OpenInventoryItem(context);
                                    _inventoryModels.InventorySelectedModel.SetItemContext(context);
                                    _inventoryModels.InventorySelectedModel.NeedHighlightItem = true;
                                    _tooltipModel.Position = (view.transform as RectTransform).GetWorldPosition();
                                    _tooltipModel.Intention = TooltipModel.ScreenIntention.OpenActions;
                                    _tooltipModel.ItemSize = (view.transform as RectTransform).GetWorldSize();
                                }

                                view.ResetClicked();
                            }
                        }

                        if (IsDoubleClick(view) || IsControlClick(view))
                        {
                            FastMoveOnClick(context);
                            view.ResetClicked();
                        }
                    }

                    if (view.IsHover
                        && !view.IsTouch
                        && view.HoverTime > TooltipRules.TooltipEnterTime
                        && lastClickTimePassed > TooltipRules.TooltipEnterTime
                        && !_tooltipModel.IsOpen
                        && !player.ClientModel.BlockedSlotsModel.IsBlocked(context)
                        && !player.ClientModel.InventoryProgressItemModel.IsEqualsSource(context))
                    {
                        var rectTransform = (RectTransform)view.transform;
                        var itemPosition = rectTransform.GetWorldPosition();
                        var itemSize = rectTransform.GetWorldSize();

                        _inventoryModels.InventorySelectedModel.SetItemContext(context);
                        _inventoryModels.InventorySelectedModel.NeedHighlightItem = false;
                        _tooltipModel.Position = itemPosition;
                        _tooltipModel.Intention = TooltipModel.ScreenIntention.OpenDescription;
                        _tooltipModel.ItemSize = itemSize;
                        _tooltipModel.OpenInventoryItem(context);
                    }

                    if (_tooltipModel.CheckInventory(context)
                        && !view.IsHover
                        && _tooltipModel.IsOpen
                        && !_tooltipModel.IsActionsOpen)
                    {
                        _tooltipModel.Intention = TooltipModel.ScreenIntention.Close;
                    }
                }
            }
        }

        private void DiscardTooltip()
        {
            _inventoryModels.InventorySelectedModel.ClearItemContext();
            _tooltipModel.Intention = TooltipModel.ScreenIntention.Close;
        }

        private bool IsDoubleClick(PointerButtonView view)
        {
            return view.ClickCount == 2 && view.InputButton == PointerEventData.InputButton.Left;
        }

        private bool IsControlClick(PointerButtonView view)
        {
            return view.IsClicked && _battleActions.Ctrl.IsPressed();
        }

        private void FastMoveOnClick(IItemMoveContext context)
        {
            var inventoryIntention = _battleEntitiesModel.Player.ClientModel.InventoryIntention;

            switch (_currentInventoryModel)
            {
                case CurrentInventoryModel.Trader traderModel:
                    {
                        if (context is TraderItemMoveContext traderItemMoveContext)
                        {
                            if (inventoryIntention.TryTraderBuy(traderItemMoveContext, _inventoryModels.ItemMoveModel, _inventoryModels.ShopSplitModel, true, out var errorType))
                            {
                                DiscardTooltip();
                            }
                            else
                            {
                                ShowNotification(errorType);
                            }
                        }
                        else if (traderModel.TraderModel.CanSell)
                        {
                            if (inventoryIntention.TryTraderSell(_battleEntitiesModel.Player.ServerModel.PrivateModel.Inventory, traderModel.TraderModel, _inventoryModels.ItemMoveModel, context,
                                    _battleRoomModel, out var price, out var errorType))
                            {
                                if (price > 0 && context.ItemModel.Value.IsSellAvailable)
                                {
                                    _uiSoundModel.PlaySound(BattleUISoundType.ChangeDirtyMoney);
                                }

                                DiscardTooltip();
                            }
                            else
                            {
                                ShowNotification(errorType);
                            }
                        }

                        break;
                    }
                case CurrentInventoryModel.CollectionTable collectionTableModel:
                    {
                        if (inventoryIntention.TryFastMoveInCollect(context, _inventoryModels.ItemMoveModel, collectionTableModel.CollectionModel, collectionTableModel.CollectionTableSlot))
                        {
                            DiscardTooltip();
                        }

                        break;
                    }
                case CurrentInventoryModel.IInventory currentIInventoryModel and CurrentInventoryModel.IExternal:
                    {
                        if (inventoryIntention.TryFastMoveInIInventory(context, _inventoryModels.ItemMoveModel, currentIInventoryModel, out var errorType))
                        {
                            DiscardTooltip();
                        }
                        else
                        {
                            ShowNotification(errorType);
                        }

                        break;
                    }
                case CurrentInventoryModel.PlotLoot plotLoot when plotLoot.TryGetCurrentPage(out var inventoryModel, out int entityId):
                    {
                        if (inventoryIntention.TryFastMoveInPlotLoot(context, _inventoryModels.ItemMoveModel, inventoryModel, entityId, out var errorType))
                        {
                            DiscardTooltip();
                        }
                        else
                        {
                            ShowNotification(errorType);
                        }

                        break;
                    }
                case CurrentInventoryModel.MoneyWorkbench moneyWorkbenchModel:
                    {
                        if (inventoryIntention.TryFastMoveInLaunderingMoneyWorkbench(context, _inventoryModels.ItemMoveModel, moneyWorkbenchModel.EntityId, out var errorType))
                        {
                            DiscardTooltip();
                            if (ClientMoneyLaunderingRules.IsLaunderingActionExceedsLaunderingCap(context, moneyWorkbenchModel.EntityId, _battleEntitiesModel))
                            {
                                ClientMoneyLaunderingRules.GetLaunderingModelsByEntityId(moneyWorkbenchModel.EntityId, _battleEntitiesModel, out ClientMoneyWorkbenchModel clientMoneyWorkbench);
                                clientMoneyWorkbench.PlayerExceedLaunderingCapOnPut = true;
                                _personalNotificationModel.Enqueue(_localization.ExchangeMoneyLocalization.ExceedLaunderingCapOnPutNotification.GetLocalizedString());
                            }
                        }
                        else
                        {
                            ShowNotification(errorType);
                        }

                        break;
                    }
                case CurrentInventoryModel.Hood:
                    {
                        if (inventoryIntention.TryFastMoveInCarUpgrades(context, _inventoryModels.ItemMoveModel, _inventoryModels.CarUpgradesSlotModel, CarUpgradesHolderTypeDescription.Hood, out var errorType))
                        {
                            DiscardTooltip();
                        }
                        else
                        {
                            ShowNotification(errorType);
                        }

                        break;
                    }
                case CurrentInventoryModel.Undercarriage:
                    {
                        if (inventoryIntention.TryFastMoveInCarUpgrades(context, _inventoryModels.ItemMoveModel, _inventoryModels.CarUpgradesSlotModel, CarUpgradesHolderTypeDescription.Undercarriage, out var errorType))
                        {
                            DiscardTooltip();
                        }
                        else
                        {
                            ShowNotification(errorType);
                        }

                        break;
                    }    
                case CurrentInventoryModel.TrunkUpgrades:
                {
                    if (inventoryIntention.TryFastMoveInCarUpgrades(context, _inventoryModels.ItemMoveModel, _inventoryModels.CarUpgradesSlotModel, CarUpgradesHolderTypeDescription.Trunk, out var errorType))
                    {
                        DiscardTooltip();
                    }
                    else
                    {
                        ShowNotification(errorType);
                    }

                    break;
                }
                case CurrentInventoryModel.Builder {EntityId: var entityId} when _battleEntitiesModel.WorldEntitiesModel.PlotBuildingDefenseBlockPrivateModels.ContainsKey(entityId):
                    {
                        if (inventoryIntention.TryFastMoveInDefenseBlock(context, _inventoryModels.ItemMoveModel,
                                _inventoryModels.BuilderSlotsModel, out var errorType))
                        {
                            DiscardTooltip();
                        }
                        else
                        {
                            ShowNotification(errorType);
                        }
                        break;
                    }
                case CurrentInventoryModel.Builder:
                {
                    if (inventoryIntention.TryFastMoveInBuilder(context, _inventoryModels.ItemMoveModel,
                            _inventoryModels.BuilderSlotsModel, out var errorType))
                    {
                        DiscardTooltip();
                    }
                    else
                    {
                        ShowNotification(errorType);
                    }

                    break;
                }
                case CurrentInventoryModel.CarBench:
                    {
                        if (inventoryIntention.TryFastMoveInCarBuilder(context, _inventoryModels.ItemMoveModel, _inventoryModels.CarBenchSlotsModel, out var errorType))
                        {
                            DiscardTooltip();
                        }
                        else
                        {
                            ShowNotification(errorType);
                        }

                        break;
                    }
                case CurrentInventoryModel.HelicopterBench:
                    {
                        if (inventoryIntention.TryFastMoveInHelicopterBuilder(context, _inventoryModels.ItemMoveModel, _inventoryModels.HelicopterBenchSlotsModel, out var errorType))
                        {
                            DiscardTooltip();
                        }
                        else
                        {
                            ShowNotification(errorType);
                        }

                        break;
                    }
            }
        }

        private void ShowNotification(NotificationError.Type errorType)
        {
            if (errorType != NotificationError.Type.None)
            {
                _personalNotificationModel.Enqueue(_notificationError.GetErrorText(errorType));
            }
        }
    }
}