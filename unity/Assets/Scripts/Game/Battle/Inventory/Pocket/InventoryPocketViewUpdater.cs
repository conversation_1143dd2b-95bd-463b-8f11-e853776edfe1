using System.Collections.Generic;
using Core;
using Core.Updater;
using Game.Battle.EntityModel.Player;
using Game.Battle.Inventory.RestrictedCells;
using Game.Battle.Inventory.Slot;
using Models.Models.GatherHangarCollectionsModel;
using Models.Models.InventoryModel;
using Models.Models.RegionUserBattleRoomModel;
using Models.Models.SettlementBonusModel;

namespace Game.Battle.Inventory
{
    public class InventoryPocketViewUpdater : IUpdater
    {
        private readonly IUpdater _updaters;

        public InventoryPocketViewUpdater(InventoryModels inventoryModels, IGatherHangarCollectionsModel gatherHangarCollectionsModel, IRegisterDisposable disposable, IInventoryModel inventoryModel,
            CurrentInventoryModel currentInventoryModel, InventoryUiView inventoryUiView, BattleScreenContent battleScreenContent, InventoryScreenContent inventoryScreenContent,
            ISettlementBonusModel settlementBonusModel, IRegionUserBattleRoomModel battleRoomModel, PlayerEntityModel player)
        {
            var serverModel = player.ServerModel;
            var playerClientModel = player.ClientModel;
            var pocketsUiView = inventoryUiView.PocketsUiView;
            var inventoryCellsUiView = inventoryUiView.InventoryCellsUiView;

            var updaterList = new List<IUpdater>();
            foreach (var itemMoveContext in playerClientModel.PocketsContexts)
            {
                var slotModel = itemMoveContext.Slot;
                updaterList.Add(new PocketSlotModelUpdater(slotModel, inventoryModels.DraggableItemsModel, inventoryModels.ClickableItemsModel, pocketsUiView, itemMoveContext, disposable));

                var slotView = pocketsUiView.GetView(slotModel.Description.Index);
                slotView.ResetView();
                updaterList.Add(new BlockedSlotBusyUpdater(disposable, itemMoveContext, playerClientModel.BlockedSlotsModel, playerClientModel.InventoryIntention));
                updaterList.Add(new InventoryPocketSlotIconUpdater(slotModel, serverModel.PrivateModel.Pockets, slotView.InventorySlotView, slotView.EquipSlotView,
                    battleScreenContent.InventoryItemViewDescriptions));
                updaterList.Add(new InventorySlotRareUpdater(slotModel.ItemModel, playerClientModel.SkinCenterModel.WeaponSkins, slotView.RareView, battleScreenContent.InventoryItemViewDescriptions,
                    battleScreenContent.WeaponSkinViewDescriptions, null));
                updaterList.Add(new InventoryCellCollectableUpdater(gatherHangarCollectionsModel, slotModel.ItemModel, slotView.CompositeParent, inventoryScreenContent.InventoryItemCollectableView,
                    disposable));

                var itemSlotCountView = inventoryScreenContent.InventoryItemCountView.Generate(slotView.CompositeParent);
                itemSlotCountView.Value.ClearCount();
                updaterList.Add(new InventoryItemCountUpdater(slotModel.ItemModel, slotModel.StackModel, itemSlotCountView.Value));
                disposable.Register(itemSlotCountView.Dispose);

                updaterList.Add(new PocketSlotViewUpdater(slotModel, serverModel.PrivateModel.Pockets, slotView, inventoryModels.InventoryDragAndDropModel));

                if (currentInventoryModel is CurrentInventoryModel.Trader traderModel)
                {
                    var itemSlotCostView = inventoryScreenContent.InventoryItemCostView.Generate(slotView.CompositeParent);
                    itemSlotCostView.Value.ClearView();
                    disposable.Register(itemSlotCostView.Dispose);
                    updaterList.Add(new ItemSlotCostUpdater(itemMoveContext, traderModel.TraderModel, itemSlotCostView.Value,
                        playerClientModel.BuyLockedBySettlementBuildingsModel, playerClientModel.BuyBlockedSlotsModel, player.PublicId, settlementBonusModel, battleRoomModel));
                }

                updaterList.Add(new InventorySlotBlockedUpdater(itemMoveContext, playerClientModel.BlockedSlotsModel, inventoryModels.InventoryDragAndDropModel, slotView.SlotBlockedView,
                    slotView.SlotLockedView, slotView.DraggableItemView, slotView.PointerButtonView, playerClientModel.BuyBlockedSlotsModel,
                    playerClientModel.BuyLockedBySettlementBuildingsModel, playerClientModel.LockedTraderItemByLocationTier));
                updaterList.Add(new InventorySlotReparentProgressBackgroundsViewUpdater(itemMoveContext, inventoryCellsUiView.InventoryProgressView, playerClientModel.InventoryProgressItemModel,
                    slotView.BackgroundParent, slotView.PointerButtonView, disposable, inventoryCellsUiView.ItemParent));
                updaterList.Add(new InventorySlotReparentSelectedBackgroundsViewUpdater(itemMoveContext, inventoryModels.InventorySelectedModel, inventoryModel, battleScreenContent.InventorySelectedView, slotView.BackgroundParent, inventoryCellsUiView.ItemParent, disposable));
                updaterList.Add(new PocketRestrictedShadowUpdater(inventoryModels.RestrictedCellsModel, slotModel, inventoryScreenContent.RestrictedShadowView, slotView.transform, disposable));
                updaterList.Add(new CooldownInventoryItemUpdater(serverModel.PrivateModel.UsableItemCooldownModel, serverModel.PrivateModel.ThrowableItemCooldownModel, slotModel.ItemModel, inventoryModels.Now,
                    inventoryScreenContent.CooldownView, slotView.CompositeParent, disposable));
            }

            _updaters = new CompositeUpdater(updaterList);
        }

        public void Update()
        {
            _updaters.Update();
        }
    }
}