using System;
using System.Collections.Generic;
using Core;
using Core.Updater;
using Game.Battle.Entities.Player.Camera;
using Game.Battle.EntityModel.BattleCharacter;
using Game.Battle.EntityModel.Car;
using Game.Battle.EntityModel.Helicopter.Models;
using Game.Battle.EntityModel.Vehicles.Views;
using Game.Battle.Role;
using UnityEngine;
using Utils.TypeCastExtensions;

namespace Game.Battle.EntityModel.Player
{
    public class InVehicleCharacterVisibleUpdater : IUpdater
    {
        private readonly BattleCameraModel _cameraModel;
        private readonly CharacterInVehiclesModel _charactersInVehiclesModel;
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly float _characterInVehicleVisibleDistanceSqr;
        private readonly float _characterInVehicleUnVisibleDistanceSqr;
        

        private readonly RaycastHit[] _resultsBuffer = new RaycastHit[32];
        private readonly int _layerMask = LayerId.ToMask(LayerId.Default);

        public InVehicleCharacterVisibleUpdater(BattleModel battleModel, CharacterRoleViewDescriptions characterRoleViewDescriptions)
        {
            _cameraModel = battleModel.BattleCameraModel;
            _charactersInVehiclesModel = battleModel.CharactersInVehiclesModel;
            _battleEntitiesModel = battleModel.BattleEntitiesModel;
            _characterInVehicleVisibleDistanceSqr = characterRoleViewDescriptions.CharacterInVehicleNicknameVisibleDistance * characterRoleViewDescriptions.CharacterInVehicleNicknameVisibleDistance;
            _characterInVehicleUnVisibleDistanceSqr = characterRoleViewDescriptions.CharacterInVehicleNicknameUnVisibleDistance * characterRoleViewDescriptions.CharacterInVehicleNicknameUnVisibleDistance;
        }

        public void Update()
        {
            if (_battleEntitiesModel.Player != null)
            {
                var playerPos = GetPlayerPos();

                foreach (var (vehicleId, characterIds) in _charactersInVehiclesModel.VehicleCharacters)
                {
                    if (_battleEntitiesModel.TryGetValue(vehicleId, out IEntityModel entity))
                    {
                        Vector3 position = entity switch
                        {
                            CarEntityModel carEntityModel => carEntityModel.ClientModel.Position.ToVector3(),
                            HelicopterEntityModel helicopterEntityModel => helicopterEntityModel.ClientModel.Position.ToVector3(),
                            _ => throw new ArgumentOutOfRangeException(nameof(entity))
                        };
                        float sqrDistance = (position - playerPos).sqrMagnitude;
                        if (sqrDistance < _characterInVehicleVisibleDistanceSqr)
                        {
                            var isVisible = false;

                            foreach (var characterId in characterIds)
                            {
                                if (IsVehicleCharacterVisible(vehicleId, characterId))
                                {
                                    isVisible = true;
                                    break;
                                }
                            }

                            if (isVisible)
                            {
                                _charactersInVehiclesModel.VehicleVisible.Add(vehicleId);
                            }
                            else
                            {
                                if (_charactersInVehiclesModel.VehicleVisible.Contains(vehicleId))
                                {
                                    _charactersInVehiclesModel.VehicleVisible.Remove(vehicleId);
                                }
                            }
                        }
                        else
                        {
                            if (_charactersInVehiclesModel.VehicleVisible.Contains(vehicleId) && sqrDistance > _characterInVehicleUnVisibleDistanceSqr)
                            {
                                _charactersInVehiclesModel.VehicleVisible.Remove(vehicleId);
                            }
                        }
                    }
                    else
                    {
                        if (_charactersInVehiclesModel.VehicleVisible.Contains(vehicleId))
                        {
                            _charactersInVehiclesModel.VehicleVisible.Remove(vehicleId);
                        }
                    }
                }
            }
        }
        
        private bool IsVehicleCharacterVisible(int vehicleId, int characterId)
        {
            if (_battleEntitiesModel.TryGetValue(characterId, out var entityModel) && entityModel is BattleCharacterEntityModel battleCharacterEntityModel)
            {
                var battleCharacter = battleCharacterEntityModel.ServerModel;
                var from = battleCharacter.Transform.ToVector3() + new Vector3(0, battleCharacter.Description.CameraCrouch.Height, 0);
                var to = _cameraModel.Position;
                return IsRaycastVehicleCharacter(vehicleId, from, to);
            }

            return false;
        }

        private bool IsRaycastVehicleCharacter(int inVehicleId, Vector3 from, Vector3 to)
        {
            var distance = (from - to).magnitude;
            return Physics.RaycastNonAlloc(from, to - from, _resultsBuffer, distance, _layerMask) == 0;
        }

        private Vector3 GetPlayerPos()
        {
            var player = _battleEntitiesModel.Player;
            if (player.ServerModel.InVehicleModel.IsInsideVehicle)
            {
                return _battleEntitiesModel[player.ServerModel.InVehicleModel.EntityId] switch
                {
                    CarEntityModel carEntityModel => carEntityModel.ClientModel.Position.ToVector3(),
                    HelicopterEntityModel helicopterEntityModel => helicopterEntityModel.ClientModel.Position.ToVector3(),
                    _ => throw new ArgumentOutOfRangeException()
                };
            }

            return player.ClientModel.SetMovement.ToVector3();
        }
    }
}