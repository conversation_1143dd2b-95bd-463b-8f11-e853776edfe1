using Game.Battle.Inventory;
using Models.Models.BattleCharacterModel;
using Models.Models.BattleCharacterSlotModel;
using Models.Models.SettlementBonusModel;
using Models.Models.WorldEntitiesModel;
using Models.References;
using Models.References.Location;
using Models.References.Realm;

namespace Game.Battle.EntityModel.Player
{
    public class PlayerEntityModel : EntityModel
    {

        public PlayerEntityModel(int id, IBattleCharacterModel serverModel, IBattleCharacterSlotModel battleCharacterSlotModel,
            ISettlementBonusModel settlementBonusModel, SectorDescription sectorDescription, LocationDescription locationDescription, 
            BattleModeDescription battleModeDescription, InventoryModels inventoryModels, IWorldEntitiesModel worldEntitiesModel) : base(id)
        {
            PublicId = battleCharacterSlotModel.PublicId;
            ServerModel = serverModel;
            ClientModel = new PlayerClientModel(serverModel, worldEntitiesModel.UnlockEntities, PublicId, settlementBonusModel, sectorDescription, locationDescription, battleModeDescription, inventoryModels, worldEntitiesModel);
        }

        public PlayerClientModel ClientModel { get; }
        public IBattleCharacterModel ServerModel { get; }
        public readonly long PublicId;
    }
}