using Game.Battle.Entities.Shared;
using Game.Battle.EntityModel.BattleCharacter;
using Game.Battle.EntityModel.Vehicles;
using Models.Models.HelicopterModel;
using Models.Models.InVehicleModel;
using Models.Models.SkeletonAnimationModel;
using Models.Models.VehicleSeatsModel;
using Models.Models.WorldEntitiesModel;
using Models.References.Vehicle.Seat;
using UnityEngine;

namespace Game.Battle.Entities.Player.Vehicle.Models.CharacterInVehicleAnimationModel
{
    public class CharacterInHelicopterAnimationModel : ICharacterInVehicleAnimationModel
    {
        private const float _changeSpeed = 3f;
        private readonly CharacterAnimationView _animationView;
        private readonly VehicleSeatTypeViewDescriptions _seatTypeViewDescriptions;
        private readonly IWorldEntitiesModel _worldEntitiesModel;
        private readonly IInVehicleModel _inVehicleModel;
        private readonly ISkeletonAnimationModel _skeletonAnimationModel;

        private IHelicopterModel _helicopter;
        private bool _isDriver;
        private float _yaw, _pitch, _roll;

        public CharacterInHelicopterAnimationModel(CharacterAnimationView animationView, VehicleSeatTypeViewDescriptions seatTypeViewDescriptions, IWorldEntitiesModel worldEntitiesModel, IInVehicleModel inVehicleModel, ISkeletonAnimationModel skeletonAnimationModel)
        {
            _animationView = animationView;
            _seatTypeViewDescriptions = seatTypeViewDescriptions;
            _worldEntitiesModel = worldEntitiesModel;
            _inVehicleModel = inVehicleModel;
            _skeletonAnimationModel = skeletonAnimationModel;
        }
        
        public void OnVehicleEnter(bool hasAnim)
        {
            if (TryGetVehicle(out var helicopterModel))
            {
                _helicopter = helicopterModel;
                int seatType = GetSeatType(_helicopter.Seats);

                _isDriver = (seatType & VehicleSeatFlags.Driver) != 0;
                VehicleSeatTypeViewDescription seatTypeViewDescription = _seatTypeViewDescriptions[_helicopter.Seats.Type];
                int seatIndex = seatTypeViewDescription.GetSitIndexByType(seatType);
                _animationView.SetDriving(seatIndex, hasAnim);
            }
        }

        public void OnVehicleLeave()
        {
            _animationView.ExitStateDriving();
            _helicopter = null;
        }

        public void OnUpdate()
        {
            if (_isDriver)
            {
                _skeletonAnimationModel.CalculateHelicopterMovementByData(_helicopter, out float helicYaw, out float helicRoll, out float helicPitch);
                _yaw = Mathf.MoveTowards(_yaw, helicYaw, Time.deltaTime * _changeSpeed);
                _pitch = Mathf.Lerp(_pitch, helicPitch, Time.deltaTime * _changeSpeed);
                _roll = Mathf.Lerp(_roll, helicRoll, Time.deltaTime * _changeSpeed);
                
                _animationView.SetHelicopterDrivingInput(_yaw, _pitch, _roll);
            }
        }

        private bool TryGetVehicle(out IHelicopterModel helicopterModel)
        {
            if (_worldEntitiesModel.Helicopters.TryGetModel(_inVehicleModel.EntityId, out var worldHelicopterModel))
            {
                helicopterModel = worldHelicopterModel;
                return true;
            }
            
            helicopterModel = null;
            return false;
        }

        private int GetSeatType(IVehicleSeatsModel seats)
        {
            return seats.GetSeatSlotDescription(_inVehicleModel.SeatSlot).Type;
        }
    }
}