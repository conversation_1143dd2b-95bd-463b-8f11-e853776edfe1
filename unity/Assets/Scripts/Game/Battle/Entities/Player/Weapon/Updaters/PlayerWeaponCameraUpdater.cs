using Cinemachine;
using CinemachineTPSO;
using Core.Updater;
using Game.Battle.EntityModel.BattleCharacter.Crouch;
using Game.Battle.Equipment.Weapon;
using Models.Models.AimingModel;
using Models.Models.AimStateModel;
using Models.Models.PrimitiveModel;
using Models.Models.WeaponAimModeModel;
using Models.Models.WeaponModel;
using Models.References;
using Models.References.Weapon;
using UnityEngine;
using Utils.TypeCastExtensions;

namespace Game.Battle.EntityModel.Player.Weapon
{
    public class PlayerWeaponCameraUpdater : IUpdater
    {
        private readonly CinemachineAimingCameraFollow _armedAimingCameraFollow;
        private readonly CinemachineVirtualCamera _armedCamera;
        private readonly IWeaponModel _weapon;
        private readonly IAimStateModel _aimStateModel;
        private readonly WeaponViewDescriptions _viewDescriptions;
        private readonly CharacterCrouchModel _crouchModel;
        private readonly IAimingModel _aiming;
        private readonly PlayerCameraViewDescriptions _playerCameraViewDescriptions;
        private readonly IPrimitiveModel<AimModeDescription> _aimModeModel;

        private WeaponDescription _oldWeapon;
        private readonly float _maxStep;
        private bool _isAimed;
        private bool _usesOptics;

        public PlayerWeaponCameraUpdater(CinemachineVirtualCamera armedCamera, IWeaponModel weapon, IAimStateModel aimStateModel, WeaponViewDescriptions viewDescriptions,
            CharacterCrouchModel crouchModel, IAimingModel aiming, PlayerCameraViewDescriptions playerCameraViewDescriptions, IPrimitiveModel<AimModeDescription> aimModeModel, float aimModeSwitchTime)
        {
            _armedCamera = armedCamera;
            _weapon = weapon;
            _aimStateModel = aimStateModel;
            _viewDescriptions = viewDescriptions;
            _crouchModel = crouchModel;
            _aiming = aiming;
            _playerCameraViewDescriptions = playerCameraViewDescriptions;
            _aimModeModel = aimModeModel;
            _armedAimingCameraFollow = armedCamera.GetCinemachineComponent<CinemachineAimingCameraFollow>();

            _isAimed = aiming.IsAimed;
            _maxStep = 1f / aimModeSwitchTime;

            if (_isAimed)
            {
                SetupAimedCamera(weapon.Value);
            }

            if (weapon.Value != null && GetUsesOptics())
            {
                _armedCamera.m_Lens.NearClipPlane = _isAimed ? _playerCameraViewDescriptions.AimedNearClipPlane : _playerCameraViewDescriptions.DefaultNearClipPlane;
            }
        }

        public void Update()
        {
            WeaponDescription weapon = _weapon.Value;

            if (weapon != _oldWeapon && weapon != null && _aimStateModel.State == AimStateDescription.Entering)
            {
                SetupAimedCamera(weapon);
                _oldWeapon = weapon;
            }

            var aimMode = _aimModeModel.Value;
            if (weapon != null && (_aimStateModel.State == AimStateDescription.Entering || _aiming.IsAimed))
            {
                WeaponAimModeDescription aimModeDescription = WeaponAimModeModel.GetMode(_aimModeModel, weapon);
                TripodDescription tripod = TripodDescription.Lerp(aimModeDescription.Camera.Stand, aimModeDescription.Camera.Crouch, _crouchModel.Crouch);


                var aimDistance = _viewDescriptions[weapon].WeaponAimViewDescription[aimMode].AimDistance;
                if (aimMode == AimModeDescription.ThirdPerson)
                {
                    SetupAimedCamera(weapon);
                    _armedAimingCameraFollow.ShoulderOffsetAimed3stPerson = tripod.ShoulderOffset.ToVector3();
                    _armedAimingCameraFollow.VerticalArmLengthAimed3stPerson = tripod.Hand;
                    _armedAimingCameraFollow.CameraDistanceAimed3stPerson = aimDistance;
                }
                else
                {
                    SetupAimedCamera(weapon);
                    _armedAimingCameraFollow.ShoulderOffsetAimed1stPerson = tripod.ShoulderOffset.ToVector3();
                    _armedAimingCameraFollow.VerticalArmLengthAimed1stPerson = tripod.Hand;
                    _armedAimingCameraFollow.CameraDistanceAimed1stPerson = aimDistance;
                }
                
                var deltaTime = Time.deltaTime * _maxStep;
                _armedAimingCameraFollow.WeightAimMode = Mathf.MoveTowards(_armedAimingCameraFollow.WeightAimMode, aimMode == AimModeDescription.ThirdPerson ? 0f : 1f, deltaTime);
            }

            var isAimed = _aiming.IsAimed;
            var usesOptics = GetUsesOptics();
            if (usesOptics)
            {
                if (isAimed != _isAimed || !_usesOptics)
                {
                    _armedCamera.m_Lens.NearClipPlane = isAimed ? _playerCameraViewDescriptions.AimedNearClipPlane : _playerCameraViewDescriptions.DefaultNearClipPlane;
                    _isAimed = isAimed;
                }
            }
            else
            {
                _armedCamera.m_Lens.NearClipPlane = _playerCameraViewDescriptions.DefaultNearClipPlane;
            }

            _usesOptics = usesOptics;
        }

        private void SetupAimedCamera(WeaponDescription weapon)
        {
            _armedAimingCameraFollow.FieldOfViewAimed = _viewDescriptions[weapon].WeaponAimViewDescription[_aimModeModel.Value].AimingFOV;
        }

        private bool GetUsesOptics()
        {
            return _weapon.Value != null && _viewDescriptions[_weapon.Value].WeaponAimViewDescription[_aimModeModel.Value].UsesOptics;
        }
    }
}