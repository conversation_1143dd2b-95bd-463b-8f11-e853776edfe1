using Core.Updater;
using Game.Global.Character;
using Models.Models.BattleStanceModel;
using Models.References;
using Models.References.Character;
using UnityEngine;

namespace Game.Battle.Entities.BattleCharacter.Rotation
{
    public class CharacterBoxingYawModelUpdater : IUpdater
    {
        private readonly CharacterRotationModel _rotationModel;
        private readonly IBattleStanceModel _battleStance;
        private readonly BattleCharacterSkeletonDescription _skeletonDescription;

        public CharacterBoxingYawModelUpdater(CharacterRotationModel rotationModel, IBattleStanceModel battleStance, BattleCharacterSkeletonDescription skeletonDescription)
        {
            _rotationModel = rotationModel;
            _battleStance = battleStance;
            _skeletonDescription = skeletonDescription;
        }

        public void Update()
        {
            if (_battleStance.BattleStance == BattleStanceDescription.Boxing)
            {
                _rotationModel.LookYaw += _skeletonDescription.BoxingYawOffset * Mathf.Rad2Deg;
            }   
        }
    }
}