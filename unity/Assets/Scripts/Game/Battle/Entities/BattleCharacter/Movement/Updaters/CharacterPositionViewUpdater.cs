using Core.Updater;
using Game.Battle.Utils;
using UnityEngine;

namespace Game.Battle.EntityModel.BattleCharacter.Movement
{
    public class CharacterPositionViewUpdater : IUpdater
    {
        private readonly IMovementModel _movementModel;
        private readonly CharacterMovementView _movementView;
        private readonly Color _color;

        private readonly Vector2 _rect = new Vector2(.3f, .3f);
        
        public CharacterPositionViewUpdater(IMovementModel movementModel, CharacterMovementView movementView, Color color)
        {
            _movementModel = movementModel;
            _movementView = movementView;
            _color = color;
        }

        public void Update()
        {
            _movementView.SetPosition(new Vector3(_movementModel.X, _movementModel.Y, _movementModel.Z));
            DebugUtils.DrawRect(_movementModel.X, _movementModel.Z, _rect, _color, 0.01f, true);
        }
    }
}