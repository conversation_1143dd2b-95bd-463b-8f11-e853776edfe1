using Game.Battle.EntityModel.BattleCharacter;
using Models.Models.BattleCharacterStateModel;

namespace Game.Battle.Entities.BattleCharacter.Weapon
{
    public static class CharacterRenderersRules
    {
        public static RendersVisibleView.Mode GetRenderVisibleMode(CharacterClientModel characterClientModel, IBattleCharacterStateModel battleCharacterStateModel)
        {
            return battleCharacterStateModel.Value == BattleCharacterState.Hidden || characterClientModel.VisibleModel.IsFullHide 
                ? RendersVisibleView.Mode.Invisible 
                : characterClientModel.VisibleModel.IsVisible || battleCharacterStateModel.Value == BattleCharacterState.Dead
                    ? RendersVisibleView.Mode.Visible
                    : RendersVisibleView.Mode.ShadowsOnly;
        }
    }
}