using IK;
using UnityEngine;

namespace Game.Battle.EntityModel.BattleCharacter.Weapon
{
    public class CharacterWeaponMagazineView : MonoBehaviour, IWeightedImpact
    {
        [Tooltip("Will be set from code when new weapon is instantiated in hands")] 
        [SerializeField] private Transform _magazine;
        [SerializeField] private Transform _magazineBone;
        [field: SerializeField] [field: Range(0f, 1f)] public float Weight { get; set; }

        private Vector3 _originalPosition;
        private Quaternion _originalRotation;
        private bool _hasMagazine;

        public void SetMagazine(Transform magazine)
        {
            _magazine = magazine;
            _originalPosition = magazine.localPosition;
            _originalRotation = magazine.localRotation;
            _hasMagazine = true;
        }

        public void UnsetMagazine()
        {
            if (!_hasMagazine) return;
            
            _magazine.localPosition = _originalPosition;
            _magazine.localRotation = _originalRotation;
            _magazine = null;
            _hasMagazine = false;
        }

        private void LateUpdate()
        {
            if (_hasMagazine)
            {
                Transform magazineParent = _magazine.parent;
                Quaternion boneRotation = Quaternion.Inverse(magazineParent.rotation) * _magazineBone.rotation;
                _magazine.localRotation = Quaternion.Slerp(_originalRotation, boneRotation, Weight);

                Matrix4x4 weaponLocalSpace = magazineParent.worldToLocalMatrix;
                Vector3 weaponBonePosition = weaponLocalSpace.MultiplyPoint(_magazineBone.position);
                _magazine.localPosition = Vector3.Lerp(_originalPosition, weaponBonePosition, Weight);
            }
        }
    }
}