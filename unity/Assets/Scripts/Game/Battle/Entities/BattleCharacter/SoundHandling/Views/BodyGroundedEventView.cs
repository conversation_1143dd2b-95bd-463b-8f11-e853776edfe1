using UnityEngine;
using Event = AK.Wwise.Event;

namespace Game.Battle.EntityModel.BattleCharacter.SoundHandling
{
    [RequireComponent(typeof(Animator))]
    public class BodyGroundedEventView : MonoBehaviour
    {
        private const float _postDelay = 0.4f;
        private float _timeToNextPost;
        
        [SerializeField] private AkGameObj _akGameObj;
        [SerializeField] private Event _bodyGrounded;
        
        private GameObject _soundEmitter;

        private void Awake()
        {
            _soundEmitter = _akGameObj.gameObject;
        }

        private void BodyGrounded()
        {
            var now = Time.time;
            if (now > _timeToNextPost)
            {
                _timeToNextPost = now + _postDelay;
                _bodyGrounded.Post(_soundEmitter);
            }
        }
    }
}