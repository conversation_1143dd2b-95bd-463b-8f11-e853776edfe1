using ClientCore.Utils;
using Core;
using Models.Models.PhysicsBodiesModel;
using Models.Models.PhysicsSceneModel;
using Models.Models.PlotVehicleModel;

namespace Game.Battle.Entities.Plot.Builder
{
    public class PlotVehicleCollidersUpdater<TPlotVehicleSlotDescription, TVehicleDescription> : IClientUpdater where TVehicleDescription : class
    {
        private readonly IPlotVehicleModel<TPlotVehicleSlotDescription, TVehicleDescription> _plotVehicleModel;
        private readonly IPhysicsSceneModel _physicsSceneModel;
        private readonly IPhysicsBodiesModel _physicsBodiesModel;

        private TVehicleDescription _description;
        private bool _hasColliders;

        public PlotVehicleCollidersUpdater(IPlotVehicleModel<TPlotVehicleSlotDescription, TVehicleDescription> plotVehicleModel, IPhysicsSceneModel physicsSceneModel, IPhysicsBodiesModel physicsBodiesModel, IRegisterDisposable registerDisposable)
        {
            _plotVehicleModel = plotVehicleModel;
            _physicsSceneModel = physicsSceneModel;
            _physicsBodiesModel = physicsBodiesModel;
            
            registerDisposable.Register(() =>
            {
                if (_hasColliders)
                {
                    _plotVehicleModel.CollidersModel.Destroy(physicsSceneModel, physicsBodiesModel);
                }
            });
        }

        public void Update()
        {
            var description = _plotVehicleModel.VehicleDescription;
            var hasColliders = _plotVehicleModel.HasColliders;

            if (_description != description || _hasColliders != hasColliders)
            {
                if (_hasColliders)
                {
                    _plotVehicleModel.CollidersModel.Destroy(_physicsSceneModel, _physicsBodiesModel);
                }

                if (hasColliders)
                {
                    _plotVehicleModel.CollidersModel.Initialize(_physicsSceneModel, _physicsBodiesModel);
                }

                _description = description;
                _hasColliders = hasColliders;
            }
        }
    }
}