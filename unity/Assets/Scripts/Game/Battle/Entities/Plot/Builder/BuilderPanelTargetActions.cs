using System.Collections.Generic;
using Game.Battle.Inventory;
using Game.Battle.Target;
using Game.Common;
using Game.Global;
using Game.Global.Localization;
using Game.Global.ModalDialog;
using Game.Global.ModalDialog.Hints;
using Game.Global.Tutorial.Hints;
using Game.Shared.Models;
using Game.Targets;
using SharedType;
using UnityEngine.Localization;

namespace Game.Battle.EntityModel.Plot.Builder
{
    public class BuilderPanelTargetActions : IPlotInteractiveActions
    {
        private readonly GlobalModel _globalModel;
        private readonly PersonalNotificationModel _personalNotificationModel;
        private readonly GlobalUISoundModel _uiSoundModel;
        private readonly InventoryDialogModel _dialogModel;
        private readonly int _plotId;
        private readonly InfoHintType _hintType;
        private readonly int _planningBoardEntityId;

        public BuilderPanelTargetActions(GlobalModel globalModel, LocalizationKeys localizationKeys, TargetPriorityViewDescription targetPriorityViewDescription, InventoryDialogModel dialogModel, int plotId,
                                         LocalizedString hintHeader, InfoHintType hintType)
        {
            _globalModel = globalModel;
            _personalNotificationModel = globalModel.PersonalNotificationModel;
            _uiSoundModel = globalModel.UISoundModel;
            _dialogModel = dialogModel;
            _plotId = plotId;
            _hintType = hintType;

            var nameLabel = new TargetLocalizedItem(hintHeader);

            UseAction = new TargetAction(
                nameLabel,
                localizationKeys.TargetActions.HintTipKeys.Builder,
                OpenBuilder,
                targetPriorityViewDescription.Builder,
                Unavailable,
                targetPriorityViewDescription.UnavailableAction,
                null
            );

            ActionSet = new HashSet<ITargetAction>() {UseAction};
        }

        public TargetAction UseAction { get; }

        public HashSet<ITargetAction> ActionSet { get; }

        private void Unavailable(LocalizedString invalidNotification)
        {
            _personalNotificationModel.Enqueue(invalidNotification.GetLocalizedString());
            _uiSoundModel.PlaySound(GlobalUISoundType.WrongClick);
        }

        private void OpenBuilder()
        {
            if (_globalModel.InfoHintsModel.CanShowHint(_hintType))
            {
                _globalModel.InfoHintsModel.EnqueueHint(new InfoHintModel(_hintType, Action: OpenDialog));
            }
            else
            {
                OpenDialog();
            }

            void OpenDialog()
            {
                _dialogModel.Open(new CurrentInventoryModel.Builder(_plotId));
            }
        }
    }
}