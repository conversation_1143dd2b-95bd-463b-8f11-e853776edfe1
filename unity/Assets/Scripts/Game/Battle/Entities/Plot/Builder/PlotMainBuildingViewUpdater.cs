using Content.Disposable;
using Core;
using Core.Updater;
using Game.Battle.Entities.Plot.Builder;
using Game.Global;
using Game.Locations;
using Models.Models.HangarPlotModel;
using Models.References.Builder;
using Models.References.Plot;
using UnityEngine;
using Utils.MinimapRenderer;
using Utils.TypeCastExtensions;

namespace Game.Battle.EntityModel.Plot.Builder
{
    public class PlotMainBuildingViewUpdater : IUpdater
    {
        private readonly IHangarPlotModel _plotModel;
        private readonly PlotMainBuildingViewDescriptions _plotMainBuildingViewDescriptions;
        private readonly Vector3 _position;
        private readonly Quaternion _orientation;
        private readonly MapMeshView _mapMeshView;
        private readonly MinimapRenderersModel _minimapRenderersModel;
        private readonly LocationPlotsViewsModel _plotsViewModel;
        private readonly ScopesLoadingModel _scopesLoadingModel;
        private IDisposableContent<PlotMainBuildingView> _dispose;
        private MainBuildingDescription _description;
        private IDisposableContent<Renderer> _mapDispose;
        private Renderer _plotMapRenderer;
        private bool _isContentLoading;
        private bool _needDispose;

        public PlotMainBuildingViewUpdater(BattleModel battleModel, IHangarPlotModel plotModel, PlotMainBuildingViewDescriptions plotMainBuildingViewDescriptions,
            PlotSchemeDescription plotSchemeDescription, System.Numerics.Vector3 plotPosition, System.Numerics.Quaternion plotOrientation, MapMeshView mapMeshView, MinimapRenderersModel minimapRenderersModel,
            IRegisterDisposable registerDisposable)
        {
            _plotModel = plotModel;
            _plotMainBuildingViewDescriptions = plotMainBuildingViewDescriptions;

            var mainBuildingSlot = plotSchemeDescription.MainBuildingSlot;
            var mainBuildingPosition = plotPosition + System.Numerics.Vector3.Transform(mainBuildingSlot.Position, plotOrientation);
            var mainBuildingOrientation = plotOrientation * mainBuildingSlot.Orientation;

            _position = mainBuildingPosition.ToVector3();
            _orientation = mainBuildingOrientation.ToQuaternion();
            _mapMeshView = mapMeshView;
            _minimapRenderersModel = minimapRenderersModel;
            _plotsViewModel = battleModel.PlotsViewModel;
            _scopesLoadingModel = battleModel.ScopesLoadingModel;
            registerDisposable.Register(Dispose);
        }

        public void Update()
        {
            var description = _plotModel.MainBuildingSlot.Value;

            if (description != _description && description == null)
            {
                Dispose();
                _plotMapRenderer = null;
            }

            if (!_isContentLoading && description != _description && description != null)
            {
                _needDispose = _description != null;
                _plotsViewModel.RemoveMainBuilding(_plotModel.PlotSlotDescription);
                TryAddToMainBuilderLoad(description);
            }

            if (_isContentLoading && IsContentLoaded(description))
            {
                if (_needDispose)
                {
                    Dispose();
                }
                CreateMainBuilder(description);
            }

            if (_plotMapRenderer != null)
            {
                _minimapRenderersModel.Add(_plotMapRenderer);
            }

            _description = description;
        }

        private void TryAddToMainBuilderLoad(MainBuildingDescription description)
        {
            if (description == null) return;

            _isContentLoading = true;
            _scopesLoadingModel.TryAddToLoad(_plotMainBuildingViewDescriptions[description]);
        }

        private void CreateMainBuilder(MainBuildingDescription description)
        {
            if (description == null) return;

            _isContentLoading = false;
            var value = _plotMainBuildingViewDescriptions[description].View.Generate();
            _dispose = value;
            value.Value.transform.SetPositionAndRotation(_position, _orientation);

            IDisposableContent<Renderer> mapContent = _plotMainBuildingViewDescriptions[description].MapView.Generate(_mapMeshView.transform);
            _plotMapRenderer = mapContent.Value;
            mapContent.Value.transform.SetLocalPositionAndRotation(_position, _orientation);
            _mapDispose = mapContent;

            _plotsViewModel.AddMainBuilding(_plotModel.PlotSlotDescription, value.Value);
        }

        private bool IsContentLoaded(MainBuildingDescription description)
        {
            return _scopesLoadingModel.LoadedScopes.Contains(_plotMainBuildingViewDescriptions[description]);
        }

        private void Dispose()
        {
            _dispose?.Dispose();
            _dispose = null;
            _mapDispose?.Dispose();
            _mapDispose = null;
            _plotsViewModel.RemoveMainBuilding(_plotModel.PlotSlotDescription);
        }
    }
}