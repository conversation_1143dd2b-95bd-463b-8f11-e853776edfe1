using Game.Battle.Target;
using Game.Locations;
using UnityEngine;

namespace Game.Battle.EntityModel.VehicleBench
{
    public class CarBenchView : MonoBehaviour
    {
        [field: SerializeField] public Transform MarkerRoot { get; private set; }
        [field: SerializeField] public Collider Collider { get; private set; }
        [field: SerializeField] public RaycastTargetOptions RaycastTargetOptions { get; private set; }
        [field: SerializeField] public TargetHintPositionView TargetHintPositionView { get; private set; }
    }
}