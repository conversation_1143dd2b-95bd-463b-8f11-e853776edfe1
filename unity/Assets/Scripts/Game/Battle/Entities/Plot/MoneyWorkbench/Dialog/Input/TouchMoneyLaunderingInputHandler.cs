using Core.Updater;
using Game.Battle.EntityModel.Plot;
using Game.Battle.Inputs;
using Game.Common;
using Game.Global;
using Game.Global.Ui;
using Models.Models.MoneyWorkbenchModel;
using Network.Connection;
using System.Collections.Generic;

namespace Game.Battle.Entities.Plot.Dialog.Input
{
    public class TouchMoneyLaunderingInputHandler : IUpdater
    {
        private readonly IUpdater _updater;

        public TouchMoneyLaunderingInputHandler(MoneyLaunderingDialogModel model, BattleModel battleModel, GlobalModel globalModel, IRoomConnection battleConnection, IMoneyWorkbenchModel moneyWorkbench,
            MoneyLaunderingDialogView view, ClientMoneyWorkbenchModel clientMoneyWorkbenchModel, BattleScreenContent content, GlobalScreenContent globalScreenContent)
        {
            _updater = new CompositeUpdater(new List<IUpdater>
            {
                new MoneyLaunderingInputHandler(battleModel, globalModel, battleConnection, moneyWorkbench, view, clientMoneyWorkbenchModel, content, globalScreenContent),
                new PointerButtonDelayInputUpdater(model.RateTooltipInputModel, view.Info.LaunderingRateContainer, PointerButtonDelayInputUpdater.TriggerMode.Press, TooltipRules.TooltipEnterTime),
                new PointerButtonDelayInputUpdater(model.SpeedTooltipInputModel, view.Info.LaunderingSpeedContainer, PointerButtonDelayInputUpdater.TriggerMode.Press, TooltipRules.TooltipEnterTime),
                new PointerButtonDelayInputUpdater(model.TaxTooltipInputModel, view.Info.TaxContainer, PointerButtonDelayInputUpdater.TriggerMode.Press, TooltipRules.TooltipEnterTime),
            });
        }

        public void Update()
        {
            _updater.Update();
        }
    }
}