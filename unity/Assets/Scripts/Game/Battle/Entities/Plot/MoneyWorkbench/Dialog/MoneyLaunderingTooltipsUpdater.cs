using Core;
using Core.Updater;
using Game.Battle.TooltipMini;
using Game.Global.Localization;
using Game.Global.Ui;
using Models.Models.MoneyWorkbenchModel;
using Models.Models.MoneyWorkbenchRules;
using Models.Models.PlotModel;
using Models.References;
using Models.References.MoneyWorkbench;
using Models.Utils.Extensions;
using System.Collections.Generic;
using UI;
using UnityEngine;

namespace Game.Battle.Entities.Plot.Dialog
{
    public class MoneyLaunderingTooltipsUpdater : IUpdater
    {
        private static readonly float _launderingPeriodPerHour = TimeDescription.Hour / (float)MoneyWorkbenchLaunderingDescription.LaunderingPeriod;
        private readonly IUpdater _updater;

        public MoneyLaunderingTooltipsUpdater(MoneyLaunderingDialogModel model, IMoneyWorkbenchModel moneyWorkbenchModel, IPlotModel plotModel, BattleModel battleModel,
            MoneyLaunderingInfoView infoView, ExchangeMoneyLocalization exchangeMoneyLocalization, IRegisterDisposable registerDisposable)
        {
            int buildingRate = battleModel.SettlementBonusModel.TryGetOwnerBonus(out var buildingBonusDescription) ? buildingBonusDescription.SettlerLaunderingRateClearCashBonus : 0;
            var settlementHelperModel = battleModel.HangarOwnerModel;

            float rateByBase = (float)MoneyWorkbenchLaunderingDescription.LaunderingRateClearCashBase / MoneyWorkbenchLaunderingDescription.LaunderingRateDirtyCash;
            float rateByResidence = (float)(plotModel.OwnedSettlementsBuildingsLaunderingRateClearCashBonus.Value + buildingRate) / MoneyWorkbenchLaunderingDescription.LaunderingRateDirtyCash;
            float rateByProtect = (float)plotModel.ClanBonus.LaunderingRateClearCashBonus / MoneyWorkbenchLaunderingDescription.LaunderingRateDirtyCash;
            float rateByModules = (float)MoneyWorkbenchRules.GetLaunderingClearCashRateByWorkbenchUpgrades(moneyWorkbenchModel) / MoneyWorkbenchLaunderingDescription.LaunderingRateDirtyCash;

            var rateContext = new TooltipMiniContext.DescriptionLabels(exchangeMoneyLocalization.LaunderingRateBonusTitle.StringReference,
                new[]
                {
                    (exchangeMoneyLocalization.LaunderingRateByBase.GetLocalizedString(), exchangeMoneyLocalization.LaunderingRateValue.Set(new MoneyFloat { Amount = rateByBase }).GetLocalizedString()),
                    (exchangeMoneyLocalization.LaunderingRateByResidenceBonus.GetLocalizedString(), exchangeMoneyLocalization.LaunderingRateValue.Set(new MoneyFloat { Amount = rateByResidence }).GetLocalizedString()),
                    (exchangeMoneyLocalization.LaunderingRateByProtectBonus.GetLocalizedString(), exchangeMoneyLocalization.LaunderingRateValue.Set(new MoneyFloat { Amount = rateByProtect }).GetLocalizedString()),
                    (exchangeMoneyLocalization.LaunderingRateByModulesBonus.GetLocalizedString(), exchangeMoneyLocalization.LaunderingRateValue.Set(new MoneyFloat { Amount = rateByModules }).GetLocalizedString()),
                });

            
            int speedByResidence = Mathf.RoundToInt(MoneyWorkbenchLaunderingDescription.LaunderingDirtyCashPerPeriodBase * _launderingPeriodPerHour);
            int speedByProtect = Mathf.RoundToInt(plotModel.ClanBonus.LaunderingDirtyCashPerPeriodBonus * _launderingPeriodPerHour);
            int speedByModules = Mathf.RoundToInt(MoneyWorkbenchRules.GetLaunderingDirtyCashPerPeriodAmountByUpgrades(moneyWorkbenchModel) * _launderingPeriodPerHour);

            var speedContext = new TooltipMiniContext.DescriptionLabels(exchangeMoneyLocalization.LaunderingSpeedBonusTitle.StringReference,
                new[]
                {
                    (exchangeMoneyLocalization.LaunderingSpeedByBase.GetLocalizedString(), exchangeMoneyLocalization.LaunderingSpeedValue.Set(new MoneyFloat { Amount = speedByResidence, Currency = Currency.DirtyCash }).GetLocalizedString()),
                    (exchangeMoneyLocalization.LaunderingSpeedByProtectBonus.GetLocalizedString(), exchangeMoneyLocalization.LaunderingSpeedValue.Set(new MoneyFloat { Amount = speedByProtect, Currency = Currency.DirtyCash }).GetLocalizedString()),
                    (exchangeMoneyLocalization.LaunderingSpeedByModulesBonus.GetLocalizedString(), exchangeMoneyLocalization.LaunderingSpeedValue.Set(new MoneyFloat { Amount = speedByModules, Currency = Currency.DirtyCash }).GetLocalizedString()),
                });

            int taxesBySettlementOwner = Mathf.RoundToInt(SettlementTaxExtensions.CalculateSettlementTaxModifier(settlementHelperModel, battleModel.SectorDescription) * 100);
            int taxesBySectorOwner = Mathf.RoundToInt(SettlementTaxExtensions.CalculateSectorTaxModifier(settlementHelperModel, battleModel.SectorDescription) * 100);
            int taxesByRealmOwner = Mathf.RoundToInt(SettlementTaxExtensions.CalculateCentralSectorTaxModifier(settlementHelperModel) * 100);

            var taxesContext = new TooltipMiniContext.DescriptionLabels(exchangeMoneyLocalization.LaunderingTaxesTitle.StringReference,
                new[]
                {
                    (exchangeMoneyLocalization.LaunderingTaxesBySettlementOwner.GetLocalizedString(), exchangeMoneyLocalization.LaunderingTaxesValue.Set(taxesBySettlementOwner).GetLocalizedString()),
                    (exchangeMoneyLocalization.LaunderingTaxesBySectorOwner.GetLocalizedString(), exchangeMoneyLocalization.LaunderingTaxesValue.Set(taxesBySectorOwner).GetLocalizedString()),
                    (exchangeMoneyLocalization.LaunderingTaxesByRealmOwner.GetLocalizedString(), exchangeMoneyLocalization.LaunderingTaxesValue.Set(taxesByRealmOwner).GetLocalizedString()),
                });

            var tooltipPositionSettings = new TooltipPositionSettings(HorizontalAnchor.Left, VerticalAlignment.Center, VerticalAlignment.Center, flipAnchorWhenNoSpace: false);
            _updater = new CompositeUpdater(new List<IUpdater>
            {
                new OpenTooltipMiniOnTriggerUpdater(model.RateTooltipInputModel, battleModel.TooltipMiniModel, infoView.LaunderingRateContainer.transform as RectTransform, rateContext, tooltipPositionSettings, registerDisposable),
                new OpenTooltipMiniOnTriggerUpdater(model.SpeedTooltipInputModel, battleModel.TooltipMiniModel, infoView.LaunderingSpeedContainer.transform as RectTransform, speedContext, tooltipPositionSettings, registerDisposable),
                new OpenTooltipMiniOnTriggerUpdater(model.TaxTooltipInputModel, battleModel.TooltipMiniModel, infoView.TaxContainer.transform as RectTransform, taxesContext, tooltipPositionSettings, registerDisposable),
            });
        }

        public void Update()
        {
            _updater.Update();
        }
    }
}