using System;
using UI;
using UnityEngine;
using UnityEngine.UI;

namespace Game.Battle.Entities.Plot.Dialog
{
    [Serializable]
    public class MoneyLaunderingTaxPercentage
    {
        [SerializeField] private TextLabel<int> _textLabel;
        [SerializeField] private Graphic _graphic;
        [SerializeField] private Color _hasOwnerColor;
        [SerializeField] private Color _noOwnerColor;
        
        public void Set(int item)
        {
            _textLabel.Set(item);
        }

        public void SetColorBasedOnSettlementState(bool hasSettlementOwner)
        {
            _graphic.color = hasSettlementOwner ? _hasOwnerColor : _noOwnerColor;
        }
    }
}