using System.Collections.Generic;
using Game.Battle.Target;
using Game.Global.UI.SkinCenter;
using Game.Shared.Models;
using Game.Targets;
using SharedType;
using UnityEngine;
using UnityEngine.Localization;

namespace Game.Battle.EntityModel.Plot
{
    public class MirrorTargetActions : IPlotInteractiveActions
    {
        private readonly PersonalNotificationModel _personalNotificationModel;
        private readonly GlobalUISoundModel _uiSoundModel;
        private readonly SkinCenterDialogModel _skinCenterDialogModel;

        public MirrorTargetActions(LocalizedString name, PersonalNotificationModel personalNotificationModel, GlobalUISoundModel uiSoundModel, LocalizationKeys localizationKeys, TargetPriorityViewDescription targetPriorityViewDescription, SkinCenterDialogModel skinCenterDialogModel)
        {
            _personalNotificationModel = personalNotificationModel;
            _uiSoundModel = uiSoundModel;
            _skinCenterDialogModel = skinCenterDialogModel;

            TargetLocalizedItem nameLabel = new TargetLocalizedItem(name);

            UseAction = new TargetAction(
                nameLabel,
                localizationKeys.TargetActions.HintTipKeys.Use,
                Use,
                targetPriorityViewDescription.Customization,
                Unavailable,
                targetPriorityViewDescription.UnavailableAction,
                null
            );

            ActionSet = new HashSet<ITargetAction>() {UseAction};
        }

        public TargetAction UseAction { get; }

        public HashSet<ITargetAction> ActionSet { get; }

        private void Unavailable(LocalizedString invalidNotification)
        {
            _personalNotificationModel.Enqueue(invalidNotification.GetLocalizedString());
            _uiSoundModel.PlaySound(GlobalUISoundType.WrongClick);
        }

        private void Use()
        {
            _skinCenterDialogModel.OpenByTab(SkinCenterNavigationTabs.Character);
        }
    }
}
