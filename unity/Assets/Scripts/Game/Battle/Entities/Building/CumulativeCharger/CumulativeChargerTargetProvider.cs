using Game.Battle.BattleScreenContentDirectory.Localization;
using Game.Battle.BattleScreenContentModels;
using Game.Battle.Inventory;
using Game.Battle.Target;
using Game.Targets;
using Models.Inventory;
using Models.Models.CumulativeChargerModel;
using Models.References.Inventory;
using System.Collections.Generic;

namespace Game.Battle.Entities.Building
{
    public class CumulativeChargerTargetProvider : CommonRaycastTargetProvider
    {
        private readonly CumulativeChargerTargetActions _targetActions;
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly ICumulativeChargerModel _model;
        private readonly UnavailableTargetActionsKeys _unavailableKeys;
        private readonly InventoryItemViewDescriptions _itemDescriptions;
        private readonly NotificationError _notificationError;
        private readonly List<object> _arguments = new(1) { null };

        public CumulativeChargerTargetProvider(UnityRaycastTargetOptions.ByEntity context, CumulativeChargerTargetActions targetActions, IBattleEntitiesModel battleEntitiesModel,
            ICumulativeChargerModel model, UnavailableTargetActionsKeys unavailableKeys, InventoryItemViewDescriptions itemDescriptions,  NotificationError notificationError) : base(context, targetActions.Actions)
        {
            _targetActions = targetActions;
            _battleEntitiesModel = battleEntitiesModel;
            _model = model;
            _unavailableKeys = unavailableKeys;
            _itemDescriptions = itemDescriptions;
            _notificationError = notificationError;
        }

        protected override ITargetAction GetTargetAction()
        {
            if (_battleEntitiesModel.Player is not { } player) return null;

            var serverModel = player.ServerModel;
            if (serverModel.ChargeCumulativeItemModel.IsActive) return null;
            if (!serverModel.Role.Value.CanChargeCumulativeItem)
            {
                return _targetActions.ChargeUnavailableRole;
            }

            if (!_model.ChargeReadyModel.Value)
            {
                _targetActions.BeginCharging.Invalid(_unavailableKeys.UnavailableByUseAgain);
            }
            else if (serverModel.CargoModel.HasCargo)
            {
                _targetActions.BeginCharging.Invalid(_unavailableKeys.HandsOccupiedNotification);
            }
            else if (serverModel.KnockoutModel.IsKnockedOut || serverModel.KnockoutModel.IsDelayAfterRevive)
            {
                _targetActions.BeginCharging.Invalid(_unavailableKeys.UnavailableWhenKnocked);
            }
            else if (_battleEntitiesModel.Player.ServerModel.CargoModel.HasCargo)
            {
                _targetActions.BeginCharging.Invalid(_notificationError.UnableToPerformAction);
            }
            else if (!InventorySearchRules.FindItem(_battleEntitiesModel.Player.ServerModel.PrivateModel.Inventory, InventoryItemDescription.UsbRubberDuckyCumulative, out _))
            {
                _arguments[0] = _itemDescriptions[InventoryItemDescription.UsbRubberDuckyCumulative].Name.GetLocalizedString();
                _notificationError.NotFoundChargableItem.Arguments = _arguments;
                _targetActions.BeginCharging.Invalid(_notificationError.NotFoundChargableItem);
            }
            else
            {
                _targetActions.BeginCharging.Valid();
            }

            return _targetActions.BeginCharging;
        }
    }
}