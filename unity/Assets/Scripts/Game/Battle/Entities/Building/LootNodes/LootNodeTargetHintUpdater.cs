using System.Collections.Generic;
using Core;
using Core.Updater;
using Game.Battle.TargetHint;
using Game.Locations;
using Game.Targets;
using Models.Models.LootNodeModel;
using UnityEngine;

namespace Game.Battle.EntityModel
{
    public class LootNodeTargetHintUpdater : IUpdater
    {
        private readonly TargetHintModel _targetHintModel;
        private readonly TargetModel _targetModel;
        private readonly HashSet<ITargetAction> _actions;
        private readonly Collider _collider;
        private readonly TargetHintPositionView _targetPositionHint;
        private readonly ILootNodeModel _lootNodeModel;
        private bool _isVisible;

        public LootNodeTargetHintUpdater(TargetHintModel targetHintModel, TargetModel targetModel, HashSet<ITargetAction> actions, Collider collider, TargetHintPositionView targetPositionHint,
            IRegisterDisposable registerDisposable, ILootNodeModel lootNodeModel)
        {
            _targetHintModel = targetHintModel;
            _targetModel = targetModel;
            _actions = actions;
            _collider = collider;
            _targetPositionHint = targetPositionHint;
            _lootNodeModel = lootNodeModel;
            registerDisposable.Register(() =>
            {
                if (_isVisible)
                {
                    _targetHintModel.Remove(collider);
                }
            });
        }

        public void Update()
        {
            var isVisible = !_lootNodeModel.StepsModel.IsEmpty && !_lootNodeModel.IsEmptyModel.Value;
            if (isVisible != _isVisible)
            {
                _isVisible = isVisible;
                if (isVisible)
                {
                    _targetHintModel.Add(_collider, _targetPositionHint, null, _actions);
                }
                else
                {
                    _targetHintModel.Remove(_collider);
                }
            }
        }
    }
}