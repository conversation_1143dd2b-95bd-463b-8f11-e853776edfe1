using Core.Updater;
using Game.Battle.Entities.RefillAmmo;
using Models.Models.InteractableObjectOpenedModel;

namespace Game.Battle.Entities.Building.RefillAmmo
{
    public class RefillEquipBoxAnimationUpdater : IUpdater
    {
        private readonly IInteractableObjectOpenedModel _interactableObjectOpenedModel;
        private readonly RefillAmmoView _refillAmmoView;

        public RefillEquipBoxAnimationUpdater(IInteractableObjectOpenedModel interactableObjectOpenedModel, RefillAmmoView refillAmmoView)
        {
            _interactableObjectOpenedModel = interactableObjectOpenedModel;
            _refillAmmoView = refillAmmoView;
            
            _refillAmmoView.SetOpened(_interactableObjectOpenedModel.IsOpened, true);
        }

        public void Update()
        {
            _refillAmmoView.SetOpened(_interactableObjectOpenedModel.IsOpened, false);
        }
    }
}