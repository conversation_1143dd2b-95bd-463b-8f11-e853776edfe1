using Content;
using Content.Disposable;
using Core.Updater;
using Game.Battle.Entities.Grenade.Models;
using Game.Battle.EntityModel.BattleCharacter.Movement;
using Game.Battle.ParticleUtility;
using Models.Models.GrenadeModel;
using UnityEngine;
using Event = AK.Wwise.Event;

namespace Game.Battle.Entities.Grenade.Updaters
{
    public class GrenadeImpactViewUpdater : PooledUpdater, IUpdater
    {
        private GrenadeCollisionPointModel _grenadeCollisionPointModel;
        private GrenadeDataInterpolationModel _dataInterpolationModel;
        private GrenadeViewDescriptions _grenadeViewDescriptions;
        private GrenadeViewDescription _grenadeViewDescription;
        private ClientGrenadeTriggerModel _grenadeTriggerModel;
        private ParticleSpawner _particleSpawner;
        private SetMovementModel _positionModel;
        private GameObject _soundEmitter;

        public GrenadeImpactViewUpdater Set(IGrenadeModel grenade, GrenadeClientModel client, ParticleSpawner particleSpawner, GrenadeViewDescriptions grenadeViewDescriptions, GameObject soundEmitter)
        {
            _grenadeCollisionPointModel = client.CollisionPoint;
            _dataInterpolationModel = client.DataInterpolation;
            _grenadeViewDescriptions = grenadeViewDescriptions;
            _grenadeViewDescription = grenadeViewDescriptions[grenade.Description];
            _grenadeTriggerModel = client.Trigger;
            _particleSpawner = particleSpawner;
            _positionModel = client.Position;
            _grenadeViewDescriptions = grenadeViewDescriptions;
            _soundEmitter = soundEmitter;
            return this;
        }

        public override void Update()
        {
            if (_grenadeTriggerModel.IsTriggered)
            {
                PlayImpact(_grenadeViewDescription.GrenadeImpactVfx, _grenadeViewDescription.ImpactEvent);
            }

            if (_grenadeCollisionPointModel.HasCollision || _dataInterpolationModel.IsStartSleeping)
            {
                PlayImpact(_grenadeViewDescriptions.CollisionWithSurfaceVfx, _grenadeViewDescription.CollisionWithSurfaceEvent);
            }
        }
        
        private void PlayImpact(IInstantiateContent<ParticleSystem> impactVfxTemplate, Event impactSfxEvent)
        {
            IDisposableContent<ParticleSystem> vfx = impactVfxTemplate.Generate();
            Vector3 position = _positionModel.ToVector3();
            vfx.Value.transform.position = position;
            _particleSpawner.Play(vfx);
            impactSfxEvent.Post(_soundEmitter);
        }
    }
}