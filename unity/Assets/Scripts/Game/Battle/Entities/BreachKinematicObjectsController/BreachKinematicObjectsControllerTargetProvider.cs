using Game.Battle.Player;
using Game.Battle.Target;
using Game.Targets;
using UnityEngine.Localization;

namespace Game.Battle.Entities.BreachKinematicObjectsController
{
    public class BreachKinematicObjectsControllerTargetProvider : CommonRaycastTargetProvider
    {
        private readonly int _id;
        private readonly BreachKinematicObjectsControllerTargetActions _actions;
        private readonly BattleModel _battleModel;
        private readonly BattleScreenContent _content;
        private readonly LocalizedString _breachUnavailableByItem;

        public BreachKinematicObjectsControllerTargetProvider(int id, BreachKinematicObjectsControllerTargetActions actions, BattleModel battleModel, BattleScreenContent content)
            : base(new UnityRaycastTargetOptions.ByEntity(id, default), actions.ActionSet)
        {
            _id = id;
            _actions = actions;
            _battleModel = battleModel;
            _content = content;

            LocalizedString referenceString = content.LocalizationKeys.TargetActions.UnavailableKeys.BreachUnavailableBecauseOfItem;
            _breachUnavailableByItem = new LocalizedString(referenceString.TableReference, referenceString.TableEntryReference);
        }

        protected override ITargetAction GetTargetAction()
        {
            if (_battleModel.BattleEntitiesModel.Player is not { } player)
            {
                return null;
            }

            if (OwnerRules.GetOwnerStatus(_id, _battleModel) != OwnerRules.OwnerStatus.EnemyOwner)
            {
                return null;
            }

            var knockoutModel = player.ServerModel.KnockoutModel;
            if (knockoutModel.IsKnockedOut || knockoutModel.IsDelayAfterRevive)
            {
                return _actions.CloseUnavailableByKnockOut;
            }
            
            return null;
        }
    }
}