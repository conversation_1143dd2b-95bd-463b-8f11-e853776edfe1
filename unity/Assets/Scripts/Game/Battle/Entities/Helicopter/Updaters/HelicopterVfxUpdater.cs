using Content.Disposable;
using Core;
using Core.Updater;
using Game.Battle.ContactType;
using Game.Battle.EntityModel.Helicopter;
using Game.Battle.EntityModel.Helicopter.Models;
using Models.Models.HelicopterModel;
using Models.Models.PhysicsSceneModel;
using Models.Models.RaycastModel;
using Models.Physics.Types;
using UnityEngine;
using Utils.TypeCastExtensions;
using SystemVector3 = System.Numerics.Vector3;

namespace Game.Battle.Entities.Helicopter.Updaters
{
    public class HelicopterVfxUpdater : IUpdater
    {
        private readonly HelicopterEngineClientModel _helicopterEngineClientModel;
        private readonly IHelicopterModel _helicopterModel;
        private readonly HelicopterViewDescriptions _helicopterViewDescriptions;
        private readonly IPhysicsSceneModel _physicsSceneModel;
        private readonly IRaycastModel _raycastModel;
        private readonly SurfaceTypeViewDescriptions _surfaceTypeViewDescriptions;
        private readonly Transform _landingEffectPoint;
        
        private IDisposableContent<ParticleSystem> _vfx;
        private byte _lastSurfaceType = byte.MaxValue;
        
        public HelicopterVfxUpdater(HelicopterEngineClientModel helicopterEngineClientModel, IHelicopterModel helicopterModel, HelicopterViewDescriptions helicopterViewDescriptions, Transform landingEffectPoint,
            IPhysicsSceneModel physicsSceneModel, IRaycastModel raycastModel, SurfaceTypeViewDescriptions surfaceTypeViewDescriptions, IRegisterDisposable registerDisposable)
        {
            _helicopterEngineClientModel = helicopterEngineClientModel;
            _helicopterModel = helicopterModel;
            _helicopterViewDescriptions = helicopterViewDescriptions;
            _physicsSceneModel = physicsSceneModel;
            _raycastModel = raycastModel;
            _surfaceTypeViewDescriptions = surfaceTypeViewDescriptions;
            _landingEffectPoint = landingEffectPoint;

            registerDisposable.Register(Dispose);
        }

        public void Update()
        {
            if (_helicopterEngineClientModel.NormalizedSpeed == 0f)
            {
                Dispose();
                return;
            }

            var hasResult = _raycastModel.HelicopterVfxRaycast(_physicsSceneModel.Data, _landingEffectPoint.position.ToSystemVector3(), -SystemVector3.UnitY,
                _helicopterViewDescriptions.BladeVfxMaxHeight, _helicopterModel.BodyId, out var contactPosition, out var surfaceType);
            
            if (hasResult && surfaceType != SurfaceType.Character && surfaceType != SurfaceType.Water && surfaceType != SurfaceType.Vehicle)
            {
                if (surfaceType != _lastSurfaceType)
                {
                    Dispose();
                    _vfx = _surfaceTypeViewDescriptions[surfaceType].HelicopterBladesVfx.Generate();
                    _vfx.Value.Play();
                    
                    _lastSurfaceType = surfaceType;
                }

                var pos = contactPosition.ToVector3();
                
                var transform = _vfx.Value.transform;
                var lerp = Vector3.Lerp(transform.position, pos, _helicopterViewDescriptions.BladeVfxLerpWeight);
                transform.position = lerp;

                var min = pos.y;
                var max = min + _helicopterViewDescriptions.BladeVfxMaxHeight;
                var normalizedHeight = Mathf.InverseLerp(min, max, _helicopterModel.Transform.Y);
                var alphaByHeight = _helicopterViewDescriptions.AlphaByHelicopterHeight.Evaluate(normalizedHeight);

                var evaluatedSpeed = _helicopterViewDescriptions.AlphaByBladeSpeed.Evaluate(_helicopterEngineClientModel.NormalizedSpeed);
                var alphaBySpeed = Mathf.Lerp(0f, 1, evaluatedSpeed);

                var main = _vfx.Value.main;
                main.startColor = new Color(1f, 1f, 1f, alphaByHeight * alphaBySpeed);
            }
            else
            {
                Dispose();
            }
        }

        private void Dispose()
        {
            if (_vfx != null)
            {
                _vfx.Value.Stop();
                _vfx.Dispose(); 
                _lastSurfaceType = byte.MaxValue;
                _vfx = null;
            }
        }
    }
}