using Core.Updater;
using Game.Battle.EntityModel.Vehicles.Views;
using Utils.TweenAnimation;

namespace Game.Battle.Entities.Car
{
    public class VehicleTrunkAnimationUpdater : IUpdater
    {
        private readonly VehicleEntityView _vehicleEntityView;
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly int _vehicleId;

        public VehicleTrunkAnimationUpdater(VehicleEntityView vehicleEntityView, IBattleEntitiesModel battleEntitiesModel, int vehicleId)
        {
            _vehicleEntityView = vehicleEntityView;
            _battleEntitiesModel = battleEntitiesModel;
            _vehicleId = vehicleId;

            var isTrunkOpened = IsTrunkOpened(vehicleId);
            var progress = isTrunkOpened ? TweenAnimation.MaxProgress : TweenAnimation.MinProgress;
            _vehicleEntityView.SetTrunkAnimationProgress(isTrunkOpened, progress);
        }

        public void Update()
        {
            var isTrunkOpened = IsTrunkOpened(_vehicleId);
            _vehicleEntityView.SetTrunkOpened(isTrunkOpened);
        }

        private bool IsTrunkOpened(int id)
        {
            return
                _battleEntitiesModel.WorldEntitiesModel.Vehicles.TryGetModel(id, out var vehicleModel)
                && vehicleModel.TrunkOpenedModel.IsOpened ||
                _battleEntitiesModel.WorldEntitiesModel.PlotVehicles.TryGetModel(id, out var plotVehicleModel)
                && plotVehicleModel.TrunkOpenedModel.IsOpened;
        }
    }
}