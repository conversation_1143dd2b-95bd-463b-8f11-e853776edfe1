using Core;
using Core.Updater;
using Game.Battle.Inventory;
using Game.Global.Ui;
using Game.Shared.Models;
using SharedType;
using System;
using UnityEngine;

namespace Game.Battle.UiUpdater
{
    public class TooltipMiniViewUpdater : IUpdater
    {
        private readonly SlotTooltipMiniView _tooltipView;
        private readonly BattleUISoundModel _uiSoundModel;
        private readonly TooltipMiniModel _tooltipModel;
        private bool _isOpen;
        private readonly BattleScreenContent _battleScreenContent;
        private TooltipMiniContext _context;
        private Vector2 _oldPosition, _oldSize;

        public TooltipMiniViewUpdater(SlotTooltipMiniView tooltipView, BattleScreenContent battleScreenContent, BattleUISoundModel uiSoundModel, TooltipMiniModel tooltipModel, IRegisterDisposable disposable)
        {
            _tooltipModel = tooltipModel;
            _tooltipView = tooltipView;
            _uiSoundModel = uiSoundModel;
            _battleScreenContent = battleScreenContent;

            _tooltipView.SetActive(false);

            _oldPosition = _tooltipModel.Position;

            _tooltipView.RectChangeListener.RectChanged += UpdatePosition;
            disposable.Register(() => _tooltipView.RectChangeListener.RectChanged -= UpdatePosition);
        }

        public void Update()
        {
            UpdateVisibility();
            UpdatePosition();
            UpdateView();
        }

        private void UpdateVisibility()
        {
            var isOpenChanged = _isOpen != _tooltipModel.IsOpen;
            if (isOpenChanged)
            {
                if (_tooltipModel.IsOpen)
                {
                    _uiSoundModel.PlaySound(BattleUISoundType.TooltipVisibilityChanged);
                }

                _isOpen = _tooltipModel.IsOpen;
                _tooltipView.SetActive(_isOpen);
            }
        }

        private void UpdateView()
        {
            if (!_tooltipModel.IsOpen)
            {
                _context = null;
                return;
            }

            if (_context == _tooltipModel.Context) return;

            _context = _tooltipModel.Context;

            if (_context is TooltipMiniContext.InventoryItem inventoryItemTooltipContext)
            {
                switch (inventoryItemTooltipContext.Context)
                {
                    case BuilderItemMoveContext builderItemMoveContext:
                        {
                            var description = _battleScreenContent.BuilderSlotViewDescriptions.Slots[builderItemMoveContext.SlotModel.Type];
                            _tooltipView.SetTitle(description.MiniTooltipTitle);
                            _tooltipView.SetDescription(description.MiniTooltipDescription);
                            break;
                        }

                    case CarUpgradeMoveContext carUpgradeMoveContext:
                        {
                            var description = _battleScreenContent.CarUpgradeSlotsViewDescription[carUpgradeMoveContext.UpgradeType];
                            _tooltipView.SetTitle(description.MiniTooltipTitle);
                            _tooltipView.SetDescription(description.MiniTooltipDescription);
                            break;
                        }
                    case DefenseBlockItemMoveContext defenseBlockItemMoveContext:
                        {
                            var description = _battleScreenContent.BuilderSlotViewDescriptions.Slots[defenseBlockItemMoveContext.SlotModel.Type];
                            _tooltipView.SetTitle(description.MiniTooltipTitle);
                            _tooltipView.SetDescription(description.MiniTooltipDescription);
                            break;
                        }
                    default:
                        throw new ArgumentOutOfRangeException(inventoryItemTooltipContext.Context?.GetType().Name);
                }
            }
            else if (_context is TooltipMiniContext.DescriptionLabels descriptionLabels)
            {
                _tooltipView.SetTitle(descriptionLabels.Title);
                _tooltipView.SetDescription(descriptionLabels.LeftDescription, descriptionLabels.RightDescription);
            }
            else
            {
                throw new ArgumentOutOfRangeException(_context?.GetType().Name);
            }
        }

        private void UpdatePosition()
        {
            Vector2 size = _tooltipView.GetCurrentSize();
            var isOpenChanged = _isOpen != _tooltipModel.IsOpen || size != _oldSize;
            if (_oldPosition != _tooltipModel.Position || isOpenChanged)
            {
                _oldPosition = _tooltipModel.Position;
                _oldSize = size;

                if (_tooltipModel.IsOpen)
                {
                    var tooltipPosition = TooltipRules.GetPosition(_oldPosition, _tooltipModel.ItemSize, _oldSize, _tooltipModel.PositionSettings);
                    _tooltipView.SetPosition(tooltipPosition);
                }
            }

            _isOpen = _tooltipModel.IsOpen;
        }
    }
}