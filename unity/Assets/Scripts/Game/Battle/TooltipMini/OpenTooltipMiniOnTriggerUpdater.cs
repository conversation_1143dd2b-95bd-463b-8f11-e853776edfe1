using Core;
using Core.Updater;
using Game.Battle.Inputs;
using Game.Global.Ui;
using UnityEngine;

namespace Game.Battle.TooltipMini
{
    public class OpenTooltipMiniOnTriggerUpdater : IUpdater
    {
        private readonly InputModel _inputModel;
        private readonly TooltipMiniModel _tooltipModel;
        private readonly RectTransform _hoveringTransform;
        private readonly TooltipMiniContext _context;
        private readonly TooltipPositionSettings _tooltipPositionSettings;

        public OpenTooltipMiniOnTriggerUpdater(InputModel inputModel, TooltipMiniModel tooltipModel, RectTransform hoveringTransform, TooltipMiniContext context, TooltipPositionSettings tooltipPositionSettings,
            IRegisterDisposable registerDisposable)
        {
            _inputModel = inputModel;
            _tooltipModel = tooltipModel;
            _hoveringTransform = hoveringTransform;
            _context = context;
            _tooltipPositionSettings = tooltipPositionSettings;

            registerDisposable.Register(() =>
            {
                if (_tooltipModel.IsOpen && _tooltipModel.Context == _context)
                {
                    _tooltipModel.Clear();
                }
            });
        }

        public void Update()
        {
            if (_inputModel.IsTriggered)
            {
                if (!_tooltipModel.IsOpen)
                {
                    _tooltipModel.Open(_hoveringTransform.GetWorldPosition(), _hoveringTransform.GetWorldSize(), _context, _tooltipPositionSettings);
                }
            }
            else
            {
                if (_tooltipModel.IsOpen && _tooltipModel.Context == _context)
                {
                    _tooltipModel.Clear();
                }
            }
        }
    }
}