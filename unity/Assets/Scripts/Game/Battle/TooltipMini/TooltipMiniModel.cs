using Game.Battle.Inventory;
using Game.Global.Ui;
using UnityEngine;

namespace Game.Battle
{
    public class TooltipMiniModel
    {
        public Vector2 Position { get; private set; }
        public Vector2 ItemSize { get; private set; }
        public TooltipMiniContext Context { get; private set; }
        public TooltipPositionSettings PositionSettings { get; private set; }
        public bool IsOpen { get; private set; }

        public void Open(Vector2 targetTopLeftCorner, Vector2 targetSize, TooltipMiniContext context, TooltipPositionSettings positionSettings)
        {
            Position = targetTopLeftCorner;
            ItemSize = targetSize;
            Context = context;
            PositionSettings = positionSettings;
            IsOpen = true;
        }

        public bool IsOpenedContext(IItemMoveContext context)
        {
            return Context is TooltipMiniContext.InventoryItem inventoryItemTooltipContext && inventoryItemTooltipContext.Context == context;
        }

        public void Clear()
        {
            Context = null;
            Position = Vector2.zero;
            ItemSize = Vector2.zero;
            PositionSettings = default;
            IsOpen = false;
        }
    }
}
