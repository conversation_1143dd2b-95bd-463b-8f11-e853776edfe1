using Core.Updater;
using UnityEngine;

namespace Game.Battle.Inventory
{
    public class CloseTooltipMiniInputUpdater : IUpdater
    {
        private readonly TooltipMiniModel _tooltipModel;
        private readonly SlotTooltipMiniView _tooltipView;
        private readonly InputActions _inputActions;

        public CloseTooltipMiniInputUpdater(TooltipMiniModel tooltipModel, SlotTooltipMiniView tooltipView, InputActions inputActions)
        {
            _tooltipModel = tooltipModel;
            _tooltipView = tooltipView;
            _inputActions = inputActions;
        }

        public void Update()
        {
            if (_tooltipModel.IsOpen && _inputActions.UI.Click.WasPressedThisFrame())
            {
                var clickPosition = _inputActions.UI.Point.ReadValue<Vector2>();
                var corners = _tooltipView.GetCorners();
                if (clickPosition.x < corners.Item1.x || clickPosition.x > corners.Item2.x || clickPosition.y < corners.Item1.y || clickPosition.y > corners.Item2.y)
                {
                    _tooltipModel.Clear();
                }
            }
        }
    }
}