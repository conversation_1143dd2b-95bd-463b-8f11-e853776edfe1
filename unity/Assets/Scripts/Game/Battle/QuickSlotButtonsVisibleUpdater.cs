using Core.Updater;
using Game.Battle.Inventory;
using Game.Battle.Ui;
using Models.Models.BattleCharacterModel;
using Models.Models.InventoryItemModel;
using Models.References.Inventory;
using Utils.UiState;

namespace Game.Battle
{
    public class QuickSlotButtonsVisibleUpdater : IUpdater
    {
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly StateGroup _primaryActionGroup;
        private readonly InventoryItemViewDescriptions _inventoryItemViewDescriptions;
        private SelectedItemIconType _oldState;

        public QuickSlotButtonsVisibleUpdater(IBattleEntitiesModel battleEntitiesModel, TouchUiView touchUiView, InventoryItemViewDescriptions inventoryItemViewDescriptions)
        {
            _battleEntitiesModel = battleEntitiesModel;
            _primaryActionGroup = touchUiView.ActiveItemStateGroup;
            _inventoryItemViewDescriptions = inventoryItemViewDescriptions;
            _oldState = (SelectedItemIconType)int.MaxValue;
        }

        public void Update()
        {
            if (_battleEntitiesModel.Player != null)
            {
                SelectedItemIconType state = GetState();
                if (state != _oldState)
                {
                    _primaryActionGroup.State = (int) state;
                    _oldState = state;
                }
            }
        }

        private SelectedItemIconType GetState()
        {
            IBattleCharacterModel player = _battleEntitiesModel.Player.ServerModel;

            QuickSlotDescription selectedSlot = player.PrivateModel.SelectedQuickSlot.Slot;
            
            if (selectedSlot == null) 
                return SelectedItemIconType.Empty;
            if (player.PrivateModel.QuickSlots.TryGetItemModel(selectedSlot, out IInventoryItemModel itemModel) && itemModel.IsEmpty) 
                return SelectedItemIconType.Empty;
            
            return _inventoryItemViewDescriptions[itemModel.Value].SelectedItemIconType;
        }

        public enum SelectedItemIconType
        {
            Empty,
            Weapon,
            Medicine,
            ArmorPlate,
            Grenade,
        }
    }
}