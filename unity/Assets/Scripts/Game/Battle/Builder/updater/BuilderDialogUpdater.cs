using System;
using System.Collections.Generic;
using Core;
using Core.Updater;
using Game.Battle.Entities.Plot;
using Game.Battle.Inputs;
using Game.Battle.Inventory;
using Game.Battle.Inventory.RestrictedCells;
using Game.Battle.LootDialog.Updaters;
using Game.Global;
using Game.Global.Ui;
using Game.Shared.Models;
using Inputs;
using Models.Models.PlotBuildingDefenseBlockPrivateModel;
using Models.Models.PlotBuildingDefenseBlockPublicModel;
using Models.Models.PositionModel;
using Models.Models.VehicleModel;
using Models.References;
using Models.References.PlotBuildingDefenseBlock;
using SharedType;
using UnityEngine;

namespace Game.Battle.Builder
{
    public class BuilderDialogUpdater : IUpdater
    {
        private readonly BattleScreenContent _battleScreenContent;
        private readonly IUpdater _updater;
        private readonly IVehicleModel _vehicleModel;
        private readonly BuilderSlotViewDescriptions _builderViewDescriptions;
        private readonly GlobalUISoundModel _globalUISoundModel;
        private readonly BattleUISoundModel _battleUISoundModel;

        public BuilderDialogUpdater(CurrentInventoryModel.Builder currentInventoryModel, BattleScreenContent battleScreenContent, Transform canvasParent, DeviceModel deviceModel, List<IUpdater> mainInputUpdaters,
            BattleModel battleModel, GlobalModel globalModel, IRegisterDisposable registerDisposable)
        {
            _battleScreenContent = battleScreenContent;
            _builderViewDescriptions = battleScreenContent.BuilderSlotViewDescriptions;
            _globalUISoundModel = globalModel.UISoundModel;
            _battleUISoundModel = battleModel.UISoundModel;
            var inventoryModels = battleModel.InventoryModels;
            var inventoryDialogModel = battleModel.InventoryDialogModel;
            var entityId = currentInventoryModel.EntityId;
            bool isDefenseBlock = battleModel.WorldEntitiesModel.PlotBuildingDefenseBlockPrivateModels.TryGetModel(entityId, out var defenseBlock);

            IDisposable disposableContent;
            IDisposable defenseDurabilityContent = null;
            BuilderDialogView uiView;
            PlotDefenseBlockDurabilityView defenseDurabilityView = null;
            switch (deviceModel.CurrentDeviceId)
            {
                case DeviceId.KeyboardMouse:
                {
                    var viewContent = battleScreenContent.KeyboardBuilderDialogView.Generate(canvasParent);
                    disposableContent = viewContent;
                    uiView = viewContent.Value;
                    if (isDefenseBlock)
                    {
                        var defenseBlockDurabilityViewContent = battleScreenContent.KeyboardPlotDefenseBlockDurabilityView.Generate(uiView.HeaderParent);
                        defenseDurabilityContent = defenseBlockDurabilityViewContent;
                        defenseDurabilityView = defenseBlockDurabilityViewContent.Value;
                    }
                    break;
                }
                case DeviceId.Touch:
                {
                    var viewContent = battleScreenContent.TouchBuilderDialogView.Generate(canvasParent);
                    disposableContent = viewContent;
                    uiView = viewContent.Value;
                    if (isDefenseBlock)
                    {
                        var defenseBlockDurabilityViewContent = battleScreenContent.TouchPlotDefenseBlockDurabilityView.Generate(uiView.HeaderParent);
                        defenseDurabilityContent = defenseBlockDurabilityViewContent;
                        defenseDurabilityView = defenseBlockDurabilityViewContent.Value;
                    }
                    break;
                }
                default:
                    throw new ArgumentOutOfRangeException();
            }


            var updaters = new List<IUpdater>();
            _updater = new CompositeUpdater(updaters);

            inventoryModels.BuilderSlotsModel = CreateBuilderSlotsModel(entityId, battleModel);
            updaters.AddRange(new IUpdater[]
            {
                new BuilderRestrictedCellsModelUpdater(battleModel.BattleEntitiesModel.Player.ServerModel, inventoryModels.RestrictedCellsModel, inventoryModels.BuilderSlotsModel),
                new BuilderSlotsUpdater(inventoryModels.BuilderSlotsModel),
                new BuilderViewUpdater(inventoryModels, registerDisposable, uiView, battleScreenContent, battleModel.BattleEntitiesModel.Player, inventoryModels.BuilderSlotsModel),
            });

            if (TryGetResetByDistanceDialogParams(entityId, battleModel, inventoryModels.BuilderSlotsModel, out var distanceParams))
            {
                updaters.Add(new ResetDialogByDistanceUpdater(battleModel.BattleEntitiesModel.Player.ServerModel, inventoryDialogModel, distanceParams.Item1, distanceParams.Item2));
            }
            else if (battleModel.BattleEntitiesModel.WorldEntitiesModel.InteractEntities.TryGetModel(entityId, out var interactEntityModel))
            {
                updaters.Add(new ResetInteractEntityModelDialogByDistanceUpdater(battleModel.BattleEntitiesModel.Player.ServerModel, inventoryDialogModel, interactEntityModel));
            }

            if (battleModel.WorldEntitiesModel.BreachEntities.TryGetModel(entityId, out var breachEntityModel))
            {
                updaters.Add(new ResetDialogByBreachUpdater(inventoryDialogModel, breachEntityModel.BreachStateEntityModel));
            }

            if (isDefenseBlock)
            {
                var tooltipInputModel = new InputModel();
                mainInputUpdaters.Add(new PointerButtonDelayInputUpdater(tooltipInputModel, defenseDurabilityView.InfoTooltipButton, PointerButtonDelayInputUpdater.TriggerMode.Hover, TooltipRules.TooltipEnterTime));
                updaters.Add(new PlotDefenseBlockDurabilityViewUpdater(battleModel.TooltipMiniModel, tooltipInputModel, defenseDurabilityView, defenseBlock, battleScreenContent.LocalizationKeys.BuilderLocalization,
                                                                       registerDisposable));
            }

            mainInputUpdaters.Add(new BuilderInputHandler(battleModel, uiView, inventoryModels.BuilderSlotsModel));

            PlayOpen(entityId, battleModel);
            globalModel.CursorLockModel.Add(CursorLockKey.Dialog, CursorLockMode.None);
            registerDisposable.Register(() =>
            {
                globalModel.CursorLockModel.Remove(CursorLockKey.Dialog);
                defenseDurabilityContent?.Dispose();
                disposableContent.Dispose();
            });
        }

        private void PlayOpen(int entityId, BattleModel battleModel)
        {
            if (battleModel.WorldEntitiesModel.Plots.TryGetModel(entityId, out var plotModel))
            {
                _globalUISoundModel.PlaySound(GlobalUISoundType.OpenDialog);
                return;
            }

            plotModel = battleModel.WorldEntitiesModel.SlotPlotModels[battleModel.WorldEntitiesModel.PlotSlotByEntity[entityId]];
            var plotDescription = plotModel.PlotDescription;
            if (plotDescription.MainBuildingSlot.EntityId == entityId)
            {
                _battleUISoundModel.PlaySound(BattleUISoundType.OpenVanDialog);
                return;
            }

            _globalUISoundModel.PlaySound(GlobalUISoundType.OpenDialog);
        }

        private BuilderSlotsModel CreateBuilderSlotsModel(int entityId, BattleModel battleModel)
        {
            if (battleModel.WorldEntitiesModel.Plots.TryGetModel(entityId, out var plotModel))
            {
                return new BuilderSlotsModel(_builderViewDescriptions.Names.Plot, plotModel);
            }

            plotModel = battleModel.WorldEntitiesModel.SlotPlotModels[battleModel.WorldEntitiesModel.PlotSlotByEntity[entityId]];
            var plotDescription = plotModel.PlotDescription;
            if (plotDescription.MainBuildingSlot.EntityId == entityId)
            {
                _builderViewDescriptions.Names.MainBuilding["0"] = _battleScreenContent.PlotMainBuildingViewDescriptions[plotModel.MainBuildingSlot.Value].Name;
                return new BuilderSlotsModel(_builderViewDescriptions.Names.MainBuilding, plotDescription.MainBuildingEntityIds, plotModel.MainBuilding);
            }

            foreach (var (outbuildingSlotDescription, outbuildingLocationPlotConstructionSlotDescription) in plotDescription.OutbuildingSlots)
            {
                if (entityId == outbuildingLocationPlotConstructionSlotDescription.EntityId)
                {
                    _builderViewDescriptions.Names.Outbuilding["0"] = _battleScreenContent.PlotOutbuildingViewDescriptions[plotModel.OutbuildingSlots[outbuildingSlotDescription].Value].Name;
                    return new BuilderSlotsModel(_builderViewDescriptions.Names.Outbuilding, plotDescription.OutbuildingEntityIds[outbuildingSlotDescription],
                        plotModel.Outbuildings[outbuildingSlotDescription]);
                }
            }

            if (battleModel.WorldEntitiesModel.CargoWorkbenches.TryGetModel(entityId, out var cargoWorkbenchModel))
            {
                return new BuilderSlotsModel(_builderViewDescriptions.Names.CargoWorkbench, plotDescription.MainBuildingEntityIds.CargoWorkbenchEntityIds, cargoWorkbenchModel);
            }

            if (battleModel.WorldEntitiesModel.CopFurnitures.TryGetModel(entityId, out var copFurnitureModel))
            {
                return new BuilderSlotsModel(_builderViewDescriptions.Names.CopFurniture, plotDescription.MainBuildingEntityIds.CopFurniture, copFurnitureModel);
            }

            if (battleModel.WorldEntitiesModel.Collections.TryGetModel(entityId, out var collectionTableModel))
            {
                return new BuilderSlotsModel(_builderViewDescriptions.Names.CollectionTable, plotDescription.MainBuildingEntityIds.CollectionTable, collectionTableModel);
            }

            if (battleModel.WorldEntitiesModel.MoneyWorkbenches.TryGetModel(entityId, out var moneyWorkbenchModel))
            {
                return new BuilderSlotsModel(_builderViewDescriptions.Names.MoneyWorkbench, plotDescription.MainBuildingEntityIds.MoneyWorkbench, moneyWorkbenchModel);
            }

            int publicModelEntityId = RaidDefenseBlockSettingsDescription.GetDefenseBlockPublicModelEntityId(entityId);
            if (battleModel.WorldEntitiesModel.PlotBuildingDefenseBlockPrivateModels.TryGetModel(entityId, out IPlotBuildingDefenseBlockPrivateModel defenseBlockPrivateModel) &&
                battleModel.WorldEntitiesModel.PlotBuildingDefenseBlockPublicModels.TryGetModel(publicModelEntityId, out IPlotBuildingDefenseBlockPublicModel defenseBlockPublicModel))
            {
                return new BuilderSlotsModel(_builderViewDescriptions.Names.DefensePanel, defenseBlockPrivateModel, defenseBlockPublicModel);
            }

            throw new NotImplementedException();
        }

        private int GetFirstSlotEntityId(BuilderSlotsModel builderSlotsModel)
        {
            foreach (var item in builderSlotsModel.Groups)
            {
                foreach (var slot in item.Slots)
                {
                    return slot.EntityId;
                }
            }

            return -1;
        }

        private bool TryGetResetByDistanceDialogParams(int entityId, BattleModel battleModel, BuilderSlotsModel builderSlotsModel, out (IPositionModel, float) distanceParams)
        {
            var firstSlotEntityId = GetFirstSlotEntityId(builderSlotsModel);
            if (battleModel.WorldEntitiesModel.PlotBuilderSlots.TryGetModel(firstSlotEntityId, out var model))
            {
                distanceParams = (new PositionModel(model.Position), model.InteractSqrDistance);
                return true;
            }

            if (battleModel.WorldEntitiesModel.Plots.TryGetModel(entityId, out var plotModel))
            {
                distanceParams = (new PositionModel(plotModel.PlotDescription.BuilderConstructionSlotsPosition), BattleDistancesDescription.BuilderSlotInteractSqrDistance);
                return true;
            }

            var panelEntityId = RaidDefenseBlockSettingsDescription.GetDefenseBlockPublicModelEntityId(entityId);
            if (battleModel.WorldEntitiesModel.PlotBuildingDefenseBlockPublicModels.TryGetModel(panelEntityId, out var defenseBlockPublicModel))
            {
                distanceParams = (new PositionModel(defenseBlockPublicModel.DescriptionModel.Value.Position), BattleDistancesDescription.BuilderSlotInteractSqrDistance);
                return true;
            }

            plotModel = battleModel.WorldEntitiesModel.SlotPlotModels[battleModel.WorldEntitiesModel.PlotSlotByEntity[entityId]];
            var plotDescription = plotModel.PlotDescription;
            if (plotDescription.MainBuildingSlot.EntityId == entityId)
            {
                distanceParams = (new PositionModel(plotModel.MainBuilding.DescriptionModel.Value.SlotsInteractPosition), BattleDistancesDescription.BuilderSlotInteractSqrDistance);
                return true;
            }

            foreach (var (outbuildingSlotDescription, outbuildingLocationPlotConstructionSlotDescription) in plotDescription.OutbuildingSlots)
            {
                if (entityId == outbuildingLocationPlotConstructionSlotDescription.EntityId)
                {
                    distanceParams = (new PositionModel(plotModel.Outbuildings[outbuildingSlotDescription].DescriptionModel.Value.SlotsInteractPosition), BattleDistancesDescription.BuilderSlotInteractSqrDistance);
                    return true;
                }
            }

            distanceParams = default;
            return false;
        }


        public void Update()
        {
            _updater.Update();
        }
    }
}