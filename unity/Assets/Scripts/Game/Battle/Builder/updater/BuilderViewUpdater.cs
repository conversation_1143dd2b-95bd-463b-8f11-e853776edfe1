using Core;
using Core.Updater;
using Game.Battle.EntityModel.Player;
using Game.Battle.Inventory;
using System.Collections.Generic;

namespace Game.Battle.Builder
{
    public class BuilderViewUpdater : IUpdater
    {
        private readonly IUpdater _updaters;


        public BuilderViewUpdater(InventoryModels inventoryModels, IRegisterDisposable registerDisposable, BuilderDialogView inventoryUiView, BattleScreenContent battleScreenContent, PlayerEntityModel player,
            BuilderSlotsModel builderSlotsModel)
        {
            var playerClientModel = player.ClientModel;

            var updaterList = new List<IUpdater>();
            var groupIndex = 0;
            
            inventoryUiView.Header.StringReference = builderSlotsModel.Header;
            foreach (var (groupType, slotModels) in builderSlotsModel.Groups)
            {
                var groupView = inventoryUiView.GroupViews[groupIndex];
                groupView.SetActive(true);
                groupView.SetTitle(battleScreenContent.BuilderSlotViewDescriptions.Groups[groupType].Title);
                groupIndex++;
                var slotIndex = 0;
                foreach (var slotModel in slotModels)
                {
                    var view = groupView.Items[slotIndex];
                    view.SetActive(true);
                    var itemMoveContext = slotModel.CreateMoveContext(); 
                    slotIndex++;
                    updaterList.Add(new BlockedSlotBusyUpdater(registerDisposable, itemMoveContext, playerClientModel.BlockedSlotsModel, playerClientModel.InventoryIntention));
                    updaterList.Add(new BuilderSlotModelUpdater(slotModel, inventoryModels, itemMoveContext, registerDisposable, view));
                    updaterList.Add(new BuilderSlotIconUpdater(slotModel.InventoryItemModel, view, battleScreenContent.InventoryItemViewDescriptions, battleScreenContent.BuilderSlotViewDescriptions, slotModel.Type));
                    updaterList.Add(new InventorySlotBlockedUpdater(itemMoveContext, playerClientModel.BlockedSlotsModel, inventoryModels.InventoryDragAndDropModel, view.SlotBlockedView,
                        view.SlotLockedView, view.DraggableItemView, view.PointerButtonView, playerClientModel.BuyBlockedSlotsModel,
                        playerClientModel.BuyLockedBySettlementBuildingsModel, playerClientModel.LockedTraderItemByLocationTier));
                    updaterList.Add(new BuilderGlowViewUpdater(view, slotModel, inventoryModels.InventoryDragAndDropModel));
                }

                for (var j = slotIndex; j < groupView.Items.Length; j++)
                {
                    groupView.Items[j].SetActive(false);
                }
            }

            for (var j = groupIndex; j < inventoryUiView.GroupViews.Length; j++)
            {
                inventoryUiView.GroupViews[j].SetActive(false);
            }

            _updaters = new CompositeUpdater(updaterList);
        }

        public void Update()
        {
            _updaters.Update();
        }
    }
}