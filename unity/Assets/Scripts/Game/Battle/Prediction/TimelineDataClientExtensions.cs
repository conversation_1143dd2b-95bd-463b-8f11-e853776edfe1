using Game.Battle.Prediction.References;
using Models.Timeline;
using Models.Timeline.Data.Entity;
using Models.Timeline.Data.Internal;
using Models.Utils.Timeline.Data;
using System;

namespace Game.Battle.Prediction
{
    public static class TimelineDataClientExtensions
    {
        public static readonly TimelineDelegates.CalculateErrorType<CarTimelineTickData> CalculateErrorTypeForCarTickFunc = CalculateErrorTypeForCarTick;
        public static readonly TimelineDelegates.CalculateErrorType<CarTimelineSubTickData> CalculateErrorTypeForCarSubTickFunc = CalculateErrorTypeForCarSubTick;
        public static readonly TimelineDelegates.CalculateErrorType<HelicopterTimelineData> CalculateErrorTypeForHelicopterFunc = CalculateErrorTypeForHelicopter;
        
        private static TimelineErrorType CalculateErrorTypeForCarTick(in CarTimelineTickData first, in CarTimelineTickData second)
        {
            return TimelineErrorType.Small;
        }
        
        private static TimelineErrorType CalculateErrorTypeForCarSubTick(in CarTimelineSubTickData first, in CarTimelineSubTickData second)
        {
            PhysicsBodyTimelineData firstPhysicsBodyData = CarTimelineDataExtensions.CastToPhysicsBodyDataFunc(first);
            PhysicsBodyTimelineData secondPhysicsBodyData = CarTimelineDataExtensions.CastToPhysicsBodyDataFunc(second);
            return CalculateErrorTypeForPhysicsBody(firstPhysicsBodyData, secondPhysicsBodyData);
        }
        
        private static TimelineErrorType CalculateErrorTypeForHelicopter(in HelicopterTimelineData first, in HelicopterTimelineData second)
        {
            PhysicsBodyTimelineData firstPhysicsBodyData = HelicopterTimelineDataExtensions.CastToPhysicsBodyDataFunc(first);
            PhysicsBodyTimelineData secondPhysicsBodyData = HelicopterTimelineDataExtensions.CastToPhysicsBodyDataFunc(second);
            return CalculateErrorTypeForPhysicsBody(firstPhysicsBodyData, secondPhysicsBodyData);
        }

        private static TimelineErrorType CalculateErrorTypeForPhysicsBody(in PhysicsBodyTimelineData firstPhysicsBodyData, in PhysicsBodyTimelineData secondPhysicsBodyData)
        {
            DeadReckoningErrorType errorType = DeadReckoningErrorExtension.CalculateErrorType(firstPhysicsBodyData, secondPhysicsBodyData);
            
            return errorType switch
            {
                DeadReckoningErrorType.Small => TimelineErrorType.Small,
                DeadReckoningErrorType.Medium => TimelineErrorType.Medium,
                DeadReckoningErrorType.Warp => TimelineErrorType.Warp,
                _ => throw new ArgumentOutOfRangeException()
            };
        }
    }
}