using Core.Updater;
using Game.Battle.EntityModel.Helicopter;
using Game.Battle.EntityModel.Helicopter.Models;
using Game.Battle.EntityModel.Player;
using Game.Global.Settings;
using Models.Prediction.BattleInputModel;
using UnityEngine;
using UnityEngine.InputSystem;

namespace Game.Battle.Inputs.CompositeUpdaters
{
    public class HelicopterYawThrustCoDInputUpdater : IUpdater
    {
        private readonly IBattleInputModel _battleInputModel;
        private readonly IChangeBufferValue<HelicopterInputSetting> _inputSetting;
        private readonly InputAction _thrust;
        private readonly InputAction _yaw;
        private readonly PlayerRotationInputModel _playerRotationInput;
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly HelicopterViewDescriptions _helicopterViewDescriptions;
        private readonly InputAction _helicopterMouseControl;

        public HelicopterYawThrustCoDInputUpdater(IBattleInputModel battleInputModel,IChangeBufferValue<HelicopterInputSetting> inputSetting, InputAction thrust, InputAction yaw, PlayerRotationInputModel playerRotationInput, IBattleEntitiesModel battleEntitiesModel, HelicopterViewDescriptions helicopterViewDescriptions)
        {
            _battleInputModel = battleInputModel;
            _inputSetting = inputSetting;
            _thrust = thrust;
            _yaw = yaw;
            _playerRotationInput = playerRotationInput;
            _battleEntitiesModel = battleEntitiesModel;
            _helicopterViewDescriptions = helicopterViewDescriptions;
        }

        public void Update()
        {
            if (_inputSetting.CurrentValue != HelicopterInputSetting.Alternative_CoD) return;
            
            if (_battleEntitiesModel[_battleEntitiesModel.Player.ServerModel.InVehicleModel.EntityId] is HelicopterEntityModel helicopterEntityModel)
            {
                var rotation = helicopterEntityModel.ClientModel.Rotation;
                Vector2 direction = new(0, _thrust.ReadValue<float>());
                if (!_yaw.IsPressed())
                {
                    var inputYaw = _playerRotationInput.Yaw * Mathf.Rad2Deg;
                    var angleYaw = Mathf.DeltaAngle(rotation.Yaw, inputYaw);
                    direction.x = Mathf.Sign(angleYaw) * Mathf.Pow(Mathf.Abs(angleYaw) / _helicopterViewDescriptions.MouseYawClamp, _helicopterViewDescriptions.MouseYawPow);
                }

                if (direction.sqrMagnitude > float.Epsilon)
                {
                    _battleInputModel.MoveDirection = new System.Numerics.Vector2(direction.x, direction.y);
                }
            }
        }
    }
}