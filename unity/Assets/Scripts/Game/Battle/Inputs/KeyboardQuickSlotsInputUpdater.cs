using Core.Updater;
using Models.Models.BattleCharacterModel;
using Models.Models.InventoryItemModel;
using Models.Models.QuickSlotsModel;
using Models.Prediction.BattleInputModel;
using Models.References.Inventory;

namespace Game.Battle.Inputs
{
    public class KeyboardQuickSlotsInputUpdater : IUpdater
    {
        private readonly QuickSlotsInputModel _quickSlotsInputModel;
        private readonly IBattleInputModel _battleInputModel;
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private QuickSlotDescription _triggeredSlot;

        public KeyboardQuickSlotsInputUpdater(QuickSlotsInputModel quickSlotsInputModel, IBattleInputModel battleInputModel, IBattleEntitiesModel battleEntitiesModel)
        {
            _quickSlotsInputModel = quickSlotsInputModel;
            _battleInputModel = battleInputModel;
            _battleEntitiesModel = battleEntitiesModel;
        }

        public void Update()
        {
            UpdateSelectSlotInput();
            UpdateToggleSelectedSlotInput();
            ClearInput();
        }

        private void UpdateToggleSelectedSlotInput()
        {
            if (_quickSlotsInputModel.ToggleSlot.IsTriggered )
            {
                IBattleCharacterModel model = _battleEntitiesModel.Player.ServerModel;
                QuickSlotDescription slot = model.PrivateModel.SelectedQuickSlot.Slot;
                if (slot != null && model.PrivateModel.QuickSlots.TryGetItemModel(slot, out IInventoryItemModel itemModel) && !itemModel.IsEmpty)
                {
                    _battleInputModel.QuickSlot = slot;
                }
                else if (slot == null )
                {
                    if (_quickSlotsInputModel.AutoSelectedQuickSlot != null && model.PrivateModel.QuickSlots.TryGetItemModel(_quickSlotsInputModel.AutoSelectedQuickSlot, out itemModel) && !itemModel.IsEmpty)
                    {
                        _battleInputModel.QuickSlot = _quickSlotsInputModel.AutoSelectedQuickSlot;
                    }
                    else
                    {
                        _battleInputModel.QuickSlot = FindSuitableSlot(model.PrivateModel.QuickSlots);
                    }
                }
            }
        }

        private void UpdateSelectSlotInput()
        {
            QuickSlotDescription triggeredSlot = GetTriggeredSlot();
            if (triggeredSlot != null)
            {
                IBattleCharacterModel model = _battleEntitiesModel.Player.ServerModel;
                if (model.PrivateModel.QuickSlots.TryGetItemModel(triggeredSlot, out var itemModel) && !itemModel.IsEmpty && model.PrivateModel.SelectedQuickSlot.Slot != triggeredSlot)
                {
                    if (_battleInputModel.QuickSlot != triggeredSlot)
                    {
                        _battleInputModel.QuickSlot = triggeredSlot;
                    }
                }
            }
        }
        
        private QuickSlotDescription GetTriggeredSlot()
        {
            if (_quickSlotsInputModel.WeaponSlot0.IsTriggered) return QuickSlotDescription.Weapon0;
            if (_quickSlotsInputModel.WeaponSlot1.IsTriggered) return QuickSlotDescription.Weapon1;
            if (_quickSlotsInputModel.PocketSlot0.IsTriggered) return QuickSlotDescription.Pocket0;
            if (_quickSlotsInputModel.PocketSlot1.IsTriggered) return QuickSlotDescription.Pocket1;
            if (_quickSlotsInputModel.PocketSlot2.IsTriggered) return QuickSlotDescription.Pocket2;
            if (_quickSlotsInputModel.PocketSlot3.IsTriggered) return QuickSlotDescription.Pocket3;

            return null;
        }
        
        private QuickSlotDescription FindSuitableSlot(IQuickSlotsModel quickSlots)
        {
            IInventoryItemModel item;
            if (quickSlots.TryGetItemModel(QuickSlotDescription.Weapon0, out item) && !item.IsEmpty) return QuickSlotDescription.Weapon0;
            if (quickSlots.TryGetItemModel(QuickSlotDescription.Weapon1, out item) && !item.IsEmpty) return QuickSlotDescription.Weapon1;
            if (quickSlots.TryGetItemModel(QuickSlotDescription.Pocket0, out item) && !item.IsEmpty) return QuickSlotDescription.Pocket0;
            if (quickSlots.TryGetItemModel(QuickSlotDescription.Pocket1, out item) && !item.IsEmpty) return QuickSlotDescription.Pocket1;
            if (quickSlots.TryGetItemModel(QuickSlotDescription.Pocket2, out item) && !item.IsEmpty) return QuickSlotDescription.Pocket2;
            if (quickSlots.TryGetItemModel(QuickSlotDescription.Pocket3, out item) && !item.IsEmpty) return QuickSlotDescription.Pocket3;
            
            return null;
        }

        private void ClearInput()
        {
            _quickSlotsInputModel.WeaponSlot0.IsTriggered = false;
            _quickSlotsInputModel.WeaponSlot1.IsTriggered = false;
            _quickSlotsInputModel.PocketSlot0.IsTriggered = false;
            _quickSlotsInputModel.PocketSlot1.IsTriggered = false;
            _quickSlotsInputModel.PocketSlot2.IsTriggered = false;
            _quickSlotsInputModel.PocketSlot3.IsTriggered = false;
            _quickSlotsInputModel.ToggleSlot.IsTriggered = false;
        }
    }
}