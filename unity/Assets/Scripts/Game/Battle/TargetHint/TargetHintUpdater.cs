using Core;
using Core.Updater;
using Game.Battle.Entities.Player.Camera;
using Game.Battle.UiUpdater;
using Game.Targets;
using System.Collections.Generic;
using UnityEngine;

namespace Game.Battle.TargetHint
{
    public class TargetHintUpdater : IUpdater
    {
        private const float _minSqrMagnitude = 0.002f;
        private const float _maxSqrMagnitude = 0.2f;

        private readonly TargetModel _targetModel;
        private readonly TargetHintModel _targetHintModel;
        private readonly BattleCameraModel _cameraModel;
        private readonly IBattleEntitiesModel _battleEntitiesModel;
        private readonly TargetHintViewDescriptions _targetHintViewDescriptions;
        private readonly Camera _camera;

        private readonly Dictionary<Collider, HintParamsModel> _colliderHintParameters = new();
        private readonly LinkedList<Collider> _raycastQueue = new();
        private readonly RaycastHit[] _raycastResults = new RaycastHit[7];
        private readonly int _layerMask = LayerId.ToMask(LayerId.Default) | LayerId.ToMask(LayerId.Target) | LayerId.ToMask(LayerId.Player) | LayerId.ToMask(LayerId.Glass);
        private readonly List<Collider> _toRemoveColliders = new();

        public TargetHintUpdater(BattleModel battleModel, IBattleEntitiesModel battleEntitiesModel, BattleScreenContent battleScreenContent, Camera camera)
        {
            _targetModel = battleModel.TargetModel;
            _targetHintModel = battleModel.TargetHintModel;
            _cameraModel = battleModel.BattleCameraModel;
            _battleEntitiesModel = battleEntitiesModel;
            _targetHintViewDescriptions = battleScreenContent.TargetHintViewDescriptions;
            _camera = camera;

            _targetHintModel.ClearHintParameters();
        }

        public void Update()
        {
            bool canShow = _battleEntitiesModel.Player != null && _targetModel.IsEnabled &&
                           !_battleEntitiesModel.Player.ServerModel.InVehicleModel.IsInsideVehicle &&
                           !_battleEntitiesModel.Player.ServerModel.PrivateModel.HideObjectStateModel.IsHidden;
            if (canShow)
            {
                foreach ((var collider, var targetHintData) in _targetHintModel.TargetColliders)
                {
                    Vector3 pos;
                    float maxLen;
                    if (targetHintData.Position == null)
                    {
                        pos = collider.transform.position;
                        maxLen = _targetHintViewDescriptions.MaxDistanceSqr;
                    }
                    else
                    {
                        pos = targetHintData.Position.GetPosition();
                        maxLen = targetHintData.Position.MaxLenSqr;
                    }

                    if ((pos - _cameraModel.Position).sqrMagnitude < maxLen)
                    {
                        Vector3 viewportPosition = _camera.WorldToViewportPoint(pos);
                        if (viewportPosition is { z: > 0, x: >= 0 and <= 1, y: >= 0 and <= 1 })
                        {
                            if (_colliderHintParameters.TryGetValue(collider, out var res))
                            {
                                res.SetPositions(pos, viewportPosition);
                                UpdateIsHovered(res, targetHintData);
                                UpdateAlpha(res);
                            }
                            else
                            {
                                _colliderHintParameters.Add(collider, new HintParamsModel(pos, viewportPosition));
                                _raycastQueue.AddFirst(collider);
                            }
                        }
                        else
                        {
                            if (_colliderHintParameters.ContainsKey(collider))
                            {
                                _toRemoveColliders.Add(collider);
                            }
                        }
                    }
                    else
                    {
                        if (_colliderHintParameters.ContainsKey(collider))
                        {
                            _toRemoveColliders.Add(collider);
                        }
                    }
                }

                foreach (var item in _colliderHintParameters.Keys)
                {
                    if (!_targetHintModel.TargetColliders.ContainsKey(item))
                    {
                        _toRemoveColliders.Add(item);
                    }
                }
            }
            else
            {
                _toRemoveColliders.AddRange(_colliderHintParameters.Keys);
            }

            foreach (var collider in _toRemoveColliders)
            {
                _colliderHintParameters.Remove(collider);
                _raycastQueue.Remove(collider);
                _targetHintModel.RemoveHint(collider);
            }
            _toRemoveColliders.Clear();

            if (_raycastQueue.Count > 0)
            {
                var node = _raycastQueue.First;
                var collider = node.Value;
                _raycastQueue.RemoveFirst();
                _raycastQueue.AddLast(node);

                var hintParamModel = _colliderHintParameters[collider];
                var hintData = _targetHintModel.TargetColliders[collider];
                var direction = hintParamModel.GlobalPosition - _cameraModel.Position;
                var raycastNum = Physics.RaycastNonAlloc(_cameraModel.Position, direction, _raycastResults, direction.magnitude, _layerMask);
                var isBlockingWithoutPlayer = BlockCalculate(raycastNum, collider, hintData, true);
                if (isBlockingWithoutPlayer)
                {
                    _targetHintModel.RemoveHint(collider);
                }
                else
                {
                    var isBlockingWithPlayer = BlockCalculate(raycastNum, collider, hintData, false);
                    hintParamModel.SetBlockingWithPlayer(isBlockingWithPlayer);
                    _targetHintModel.AddHint(collider, hintParamModel);
                }
            }
        }

        private bool BlockCalculate(int raycastNum, Collider collider, TargetHintData hintData, bool ignorePlayer)
        {
            float colliderDistance = float.MaxValue;
            float solidObjectDistance = float.MaxValue;

            bool wasBlock = false;

            for (var i = 0; i < raycastNum; i++)
            {
                var raycastHit = _raycastResults[i];

                var hitLayer = raycastHit.transform.gameObject.layer;

                if (raycastHit.collider == collider)
                {
                    colliderDistance = raycastHit.distance;
                }
                else
                {
                    bool isSolidObject = _targetHintModel.TargetHintBlockingGlasses.Contains(raycastHit.collider) || hitLayer != LayerId.Glass;
                    bool needIgnore = hintData.IgnoreColliders?.Contains(raycastHit.collider) ?? ignorePlayer && hitLayer == LayerId.Player;
                    if (isSolidObject && !needIgnore && raycastHit.distance < solidObjectDistance)
                    {
                        wasBlock = true;
                        solidObjectDistance = raycastHit.distance;
                    }
                }
            }

            return wasBlock && colliderDistance >= solidObjectDistance;
        }

        private void UpdateIsHovered(HintParamsModel hintParamsModel, TargetHintData hintData)
        {
            hintParamsModel.SetIsHovered(_targetModel.TryGetTarget(out var targetAction) && hintData.TargetActions.Contains(targetAction));
        }

        private void UpdateAlpha(HintParamsModel hintParamsModel)
        {
            const float delta = _maxSqrMagnitude - _minSqrMagnitude;
            
            var screenNormalizedPosition = (Vector2)hintParamsModel.ScreenNormalizedPosition;
            var screenCenter = new Vector2(0.5f, 0.5f);
            var fromScreenCenterSqrMagnitude = (screenNormalizedPosition - screenCenter).sqrMagnitude;

            float alphaByScreen = Mathf.Lerp(0f, 1f, 1f - (fromScreenCenterSqrMagnitude - _minSqrMagnitude) / delta);
            float alphaByDistance = 1f - Mathf.InverseLerp(_targetHintViewDescriptions.MinDistance, _targetHintViewDescriptions.MaxDistance, hintParamsModel.ScreenNormalizedPosition.z);

            hintParamsModel.SetAlpha(alphaByScreen * alphaByDistance);
        }
    }
}