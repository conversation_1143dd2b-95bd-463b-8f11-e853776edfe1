using UnityEngine;
using UnityEngine.UI;
using Utils.UiState;

namespace Game.Battle.TargetHint
{
    public class TargetHintMarkerView : MonoBehaviour
    {
        [SerializeField] private RectTransform _rectTransform;
        [SerializeField] private Image _image;
        [SerializeField] private StateGroup _stateGroup;

        public void SetNormalizedPosition(Vector3 position)
        {
            Vector2 vector2 = position;
            _rectTransform.anchorMin = vector2;
            _rectTransform.anchorMax = vector2;
        }

        public void SetAlpha(float alphaNormalized)
        {
            _image.color = _image.SetAlpha(alphaNormalized);
        }

        public void SetScale(float value)
        {
            _rectTransform.localScale = new Vector3(value, value, value);
        }

        public void SetIsHovered(bool value)
        {
            _stateGroup.State = value ? 1 : 0;
        }
    }
}