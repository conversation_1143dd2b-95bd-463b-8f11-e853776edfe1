using Core;
using Core.Updater;
using Game.Battle.Inventory;
using Game.Global.Ui;
using UnityEngine;

namespace Game.Battle.UiUpdater
{
    public class TooltipVisiblePositionUpdater : IUpdater
    {
        private readonly ItemTooltipView _tooltipView;
        private readonly TooltipModel _tooltipModel;

        private bool _isOpen;
        private Vector2 _oldPosition, _oldSize;
        private bool _isDescriptionOpen;
        private bool _isActionOpen;

        public TooltipVisiblePositionUpdater(ItemTooltipView tooltipView, TooltipModel tooltipModel, IRegisterDisposable disposable)
        {
            _tooltipModel = tooltipModel;
            _tooltipView = tooltipView;
            _oldPosition = _tooltipModel.Position;

            _tooltipView.SetActive(false, false, false);
            _tooltipView.RectChangeListener.RectChanged += UpdatePosition;
            disposable.Register(() => _tooltipView.RectChangeListener.RectChanged -= UpdatePosition);
        }

        public void Update()
        {
            UpdatePosition();
        }

        private void UpdatePosition()
        {
            Vector2 size = _tooltipView.GetCurrentSize();
            var isOpenChanged = _isOpen != _tooltipModel.IsOpen || _tooltipModel.IsDescriptionOpen != _isDescriptionOpen || _tooltipModel.IsActionsOpen != _isActionOpen || size != _oldSize;
            if (_oldPosition != _tooltipModel.Position || isOpenChanged)
            {
                _oldPosition = _tooltipModel.Position;
                _oldSize = size;

                if (_tooltipModel.IsOpen)
                {
                    var tooltipPosition = TooltipRules.GetPosition(_oldPosition, _tooltipModel.ItemSize, _oldSize, TooltipPositionSettings.Item);
                    _tooltipView.SetPosition(tooltipPosition);
                }
            }

            _isOpen = _tooltipModel.IsOpen;
            _isDescriptionOpen = _tooltipModel.IsDescriptionOpen;
            _isActionOpen = _tooltipModel.IsActionsOpen;
        } 
    }
}