using System;
using Core.Updater;
using Game.Battle.PlayerContextMenu.Enums;
using Game.Battle.PlayerContextMenu.View;
using Game.Global;
using Game.Global.Clan.Model;

namespace Game.Battle.PlayerContextMenu.Updaters
{
    public class PlayerContextMenuButtonsActiveUpdater : IUpdater
    {
        private readonly PlayerContextMenuView _dialogView;
        private readonly Func<ContextMenuButtonType, bool> _getIsActive;

        public PlayerContextMenuButtonsActiveUpdater(PlayerContextMenuView dialogView, Func<ContextMenuButtonType, bool> getIsActive)
        {
            _dialogView = dialogView;
            _getIsActive = getIsActive;

            foreach (var button in _dialogView.Buttons)
            {
                button.ButtonAnimatorView.SetForceActive(_getIsActive(button.ContextMenuButtonType));
            }
        }

        public void Update()
        {
            foreach (var button in _dialogView.Buttons)
            {
                button.ButtonAnimatorView.SetActive(_getIsActive(button.ContextMenuButtonType));
            }
        }
    }
}