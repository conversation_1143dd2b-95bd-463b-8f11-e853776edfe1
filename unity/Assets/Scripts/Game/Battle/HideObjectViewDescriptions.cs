using System;
using AK.Wwise;
using Content.ContentWrappers;
using Core;
using Framework.Core.Identified;
using Game.Locations;
using Models.References.HideObject;
using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.Localization;
using Event = AK.Wwise.Event;

namespace Game.Battle
{
    public class HideObjectViewDescriptions : ViewDescriptions<HideObjectDescription, HideObjectViewDescription>
    {
        [field: SerializeField] public LocalizedString SearchString { get; private set; }
        [field: SerializeField] public LocalizedString HideString { get; private set; }
    }

    [Serializable]
    public class HideObjectViewDescription : IIdentified, ISerializationCallbackReceiver
    {
        [HideInInspector] [SerializeField] private string _id;
        [field: SerializeField] public IdentifiedProperty<HideObjectDescription> Identified { get; private set; }
        public string Id => Identified.Id;
        
        [field: SerializeField] public Event HideEvent { get; private set; }
        [field: SerializeField] public Event HideClapEvent { get; private set; }
        [field: SerializeField] public Event SearchEvent { get; private set; }
        [field: SerializeField] public LocalizedString Name { get; private set; }
        [field: SerializeField] public float CharacterNicknameOffset { get; private set; }

        void ISerializationCallbackReceiver.OnBeforeSerialize() { }

        void ISerializationCallbackReceiver.OnAfterDeserialize()
        {
            _id = Identified.Id;
        }
    }
}