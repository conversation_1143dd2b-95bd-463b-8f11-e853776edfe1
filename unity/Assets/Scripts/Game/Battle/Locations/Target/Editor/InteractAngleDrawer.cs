using UnityEditor;
using UnityEngine;

namespace Game.Battle.Target.Editor
{
    [CustomPropertyDrawer(typeof(InteractAngle))]
    public class InteractAngleDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            var angleProperty = property.FindPropertyRelative("_angle");
            var cosProperty = property.FindPropertyRelative($"<{nameof(InteractAngle.Cos)}>k__BackingField");
            
            EditorGUI.PropertyField(position, angleProperty, label, true);
            cosProperty.floatValue = Mathf.Cos(angleProperty.floatValue * .5f * Mathf.Deg2Rad);
        }
    }
}