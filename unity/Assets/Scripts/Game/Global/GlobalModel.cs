using ClientCore.ApplyDataUpdater;
using ClientCore.Models.DelayedResponsesModel;
using ClientCore.Time;
using Core;
using Game.Battle.Inputs;
using Game.Global.Chat;
using Game.Global.Chat.BlackList;
using Game.Global.Clan.ClanBattleEnterDialog;
using Game.Global.Clan.ClanDialog;
using Game.Global.Clan.ClanInvitePopup;
using Game.Global.Clan.ClanSearch;
using Game.Global.Clan.ClanSettings;
using Game.Global.Clan.Model;
using Game.Global.Clan.PopupClan;
using Game.Global.Clan.RankDialog;
using Game.Global.ImprisonmentYears;
using Game.Global.ModalDialog;
using Game.Global.Models;
using Game.Global.Models.Screens;
using Game.Global.Party;
using Game.Global.Party.Dialog;
using Game.Global.PlayerPrefsFile;
using Game.Global.PopupNotificationDialog;
using Game.Global.Prison;
using Game.Global.RegionUserNotification;
using Game.Global.ServerShutdownNotifications;
using Game.Global.Settings;
using Game.Global.SettingsDialog;
using Game.Global.SkinCenter;
using Game.Global.SkinCenter.Model;
using Game.Global.Sounds;
using Game.Global.Tutorial;
using Game.Global.Tutorial.Hints;
using Game.Global.UI;
using Game.Global.Ui.SideMenu.Dialog;
using Game.Global.UI.SkinCenter;
using Game.Global.UserInited;
using Game.Shared;
using Game.Shared.Models;
using Inputs;
using Models.Data;
using Models.Models.CustomizationVersionModel;
using Models.Models.ExpiringResourceModel;
using Models.Models.PrimitiveModel;
using Models.Models.PublicIdModel;
using Models.Models.RegionUserBattleRoomModel;
using Models.Models.RegionUserCityModel;
using Models.Models.RegionUserHangarModel;
using Models.Models.UserRegionModel;
using Models.References.Chat;

namespace Game.Global
{
    public class GlobalModel
    {
        public GlobalModel(UserData userData, RegionUserData regionUserData, IRegionUserBattleRoomModel battleRoomModel, UserTimeModel userTimeModel, UserTimeModel regionUserTimeModel,
            InitedModel regionUserInitedModel, BuiltInScreenContent builtInScreenContent) {
            UserTimeModel = userTimeModel;
            RegionUserTimeModel = regionUserTimeModel;
            PublicIdModel = new PublicIdModel(userData.IsRegisteringPublicId, userData.PublicId, userData.Nickname);
            RegionModel = new UserRegionModel(userData);
            PartyModel = new PartyModel(regionUserData.Party, regionUserData.UserParty.Invites, regionUserData.UserParty.PartyId);
            BattleRoomModel = battleRoomModel;
            CityBattleStateModel = new CityBattleStateModel(BattleRoomModel, regionUserData);
            CityModel = new RegionUserCityModel(regionUserData);
            HangarModel = new RegionUserHangarModel(regionUserData);
            RegionUserDelayedResponsesModel = new DelayedResponsesModel(regionUserTimeModel.ServerNow);
            CoinsModel = new CoinsByDataModel(regionUserData.Coins);
            CryptoCoinsModel = new UserCryptoCoinsModel(userData.CryptoCoins, regionUserData.CryptoCoins, regionUserInitedModel);
            RegionUserCustomizationVersionModel = new CustomizationVersionModel(regionUserData.CustomizationVersion);
            RegionUserModel = new RegionUserModel(regionUserData, regionUserTimeModel.ServerNow);
            ImprisonmentYearsModel = new UserImprisonmentYearsModel(regionUserInitedModel, regionUserData, userData);
            ClanModel = new ClanModel(regionUserData);
            MovePlotModel = new MovePlotModel(regionUserData);
            SettlementOwnersModel = new SettlementOwnersModel(PublicIdModel);
            RegionUserSkinCenterModel = new SkinCenterModel(regionUserData.Customization, regionUserData.OwnedCharacterSkins, regionUserData.Emotions, regionUserData.WeaponSkins, regionUserData.Vehicles, regionUserData.TattoosData);
            DisconnectInfoModel = new DisconnectInfoModel(ReceivedRequestsModel);
            ModalDialogModel = new ModalDialogModel();
            ChatDialogModel = new ChatDialogModel(ChatsModel);
            BadWordsFilterModel = new BadWordsFilterModel(RegionModel, builtInScreenContent.BadWordsByRegionViewDescription, ChatsModel);
            PrivateReceivedChat = new DataPrivateChatMessageSource(userData.ChatMessages, userData.ChatUsers, new ChatSourceSettings(ChatMessageComposition.All, ChatMessageType.Private), 
                BadWordsFilterModel, userTimeModel.ServerNow);
            UserChatCooldown = new ExpiringResourceModel(userData.SendMessageInChatExpiringResourceTs, userData.SendMessageInChatExpiringResourceAmount, ChatDescription.SendMessageExpiringResource);
            RegionUserChatCooldown = new ExpiringResourceModel(regionUserData.SendMessageInChatExpiringResourceTs, regionUserData.SendMessageInChatExpiringResourceAmount, ChatDescription.SendMessageExpiringResource);
            BlackListModel = new BlackListModel(userData.Blacklist);
            PrisonModel = new PrisonModel(regionUserData);
            UserExpiringResourcesModel = new UserExpiringResourcesModel(userData);
            InfoHintsModel = new InfoHintsModel(PlayerPrefsModel);
            IsClanBattleFound = new PrimitiveModel<bool>(regionUserData.IsClanBattleFound);
        }

        public UserTimeModel UserTimeModel { get; }
        public UserTimeModel RegionUserTimeModel { get; }
        public PlayerInfoModel PlayerInfoModel { get; } = new();
        public DeviceModel DeviceModel { get; } = new();
        public CursorLockModel CursorLockModel { get; } = new();
        public GlobalNotificationModel GlobalNotificationModel { get; } = new();
        public PersonalNotificationModel PersonalNotificationModel { get; } = new();
        public MoneyNotificationModel MoneyNotificationModel { get; } = new();
        public SensitivityModel SensitivityModel { get; } = new();
        public InBattleSettingsModel InBattleSettingsModel { get; } = new();
        public KeyboardMouseControlsSettingsModel KeyboardMouseControlsSettingsModel { get; } = new();
        public GlobalUISoundModel UISoundModel { get; } = new();
        public ModalDialogModel ModalDialogModel { get; }
        public IPublicIdModel PublicIdModel { get; }
        public ScreensModel ScreensModel { get; } = new();
        public SpaceSoundModel SpaceSoundModel { get; } = new();
        public IUserRegionModel RegionModel { get; }
        public IRegionUserCityModel CityModel { get; }
        public IRegionUserHangarModel HangarModel { get; }
        public PartyModel PartyModel { get; }
        public ClanModel ClanModel { get; }
        public CityBattleStateModel CityBattleStateModel { get; }
        public ICoinsReadonlyModel CoinsModel { get; }
        public ICoinsReadonlyModel CryptoCoinsModel { get; }
        public ICustomizationVersionModel RegionUserCustomizationVersionModel { get; }
        public IRegionUserBattleRoomModel BattleRoomModel { get; }
        public IImprisonmentYearsReadonlyModel ImprisonmentYearsModel { get; }
        public GameSetting GameSetting { get; set; }
        public AdvancedGameSettings AdvancedGameSettings { get; set; }
        public AutoPrefs AutoPrefs { get; set; }
        public ServerShutdownScreenModel ServerShutdownTimeScreenModel { get; } = new();
        public IDelayedResponsesModel RegionUserDelayedResponsesModel { get; }
        public SideMenuDialogScreenModel SideMenuDialogScreenModel { get; } = new();
        public PopupNotificationDialogModel PopupNotificationDialogModel { get; } = new();
        public PopupNotificationsModel PopupNotificationsModel { get; } = new();
        public SettingsDialogModel SettingsDialogModel { get; } = new();
        public PartyManagerDialogModel PartyManagerDialogModel { get; } = new();
        public InviteToPartyIntentionModel InviteToPartyIntentionModel { get; } = new();
        public PopupClanDialogModel PopupClanDialogModel { get; } = new();
        public ClanDialogModel ClanDialogModel { get; } = new();
        public ClanSettingsDialogModel ClanSettingsDialogModel { get; } = new();
        public ClanSearchDialogModel ClanSearchDialogModel { get; } = new();
        public ClanRankDialogModel ClanRankDialogModel { get; } = new();
        public ClanInvitePopupModel ClanInvitePopupModel { get; } = new();
        public ClanBattleEnterDialogModel ClanBattleEnterDialogModel { get; } = new();
        public SkinCenterDialogModel SkinCenterDialogModel { get; } = new();
        public SkinCenterModel RegionUserSkinCenterModel { get; }
        public GlobalInputHoldModel InputHoldModel { get; } = new();
        public MovePlotModel MovePlotModel { get; }

        public LoadingModel LoadingModel { get; } = new();
        public bool IsSelfSettlementAndSector => HangarModel.HasAddress && CityBattleStateModel.Settlement == HangarModel.SelfSettlement && HangarModel.SelfSector == CityBattleStateModel.Sector;
        public RegionUserNotificationModel RegionUserNotificationModel { get; } = new();

        public RegionUserModel RegionUserModel { get; }

        public ClanBattleAppointmentNotificationsModel ClanBattleAppointmentNotificationsModel { get; } = new();
        public SettlementOwnersModel SettlementOwnersModel { get; }
        public ReceivedRequestsModel ReceivedRequestsModel { get; } = new();
        public DisconnectInfoModel DisconnectInfoModel { get; }
        public SkinsCheatStateModel SkinsCheatStateModel { get; } = new();
        public PlayerPrefsModel PlayerPrefsModel { get; } = new();
        public GlobalPlayerRoleModel GlobalPlayerRoleModel { get; } = new();
        public ResolveMainMenuModel ResolveMainMenuModel { get; } = new();
        public ConfiscationModel ConfiscationModel { get; } = new();
        public ChatsModel ChatsModel { get; } = new();
        public ChatDialogModel ChatDialogModel { get; }
        public BlackListModel BlackListModel { get; }
        public BadWordsFilterModel BadWordsFilterModel { get; }
        public BlackListDialogModel BlackListDialog { get; } = new();
        public SystemChatMessageSource SystemChat { get; } = new(false);
        public SystemChatMessageSource SystemRulesChat { get; } = new(false);
        public DataPrivateChatMessageSource PrivateReceivedChat { get; }
        public IExpiringResourceModel UserChatCooldown { get; }
        public IExpiringResourceModel RegionUserChatCooldown { get; }
        public PrisonModel PrisonModel { get; }
        public SwitchRealmDialogModel SwitchRealmDialogModel { get; } = new();
        public UserExpiringResourcesModel UserExpiringResourcesModel { get; }
        public RegionsInfoModel RegionsInfo { get; set; }
        public RealmsInfoModel RealmsInfo { get; set; }
        public TutorialModel TutorialModel { get; } = new();
        public InfoHintsModel InfoHintsModel { get; }
        public IPrimitiveModel<bool> IsClanBattleFound { get; }
        
        public InputModel SuicideInputModel { get; } = new() { Mode = InputModel.InputMode.Press };
        public InputModel SurrenderToFedsInputModel { get; } = new() { Mode = InputModel.InputMode.Press };
        public InputModel LeaveGameInputModel { get; } = new() { Mode = InputModel.InputMode.Press };
        public InputModel OpenSettingsInputModel { get; } = new() { Mode = InputModel.InputMode.Press };
        public InputModel OpenPartyInputModel { get; } = new() { Mode = InputModel.InputMode.Press };
        public InputModel OpenClanInputModel { get; } = new() { Mode = InputModel.InputMode.Press };
        public InputModel OpenSideMenuInputModel { get; } = new() { Mode = InputModel.InputMode.Press };
        public InputModel OpenSkinCenterInputModel { get; } = new() { Mode = InputModel.InputMode.Press };
    }
}