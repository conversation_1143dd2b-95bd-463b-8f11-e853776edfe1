using Core.Updater;
using UnityEngine.InputSystem;

namespace Game.Global.Clan.ClanDialog.Updater
{
    public class EditClanInputActiveUpdater : IUpdater
    {
        private readonly TabData<ClanTabs> _tabs;
        private readonly ClanTabs _activeWithTab;
        private readonly InputAction _editAction;

        private bool _isVisible;

        public EditClanInputActiveUpdater(TabData<ClanTabs> tabs, ClanTabs activeWithTab, InputAction editAction)
        {
            _tabs = tabs;
            _activeWithTab = activeWithTab;
            _editAction = editAction;

            _isVisible = tabs.Value == activeWithTab;
            SetVisible(_isVisible);

        }

        public void Update()
        {
            bool isVisible = _tabs.Value == _activeWithTab;
            if (isVisible != _isVisible)
            {
                SetVisible(isVisible);
                _isVisible = isVisible;
            }
        }

        private void SetVisible(bool isVisible)
        {
            if (isVisible)
            {
                _editAction.Enable();
            }
            else
            {
                _editAction.Disable();
            }
        }
    }
}