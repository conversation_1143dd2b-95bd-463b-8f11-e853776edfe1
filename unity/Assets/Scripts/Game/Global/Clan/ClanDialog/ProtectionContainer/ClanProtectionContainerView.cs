using TMPro;
using UI;
using UnityEngine;
using Utils.UiState;

namespace Game.Global.Clan.ClanDialog
{
    public class ClanProtectionContainerView : MonoBehaviour
    {
        [field: SerializeField] public TextMeshProUGUI ConquestObjectsCount { get; private set; }
        [field: SerializeField] public TextLabel<Money> LaunderingSpeed { get; private set; }
        [field: SerializeField] public TextLabel<MoneyFloat> LaunderingRate { get; private set; }
        [field: SerializeField] public TextMeshProUGUI TaxInfo { get; private set; }
        
        [field: SerializeField] public Transform Container { get; private set; }
        
        [SerializeField] private StateGroup _hasLinesState;
        [SerializeField, EnumInfo(typeof(ClanProtectionState))] private EnumStateElement<ClanProtectionState> _timeState;
        

        public void SetHasLines(bool value)
        {
            _hasLinesState.State = value ? 1 : 0;
        }
        
        public void SetShowTime(ClanProtectionState value)
        {
            _timeState.State = value;
        }

        public enum ClanProtectionState
        {
            Complete,
            Time,
            Hide
        }
    }
}