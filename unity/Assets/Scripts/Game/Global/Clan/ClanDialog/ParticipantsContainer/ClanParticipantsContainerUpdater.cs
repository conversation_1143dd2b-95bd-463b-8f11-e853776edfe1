using System;
using System.Collections.Generic;
using System.Linq;
using Content;
using Content.Disposable;
using Core;
using Core.Updater;
using Framework.Core.Now;
using Game.Common;
using Game.Global.Clan.Model;
using Game.Global.Sectors;
using Game.Global.Ui;
using Game.UI;
using Models.References.Clan;
using UnityEngine;

namespace Game.Global.Clan.ClanDialog
{
    public class ClanParticipantsContainerUpdater : IUpdater
    {
        private readonly ClanDialogModel _dialogModel;
        private readonly ClanParticipantsContainerView _view;
        private readonly IInstantiateContent<ClanParticipantLineView> _clanParticipantLineContent;
        private readonly ClanRankViewDescriptions _clanRankViewDescriptions;
        private readonly ClanModel _clanModel;
        private readonly ClanLocalization _clanLocalization;
        private readonly ClickableItemsModel<EnumValue<ClanMemberSlotDescription>> _participantsClickableModel;
        private readonly ClickableListModel _leaveClickableModel;
        private readonly LocationNameViewDescriptions _locationNameViewDescriptions;
        private readonly SectorsViewDescription _sectorsViewDescription;
        private readonly ISetNow _serverNow;
        private readonly List<(IDisposableContent<ClanParticipantLineView> content, EnumValue<ClanMemberSlotDescription> slot)> _items = new();
        private Vector2 _wasContextPosition;
        private ClanMembersTabs _previousClanMembersTabsValue = ClanMembersTabs.Participants;
        private readonly ClanMemberModel[] _clanMemberModes; 
        private readonly ClanComparer _clanComparer;
        private readonly List<IUpdater> _updaters = new();
        private readonly IUpdater _updater;

        public ClanParticipantsContainerUpdater(ClanDialogModel dialogModel, ClanParticipantsContainerView view, IInstantiateContent<ClanParticipantLineView> clanParticipantLineContent, 
            IRegisterDisposable registerDisposable, GlobalModel globalModel, ClickableItemsModel<EnumValue<ClanMemberSlotDescription>> participantsClickableModel, ClickableListModel leaveClickableModel, 
            GlobalScreenContent globalScreenContent)
        {
            _dialogModel = dialogModel;
            _view = view;
            _clanParticipantLineContent = clanParticipantLineContent;
            _clanRankViewDescriptions = globalScreenContent.ClanRankViewDescriptions;
            _clanModel = globalModel.ClanModel;
            _clanLocalization = globalScreenContent.LocalizationKeys.ClanLocalization;
            _participantsClickableModel = participantsClickableModel;
            _leaveClickableModel = leaveClickableModel;
            _locationNameViewDescriptions = globalScreenContent.LocationNameViewDescriptions;
            _sectorsViewDescription = globalScreenContent.SectorsViewDescription;
            _serverNow = globalModel.RegionUserTimeModel.ServerNow;
            _clanComparer = new ClanComparer();
            _clanMemberModes = _clanModel.Members.Values.ToArray();
            _updater = new CompositeUpdater(_updaters);
            registerDisposable.Register(() =>
            {
                foreach (var (content, slot) in _items)
                {
                    content.Dispose();
                    _participantsClickableModel.Remove(slot);
                }

                _leaveClickableModel.Items.Clear();

                _items.Clear();
            });
        }

        public void Update()
        {
            if (_clanModel.HasClan && _dialogModel.CurrentTab.Value == ClanTabs.Members && _dialogModel.CurrentMembersTab.Value == ClanMembersTabs.Participants)
            {
                UpdateClanMembers();
            }

            if (_dialogModel.CurrentMembersTab.Value != _previousClanMembersTabsValue)
            {
                _previousClanMembersTabsValue = _dialogModel.CurrentMembersTab.Value;
                _view.ContextMenu.SetActive(false);
            }

            _updater.Update();
        }

        private void UpdateClanMembers()
        {
            ClanParticipantLineView participantForContext = null;
            var index = 0;
            Array.Sort(_clanMemberModes, _clanComparer);
                
            foreach (var clanMemberModel in _clanMemberModes)
            {
                if (!clanMemberModel.IsExist)
                {
                    continue;
                }
                IDisposableContent<ClanParticipantLineView> view;
                EnumValue<ClanMemberSlotDescription> slot;
                if (index < _items.Count)
                {
                    (view, slot) = _items[index];
                }
                else
                {
                    view = _clanParticipantLineContent.Generate(_view.ScrollContent);
                    slot = new EnumValue<ClanMemberSlotDescription>();
                    _participantsClickableModel.Add(slot, view.Value.Menu);
                    _leaveClickableModel.Items.Add(view.Value.Leave.ClickButtonView); 
                    _items.Add((view, slot));
                }

                index++;
                var nickname = clanMemberModel.Name + (_clanModel.PlayerAsClanMember == clanMemberModel ? " " + _clanLocalization.NicknameHimself.GetLocalizedString() : "");
                view.Value.SetNickname(nickname);
                view.Value.SetIsPlayer(_clanModel.PlayerAsClanMember == clanMemberModel);

                view.Value.SetReputationPoints(0);

                if (clanMemberModel.IsOnline)
                {
                    if (clanMemberModel.IsClanBattle)
                    {
                        view.Value.OnlineStatus.SetOnline(_clanLocalization.ParticipantIsOnClanBattle, string.Empty);
                    }
                    else if (LocationNameRules.TryGetNameSettlement(_sectorsViewDescription, clanMemberModel.Sector, clanMemberModel.Settlement, out var title, out var suffix, out _))
                    {
                        view.Value.OnlineStatus.SetOnline(title, suffix);
                    }
                    else if (LocationNameRules.TryGetNameCity(_locationNameViewDescriptions, clanMemberModel.Location, out var cityTitle, out _))
                    {
                        view.Value.OnlineStatus.SetOnline(cityTitle.StringReference, string.Empty);
                    }
                    else
                    {
                        view.Value.OnlineStatus.SetOnline(_clanLocalization.ParticipantStatusInRealm, string.Empty);
                    }
                }
                else
                {
                    var passedTimeSinceLastOnlineTs = TimeSpan.FromMilliseconds((long)_serverNow.Get - clanMemberModel.LastOnlineTs);
                    view.Value.OnlineStatus.SetOffline(passedTimeSinceLastOnlineTs);
                }


                var desc = _clanRankViewDescriptions[clanMemberModel.Rank];
                view.Value.SetRank(desc.Name, desc.Icon);

                slot.Value = clanMemberModel.Slot;

                if (_dialogModel.SelectedParticipant == slot.Value)
                {
                    participantForContext = view.Value;
                    view.Value.SetSelect(true);
                }
                else
                {
                    view.Value.SetSelect(false);
                }
            }

            if (participantForContext != null)
            {
                _view.ContextMenu.SetActive(true);
                UpdatePosition(participantForContext);
            }
            else
            {
                _view.ContextMenu.SetActive(false);
            }

            for (var i = index; i < _items.Count; i++)
            {
                var (content, slot) = _items[i];
                content.Dispose();
                _participantsClickableModel.Remove(slot);
            }

            _items.RemoveRange(index, _items.Count - index);
        }

        private void UpdatePosition(ClanParticipantLineView participantForContext)
        {
            var contextMenu = _view.ContextMenu.ContextMenu;
            var targetPosition = participantForContext.ContextMenuContainer.GetWorldPosition();
            var targetSize = participantForContext.ContextMenuContainer.GetWorldSize();
            var position = TooltipRules.GetPosition(targetPosition, targetSize, contextMenu.RectTransform.GetWorldSize(), new TooltipPositionSettings(HorizontalAnchor.Right, VerticalAlignment.Top, VerticalAlignment.Top, flipAnchorWhenNoSpace: false));

            if (_wasContextPosition != position)
            {
                _wasContextPosition = position;
                contextMenu.RectTransform.SetWorlPositionIgnorePivot(position);
            }
        }


        private class ClanComparer : IComparer<ClanMemberModel>
        {
            private const int _offlineOffset = 1000;

            private int GetPriority(ClanMemberModel model)
            {
                if (!model.IsExist)
                {
                    return int.MaxValue;
                }
                
                return 
                    (model.IsOnline ? 0 : _offlineOffset)
                    + model.Rank.Index;
            }

            public int Compare(ClanMemberModel a, ClanMemberModel b)
            {
                var result = GetPriority(a) - GetPriority(b);
                return result == 0
                    ? (int)(a.PublicId - b.PublicId)
                    : result;
            }
        }
    }
}