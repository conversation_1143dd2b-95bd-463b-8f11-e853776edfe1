using Core.Updater;
using Game.Global.Clan.ClanDialog;
using Game.Global.Clan.Model;
using Game.Shared.Models.Screens;

namespace Game.Global.Clan.ClanSettings
{
    public class ClanSettingsCloseOnRankChangedUpdater : IUpdater
    {
        private readonly ClanDialogModel _clanDialogModel;
        private readonly ClanSettingsDialogModel _dialogModel;
        private readonly ClanModel _clanModel;

        public ClanSettingsCloseOnRankChangedUpdater(ClanDialogModel clanDialogModel, ClanSettingsDialogModel dialogModel, ClanModel clanModel)
        {
            _clanDialogModel = clanDialogModel;
            _dialogModel = dialogModel;
            _clanModel = clanModel;
        }

        public void Update()
        {
            var player = _clanModel.PlayerAsClanMember;
            if (_clanModel.HasClan && player != null && !player.Rank.CanChangeSettings)
            {
                if (_dialogModel.ShowClanDialogAfterClose)
                {
                    _clanDialogModel.Intention = IntentionScreen.Open;
                }
                _dialogModel.Intention = IntentionScreen.Close;
            }
        }
    }
}