using Models.Models.BattleCharacterModel;
using Models.References;
using Models.References.Arrest;
using Models.References.Inventory;
using Models.References.Knockout;
using Models.References.Party;
using Models.References.Vehicle;
using UnityEngine;

namespace Game.Global.Party
{
    public class PlayerMemberBattleDataModel : IMemberBattleDataModel
    {
        private readonly IBattleCharacterModel _battleCharacterModel;

        public PlayerMemberBattleDataModel(IBattleCharacterModel battleCharacterModel)
        {
            _battleCharacterModel = battleCharacterModel;
        }

        public PartyReadyCheckStatusDescription ReadyCheckBecomeRobberStatusDescription => _battleCharacterModel.PlayerBattleModel.ReadyCheckStatusModel.Value;

        public long EntityId => _battleCharacterModel.Id;
        public int Health => _battleCharacterModel.PrivateModel.Health.Amount;
        public int Armor => _battleCharacterModel.PrivateModel.Armor.Amount;
        public PlayerRoleDescription Role => _battleCharacterModel.Role.Value;
        public InventoryItemDescription EquipArmorItem => _battleCharacterModel.PrivateModel.EquipSlots[EquipSlotDescription.Vest].ItemModel.Value;
        public int HideObjectEntityId => _battleCharacterModel.PrivateModel.HideObjectStateModel.HideObjectEntityId;
        public long ArrestTs => _battleCharacterModel.ArrestTargetModel.EndTs;
        public ArrestStateDescription ArrestStateDescription => _battleCharacterModel.ArrestTargetModel.ArrestState;
        public long InterruptArrestingTs => _battleCharacterModel.ArrestTargetModel.InterruptArrestingEndTs;
        public long KnockoutEndTs => _battleCharacterModel.KnockoutModel.EndTs;
        public KnockoutPhaseDescription KnockoutPhase => _battleCharacterModel.KnockoutModel.IsDelayAfterRevive ? KnockoutPhaseDescription.DelayAfterRevive : KnockoutPhaseDescription.KnockedOut;
        public long ReviveEndTs => _battleCharacterModel.RevivalModel.ReviveEndTs;
        public bool IsInVehicle => _battleCharacterModel.InVehicleModel.IsInsideVehicle;
        public int InVehicleEntityId => _battleCharacterModel.InVehicleModel.EntityId;
        public bool HasMark => _battleCharacterModel.PlayerBattleModel.MapMarkModel.HasMark;
        public Vector3 Mark => new(_battleCharacterModel.PlayerBattleModel.MapMarkModel.X, _battleCharacterModel.PlayerBattleModel.MapMarkModel.Y, _battleCharacterModel.PlayerBattleModel.MapMarkModel.Z);
        public Vector3 PingMark => new(_battleCharacterModel.PlayerBattleModel.PingMarkModel.X, _battleCharacterModel.PlayerBattleModel.PingMarkModel.Y, _battleCharacterModel.PlayerBattleModel.PingMarkModel.Z);
        public PingMarkTypeDescription PingMarkType => _battleCharacterModel.PlayerBattleModel.PingMarkModel.Type;
        public long PingMarkTs => _battleCharacterModel.PlayerBattleModel.PingMarkModel.Ts;
    }
}