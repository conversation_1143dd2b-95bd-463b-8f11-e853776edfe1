using System;
using UnityEngine;

namespace Game.Global.Ui
{
    // https://confluence.i.kefir.games/display/TPSO/Tooltips
    public static class TooltipRules
    {
        public const float TooltipEnterTime = 0.3f;

        public static Vector2 GetPosition(Vector2 targetTopLeftCorner, Vector2 targetSize, Vector2 tooltipSize, TooltipPositionSettings settings)
        {
            var targetHalfSize = targetSize * 0.5f;
            var targetCenter = new Vector2(targetTopLeftCorner.x + targetHalfSize.x, targetTopLeftCorner.y - targetHalfSize.y);
            var tooltipHalfSize = tooltipSize * 0.5f;

            float screenWidth = Screen.width;
            float screenHeight = Screen.height;
            var screenCenter = new Vector2(screenWidth / 2, screenHeight / 2);

            float targetTop = targetCenter.y + targetHalfSize.y;
            float targetBottom = targetCenter.y - targetHalfSize.y;
            float targetLeft = targetCenter.x - targetHalfSize.x;
            float targetRight = targetCenter.x + targetHalfSize.x;

            float tooltipTop = tooltipHalfSize.y;
            float tooltipBottom = -tooltipHalfSize.y;
            float tooltipLeft = -tooltipHalfSize.x;
            float tooltipRight = tooltipHalfSize.x;

            var tooltipPosition = Vector2.zero;
            if (settings.IsHorizontalAnchor)
            {
                float targetHorizontalPadding = targetHalfSize.x + settings.Padding + tooltipHalfSize.x;
                float xLeft = targetCenter.x - targetHorizontalPadding;
                float xRight = targetCenter.x + targetHorizontalPadding;

                tooltipPosition.x = settings.HorizontalAnchor switch
                {
                    HorizontalAnchor.Left => xLeft,
                    HorizontalAnchor.Right => xRight,
                    HorizontalAnchor.NearestToScreenCenter => targetCenter.x >= screenCenter.x ? xLeft : xRight,
                    _ => targetCenter.x
                };
                tooltipPosition.y = (settings.VerticalTooltipAlignment, settings.VerticalTargetAlignment) switch
                {
                    (VerticalAlignment.Bottom, VerticalAlignment.Top) => targetTop - tooltipBottom,
                    (VerticalAlignment.Center, VerticalAlignment.Top) => targetTop,
                    (VerticalAlignment.Top, VerticalAlignment.Top) => targetTop - tooltipTop,

                    (VerticalAlignment.Bottom, VerticalAlignment.Center) => targetCenter.y - tooltipBottom,
                    (VerticalAlignment.Center, VerticalAlignment.Center) => targetCenter.y,
                    (VerticalAlignment.Top, VerticalAlignment.Center) => targetCenter.y - tooltipTop,

                    (VerticalAlignment.Bottom, VerticalAlignment.Bottom) => targetBottom - tooltipBottom,
                    (VerticalAlignment.Center, VerticalAlignment.Bottom) => targetBottom,
                    (VerticalAlignment.Top, VerticalAlignment.Bottom) => targetBottom - tooltipTop,

                    _ => throw new ArgumentOutOfRangeException()
                };
            }
            else if (settings.IsVerticalAnchor)
            {
                float targetVerticalPadding = targetHalfSize.y + settings.Padding + tooltipHalfSize.y;
                float yTop = targetCenter.y + targetVerticalPadding;
                float yBottom = targetCenter.y - targetVerticalPadding;

                tooltipPosition.y = settings.VerticalAnchor switch
                {
                    VerticalAnchor.Top => yTop,
                    VerticalAnchor.Bottom => yBottom,
                    VerticalAnchor.NearestToScreenCenter => targetCenter.y <= screenCenter.y ? yTop : yBottom,
                    _ => targetCenter.y
                };
                tooltipPosition.x = (settings.HorizontalTooltipAlignment, settings.HorizontalTargetAlignment) switch
                {
                    (HorizontalAlignment.Left, HorizontalAlignment.Left) => targetLeft - tooltipLeft,
                    (HorizontalAlignment.Center, HorizontalAlignment.Left) => targetLeft,
                    (HorizontalAlignment.Right, HorizontalAlignment.Left) => targetLeft - tooltipRight,

                    (HorizontalAlignment.Left, HorizontalAlignment.Center) => targetCenter.x - tooltipLeft,
                    (HorizontalAlignment.Center, HorizontalAlignment.Center) => targetCenter.x,
                    (HorizontalAlignment.Right, HorizontalAlignment.Center) => targetCenter.x - tooltipRight,

                    (HorizontalAlignment.Left, HorizontalAlignment.Right) => targetRight - tooltipLeft,
                    (HorizontalAlignment.Center, HorizontalAlignment.Right) => targetRight,
                    (HorizontalAlignment.Right, HorizontalAlignment.Right) => targetRight - tooltipRight,

                    _ => throw new ArgumentOutOfRangeException()
                };
            }

            var safeArea = Screen.safeArea;
            float xMin = Math.Max(safeArea.xMin, settings.BorderPadding);
            float xMax = Math.Min(safeArea.xMax, screenWidth - settings.BorderPadding);
            float yMin = Math.Max(safeArea.yMin, settings.BorderPadding);
            float yMax = Math.Min(safeArea.yMax, screenHeight - settings.BorderPadding);

            if (settings.FlipAnchorWhenNoSpace)
            {
                float xLeft = tooltipPosition.x - tooltipHalfSize.x;
                float xRight = tooltipPosition.x + tooltipHalfSize.x;
                float yTop = tooltipPosition.y + tooltipHalfSize.y;
                float yBottom = tooltipPosition.y - tooltipHalfSize.y;

                float targetHorizontalPadding = targetHalfSize.x + settings.Padding + tooltipHalfSize.x;
                float targetVerticalPadding = targetHalfSize.y + settings.Padding + tooltipHalfSize.y;

                if (settings.HorizontalAnchor == HorizontalAnchor.Left && xLeft < xMin)
                {
                    tooltipPosition.x = targetCenter.x + targetHorizontalPadding;
                }
                else if (settings.HorizontalAnchor == HorizontalAnchor.Right && xRight > xMax)
                {
                    tooltipPosition.x = targetCenter.x - targetHorizontalPadding;
                }

                if (settings.VerticalAnchor == VerticalAnchor.Top && yTop > yMax)
                {
                    tooltipPosition.y = targetCenter.y - targetVerticalPadding;
                }
                else if (settings.VerticalAnchor == VerticalAnchor.Bottom && yBottom < yMin)
                {
                    tooltipPosition.y = targetCenter.y + targetVerticalPadding;
                }
            }

            tooltipPosition.x = Mathf.Clamp(tooltipPosition.x, xMin + tooltipHalfSize.x, xMax - tooltipHalfSize.x);
            tooltipPosition.y = Mathf.Clamp(tooltipPosition.y, yMin + tooltipHalfSize.y, yMax - tooltipHalfSize.y);

            return tooltipPosition;
        }
    }
}