namespace Game.Global.Ui
{
    public readonly struct TooltipPositionSettings
    {
        public HorizontalAnchor HorizontalAnchor { get; }
        public VerticalAlignment VerticalTooltipAlignment { get; }
        public VerticalAlignment VerticalTargetAlignment { get; }
        public VerticalAnchor VerticalAnchor { get; }
        public HorizontalAlignment HorizontalTooltipAlignment { get; }
        public HorizontalAlignment HorizontalTargetAlignment { get; }
        public float Padding { get; }
        public float BorderPadding { get; }
        public bool FlipAnchorWhenNoSpace { get; }

        public bool IsHorizontalAnchor => HorizontalAnchor != HorizontalAnchor.None;
        public bool IsVerticalAnchor => VerticalAnchor != VerticalAnchor.None;

        public TooltipPositionSettings(HorizontalAnchor horizontalAnchor, VerticalAlignment tooltipAlignment, VerticalAlignment targetAlignment, float padding = 8, float borderPadding = 10, bool flipAnchorWhenNoSpace = true)
        {
            HorizontalAnchor = horizontalAnchor;
            VerticalTooltipAlignment = tooltipAlignment;
            VerticalTargetAlignment = targetAlignment;
            VerticalAnchor = VerticalAnchor.None;
            HorizontalTooltipAlignment = default;
            HorizontalTargetAlignment = default;
            Padding = padding;
            BorderPadding = borderPadding;
            FlipAnchorWhenNoSpace = flipAnchorWhenNoSpace;
        }

        public TooltipPositionSettings(VerticalAnchor verticalAnchor, HorizontalAlignment tooltipAlignment, HorizontalAlignment targetAlignment, float padding = 8, float borderPadding = 10, bool flipAnchorWhenNoSpace = true)
        {
            HorizontalAnchor = HorizontalAnchor.None;
            VerticalTooltipAlignment = default;
            VerticalTargetAlignment = default;
            VerticalAnchor = verticalAnchor;
            HorizontalTooltipAlignment = tooltipAlignment;
            HorizontalTargetAlignment = targetAlignment;
            Padding = padding;
            BorderPadding = borderPadding;
            FlipAnchorWhenNoSpace = flipAnchorWhenNoSpace;
        }

        public static TooltipPositionSettings Item => new(HorizontalAnchor.NearestToScreenCenter, VerticalAlignment.Center, VerticalAlignment.Center);
        public static TooltipPositionSettings MiniInfo => new(HorizontalAnchor.NearestToScreenCenter, VerticalAlignment.Center, VerticalAlignment.Top);
    }

    public enum HorizontalAnchor
    {
        None,
        Left,
        Right,
        NearestToScreenCenter
    }

    public enum VerticalAnchor
    {
        None,
        Top,
        Bottom,
        NearestToScreenCenter
    }

    public enum HorizontalAlignment
    {
        Right,
        Center,
        Left
    }

    public enum VerticalAlignment
    {
        Top,
        Center,
        Bottom
    }
}