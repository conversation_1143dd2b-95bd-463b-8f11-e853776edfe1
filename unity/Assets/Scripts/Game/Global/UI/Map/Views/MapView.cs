using System;
using Core;
using UnityEngine;

namespace Game.Global.UI.Map
{
    public class MapView : MonoBehaviour
    {
        [field: SerializeField] private ScrollRectClamp _scrollView;
        [field: SerializeField] private RectTransform _viewPort;
        [field: SerializeField] private RectTransform _content;
        public RectTransform ViewPort => _viewPort;
        public bool IsScrolling => _scrollView.velocity != Vector2.zero;
        public void StopMovement() => _scrollView.StopMovement();

        public Vector3 GetContentPosition() => _content.position;

        public void SetZoom(float zoom)
        {
            _content.localScale = Vector3.one * zoom;
        }

        public void SetAnchor(MapPosition position)
        {
            _content.SetPivotWithWorldPositionStays(GetMapNormalizedPosition(position));
        }
        
        public void SetPosition(MapPosition position)
        {
            _content.pivot = GetMapNormalizedPosition(position);
            _scrollView.Late();
        }

        public Vector2 GetMapNormalizedPosition(MapPosition position)
        {
            switch (position)
            {
                case MapPosition.ViewPortNormalizedPosition viewPortNormalizedPosition:
                    return GetMapNormalizedPositionFromViewPortNormalizedPosition(viewPortNormalizedPosition.Value);
                case MapPosition.MapContentPosition mapPosition:
                    return GetMapNormalizedPositionFromMapPosition(mapPosition);
                case MapPosition.MapNormalizedPosition mapNormalizedPosition:
                    return mapNormalizedPosition.Value;
                case MapPosition.ScreenPosition worldPosition:
                    return GetMapNormalizedPositionFromScreenPosition(worldPosition.Value);
                default:
                    throw new ArgumentException();
            }
        }

        private Vector2 GetMapNormalizedPositionFromMapPosition(MapPosition.MapContentPosition mapContentPosition)
        {
            return mapContentPosition.Value / _content.rect.size;
        }

        private Vector2 GetMapNormalizedPositionFromViewPortNormalizedPosition(Vector2 viewPortNormalizedPosition)
        {
            var viewPortWorldRect = _viewPort.GetWorldRect();
            var worldPosition = viewPortWorldRect.min + Vector2.Scale(viewPortWorldRect.size, viewPortNormalizedPosition);
            return GetMapNormalizedPositionFromScreenPosition(worldPosition);
        }

        private Vector2 GetMapNormalizedPositionFromScreenPosition(Vector2 screenPosition)
        {
            var contentWorldRect = _content.GetWorldRect();
            return (screenPosition - contentWorldRect.min) / contentWorldRect.size;
        }

        public void SetEnableScroll(bool value)
        {
            _scrollView.enabled = value;
        }
    }
}