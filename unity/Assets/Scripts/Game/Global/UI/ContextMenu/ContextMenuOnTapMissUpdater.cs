using Core;
using System;
using Core.Updater;
using UnityEngine;

namespace Game.Global.UI.ContextMenu
{
    public class ContextMenuOnTapMissUpdater : IUpdater
    {
        private readonly InputActions _inputActions;
        private readonly ContextMenuView _contextMenuView;
        private readonly Action _onTapMiss;

        public ContextMenuOnTapMissUpdater(InputActions inputActions, ContextMenuView contextMenuView, Action onTapMiss)
        {
            _inputActions = inputActions;
            _contextMenuView = contextMenuView;
            _onTapMiss = onTapMiss;
        }

        public void Update()
        {
            if (_inputActions.UI.Click.WasReleasedThisFrame() && _contextMenuView.gameObject.activeInHierarchy)
            {
                var clickPosition = _inputActions.UI.Point.ReadValue<Vector2>();
                var viewRect = _contextMenuView.RectTransform.GetWorldRect();
                if (clickPosition.x < viewRect.xMin || clickPosition.x > viewRect.xMax || clickPosition.y < viewRect.yMin || clickPosition.y > viewRect.yMax)
                {
                    _onTapMiss.Invoke();
                }
            }
        }
    }
}