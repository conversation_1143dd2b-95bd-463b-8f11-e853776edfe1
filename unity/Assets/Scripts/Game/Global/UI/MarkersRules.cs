using UnityEngine;

namespace Game.Global.UI
{
    public static class MarkersRules
    {
        public static readonly Vector2 CenterNormalizedPoint = Vector2.one * 0.5f;

        private const float HalfViewport = 0.5f;
        private const float EllipseRadiusCoefX = 0.75f;
        private const float EllipseRadiusCoefY = 0.75f;
        private const float EllipseRadiusY = HalfViewport * EllipseRadiusCoefY;
        private const float EllipseRadiusX = HalfViewport * EllipseRadiusCoefX;
        private const float AngleOffset = 180f;
        private const float BorderOffset = 100f;

        public static Vector2 GetViewportPositionByBounds(UnityEngine.Camera camera, Vector3 viewWorldPosition, Vector2 boundsMin, Vector2 boundsMax, out bool inScreen)
        {
            Vector3 viewPortNormalizedPosition = camera.WorldToViewportPoint(viewWorldPosition);
            return GetViewPortNormalizedByBounds(viewPortNormalizedPosition, boundsMin, boundsMax, out inScreen);
        }

        private static Vector2 GetViewPortNormalizedByBounds(Vector3 viewPortNormalizedPosition, Vector2 boundsMin, Vector2 boundsMax, out bool inScreen)
        {
            Vector3 viewportPosition = viewPortNormalizedPosition;
            if (viewportPosition.z >= 0
                && viewportPosition.x >= boundsMin.x && viewportPosition.x <= boundsMax.x
                && viewportPosition.y >= boundsMin.y && viewportPosition.y <= boundsMax.y)
            {
                inScreen = true;
                return viewportPosition;
            }

            Vector2 viewportPosition2D = ((Vector2)viewportPosition - CenterNormalizedPoint) * Mathf.Sign(viewportPosition.z);
            Vector2 halfBoundsSize = (boundsMax - boundsMin) * .5f;

            float kX = Mathf.Abs(viewportPosition2D.x) / halfBoundsSize.x;
            float kY = Mathf.Abs(viewportPosition2D.y) / halfBoundsSize.y;

            float x, y;
            if (kX > kY)
            {
                x = halfBoundsSize.x * Mathf.Sign(viewportPosition2D.x);
                y = kX != 0 ? viewportPosition2D.y / kX : 0;
            }
            else
            {
                x = kY != 0 ? viewportPosition2D.x / kY : 0;
                y = halfBoundsSize.y * Mathf.Sign(viewportPosition2D.y);
            }

            inScreen = false;
            return new Vector2(x, y) + CenterNormalizedPoint;
        }

        public static Vector2 GetViewPortNormalizedByRectangle(Vector2 viewPortNormalizedPosition, Vector2 leftDown, Vector2 rightUp, out bool inRectangle)
        {
            Vector3 viewportPosition = viewPortNormalizedPosition;
            if (viewportPosition.x >= leftDown.x && viewportPosition.x <= rightUp.x && viewportPosition.y >= leftDown.y && viewportPosition.y <= rightUp.y)
            {
                inRectangle = true;
                return viewportPosition;
            }

            var centerNormalizedPoint = leftDown + (rightUp - leftDown) * 0.5f;
            Vector2 viewportPosition2D = (Vector2)viewportPosition - centerNormalizedPoint;
            float tMax = 0;
            if (viewportPosition2D.x != 0)
            {
                if (viewportPosition.x < leftDown.x)
                {
                    float tx1 = (leftDown.x - viewportPosition.x) / viewportPosition2D.x;
                    tMax = Mathf.Max(tMax, Mathf.Abs(tx1));
                }
                else if (viewportPosition.x > rightUp.x)
                {
                    float tx2 = (rightUp.x - viewportPosition.x) / viewportPosition2D.x;
                    tMax = Mathf.Max(tMax, Mathf.Abs(tx2));
                }
            }

            if (viewportPosition2D.y != 0f)
            {
                if (viewportPosition.y < leftDown.y)
                {
                    float ty1 = (leftDown.y - viewportPosition.y) / viewportPosition2D.y;
                    tMax = Mathf.Max(tMax, Mathf.Abs(ty1));
                }
                else if (viewportPosition.y > rightUp.y)
                {
                    float ty2 = (rightUp.y - viewportPosition.y) / viewportPosition2D.y;
                    tMax = Mathf.Max(tMax, Mathf.Abs(ty2));
                }
            }


            var position = viewPortNormalizedPosition - viewportPosition2D * tMax;
            inRectangle = false;
            return position;
        }

        public static Vector2 GetOffsetRatio(RectTransform canvas)
        {
            Vector2 size = canvas.sizeDelta;
            Vector2 padding = size - Vector2.one * BorderOffset;

            return new Vector2(padding.x / size.x, padding.y / size.y);
        }

        public static void GetBoundsByRatio(Vector2 ratio, out Vector2 boundMin, out Vector2 boundMax)
        {
            Vector2 halfRatio = ratio * 0.5f;
            boundMin = CenterNormalizedPoint - halfRatio;
            boundMax = CenterNormalizedPoint + halfRatio;
        }

        public static Vector2 GetEllipseBorderPosition(Vector3 viewPortNormalizedPosition, out bool insideEllipse, out float angle)
        {
            angle = 0f;

            var originViewPortNormalizedPosition = (Vector2)viewPortNormalizedPosition - CenterNormalizedPoint;

            Vector2 pointerPosition;

            var scaledX = originViewPortNormalizedPosition.x / EllipseRadiusX;
            var scaledY = originViewPortNormalizedPosition.y / EllipseRadiusY;
            var length = Mathf.Sqrt(scaledX * scaledX + scaledY * scaledY);
            var normalized = new Vector2(scaledX / length, scaledY / length);
            var positionOnEllipse = new Vector2(normalized.x * EllipseRadiusX, normalized.y * EllipseRadiusY);
            var isTargetBehindCamera = viewPortNormalizedPosition.z < 0f;
            insideEllipse = !(originViewPortNormalizedPosition.sqrMagnitude >= positionOnEllipse.sqrMagnitude || isTargetBehindCamera);

            if (insideEllipse)
            {
                pointerPosition = new Vector2(viewPortNormalizedPosition.x, viewPortNormalizedPosition.y);
            }
            else
            {
                pointerPosition = isTargetBehindCamera ? new Vector2(-positionOnEllipse.x, -positionOnEllipse.y) : new Vector2(positionOnEllipse.x, positionOnEllipse.y);
                pointerPosition += CenterNormalizedPoint;

                var signedY = isTargetBehindCamera ? originViewPortNormalizedPosition.y : -originViewPortNormalizedPosition.y;
                var signedX = isTargetBehindCamera ? originViewPortNormalizedPosition.x : -originViewPortNormalizedPosition.x;
                angle = Mathf.Atan2(signedY, signedX) * Mathf.Rad2Deg - AngleOffset;
            }

            return new Vector2(pointerPosition.x, pointerPosition.y);
        }

        public static float GetAngle(Vector2 viewPortNormalizedPosition, Vector2 target)
        {
            var vector = viewPortNormalizedPosition - target;
            float angle = Mathf.Atan2(vector.y, vector.x) * Mathf.Rad2Deg - AngleOffset;
            return angle;
        }
    }
}