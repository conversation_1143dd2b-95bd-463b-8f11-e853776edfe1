using System.Collections.Generic;
using Core;
using Core.Updater;
using Framework.Core.Now;
using Game.Common;
using Game.Global;
using Game.Global.PopupNotificationDialog;
using Game.Shared.Models;
using Models.References;
using Models.References.Realm;
using SharedType;

namespace Game.Battle.TaxPoint.Updaters
{
    public class TaxPointIconUiUpdater : IUpdater
    {
        private readonly IconNotificationModel _notificationModel;
        private readonly GlobalScreenContent _globalScreenContent;
        private readonly BattleModeDescription _battleMode;
        private readonly GlobalUISoundModel _soundModel;
        private readonly INow _now;
        private readonly Dictionary<(SectorDescription, SettlementDescription), OwnedSettlementModel> _playerOwnedSettlements;
        private int _count;

        public TaxPointIconUiUpdater(GlobalModel globalModel, IconNotificationModel notificationModel,
                                     GlobalScreenContent globalScreenContent, BattleModeDescription battleMode)
        {
            _notificationModel = notificationModel;
            _globalScreenContent = globalScreenContent;
            _battleMode = battleMode;
            _soundModel = globalModel.UISoundModel;
            _now = globalModel.RegionUserTimeModel.ServerNow;
            _playerOwnedSettlements = globalModel.SettlementOwnersModel.PlayerOwnedSettlements;
        }

        public void Update()
        {
            int newCount = GetTaxCount();
            if (_count == newCount)
            {
                return;
            }

            int difference = newCount - _count;
            if (difference > 0)
            {
                var notificationString = _globalScreenContent.LocalizationKeys.SettlementTaxes;
                _notificationModel.AddNotificationMessage(notificationString);
                _soundModel.PlaySound(GlobalUISoundType.TakeTaxHasBecomeAvailableForCollection);
            }
            _notificationModel.Count += difference;
            _count = newCount;
        }

        private int GetTaxCount()
        {
            if (IsClanBattle())
            {
                return 0;
            }

            int count = 0;
            foreach (var settlement in _playerOwnedSettlements.Values)
            {
                if (settlement.TaxEndTs < _now.Get())
                {
                    count++;
                }
            }
            return count;
        }

        private bool IsClanBattle()
        {
            return _battleMode is { IsClanBattle: true };
        }
    }
}