using Content.ContentWrappers;
using Core;
using Game.Global.SkinCenter.Context;
using Game.Global.SkinCenter.UI.Views;
using Game.Global.UI.SliceScroll;
using Models.References.Character.Customizer;
using Models.References.Inventory;
using TMPro;
using UI;
using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Components;
using UnityEngine.UI;
using Utils.UiState;

namespace Game.Global.UI.SkinCenter
{
    public class SkinCenterItemView : SliceScrollItemView<ISkinCenterItemContext>, IComponentContent
    {
        [field: SerializeField] private ImageView Image { get; set; }
        [field: SerializeField] private ImageExtend NativeImage { get; set; }
        [field: SerializeField] private GameObject Selected { get; set; }
        [field: SerializeField] private GameObject Confirmed { get; set; }
        [field: SerializeField] private EnumStateElement<CellLockedType> LockedState { get; set; }
        [field: SerializeField] private EnumStateElement<RarityItem> Rare { get; set; }
        [field: SerializeField] private TextMeshProUGUI Index { get; set; }
        [field: SerializeField] private LocalizeStringEvent Name { get; set; }
        [field: SerializeField] private Button Button { get; set; }
        [field: SerializeField] private GameObject ItemParts { get; set; }
        [field: SerializeField] private TextLabel<int, int> ItemPartsText { get; set; }
        [field: SerializeField] public SkinCenterContainerView LoadingIndicator { get; private set; }

        private bool? _selected;
        private bool? _confirmed;
        private int? _lastIndex;
        private bool? _hasName;
        private bool? _isActive;
        private (int? owned, int? max) _itemParts;
        private CharacterSexDescription _sexData;

        public ISkinCenterItemContext ItemContext { get; private set; }
        
        private void OnEnable()
        {
            Button.onClick.AddListener(OnButtonClicked);
            _isActive = true;
        }

        private void OnDisable()
        {
            Button.onClick.RemoveListener(OnButtonClicked);
            _isActive = false;
        }
        
        private void OnButtonClicked()
        {
            if (ItemContext != null)
            {
                ItemContext.IsClicked = true;
            }
        }

        public override void SetModel(ISkinCenterItemContext itemContext, CharacterSexDescription sexData)
        {
            ItemContext = itemContext;
            _sexData = sexData;
            
            SetActive(itemContext != null);
            
            if (itemContext == null) return;
            
            SetIcon(itemContext, sexData, out bool hasEmptyIcon);
            SetSelected(itemContext.Selected);
            Rare.State = itemContext.Rarity;
            SetConfirmed(!hasEmptyIcon && itemContext.Confirmed);
            SetIndex(itemContext.Index);
            ShowName(itemContext.Name);

            bool isOwned = itemContext.IsOwned;
            if (itemContext.HasParts)
            {
                LockedState.State = isOwned ? CellLockedType.None : CellLockedType.Blocked;
                if (isOwned)
                {
                    ShowNumbers(itemContext.OwnedParts, itemContext.MaxParts);
                }
                else
                {
                    HideNumbers();
                }
            }
            else
            {
                LockedState.State = isOwned ? CellLockedType.None :  
                                    !itemContext.IsAvailable ? CellLockedType.Blocked :
                                    CellLockedType.Crypto;
                HideNumbers();
            }
        }

        private void SetIcon(ISkinCenterItemContext itemContext, CharacterSexDescription sexData, out bool hasEmptyIcon)
        {
            var sprite = itemContext.GetEmptyIconBySexData(sexData);
            hasEmptyIcon = sprite != null;

            if (!hasEmptyIcon)
            {
                sprite = itemContext.GetIconBySexData(sexData);
            }
            
            Image.Enabled = sprite != null && sprite.texture != null && !hasEmptyIcon;
            NativeImage.Enabled = sprite != null && sprite.texture != null && hasEmptyIcon;
            
            if (hasEmptyIcon)
            {
                NativeImage.SetSprite(sprite);
            }
            else
            {
                Image.SetSprite(sprite);
                
                if (Image.RectTransform.pivot != itemContext.CellDescription.ImagePivot)
                {
                    Image.RectTransform.pivot = itemContext.CellDescription.ImagePivot;
                }
                
                if (Image.RectTransform.sizeDelta != itemContext.CellDescription.ImageSizeDelta)
                {
                    Image.RectTransform.sizeDelta = itemContext.CellDescription.ImageSizeDelta;
                }
                
                if (Image.RectTransform.anchoredPosition != itemContext.CellDescription.ImageAnchoredPosition)
                {
                    Image.RectTransform.anchoredPosition = itemContext.CellDescription.ImageAnchoredPosition;
                }
            }
            LoadingIndicator.SetActive(!sprite || !sprite.texture);
        }

        private void SetSelected(bool value)
        {
            if (_selected != value)
            {
                _selected = value;
                Selected.SetActive(value);
            }
        }

        private void SetConfirmed(bool value)
        {
            if (_confirmed != value)
            {
                _confirmed = value;
                Confirmed.SetActive(value);
            }
        }

        private void SetActive(bool value)
        {
            if (_isActive != value)
            {
                _isActive = value;
                gameObject.SetActive(value);
            }
        }

        private void SetIndex(int index)
        {
            if (_lastIndex != index)
            {
                _lastIndex = index;
                var hasIndex = index > 0;
                Index.SetActive(hasIndex);
                if (hasIndex) Index.text = index.ToString();
            }
        }

        private void ShowName(LocalizedString itemName)
        {
            var hasName = itemName != null;
            if (_hasName != hasName)
            {
                _hasName = hasName;
                Name.SetActive(hasName);
            }

            if (hasName && Name.StringReference != itemName)
            {
                Name.StringReference = itemName;
            }
        }

        private void ShowNumbers(int ownedCount, int maxCount)
        {
            if (ownedCount != _itemParts.owned || maxCount != _itemParts.max)
            {
                _itemParts = (ownedCount, maxCount);
                ItemParts.SetActive(maxCount > 0);
                ItemPartsText.Set(ownedCount, maxCount);
            }
        }

        private void HideNumbers()
        {
            _itemParts = (null, null);
            ItemParts.SetActive(false);
        }

        private void Update()
        {
            SetIcon(ItemContext, _sexData, out _);
        }

        void IComponentContent.ResetOnDisposeContent()
        {
            NativeImage.sprite = null;
            Image.SetSprite(null);
        }
    }
}