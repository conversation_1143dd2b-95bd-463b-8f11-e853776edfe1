using Core.Updater;
using Game.Global.SkinCenter.ViewDescriptions;
using Game.Global.SkinCenter.ViewDescriptions.Character;
using Models.References.Character.Customizer;

namespace Game.Global.UI.SkinCenter
{
    public class KeyboardSkinCenterColorVisibilityUpdater : IUpdater
    {
        private readonly SkinCenterDialogModel _dialogModel;
        private readonly KeyboardSkinCenterDialogView _dialogView;
        private readonly CharacterColorTabViewDescriptions _characterColorTabViewDescriptions;
        private readonly CustomizerViewDescription _customizerViewDescription;

        public KeyboardSkinCenterColorVisibilityUpdater(SkinCenterDialogModel dialogModel, KeyboardSkinCenterDialogView dialogView, CharacterColorTabViewDescriptions characterColorTabViewDescriptions,
            CustomizerViewDescription customizerViewDescription)
        {
            _dialogModel = dialogModel;
            _dialogView = dialogView;
            _characterColorTabViewDescriptions = characterColorTabViewDescriptions;
            _customizerViewDescription = customizerViewDescription;
        }

        public void Update()
        {
            if (_dialogModel.CurrentTab is SkinCenterTab.Character {Value: {HasColor: true}} character)
            {
                var isPrimaryActive = character.Value.PrimaryColor != null && NeedShowPrimaryColor(character.Value);
                var isSecondaryActive = character.Value.SecondaryColor != null && NeedShowSecondaryColor(character.Value);
                var isTertiaryActive = character.Value.TertiaryColor != null && NeedShowTertiaryColor(character.Value);
                var isActive = isPrimaryActive || isSecondaryActive || isTertiaryActive;
                _dialogView.ColorContainer.SetActive(isActive);
                if (isActive)
                {
                    _dialogView.SkinCenterDialogView.PrimaryColorContainer.SetActive(isPrimaryActive);
                    _dialogView.SkinCenterDialogView.SecondaryColorContainer.SetActive(isSecondaryActive);
                    _dialogView.SkinCenterDialogView.TertiaryColorContainer.SetActive(isTertiaryActive);

                    if (isPrimaryActive)
                    {
                        _dialogView.PrimaryColorTitle.StringReference = _characterColorTabViewDescriptions[character.Value.PrimaryColor].Title;
                    }

                    if (isSecondaryActive)
                    {
                        _dialogView.SecondaryColorTitle.StringReference = _characterColorTabViewDescriptions[character.Value.SecondaryColor].Title;
                    }

                    if (isTertiaryActive)
                    {
                        _dialogView.TertiaryColorTitle.StringReference = _characterColorTabViewDescriptions[character.Value.TertiaryColor].Title;
                    }
                }
            }
            else
            {
                _dialogView.ColorContainer.SetActive(false);
            }
        }

        private bool NeedShowPrimaryColor(CharacterTabDescription characterTab)
        {
            return
                !(characterTab == CharacterTabDescription.Hair && _dialogModel.TemporaryModel.Character.Hair == CharacterHairDescription.NoHair)
                && !(characterTab == CharacterTabDescription.FHair && _dialogModel.TemporaryModel.Character.FHair == CharacterFHairDescription.NoHair)
                && !(characterTab == CharacterTabDescription.FMakeup && _dialogModel.TemporaryModel.Character.FMakeup == CharacterFMakeupDescription.Clear)
                && !(characterTab == CharacterTabDescription.Beard && _dialogModel.TemporaryModel.Character.Beard == CharacterBeardDescription.NoBeard);
        }

        private bool NeedShowSecondaryColor(CharacterTabDescription characterTab)
        {
            return 
                !(characterTab == CharacterTabDescription.Hair && _dialogModel.TemporaryModel.Character.Hair == CharacterHairDescription.NoHair)
                && !(characterTab == CharacterTabDescription.FHair && _dialogModel.TemporaryModel.Character.FHair == CharacterFHairDescription.NoHair)
                && !(characterTab == CharacterTabDescription.FMakeup && _dialogModel.TemporaryModel.Character.FMakeup == CharacterFMakeupDescription.Clear)
                && !(characterTab == CharacterTabDescription.Beard && _dialogModel.TemporaryModel.Character.Beard == CharacterBeardDescription.NoBeard)
                && !(characterTab == CharacterTabDescription.Beard && !_customizerViewDescription.BeardsViewDescription[_dialogModel.TemporaryModel.Character.Beard].HasExtraColor)
                && !(characterTab == CharacterTabDescription.Hair&& _dialogModel.TemporaryModel.Character.Hair == CharacterHairDescription.NoHair && _customizerViewDescription.HairsViewDescription[_dialogModel.TemporaryModel.Character.Hair].BaldStatus == CharacterHairViewDescription.BaldEnum.OnlyBald);
        }

        private bool NeedShowTertiaryColor(CharacterTabDescription characterTab)
        {
            return true;
        }
    }
}