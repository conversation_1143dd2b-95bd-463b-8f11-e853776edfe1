using Game.Global.SkinCenter;
using Models.References.Car;

namespace Game.Global.UI.SkinCenter
{
    public sealed class TemporaryCarValue : TemporaryEnumValue<CarDescription>
    {
        private readonly CarSlotDescription _slot;
        private readonly SkinCenterModel _skinCenterModel;

        public TemporaryCarValue(CarSlotDescription slot, SkinCenterModel skinCenterModel) : base(CarDescription.Enum, skinCenterModel.Vehicle.SelectedCars[slot].Vehicle ?? slot.DefaultCar)
        {
            _slot = slot;
            _skinCenterModel = skinCenterModel;
        }

        public override CarDescription OriginalValue => _skinCenterModel.Vehicle.SelectedCars[_slot].Vehicle ?? _slot.DefaultCar;

        protected override int GetCryptoPriceToBuy(CarDescription selectedCar)
        {
            return selectedCar != null && selectedCar.CanBuy && !_skinCenterModel.Vehicle.OwnedCars.Contains(selectedCar) ? selectedCar.CryptoCoinsPrice : 0;
        }

        protected override bool IsOwned(CarDescription selectedCar)
        {
            return GetCryptoPriceToBuy(selectedCar) == 0;
        }

        protected override (int owned, int max) GetParts(CarDescription selectedCar)
        {
            return (0, 0);
        }

        protected override void SetValue(CarDescription value)
        {
            _value = value;
        }

        protected override int GetImprisonmentYearsRequired(CarDescription value)
        {
            return value?.ImprisonmentYearsRequired ?? 0;
        }
    }
}