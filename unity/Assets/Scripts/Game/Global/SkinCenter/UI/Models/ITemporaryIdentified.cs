using Framework.Core.Identified;

namespace Game.Global.UI.SkinCenter
{
    public interface ITemporaryIdentified : IIdentified
    {
        string Id { get; set; }
        string OriginalId { get; }
        string LastCanApplyId { get; set; }
        bool HasChanges { get; }
        int GetCryptoPriceToBuy();
        bool IsOwned(string id);
        (int owned, int max) GetParts(string id);
        int GetImprisonmentYearsRequired(string id);
    }
}