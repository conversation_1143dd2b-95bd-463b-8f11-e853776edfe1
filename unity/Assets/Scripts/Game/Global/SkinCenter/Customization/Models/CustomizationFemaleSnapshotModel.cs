using System.Collections.Generic;
using Content.ContentWrappers;
using Game.Global.SkinCenter.ViewDescriptions.Character;
using LODGroupQuality;
using Models.Models.CustomizationModel;
using Models.References.Character.Customizer;
using Models.References.Cop;
using Models.References.Tattoo;

namespace Game.Global.SkinCenter.Customization.Models
{
    public class CustomizationFemaleSnapshotModel
    {
        private readonly CustomizerViewDescription _customizerViewDescription;

        private int _version;

        private CharacterFBodyDescription _fBody;
        private CharacterFHairDescription _fHair;
        private CharacterFHeadDescription _fHead;
        private CharacterHairColorDescription _fHairColor;
        private CharacterHairColorDescription _fHairExtraColor;
        private CharacterHairColorDescription _fEyebrowsColor;
        private CharacterSkinColorDescription _fSkinColor;
        private CharacterBagDescription _fBag;
        private CharacterMaskDescription _fMask;
        private CharacterEyeColorDescription _fEyeColor;
        private CharacterFMakeupDescription _fMakeup;
        private CharacterFMakeupEyeColorDescription _fMakeupEyeColor;
        private CharacterFMakeupLipColorDescription _fMakeupLipColor;
        private CharacterFMarkDescription _fMark;
        private CharacterKeychainDescription _fKeychain;
        private TattooDescription _fTattoo;

        private bool _isCopEquipVisible;
        private bool _isPrisonerEquipVisible;
        private bool _isNaked;
        private CopCustomizeDescription _copCustomizeDescription;

        public bool IsBagVisible { get; private set; }
        public bool IsBagStrapVisible { get; private set; }
        public bool IsMaskVisible { get; private set; }
        public bool IsDetailCustomization { get; private set; }
        public int TattooGrade { get; private set; }
        
        public MaskType MaskType => IsMaskVisible ? Mask.MaskType : MaskType.Type01NoMask;

        public CharacterBagViewDescription Bag => _customizerViewDescription.BagsViewDescription[_fBag];
        public CharacterFBodyViewDescription Body
        {
            get
            {
                if (_isNaked)
                {
                    return _customizerViewDescription.NakedViewDescriptions.WomanBody;
                }
                if (_isCopEquipVisible)
                {
                    return _customizerViewDescription.CopViewDescriptions[_copCustomizeDescription].WomanPoliceBody;
                }
                if (_isPrisonerEquipVisible)
                {
                    return _customizerViewDescription.PrisonerViewDescriptions.WomanBody;
                }
                return _customizerViewDescription.FBodiesViewDescription[_fBody];
            }
        }
        public CharacterFHairViewDescription Hair => _customizerViewDescription.FHairsViewDescription[_fHair];
        public CharacterFHeadViewDescription Head => _customizerViewDescription.FHeadsViewDescription[_fHead];
        public CharacterFMakeupViewDescription Makeup => _customizerViewDescription.FMakeupsViewDescription[_fMakeup];
        public CharacterFMarkViewDescription Mark => _customizerViewDescription.FMarksViewDescription[_fMark];

        public CharacterFMaskViewDescription Mask => _isCopEquipVisible
            ? _customizerViewDescription.CopViewDescriptions[_copCustomizeDescription].WomanPoliceMask
            : _customizerViewDescription.FMasksViewDescription[_fMask];

        public CharacterKeychainViewDescription Keychain => _customizerViewDescription.KeychainsViewDescription[_fKeychain];
        public CharacterTattooSetViewDescription Tattoo => _customizerViewDescription.FTattooViewDescriptions[_fTattoo];
        public CharacterKeychainsViewDescription KeychainsViewDescription => _customizerViewDescription.KeychainsViewDescription;
        public CharacterHairColorViewDescription HairColor => _customizerViewDescription.HairColorsViewDescription[_fHairColor];
        public CharacterHairColorViewDescription HairExtraColor => _customizerViewDescription.HairColorsViewDescription[_fHairExtraColor];
        public CharacterHairColorViewDescription EyebrowsColor => _customizerViewDescription.HairColorsViewDescription[_fEyebrowsColor];
        public CharacterSkinColorViewDescription SkinColor => Head.SkinColors[_fSkinColor];
        public CharacterFMakeupEyesColorViewDescription MakeupEyesColor => _customizerViewDescription.FMakeupEyesColorsViewDescription[_fMakeupEyeColor];
        public CharacterFMakeupLipColorsViewDescription MakeupLipsColor => _customizerViewDescription.FMakeupLipsColorsViewDescription[_fMakeupLipColor];
        public CharacterEyesColorViewDescription EyesColor => _customizerViewDescription.EyesColorsViewDescription[_fEyeColor];

        public CustomizationFemaleSnapshotModel(CustomizerViewDescription customizerViewDescription)
        {
            _customizerViewDescription = customizerViewDescription;
        }

        public void Set(ICustomizationModel customizationModel, int version, CharacterCustomizationModel characterCustomizationModel)
        {
            _version = version;

            _fBag = customizationModel.FBag;
            _fBody = customizationModel.FBody;
            _fHair = customizationModel.FHair;
            _fHead = customizationModel.FHead;
            _fMakeup = customizationModel.FMakeup;
            _fMark = customizationModel.FMark;
            _fMask = customizationModel.FMask;
            _fKeychain = customizationModel.FKeychain;
            _fTattoo = customizationModel.FTattoo;
            _fHairColor = customizationModel.FHairColor;
            _fHairExtraColor = customizationModel.FHairExtraColor;
            _fEyebrowsColor = customizationModel.FEyebrowsColor;
            _fSkinColor = customizationModel.FSkinColor;
            _fMakeupEyeColor = customizationModel.FMakeupEyeColor;
            _fMakeupLipColor = customizationModel.FMakeupLipColor;
            _fEyeColor = customizationModel.FEyeColor;

            _isCopEquipVisible = characterCustomizationModel.IsCopEquipVisible;
            _isNaked = characterCustomizationModel.IsNaked;
            _copCustomizeDescription = characterCustomizationModel.CopCustomizeDescription;
            _isPrisonerEquipVisible = characterCustomizationModel.IsPrisonerEquipVisible;

            IsBagVisible = characterCustomizationModel.IsBagVisible;
            IsBagStrapVisible = characterCustomizationModel.IsBagStrapVisible;
            IsMaskVisible = characterCustomizationModel.IsMaskVisible;
            IsDetailCustomization = characterCustomizationModel.IsDetailCustomization;
            TattooGrade = characterCustomizationModel.TattooGrade;
        }

        public bool IsEquals(int version, CharacterCustomizationModel characterCustomizationModel)
        {
            return _version == version &&
                   _isCopEquipVisible == characterCustomizationModel.IsCopEquipVisible &&
                   _isPrisonerEquipVisible == characterCustomizationModel.IsPrisonerEquipVisible &&
                   _isNaked == characterCustomizationModel.IsNaked &&
                   _copCustomizeDescription == characterCustomizationModel.CopCustomizeDescription &&
                   IsBagVisible == characterCustomizationModel.IsBagVisible &&
                   IsBagStrapVisible == characterCustomizationModel.IsBagStrapVisible &&
                   IsMaskVisible == characterCustomizationModel.IsMaskVisible &&
                   IsDetailCustomization == characterCustomizationModel.IsDetailCustomization &&
                   TattooGrade == characterCustomizationModel.TattooGrade;
        }

        public IEnumerable<ILoadedContent> GetContentToLoad(LODGroupQualitySetups setups)
        {
            foreach (var content in Head.GetContentToLoad(setups)) yield return content;
            foreach (var content in Body.GetContentToLoad(setups)) yield return content;
            foreach (var content in Hair.GetContentToLoad(MaskType.Type01NoMask, setups)) yield return content;
            if (IsMaskVisible && Mask.MaskType != MaskType.Type01NoMask)
            {
                foreach (var content in Hair.GetContentToLoad(Mask.MaskType, setups)) yield return content;
            }

            foreach (var content in Makeup.GetContentToLoad()) yield return content;
            foreach (var content in Mark.GetContentToLoad()) yield return content;
            foreach (var content in Bag.GetContentToLoad(setups)) yield return content;
            foreach (var content in Mask.GetContentToLoad(Head.HeadType, setups)) yield return content;
            foreach (var content in Tattoo.GetContentToLoad()) yield return content;

            var hasKeychain = false;
            foreach (var content in Keychain.GetContentToLoad())
            {
                yield return content;
                hasKeychain = true;
            }
            if (hasKeychain)
            {
                foreach (var content in KeychainsViewDescription.GetContentToLoad()) yield return content;
            }
        }
    }
}