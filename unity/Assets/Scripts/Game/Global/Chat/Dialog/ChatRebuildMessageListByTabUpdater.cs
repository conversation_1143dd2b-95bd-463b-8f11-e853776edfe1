using Content;
using Core;
using Core.Updater;
using Game.UI;

namespace Game.Global.Chat
{
    public class ChatRebuildMessageListByTabUpdater : IUpdater
    {
        private readonly GlobalModel _globalModel;
        private readonly ChatDialogModel _chatDialog;
        private readonly ChatDialogView _view;
        private readonly IInstantiateContent<ChatMessageView> _messageTemplate;
        private readonly IInstantiateContent<PrivateChatContactView> _contactTemplate;
        private readonly ChatViewDescription _chatViewDescription;
        private readonly ClickableItemsModel<PrivateChannel> _privatesClickableItems;
        private readonly ClickableItemsModel<ChatMessagePair> _clickableMessages;

        private ChatTab _oldTab;
        private IUpdater _updater;
        private IRegisterDisposable _disposable;

        public ChatRebuildMessageListByTabUpdater(GlobalModel globalModel, ChatDialogModel chatDialog, ChatDialogView view, IInstantiateContent<ChatMessageView> messageTemplate,
            IInstantiateContent<PrivateChatContactView> contactTemplate, ChatViewDescription chatViewDescription, ClickableItemsModel<PrivateChannel> privatesClickableItems,
            ClickableItemsModel<ChatMessagePair> clickableItemsModel, IRegisterDisposable disposable)
        {
            _globalModel = globalModel;
            _chatDialog = chatDialog;
            _view = view;
            _messageTemplate = messageTemplate;
            _contactTemplate = contactTemplate;
            _chatViewDescription = chatViewDescription;
            _privatesClickableItems = privatesClickableItems;
            _clickableMessages = clickableItemsModel;

            disposable.Register(() =>
            {
                _disposable?.Dispose();  
            });
        }

        public void Update()
        {
            ResolveSelectedTab();
            _updater?.Update();
        }

        private void ResolveSelectedTab()
        {
            if (_chatDialog.SelectedTab != _oldTab)
            {
                if (_oldTab != null)
                {
                    _disposable.Dispose();
                    _disposable = null;
                    _updater = null;
                }

                ChatTab newTab = _chatDialog.SelectedTab;
                if (newTab != null)
                {
                    _disposable = new RegisterDisposable();

                    if (newTab is ChatTab.Channel { ChatChannel: var channel })
                    {
                        _updater = new ChatMessageListUpdater(_globalModel, channel, _view, _messageTemplate, _chatViewDescription,
                            _clickableMessages, _disposable);
                    }
                    else if (newTab is ChatTab.PrivateList)
                    {
                        _updater = new PrivateChatTabViewUpdater(_globalModel, _globalModel.ChatsModel.PrivateChannels, _view, _messageTemplate, 
                            _contactTemplate, _privatesClickableItems, _chatViewDescription, _clickableMessages, _disposable);
                    }
                }

                _oldTab = newTab;
            }
        }
    }
}