using UI;

namespace Game.Global.Chat
{
    public struct ChatMessageContext
    {
        public string Color { get; init; }
        public string Message { get; init; }
        public string Author { get; init; }
        public string Time { get; init; }
        public ParseSafety Safety { get; init; }
            
        public enum ParseSafety
        {
            NoParse,
            Parse,
        }
    }
}