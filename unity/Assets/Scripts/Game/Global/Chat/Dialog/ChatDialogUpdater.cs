using Content;
using Content.Disposable;
using Core;
using Core.Updater;
using Framework.Core.Unit;
using Game.Common;
using Game.Global.ModalDialog.Button;
using Game.UI;
using Inputs;
using Network.Connections;
using System;
using System.Collections.Generic;
using UI;
using UnityEngine;

namespace Game.Global.Chat
{
    public class ChatDialogUpdater : IUpdater
    {
        private readonly IUpdater _updater;

        public ChatDialogUpdater(GlobalModel globalModel, ChatDialogModel chatDialogModel, GlobalScreenContent globalScreenContent, InputHandlersManager inputHandlers, 
            InputActions inputActions, RectTransform parent, IRoomConnections roomConnections, IRegisterDisposable disposable)
        {
            ChatsModel chats = globalModel.ChatsModel;
            IDisposableContent<ChatDialogView> chatContent;
            IInstantiateContent<ChatMessageView> chatMessage;
            IInstantiateContent<PrivateChatContactView> privateContactView;
            IInstantiateContent<ButtonView> contextActionView;

            switch (globalModel.DeviceModel.CurrentDeviceId)
            {
                case DeviceId.KeyboardMouse:
                    chatContent = globalScreenContent.ChatViewDescription.KeyboardChat.Generate(parent);
                    chatMessage = globalScreenContent.ChatViewDescription.KeyboardMessage;
                    privateContactView = globalScreenContent.ChatViewDescription.KeyboardChatContact;
                    contextActionView = globalScreenContent.ChatViewDescription.KeyboardChatContextAction;
                    break;
                case DeviceId.Touch:
                    chatContent = globalScreenContent.ChatViewDescription.TouchChat.Generate(parent);
                    chatMessage = globalScreenContent.ChatViewDescription.TouchMessage;
                    privateContactView = globalScreenContent.ChatViewDescription.TouchChatContact;
                    contextActionView = globalScreenContent.ChatViewDescription.TouchChatContextAction;
                    break;
                default: throw new ArgumentOutOfRangeException();
            }

            ChatDialogView view = chatContent.Value;
            ClickableItemsModel<ChatTab> chatTabs = new ClickableItemsModel<ChatTab>();
            ClickableItemsModel<PrivateChannel> privateContacts = new ClickableItemsModel<PrivateChannel>();
            ClickableItemsModel<ChatMessagePair> messages = new ClickableItemsModel<ChatMessagePair>();
            ClickableItemsModel<ContextAction> contextActions = new ClickableItemsModel<ContextAction>(); 
            List<(TabView<Unit>, ChatTab, IText)> tabViews = new List<(TabView<Unit>, ChatTab, IText)>();

            ChatLocalization chatLocalization = globalScreenContent.ChatViewDescription.ChatLocalization;
            RegisterTab(new ChatTab.Channel(chats.Battle), view.Battle, chatTabs, tabViews, chatLocalization.BattleChatName);
            RegisterTab(new ChatTab.Channel(chats.Party), view.Party, chatTabs, tabViews, chatLocalization.PartyChatName);
            RegisterTab(new ChatTab.PrivateList(chats.PrivateChannels), view.Private, chatTabs, tabViews, chatLocalization.PrivateChatName);
            RegisterTab(new ChatTab.Channel(chats.Clan), view.Clan, chatTabs, tabViews, chatLocalization.ClanChatName);
            RegisterTab(new ChatTab.Channel(chats.ShooterPolice), view.Police, chatTabs, tabViews, chatLocalization.PoliceChatName);
            RegisterTab(new ChatTab.Channel(chats.General), view.General, chatTabs, tabViews, chatLocalization.SystemChatName);

            IInputHandler inputUpdater = new ChatDialogInputUpdater(globalModel, chatDialogModel, view, inputActions, chatTabs, globalScreenContent.ChatViewDescription, tabViews, privateContacts, 
                messages, contextActions, roomConnections, globalScreenContent);
            inputHandlers.Add(inputUpdater);

            List<IUpdater> updaters = new List<IUpdater>()
            {
                new ChatRebuildMessageListByTabUpdater(globalModel, chatDialogModel, view, chatMessage, privateContactView, globalScreenContent.ChatViewDescription, privateContacts, messages, disposable),
                new ChatSelectedTabViewUpdater(chatDialogModel, view, tabViews),
                new ChatTabNotificationUpdater(tabViews),
                new ChatInputViewUpdater(view, globalModel, chatDialogModel, globalScreenContent.ChatViewDescription, globalModel.RegionUserTimeModel.ServerNow),
                new ChatContextViewUpdater(view.ChatContextView, chatDialogModel.Context),
                new ChatContextActionsViewUpdater(globalModel, chatDialogModel.Context, view.ChatContextView, globalScreenContent.ChatViewDescription, contextActionView, contextActions, disposable),
            };
            if (globalModel.DeviceModel.CurrentDeviceId == DeviceId.KeyboardMouse)
            {
                updaters.Add(new ChatInputFieldSelectionUpdater(chatDialogModel, view));
            }
            _updater = new CompositeUpdater(updaters);
            globalModel.CursorLockModel.Add(CursorLockKey.Dialog, CursorLockMode.None);
            
            disposable.Register(() =>
            {
                chatDialogModel.Context.Close();
                globalModel.CursorLockModel.Remove(CursorLockKey.Dialog);
                inputHandlers.Remove(inputUpdater);
                chatContent.Dispose();
            });
        }

        private void RegisterTab(ChatTab tab, TabView<Unit> tabView, ClickableItemsModel<ChatTab> chatTabs, List<(TabView<Unit>, ChatTab, IText)> tabViews, IText chatTitle)
        {
            chatTabs.Add(tab, tabView.Button);
            tabViews.Add((tabView, tab, chatTitle));
        }

        public void Update()
        {
            _updater.Update();
        }
    }
}