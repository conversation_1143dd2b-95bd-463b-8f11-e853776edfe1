using TMPro;
using UI;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.UI;

namespace Game.ExitScreen
{
    public class ExitScreenView : MonoBehaviour
    {
        [field: SerializeField] public ClickButtonView ButtonView { get; private set; }
        [SerializeField] private TextMeshProUGUI _description;
        
        private InputAction _exitAction;

        public void Show(string error, InputAction exitAction)
        {
            _description.text = error;
            Show(exitAction);
        }

        public void Show(InputAction exitAction)
        {
            _exitAction = exitAction;
            gameObject.SetActive(true);
        }

        private void Update()
        {
            if (ButtonView.IsClicked || _exitAction is { triggered: true })
            {
                Application.Quit();
            }
        }
    }
}