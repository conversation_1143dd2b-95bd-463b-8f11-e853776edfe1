using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Core.Unit;
using Framework.Value.Values;
using System.Net;

namespace Network.Connection
{
    public interface IPeerConnection : IRoomConnection
    {
        IAwaiter<IEither<int, Unit>> Connect(IPAddress address, int port, int rtt);
        IAwaiter<IEither<IValue, IValue>> Authorize(string destination, string peer, string token);
    }
}