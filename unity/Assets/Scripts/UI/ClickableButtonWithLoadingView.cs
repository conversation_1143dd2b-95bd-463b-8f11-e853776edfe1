using UnityEngine;
using UnityEngine.Localization;
using UnityEngine.Localization.Components;
using Utils.UiState;

namespace  UI
{
    public class ClickableButtonWithLoadingView : MonoBehaviour
    {
        [field: SerializeField] public CompositeButtonView Button { get; private set; }
        [SerializeField] private EnumStateElement<ButtonState> _buttonState;
        [SerializeField] private DateView _timer;
        [SerializeField] private LocalizeStringEvent[] _texts;

        public void SetLoadingState()
        {
            _buttonState.State = ButtonState.Loading;
        }
        
        public void SetSimpleState()
        {
            _buttonState.State = ButtonState.Text;
        }

        public void SetTimerState()
        {
            _buttonState.State = ButtonState.TimerAndText;
        }

        public void SetLocalizeWithTimeLeftState(LocalizedString localize, long time, TimeSetDescription.TimeFormat format)
        {
            foreach (var text in _texts)
            {
                if (text.StringReference != localize)
                {
                    text.StringReference = localize;
                }
            }

            _timer.Set(time, format);

            _buttonState.State = ButtonState.TimerAndText;
        }

        public void SetLocalize(LocalizedString localize)
        {
            foreach (var text in _texts)
            {
                if (text.StringReference != localize)
                {
                    text.StringReference = localize;
                }
            }

            _buttonState.State = ButtonState.Text;
        }

        private enum ButtonState
        {
            Loading = 0,
            TimerAndText = 1,
            Text = 2,
        }
    }
}