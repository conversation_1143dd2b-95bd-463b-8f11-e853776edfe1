using System;
using System.Collections.Generic;
using UnityEngine.Localization;
using UnityEngine.Localization.Tables;

namespace UI
{
    internal interface ILocalizationArgument<in T>
    {
        bool SetValue(T value);
    }

    internal interface IBox
    {
        object Value { get; }
        bool TryAsConvertible(out IConvertible convertible);
    }
    
    internal class Box<T> : ILocalizationArgument<T>, IFormattable, IBox
    {
        private T _value;

        public bool SetValue(T value)
        {
            bool isChanged = !EqualityComparer<T>.Default.Equals(value, _value);
            _value = value;
            return isChanged;
        }

        public T Value => _value;

        public override string ToString() => _value == null ? "" : _value.ToString();

        public string ToString(string format, IFormatProvider formatProvider)
        {
            if (_value == null) return string.Empty;
            if (_value is IFormattable formated)
            {
                return formated.ToString(format, formatProvider);
            }
            return _value.ToString();
        }

        bool IBox.TryAsConvertible(out IConvertible convertible)
        {
            if (_value is IConvertible value)
            {
                convertible = value;
                return true;
            }

            convertible = default;
            return false;
        }

        object IBox.Value => _value;
    }

    internal class LocalizedStringBox : ILocalizationArgument<LocalizedString>
    {
        private LocalizedString _value;
        private TableReference _table;
        private TableEntryReference _tableEntry;
        
        public bool SetValue(LocalizedString value)
        {
            bool isChanged = !ReferenceEquals(value, _value) || (value != null && 
                                                                 (!value.TableReference.Equals(_table) || !value.TableEntryReference.Equals(_tableEntry)));
            _value = value;
            if (value != null)
            {
                _table = value.TableReference;
                _tableEntry = value.TableEntryReference;
            }
            return isChanged;
        }

        public override string ToString() => _value == null ? string.Empty : _value.GetLocalizedString();
    }
}