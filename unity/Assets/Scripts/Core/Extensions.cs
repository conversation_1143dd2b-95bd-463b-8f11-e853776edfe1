using Framework.Core.Now;
using Framework.Replication.Enum;
using JetBrains.Annotations;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Core
{
    public static class Extensions
    {
        public static Vector3 Vector3(this Vector2 vector2) => new Vector3(vector2.x, 0f, vector2.y);
        public static Vector2 Vector2(this Vector3 vector3) => new Vector2(vector3.x, vector3.z);

        public static long Get(this INow now)
        {
            return (long) now.Get;
        }
        
        public static T Random<T>(this IEnum<T> @enum)
        {
            if (@enum.TryFromIndex(UnityEngine.Random.Range(0, @enum.Count), out T item))
            {
                return item;
            }

            return default;
        }

        public static void SetActive(this Component monoBehaviour, bool isActive)
        {
            GameObject go = monoBehaviour.gameObject;
            if (go.activeSelf != isActive)
            {
                go.SetActive(isActive);
            }
        }
        
        public static void SetActive(this Component[] components, bool isActive)
        {
            foreach (var component in components)
            {
                SetActive(component, isActive);
            }
        }
        
        public static void SetActive(this GameObject go, bool isActive)
        {
            if (go.activeSelf != isActive)
            {
                go.SetActive(isActive);
            }
        }
        
        public static void SetActive(this GameObject[] objects, bool isActive)
        {
            foreach (var obj in objects)
            {
                SetActive(obj, isActive);
            }
        }
        
        public static void SetEnabled(this Behaviour behaviour, bool isEnabled)
        {
            if (behaviour.enabled != isEnabled)
            {
                behaviour.enabled = isEnabled;
            }
        }

        public static void SetLayerRecursively(this GameObject go, int layer, string tag = default, int excludingLayer = -1)
        {
            if (go.layer != excludingLayer)
            {
                go.layer = layer;  
            }
            
            if (!string.IsNullOrEmpty(tag)) go.tag = tag;
            for (int i = 0; i < go.transform.childCount; i++)
            {
                go.transform.GetChild(i).gameObject.SetLayerRecursively(layer, tag, excludingLayer: excludingLayer);
            }
        }
        
        public static long Clamp(long value, long min, long max)
        {
            return Math.Min(max, Math.Max(min, value));
        }
        
        public static Vector2 GetWorldPosition(this RectTransform transform)
        {
            var size = GetWorldSize(transform);
            var offset = size * transform.pivot;
            return new Vector2(transform.position.x - offset.x, transform.position.y - offset.y + size.y);
        }
        
        public static Vector2 GetWorldPositionYInverse(this RectTransform transform)
        {
            var size = GetWorldSize(transform);
            var offset = size * transform.pivot;
            return new Vector2(transform.position.x - offset.x, transform.position.y - offset.y);
        }

        public static Vector2 GetWorldSize(this RectTransform transform)
        {
            return UnityEngine.Vector2.Scale(transform.rect.size, transform.lossyScale);
        }
        
        public static Rect GetWorldRect(this RectTransform transform)
        {
            var worldSize = GetWorldSize(transform);
            var offset = UnityEngine.Vector2.Scale(transform.pivot, worldSize);
            return new Rect((Vector2)transform.position - offset, worldSize);
        }

        public static Vector2 WorldToNormalizedUnclamped(this RectTransform transform, Vector2 worldPos)
        {
            var worldSize = GetWorldSize(transform);
            var startPos = (Vector2)transform.position - UnityEngine.Vector2.Scale(transform.pivot, worldSize);
            return new Vector2((worldPos.x - startPos.x) / worldSize.x, (worldPos.y - startPos.y) / worldSize.y);
        }
        
        public static void SetPivotWithWorldPositionStays(this RectTransform transform, Vector2 pivot)
        {
            Vector3 deltaPosition = transform.pivot - pivot;
            deltaPosition = UnityEngine.Vector2.Scale(deltaPosition, transform.rect.size);
            deltaPosition = UnityEngine.Vector2.Scale(deltaPosition, transform.localScale);
            deltaPosition = transform.rotation * deltaPosition;
            transform.pivot = pivot;
            transform.localPosition -= deltaPosition;
        }

        public static void SetWorlPositionIgnorePivot(this RectTransform transform, Vector2 position)
        {
            Vector2 pivotOffset = new Vector2((transform.pivot.x - 0.5f) * transform.rect.width, (transform.pivot.y - 0.5f) * transform.rect.height);
            transform.position = position + pivotOffset;
        }

        [CanBeNull]
        public static TSource FirstOrDefault<TSource>(this TSource[] source, Func<TSource, bool> predicate)
        {
            foreach (var element in source)
            {
                if (predicate(element)) return element;
            }

            return default;
        }

        public static TSource First<TSource>(this TSource[] source, Func<TSource, bool> predicate)
        {
            foreach (var element in source)
            {
                if (predicate(element)) return element;
            }

            throw new IndexOutOfRangeException();
        }

        [CanBeNull]
        public static TSource FirstOrDefault<TSource>(this List<TSource> source, Func<TSource, bool> predicate)
        {
            foreach (var element in source)
            {
                if (predicate(element)) return element;
            }

            return default;
        }

        public static TSource First<TSource>(this List<TSource> source, Func<TSource, bool> predicate)
        {
            foreach (var element in source)
            {
                if (predicate(element)) return element;
            }

            throw new IndexOutOfRangeException();
        }
    }
}