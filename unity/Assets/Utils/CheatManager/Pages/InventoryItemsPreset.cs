using System.Collections.Generic;
using Framework.Core.Identified;
using Models.References.Inventory;

namespace Utils.CheatManager.Pages
{
    public class InventoryItemsPreset : Identified
    {
        private static readonly List<InventoryItemsPreset> _enum = new();
        public static IReadOnlyList<InventoryItemsPreset> Enum => _enum;

        private static readonly InventoryItemDescription _backpackItem = InventoryItemDescription.BackpackLegend;
        private static readonly InventoryItemDescription _beltItem = InventoryItemDescription.BeltLegend;
        private static readonly InventoryItemsPresetItem _backpackPresetItem = new InventoryItemsPresetItem(EquipSlotDescription.Backpack, _backpackItem, 1, 0, 0);
        private static readonly InventoryItemsPresetItem _beltPresetItem = new InventoryItemsPresetItem(EquipSlotDescription.Belt, _beltItem, 1, 0, 0);

        public InventoryItemsPreset(string id, InventoryItemsPresetItem[] items) : base(id)
        {
            Items = items;
            
            _enum.Add(this);
        }
        
        public IReadOnlyList<InventoryItemsPresetItem> Items { get; }
        
        public static InventoryItemsPreset FullEquip { get; } = new InventoryItemsPreset("full_equip",
            new[]
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Vest, InventoryItemDescription.Vest4Mk1, 1, InventoryItemDescription.Vest4Mk1.Armor.MaxArmorAmount, 0),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.ApScalarM3),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot0, InventoryItemDescription.SyringeEpic),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot1, InventoryItemDescription.FragGrenadeUncommon),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot2, InventoryItemDescription.ArmorPlateEpic),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot3, InventoryItemDescription.AfakUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Drill),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.Dp9M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot10, InventoryItemDescription.BulettiThunderX),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot18, InventoryItemDescription.Ordr12M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot22, InventoryItemDescription.BreachItem2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot23, InventoryItemDescription.StorageBreachItem),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot28, InventoryItemDescription.VehicleTrunkBreachItem),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot29, InventoryItemDescription.PainkillerEpic),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot33, InventoryItemDescription.SmokeGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot34, InventoryItemDescription.FlashGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot35, InventoryItemDescription.GasGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.PoliceRadio),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.Lockpick1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.ShotgunAmmo),
            }
        );

        public static InventoryItemsPreset Aviation { get; } = new InventoryItemsPreset("aviation",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.HelicopterGarage),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.RomanovR8),
            }
        );
        
        public static InventoryItemsPreset Cars { get; } = new InventoryItemsPreset("cars",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.FukurouSpark),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.FukurouMillennium),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.LonghornVan),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.KronenE),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.LonghornTitan),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.KronenJ),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.LonghornWild),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot26, InventoryItemDescription.BulettiThunderX),
            }
        );
        
        public static InventoryItemsPreset CarUpgrades1 { get; } = new InventoryItemsPreset("car_upgrades_1",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.PoliceRadioCarUpgradeCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.PoliceRadioCarUpgradeRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot6, InventoryItemDescription.EcuCarUpgradeCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot8, InventoryItemDescription.EcuCarUpgradeRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.TurbochargerCarUpgradeCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.TurbochargerCarUpgradeRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.EngineCoolingCarUpgradeCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot26, InventoryItemDescription.EngineCoolingCarUpgradeRare),
            }
        );

        public static InventoryItemsPreset CarUpgrades2 { get; } = new InventoryItemsPreset("car_upgrades_2",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.SuspensionCarUpgradeCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot1, InventoryItemDescription.SuspensionCarUpgradeRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.SteeringRackCarUpgradeCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot13, InventoryItemDescription.SteeringRackCarUpgradeRare),
            }
        );
        
        public static InventoryItemsPreset CarUpgrades3 { get; } = new InventoryItemsPreset("car_upgrades_3",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.TrunkInventoryCarUpgradeCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.TrunkInventoryCarUpgradeRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.TrunkCargoInventoryCarUpgradeCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.TrunkCargoInventoryCarUpgradeRare),
            }
        );
        
        public static InventoryItemsPreset Keys { get; } = new InventoryItemsPreset("keys",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.RoomKey6),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot3, InventoryItemDescription.RoomKey0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.RoomKey1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot5, InventoryItemDescription.RoomKey2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot6, InventoryItemDescription.RoomKey3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot7, InventoryItemDescription.RoomKey4),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot8, InventoryItemDescription.RoomKey5),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot9, InventoryItemDescription.SafeKey10),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot10, InventoryItemDescription.SafeKey0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot11, InventoryItemDescription.SafeKey1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.SafeKey2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot13, InventoryItemDescription.SafeKey3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.SafeKey4),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot15, InventoryItemDescription.SafeKey5),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.SafeKey6),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot17, InventoryItemDescription.SafeKey7),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot18, InventoryItemDescription.SafeKey8),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot19, InventoryItemDescription.SafeKey9),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot20, InventoryItemDescription.Drill),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.EpicKey0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.DepositBoxKey),
             }
        );
        
        public static InventoryItemsPreset CumulativeStates { get; } = new InventoryItemsPreset("cumulative_states",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.TabletCumulative),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.TabletCumulative, InventoryItemDescription.TabletCumulative.CumulativeItemDescription.MaxPointsAmount - 1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.Tablet),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.CargoManifestCumulative1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.CargoManifestCumulative1, InventoryItemDescription.CargoManifestCumulative1.CumulativeItemDescription.MaxPointsAmount - 1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.CargoManifest1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.DepositBoxKeyCumulative),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot25, InventoryItemDescription.DepositBoxKeyCumulative, InventoryItemDescription.DepositBoxKeyCumulative.CumulativeItemDescription.MaxPointsAmount - 1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot26, InventoryItemDescription.DepositBoxKey),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot27, InventoryItemDescription.UsbRubberDuckyCumulative),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot28, InventoryItemDescription.UsbRubberDuckyCumulative, InventoryItemDescription.UsbRubberDuckyCumulative.CumulativeItemDescription.MaxPointsAmount - 1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot29, InventoryItemDescription.UsbRubberDucky),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot30, InventoryItemDescription.ActionCameraCumulative1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot32, InventoryItemDescription.ActionCameraCumulative1, InventoryItemDescription.ActionCameraCumulative1.CumulativeItemDescription.MaxPointsAmount - 1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot34, InventoryItemDescription.ActionCamera1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.LabFlaskCumulative1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.LabFlaskCumulative1, InventoryItemDescription.LabFlaskCumulative1.CumulativeItemDescription.MaxPointsAmount - 1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.LabFlask1),
            }
        );
        
        public static InventoryItemsPreset CumulativeAll { get; } = new InventoryItemsPreset("cumulative_all",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.TabletCumulative),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.CargoManifestCumulative1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.CargoManifestCumulative2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.CargoManifestCumulative3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.DepositBoxKeyCumulative),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot27, InventoryItemDescription.UsbRubberDuckyCumulative),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot30, InventoryItemDescription.ActionCameraCumulative1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot32, InventoryItemDescription.ActionCameraCumulative2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot34, InventoryItemDescription.ActionCameraCumulative3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.LabFlaskCumulative1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.LabFlaskCumulative2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.LabFlaskCumulative3),
            }
        );
        
        public static InventoryItemsPreset Breach { get; } = new InventoryItemsPreset("breach",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Lock0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.Lock1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.BreachItem0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.BreachItem1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot15, InventoryItemDescription.BreachItem2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.StorageBreachItem),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot17, InventoryItemDescription.VehicleTrunkBreachItem),
            }
        );
        
        public static InventoryItemsPreset Raid { get; } = new InventoryItemsPreset("raid",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.PlotBuildingDefenseModuleUncommon, InventoryItemDescription.PlotBuildingDefenseModuleUncommon.StackSize, 0, 0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.PlotBuildingDefenseModuleEpic, InventoryItemDescription.PlotBuildingDefenseModuleEpic.StackSize, 0, 0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.PlotBuildingDefenseModuleLegendary, InventoryItemDescription.PlotBuildingDefenseModuleLegendary.StackSize, 0, 0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot6, InventoryItemDescription.DecoderUncommon, InventoryItemDescription.DecoderUncommon.StackSize, 0, 0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot8, InventoryItemDescription.DecoderEpic, InventoryItemDescription.DecoderEpic.StackSize, 0, 0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot10, InventoryItemDescription.DecoderLegendary, InventoryItemDescription.DecoderLegendary.StackSize, 0, 0),
            }
        );

        public static InventoryItemsPreset Buildings1 { get; } = new InventoryItemsPreset("building_1",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Home0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.Home2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.Home1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.CompoundWall0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.CompoundWall1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.CompoundWall2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.CompoundWall1Test),
            }
        );
        
        public static InventoryItemsPreset Buildings2 { get; } = new InventoryItemsPreset("building_2",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.CarGarage0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.CarGarage1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.StorageOutbuilding),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.Watchtower),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.HelicopterGarage),
            }
        );
        
        public static InventoryItemsPreset Furniture { get; } = new InventoryItemsPreset("furniture",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.CopFurniture),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.CollectionTable),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.CargoWorkbench),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.StorageSmall),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.StorageMedium),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.StorageBig),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.ShelvingSmall),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot26, InventoryItemDescription.ShelvingMedium),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot28, InventoryItemDescription.ShelvingBig),
            }
        );
        
        public static InventoryItemsPreset UpgradesCargoWorkbench { get; } = new InventoryItemsPreset("upgrades_cargo_workbench",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.CargoWorkbenchUpgradeAdditionalSlot),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.CargoWorkbenchUpgradeAdditionalSlot),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.CargoWorkbenchUpgradeToolbox0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.CargoWorkbenchUpgradeToolbox1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.CargoWorkbenchUpgradeCircularSaw0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.CargoWorkbenchUpgradeCircularSaw1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.CargoWorkbenchUpgradePlasmaCutter0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot26, InventoryItemDescription.CargoWorkbenchUpgradePlasmaCutter1),
            }
        );
        
        public static InventoryItemsPreset UpgradesCollectionTable { get; } = new InventoryItemsPreset("upgrades_collection_table",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Vest, InventoryItemDescription.Vest4Mk1, 1, InventoryItemDescription.Vest4Mk1.Armor.MaxArmorAmount, 0),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.CollectionTableUpgradeRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.CollectionTableUpgradeLegendary),
            }
        );
        
        public static InventoryItemsPreset UpgradesCopFurniture { get; } = new InventoryItemsPreset("upgrades_cop_furniture",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.CopFurnitureUpgradeEquip0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot1, InventoryItemDescription.CopFurnitureUpgradeEquip1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.CopFurnitureUpgradeEquip2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot6, InventoryItemDescription.CopFurnitureUpgradeReward0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot7, InventoryItemDescription.CopFurnitureUpgradeReward1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot8, InventoryItemDescription.CopFurnitureUpgradeReward2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.CopFurnitureUpgradeWeapon0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.CopFurnitureUpgradeWeapon1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.CopFurnitureUpgradeWeapon2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.CopFurnitureUpgradeCar0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot26, InventoryItemDescription.CopFurnitureUpgradeCar1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot28, InventoryItemDescription.CopFurnitureUpgradeHelicopter0),
            }
        );
        
        public static InventoryItemsPreset UpgradesMoneyWorkbench { get; } = new InventoryItemsPreset("upgrades_money_workbench",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.MoneyWorkbenchUpgradeSpeedup0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.MoneyWorkbenchUpgradeSpeedup1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.MoneyWorkbenchUpgradeSpeedup2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.MoneyWorkbenchUpgradeLaunderingRate0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.MoneyWorkbenchUpgradeLaunderingRate1),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.MoneyWorkbenchUpgradeLaunderingRate2),
            }
        );

        public static InventoryItemsPreset Cash { get; } = new InventoryItemsPreset("cash",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot1, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot3, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot5, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot6, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot7, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot8, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot9, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot10, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot11, InventoryItemDescription.CashLegendary),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot13, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot15, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot17, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot18, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot19, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot20, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot21, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot22, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot23, InventoryItemDescription.CashRare),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.CashUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot25, InventoryItemDescription.CashUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot26, InventoryItemDescription.CashUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot27, InventoryItemDescription.CashUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot28, InventoryItemDescription.CashUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot29, InventoryItemDescription.CashUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot30, InventoryItemDescription.CashUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot31, InventoryItemDescription.CashUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot32, InventoryItemDescription.CashUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot33, InventoryItemDescription.CashCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot34, InventoryItemDescription.CashCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot35, InventoryItemDescription.CashCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.CashCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.CashCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.CashCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.CashCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.CashCommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.CashCommon),
            }
        );
        
        public static InventoryItemsPreset Luxury { get; } = new InventoryItemsPreset("luxury",
            new[]
            {
                _beltPresetItem,
                _backpackPresetItem,

                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.GrenadeLauncherShell),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.GoldBar),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.GemstoneDiamond),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot6, InventoryItemDescription.VinylRecord2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot8, InventoryItemDescription.YachtKey),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot10, InventoryItemDescription.RomanovR8),
            }
        );
        
        public static InventoryItemsPreset UsableItems { get; } = new InventoryItemsPreset("usable_items",
            new[]
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(PocketSlotDescription.Slot0, InventoryItemDescription.SyringeEpic),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot1, InventoryItemDescription.PainkillerEpic),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot2, InventoryItemDescription.ArmorPlateUncommon),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot3, InventoryItemDescription.AfakUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.SyringeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot1, InventoryItemDescription.SyringeEpic),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.PainkillerUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot3, InventoryItemDescription.PainkillerEpic),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.AfakUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot5, InventoryItemDescription.ArmorPlateUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot6, InventoryItemDescription.ArmorPlateEpic),
            }
        );
        
        public static InventoryItemsPreset Grenade { get; } = new InventoryItemsPreset("grenade",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(PocketSlotDescription.Slot0, InventoryItemDescription.FragGrenadeUncommon),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot1, InventoryItemDescription.SmokeGrenadeUncommon),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot2, InventoryItemDescription.GasGrenadeUncommon),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot3, InventoryItemDescription.FlashGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.FragGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot1, InventoryItemDescription.FragGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.SmokeGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot3, InventoryItemDescription.SmokeGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.GasGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot5, InventoryItemDescription.GasGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot6, InventoryItemDescription.FlashGrenadeUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot7, InventoryItemDescription.FlashGrenadeUncommon),
            }
        );
        
        public static InventoryItemsPreset Vests { get; } = new InventoryItemsPreset("vests",
            new []
            {
                _backpackPresetItem,
                _beltPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Vest, InventoryItemDescription.Vest4Mk1, 1, InventoryItemDescription.Vest4Mk1.Armor.MaxArmorAmount, 0),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot0, InventoryItemDescription.ArmorPlateEpic),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot1, InventoryItemDescription.ArmorPlateEpic),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot2, InventoryItemDescription.ArmorPlateUncommon),
                new InventoryItemsPresetItem(PocketSlotDescription.Slot3, InventoryItemDescription.ArmorPlateUncommon),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Vest1Mk1, 1, InventoryItemDescription.Vest1Mk1.Armor.MaxArmorAmount, 0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.Vest2Mk1, 1, InventoryItemDescription.Vest2Mk1.Armor.MaxArmorAmount, 0),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.Vest3Mk1, 1, InventoryItemDescription.Vest3Mk1.Armor.MaxArmorAmount, 0),
            }
        );

        public static InventoryItemsPreset WeaponLegendaryPack { get; } = new InventoryItemsPreset("weapon_legendary_pack",
            new[]
            {
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.SrReaperM3),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.ApScalarM3),
                _backpackPresetItem,
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot1, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot2, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot3, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot4, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot5, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot6, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot7, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot8, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot9, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot10, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot11, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot12, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot13, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot14, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot15, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot16, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot17, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot18, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot19, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot20, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot21, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot22, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot23, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot24, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot25, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot26, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot27, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot28, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot29, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot30, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot31, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot32, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot33, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot34, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot35, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.MpAmmo),

            }
        );
        
        public static InventoryItemsPreset WeaponKl545Pack { get; } = new InventoryItemsPreset("weapon_kl545_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Kl545M1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Kl545M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Kl545M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.RifleAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponJp7Pack { get; } = new InventoryItemsPreset("weapon_jp7_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Jp7M1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Jp7M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Jp7M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.MpAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponDp9Pack { get; } = new InventoryItemsPreset("weapon_dp9_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Dp9M1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Dp9M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.PistolAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponCb45Pack { get; } = new InventoryItemsPreset("weapon_cb45_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Cb45M1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Cb45M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Cb45M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.MpAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponHarris500Pack { get; } = new InventoryItemsPreset("weapon_harris500_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Harris500M1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Harris500M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Harris500M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.ShotgunAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponRc556Pack { get; } = new InventoryItemsPreset("weapon_rc556_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Rc556M1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Rc556M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Rc556M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.RifleAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponStarhPack { get; } = new InventoryItemsPreset("weapon_starh_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.StarhM1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.StarhM2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.StarhM3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.RifleAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponSrReaperPack { get; } = new InventoryItemsPreset("weapon_sr_reaper_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.SrReaperM1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.SrReaperM2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.SrReaperM3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.RifleAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponPdw90Pack { get; } = new InventoryItemsPreset("weapon_pdw90_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Pdw90M1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Pdw90M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Pdw90M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.MpAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponApScalarPack { get; } = new InventoryItemsPreset("weapon_ap_scalar_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.ApScalarM1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.ApScalarM2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.ApScalarM3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.MpAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.MpAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponOrdr12Pack { get; } = new InventoryItemsPreset("weapon_ordr12_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Ordr12M1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Ordr12M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Ordr12M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.ShotgunAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponLbr44Pack { get; } = new InventoryItemsPreset("weapon_lbr44_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Lbr44M1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Lbr44M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.PistolAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.PistolAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponTag12KPack { get; } = new InventoryItemsPreset("weapon_tag12k_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Tag12Km1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Tag12Km2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Tag12Km3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.ShotgunAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.ShotgunAmmo),
            }
        );
        
        public static InventoryItemsPreset WeaponDrm14Pack { get; } = new InventoryItemsPreset("weapon_drm14_pack",
            new[]
            {
                _backpackPresetItem,
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon0, InventoryItemDescription.Drm14M1),
                new InventoryItemsPresetItem(EquipSlotDescription.Weapon1, InventoryItemDescription.Drm14M2),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot0, InventoryItemDescription.Drm14M3),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot36, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot37, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot38, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot39, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot40, InventoryItemDescription.RifleAmmo),
                new InventoryItemsPresetItem(InventorySlotDescription.Slot41, InventoryItemDescription.RifleAmmo),
            }
        );
    }

    public class InventoryItemsPresetItem
    {
        public SlotSourceDescription SlotSource { get; }
        public InventorySlotDescription InventorySlot { get; }
        public EquipSlotDescription EquipSlot { get; }
        public PocketSlotDescription PocketSlot { get; }
        public InventoryItemDescription Item { get; }
        public int Count { get; }
        public int Ammo { get; }
        public int Durability { get; }
        public int CumulativePoints { get; }
        
        public InventoryItemsPresetItem(InventorySlotDescription inventorySlot, InventoryItemDescription item, int count, int ammo, int durability, int cumulativePoints = 0)
        {
            SlotSource = SlotSourceDescription.Inventory;
            InventorySlot = inventorySlot;
            Item = item;
            Count = count;
            Ammo = ammo;
            Durability = durability;
            CumulativePoints = cumulativePoints;
        }
        
        public InventoryItemsPresetItem(InventorySlotDescription inventorySlot, InventoryItemDescription item, int cumulativePoints = 0)
        {
            SlotSource = SlotSourceDescription.Inventory;
            InventorySlot = inventorySlot;
            Item = item;
            CumulativePoints = cumulativePoints;
            Count = item.StackSize;
            Ammo = item.IsWeapon ? item.Weapon.Magazine : 0;
            Durability = 0;
        }
        
        public InventoryItemsPresetItem(EquipSlotDescription equipSlot, InventoryItemDescription item, int count, int ammo, int durability, int cumulativePoints = 0)
        {
            SlotSource = SlotSourceDescription.Equip;
            EquipSlot = equipSlot;
            Item = item;
            Count = count;
            Ammo = ammo;
            Durability = durability;
            CumulativePoints = cumulativePoints;
        }
        
        public InventoryItemsPresetItem(EquipSlotDescription equipSlot, InventoryItemDescription item, int cumulativePoints = 0)
        {
            SlotSource = SlotSourceDescription.Equip;
            EquipSlot = equipSlot;
            Item = item;
            Count = item.StackSize;
            Ammo = item.IsWeapon ? item.Weapon.Magazine : 0;
            Durability = 0;
            CumulativePoints = cumulativePoints;
        }
        
        public InventoryItemsPresetItem(PocketSlotDescription pocketSlot, InventoryItemDescription item, int count, int cumulativePoints = 0)
        {
            SlotSource = SlotSourceDescription.Pocket;
            PocketSlot = pocketSlot;
            Item = item;
            Count = count;
            CumulativePoints = cumulativePoints;
        }
        
        public InventoryItemsPresetItem(PocketSlotDescription pocketSlot, InventoryItemDescription item, int cumulativePoints = 0)
        {
            SlotSource = SlotSourceDescription.Pocket;
            PocketSlot = pocketSlot;
            Item = item;
            Count = item.StackSize;
            CumulativePoints = cumulativePoints;
        }
    }
}