<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" xsi="http://www.w3.org/2001/XMLSchema-instance" engine="UnityEngine.UIElements" editor="UnityEditor.UIElements" noNamespaceSchemaLocation="../../../../UIElementsSchema/UIElements.xsd" editor-extension-mode="False">
    <Style src="project://database/Assets/Utils/CheatManager/CheatManager.uss?fileID=7433441132597879392&amp;guid=2ed2fcac0215ce849b548b0cff794510&amp;type=3#CheatManager" />
    <ui:VisualElement class="cheat-page">
        <ui:VisualElement class="cheat-page__content">
            <ui:GroupBox text="Cheat set items preset" name="cheat_set_preset" class="cheat-page__content-item">
                <ui:DropdownField label="Preset" index="-1" choices="System.Collections.Generic.List`1[System.String]" name="preset_dropdown" />
                <ui:Button text="Set preset" display-tooltip-when-elided="true" name="set_preset_button" />
                <ui:Button text="Set collection" display-tooltip-when-elided="true" name="set_collection_button" />
                <ui:Button text="Clear backpack" display-tooltip-when-elided="true" name="clear_backpack_button" />
                <ui:Button text="Clear equip" display-tooltip-when-elided="true" name="clear_equip_button" />
            </ui:GroupBox>
            <ui:GroupBox text="Find robbery" name="find_robbery" class="cheat-page__content-item">
                <ui:Button text="Find robbery" display-tooltip-when-elided="true" name="find_robbery_button" />
                <ui:Button text="Test robbery" display-tooltip-when-elided="true" name="test_robbery_button" />
                <ui:Button text="Hub robbery" display-tooltip-when-elided="true" name="hub_robbery_button" />
                <ui:Button text="Blocking robbery" display-tooltip-when-elided="true" name="blocking_robbery_button" />
                <ui:Button text="Leave battle" display-tooltip-when-elided="true" name="leave_battle" />
            </ui:GroupBox>
            <ui:GroupBox text="Cheat sprint" name="cheat_sprint" class="cheat-page__content-item">
                <ui:Button text="Enable" display-tooltip-when-elided="true" name="enable_cheat_sprint_button" />
                <ui:Button text="Disable" display-tooltip-when-elided="true" name="disable_cheat_sprint_button" />
            </ui:GroupBox>
            <ui:GroupBox text="Teleport" name="teleport" class="cheat-page__content-item">
                <ui:DropdownField label="Select point" index="-1" choices="System.Collections.Generic.List`1[System.String]" name="point_dropdown" />
                <ui:Button text="Teleport" display-tooltip-when-elided="true" name="teleport" />
                <ui:Button text="Teleport plot" display-tooltip-when-elided="true" name="teleport_self_plot" />
                <ui:Button text="Teleport to map marker" display-tooltip-when-elided="true" name="teleport_to_map_marker" />
            </ui:GroupBox>
            <ui:GroupBox name="fps" text="FPS" class="cheat-page__content-item">
                <ui:Button text="Enable" display-tooltip-when-elided="true" name="enable_fps_counter" />
                <ui:Button text="Disable" display-tooltip-when-elided="true" name="disable_fps_counter" />
            </ui:GroupBox>
            <ui:GroupBox name="debug_id" text="Debug Id" class="cheat-page__content-item">
                <ui:Button text="Enable" display-tooltip-when-elided="true" name="enable_debug_id_counter" />
                <ui:Button text="Disable" display-tooltip-when-elided="true" name="disable_debug_id_counter" />
            </ui:GroupBox>
        </ui:VisualElement>
    </ui:VisualElement>
</ui:UXML>
