using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using Utils.Geometry;
using Utils.InstancedRendering.Data;
using Utils.InstancedRendering.SharedJobs;

namespace Utils.InstancedRendering.CullingStrategies
{
    public class FrustumCullingStrategy : ICullingStrategy
    {
        public void PreCull()
        {
            
        }

        public JobHandle ScheduleCulling(float3 cameraPosition, CameraPlanes cameraPlanes, float cullDistance, NativeArray<float>.ReadOnly lodSwitchOffsetFactors, NativeArray<float4>.ReadOnly positionsAndScales,
            Bounds bounds, float4 boundsRectMinMax, NativeList<Instance> result, float lodBias)
        {
            return new InstancesCullingFrustum()
            {
                CameraPosition = cameraPosition,
                Planes = cameraPlanes.Planes,
                CullDistance = cullDistance,
                LodBias = lodBias,
                LodSwitchOffsetFactors = lodSwitchOffsetFactors,
                PositionsAndScales = positionsAndScales,
                Bounds = bounds,
                ResultInstances = result.AsParallelWriter()
            }.Schedule(positionsAndScales.Length, 256);
        }

        public void Dispose(JobHandle jobHandle)
        {
            
        }
    }
}