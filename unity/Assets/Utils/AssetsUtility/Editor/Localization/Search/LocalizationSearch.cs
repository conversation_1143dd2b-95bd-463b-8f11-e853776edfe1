using System;
using System.Collections.Generic;
using System.IO;
using UnityEditor;
using UnityEditor.Localization;
using UnityEngine;
using UnityEngine.Localization.Tables;
using Utils.AssetsUtilityEditor;
using Utils.Editor.Localization.Search.Indexers;
using Utils.Editor.Localization.Search.Settings;
using Object = UnityEngine.Object;

namespace Utils.Editor.Localization.Search
{
    public static class LocalizationSearch
    {
        private const string _libraryFolderPath = "Library/Search/Localization";
        private const string _indexLibraryPath = _libraryFolderPath + "/LocalizationSearch.index";
        private const string _guidToEntriesMapLibraryPath = _libraryFolderPath +  "/LocalizationEntriesMap.bytes";

        public const string UseLocalization = "use_localization";
        public const string LocalizationKeys = "localization_keys";
        public static LocalizationSearchCache SearchCache;
        public static LocalizationSearchIndexer Indexer;
        public static bool IsReady => !_isLoading && Indexer.IsReady();

        private static bool _isLoading;

        [MenuItem("Kefir/Localization Tools/Provider/" + nameof(RefreshIndexer))]
        public static void RefreshIndexer()
        {
            LoadIndexer(success =>
            {
                if (success)
                {
                    var newIndexer = BuildIndexer();

                    Indexer.Start();
                    Indexer.Merge(Array.Empty<string>(), newIndexer);
                    Indexer.Finish(Cache, removedDocuments: null);
                }
                else
                {
                    Indexer = BuildIndexer(onFinished: Cache);
                }
            });
        }

        [MenuItem("Kefir/Localization Tools/Provider/" + nameof(ClearIndexer))]
        public static void ClearIndexer()
        {
            SearchCache = null;
            Indexer = null;

            if (File.Exists(_indexLibraryPath))
            {
                File.Delete(_indexLibraryPath);
            }

            if (File.Exists(_guidToEntriesMapLibraryPath))
            {
                File.Delete(_guidToEntriesMapLibraryPath);
            }

            Debug.Log("Localization search indexer was cleared.");
        }

        private static void LoadIndexer(Action<bool> onLoaded = null)
        {
            _isLoading = true;

            SearchCache = new LocalizationSearchCache();
            if (File.Exists(_guidToEntriesMapLibraryPath))
            {
                byte[] localizationEntriesMapBytes = File.ReadAllBytes(_guidToEntriesMapLibraryPath);
                SearchCache.FromBytes(localizationEntriesMapBytes);
            }

            Indexer = new LocalizationSearchIndexer();
            Indexer.Setup(SearchCache);

            if (File.Exists(_indexLibraryPath))
            {
                byte[] indexBytes = File.ReadAllBytes(_indexLibraryPath);
                Indexer.Load(indexBytes, successful =>
                {
                    Debug.Log($"Localization search indexer is loaded: {successful}.");
                    _isLoading = false;
                    onLoaded?.Invoke(successful);
                });
            }
            else
            {
                Debug.LogWarning("Can't load localization search data, maybe cache doesn't exist.");
                _isLoading = false;
                onLoaded?.Invoke(false);
            }
        }

        private static LocalizationSearchIndexer BuildIndexer(Action<byte[]> onFinished = null)
        {
            var indexer = new LocalizationSearchIndexer();
            try
            {
                Debug.Log("Start localization search indexing.");

                indexer.Setup(SearchCache);
                indexer.Start();
                indexer.Build(ScriptableObjectInstance.Get<LocalizationSearchSettings>());
                indexer.Finish(bytes => onFinished?.Invoke(bytes), removedDocuments: null);

                Debug.Log("Finish localization search indexing.");
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
            }
            return indexer;
        }

        private static void Cache(byte[] bytes)
        {
            if (!Directory.Exists(_libraryFolderPath))
            {
                Directory.CreateDirectory(_libraryFolderPath);
            }

            File.WriteAllBytes(_indexLibraryPath, bytes);
            File.WriteAllBytes(_guidToEntriesMapLibraryPath, SearchCache.ToBytes());
        }

        public static bool TryExtractLocalization(Object target, List<LocalizationEntry> outputLocalizationEntries)
        {
            outputLocalizationEntries.Clear();

            var serializedObject = new SerializedObject(target);
            var propertiesIterator = serializedObject.GetIterator();
            while (propertiesIterator.Next(true))
            {
                if (propertiesIterator.type == "LocalizedString")
                {
                    if (propertiesIterator.isArray)
                    {
                        continue;
                    }

                    var collectionNameProperty = propertiesIterator.FindPropertyRelative("m_TableReference.m_TableCollectionName");
                    var collectionName = collectionNameProperty.stringValue;
                    TableReference tableReference = collectionName.StartsWith("GUID:") ? Guid.Parse(collectionName.Substring("GUID:".Length, collectionName.Length - "GUID:".Length)) : collectionName;

                    var key = propertiesIterator.FindPropertyRelative("m_TableEntryReference.m_Key");
                    var keyId = propertiesIterator.FindPropertyRelative("m_TableEntryReference.m_KeyId");
                    TableEntryReference tableEntryReference = keyId.longValue == 0 ? key.stringValue : keyId.longValue;

                    var collection = LocalizationEditorSettings.GetStringTableCollection(tableReference);
                    if (collection == null)
                    {
                        continue;
                    }

                    var table = collection.SharedData;
                    var entry = table.GetEntryFromReference(tableEntryReference);
                    if (entry != null)
                    {
                        outputLocalizationEntries.Add(new LocalizationEntry(table, entry));
                    }
                }
            }

            return outputLocalizationEntries.Count > 0;
        }

        public static bool TryExtractLocalization(PropertyModification[] propertyModifications, List<LocalizationEntry> outputLocalizationEntries)
        {
            outputLocalizationEntries.Clear();

            var modificationsPerComponent = new Dictionary<int, List<PropertyModification>>();
            foreach (var modification in propertyModifications)
            {
                var target = modification?.target;
                if (target== null)
                {
                    continue;
                }

                int targetCode = target.GetHashCode();
                if (!modificationsPerComponent.TryGetValue(targetCode, out var modifications))
                {
                    modifications = new List<PropertyModification>();
                    modificationsPerComponent.Add(targetCode, modifications);
                }
                modifications.Add(modification);
            }

            foreach (var modificationPerComponent in modificationsPerComponent)
            {
                foreach (var propertyModification in modificationPerComponent.Value)
                {
                    TableEntryReference tableEntryReference = null;
                    if (propertyModification.propertyPath.EndsWith("m_TableEntryReference.m_Key"))
                    {
                        tableEntryReference = propertyModification.value;
                    }
                    else if (propertyModification.propertyPath.EndsWith("m_TableEntryReference.m_KeyId"))
                    {
                        tableEntryReference = long.Parse(propertyModification.value);
                    }

                    foreach (var collection in LocalizationEditorSettings.GetStringTableCollections())
                    {
                        if (collection == null)
                        {
                            continue;
                        }

                        var table = collection.SharedData;
                        var entry = table.GetEntryFromReference(tableEntryReference);
                        if (entry != null)
                        {
                            outputLocalizationEntries.Add(new LocalizationEntry(table, entry));
                        }
                    }
                }
            }
            return outputLocalizationEntries.Count > 0;
        }

        public static bool TryGetEntriesByKey(string key, List<LocalizationEntry> outputTableEntries)
        {
            outputTableEntries.Clear();

            foreach (var collection in LocalizationEditorSettings.GetStringTableCollections())
            {
                foreach (var entry in collection.SharedData.Entries)
                {
                    if (entry.Key.Equals(key))
                    {
                        outputTableEntries.Add(new LocalizationEntry(collection.SharedData, entry));
                    }
                }
            }
            return outputTableEntries.Count > 0;
        }
    }
}