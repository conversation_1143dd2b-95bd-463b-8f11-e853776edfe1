using System;
using UnityEditor.ShaderGraph;
using UnityEditor.ShaderGraph.Internal;
using UnityEngine;

namespace UnityEditor.Rendering.Universal.ShaderGraph
{
    internal static class LightProbesModeSupport
    {
        private static readonly KeywordDescriptor _lightProbesAmbientBlend = new()
        {
            displayName = "_LIGHT_PROBES_AMBIENT_BLEND",
            referenceName = "_LIGHT_PROBES_AMBIENT_BLEND",
            type = KeywordType.Boolean,
            definition = KeywordDefinition.Predefined,
            scope = KeywordScope.Local,
            stages = KeywordShaderStage.Default
        };
        
        private static readonly KeywordDescriptor _lightProbesAmbientAndCustomBlend = new()
        {
            displayName = "_LIGHT_PROBES_AMBIENT_AND_CUSTOM_BLEND",
            referenceName = "_LIGHT_PROBES_AMBIENT_AND_CUSTOM_BLEND",
            type = KeywordType.Boolean,
            definition = KeywordDefinition.Predefined,
            scope = KeywordScope.Local,
            stages = KeywordShaderStage.Default
        };
        
        internal static void AddToPass(ref PassDescriptor pass, LightProbesMode lightProbesMode, bool supportRotatedReflectionProbes)
        {
            if (supportRotatedReflectionProbes)
            {
                return;
            }
            
            switch (lightProbesMode)
            {
                case LightProbesMode.Default:
                    {
                        break;
                    }
                case LightProbesMode.LightProbesAmbientBlend:
                    {
                        pass.defines.Add(_lightProbesAmbientBlend, 1);
                        break;
                    }
                case LightProbesMode.LightProbesAmbientAndCustomBlend:
                    {
                        pass.defines.Add(_lightProbesAmbientAndCustomBlend, 1);
                        break;
                    }
                default:
                    throw new ArgumentOutOfRangeException(nameof(lightProbesMode), lightProbesMode, null);
            }
        }

        internal static void CollectShaderProperties(PropertyCollector collector, GenerationMode generationMode, LightProbesMode lightProbesMode, bool supportRotatedReflectionProbes)
        {
            if (supportRotatedReflectionProbes)
            {
                return;
            }
            
            switch (lightProbesMode)
            {
                case LightProbesMode.Default:
                    {
                        break;
                    }
                case LightProbesMode.LightProbesAmbientBlend:
                    {
                        collector.AddFloatProperty("_AmbientProbeBlendFactor", 0, HLSLDeclaration.UnityPerMaterial);
                        break;
                    }
                case LightProbesMode.LightProbesAmbientAndCustomBlend:
                    {
                        collector.AddFloatProperty("_AmbientProbeBlendFactor", 0, HLSLDeclaration.UnityPerMaterial);
                        collector.AddFloat4Property("_Custom_SHAr", Vector4.zero, HLSLDeclaration.UnityPerMaterial);
                        collector.AddFloat4Property("_Custom_SHAg", Vector4.zero, HLSLDeclaration.UnityPerMaterial);
                        collector.AddFloat4Property("_Custom_SHAb", Vector4.zero, HLSLDeclaration.UnityPerMaterial);
                        collector.AddFloat4Property("_Custom_SHBr", Vector4.zero, HLSLDeclaration.UnityPerMaterial);
                        collector.AddFloat4Property("_Custom_SHBg", Vector4.zero, HLSLDeclaration.UnityPerMaterial);
                        collector.AddFloat4Property("_Custom_SHBb", Vector4.zero, HLSLDeclaration.UnityPerMaterial);
                        collector.AddFloat4Property("_Custom_SHC", Vector4.zero, HLSLDeclaration.UnityPerMaterial);
                        break;
                    }
                default:
                    throw new ArgumentOutOfRangeException(nameof(lightProbesMode), lightProbesMode, null);
            }
        }
    }
}