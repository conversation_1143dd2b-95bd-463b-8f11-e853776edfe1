using UnityEditor.ShaderGraph;
using UnityEditor.ShaderGraph.Internal;
using UnityEngine;

namespace UnityEditor.Rendering.Universal.ShaderGraph
{
    internal static class PropertyCollectorExtension
    {
        internal static void AddFloat4Property(this PropertyCollector collector, string referenceName, Vector4 defaultValue, HLSLDeclaration declarationType = HLSLDeclaration.DoNotDeclare)
        {
            collector.AddShaderProperty(new Vector4ShaderProperty()
            {
                hidden = true,
                overrideHLSLDeclaration = true,
                hlslDeclarationOverride = declarationType,
                value = defaultValue,
                displayName = referenceName,
                overrideReferenceName = referenceName,
            });
        }
    }
}