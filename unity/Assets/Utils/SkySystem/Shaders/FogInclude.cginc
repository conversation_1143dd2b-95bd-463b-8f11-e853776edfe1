#ifndef FOG_INCLUDE
#define FOG_INCLUDE

#include "SkyUtils.hlsl"
#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/EntityLighting.hlsl"
#pragma multi_compile_fragment _ FOG_SKYBOX_IS_CUBEMAP
#pragma multi_compile_fragment _ FOG_SECOND_LAYER_ENABLED
#pragma multi_compile_fragment _ FAR_FOG_ENABLED

float _FogEnabled;
float4 _FogParameters;
float4 _FogParameters2;
float4 _FogParameters3;
float4 _FogParameters4;
float4 _FogParameters5;
float4 _FogColor;
float4 _FogColorObjects;
float4 _FogColorNearSun;
float4 _FogColorNearSunObjects;
float3 _SkyboxSunDirection;

#define RayOriginTerm1 _FogParameters.x
#define HeightFalloff1 _FogParameters.y
#define Density1 _FogParameters.z
#define HeightOffset1 _FogParameters.w

#define RayOriginTerm2 _FogParameters2.x
#define HeightFalloff2 _FogParameters2.y
#define Density2 _FogParameters2.z
#define HeightOffset2 _FogParameters2.w

#define MaxOpacity1 _FogParameters3.x
#define StartDistance1 _FogParameters3.y
#define MaxOpacity2 _FogParameters3.z
#define StartDistance2 _FogParameters3.w

#define FogSkyBlend _FogParameters4.x
#define FogNearSunFactor _FogParameters4.y

#define FarFogDistanceStart _FogParameters5.x
#define FarFogDistanceEnd _FogParameters5.y
#define FarFogVerticalStart _FogParameters5.z
#define FarFogVerticalEnd _FogParameters5.w

#if FOG_SKYBOX_IS_CUBEMAP
TEXTURECUBE(_FogSkyboxTexture);
#else
TEXTURE2D(_FogSkyboxTexture);
#endif

SamplerState sampler_FogSkyboxTexture;
float4 _FogSkyboxTexture_HDR;
float _FogSkyboxTextureMinMip;
float _FogSkyboxTextureMaxMip;
float _FogSkyboxTextureMipMapSelectionBias;
float _FogSkyboxRotation;

float3 GetSkyColor(float3 viewDirection, float fogFactor)
{
    // https://advances.realtimerendering.com/other/2016/naughty_dog/NaughtyDog_TechArt_Final.pdf Mip Fog.
    half mipMap = lerp(_FogSkyboxTextureMinMip, _FogSkyboxTextureMaxMip, pow(1 - fogFactor, _FogSkyboxTextureMipMapSelectionBias));

    #if FOG_SKYBOX_IS_CUBEMAP
        viewDirection = RotateAroundYInDegrees(viewDirection, _FogSkyboxRotation);
        float4 environment = SAMPLE_TEXTURECUBE_LOD(_FogSkyboxTexture, sampler_FogSkyboxTexture, viewDirection, mipMap);
        return DecodeHDREnvironment(environment, _FogSkyboxTexture_HDR);
    #else
        float2 coords = GetLatLongCoords(viewDirection, 0);
        float4 environment =  SAMPLE_TEXTURE2D_LOD(_FogSkyboxTexture, sampler_FogSkyboxTexture, coords + half2(-_FogSkyboxRotation / 360, 0), mipMap);
        return DecodeHDREnvironment(environment, _FogSkyboxTexture_HDR);
    #endif
}

float InverseLerpFloat(float a, float b, float t)
{
    return saturate((t - a)/(b - a));
}

float Pow2(float x) 
{ 
    return x * x;
}

// Calculate the line integral of the ray from the camera to the receiver position through the fog density function
// The exponential fog density function is d = GlobalDensity * exp(-HeightFalloff * y)
float CalculateLineIntegralShared(float FogHeightFalloff, float RayDirectionY, float RayOriginTerms)
{
    float Falloff = max(-127.0f, FogHeightFalloff * RayDirectionY);    // if it's lower than -127.0, then exp2() goes crazy in OpenGL's GLSL.
    float LineIntegral = (1.0f - exp2(-Falloff)) / Falloff;
    float LineIntegralTaylor = log(2.0) - (0.5 * Pow2(log(2.0))) * Falloff;		// Taylor expansion around 0

    return RayOriginTerms * (abs(Falloff) > 0.01f ? LineIntegral : LineIntegralTaylor);
}

float CalculateFogAmountForLayer(float rayOriginTerm, float3 rayDirection, float rayLengthInv, float rayLength, float startDistance, float heightOffset, float heightFalloff, float density)
{
    // Create "local" variables otherwise if we modify them in the first fog layer the second will have them changed.
    float rayDirectionY = rayDirection.y;
    float rayLengthWithPossibleModifications = rayLength;
    float rayOriginTermWithPossibleModifications = rayOriginTerm;
    if (startDistance > 0)
    {
        float excludeIntersectionTime = startDistance * rayLengthInv;
        float cameraToExclusionIntersectionY = excludeIntersectionTime * rayDirectionY;
        float exclusionIntersectionY = _WorldSpaceCameraPos.y + cameraToExclusionIntersectionY;
        float exclusionIntersectionToReceiverY = rayDirectionY - cameraToExclusionIntersectionY;

        rayLengthWithPossibleModifications = (1.0f - excludeIntersectionTime) * rayLengthWithPossibleModifications;
        rayDirectionY = exclusionIntersectionToReceiverY;
        
        float exponent = max(-127.0f, heightFalloff * (exclusionIntersectionY - heightOffset));
        rayOriginTermWithPossibleModifications = density * exp2(-exponent);
    }

    return  CalculateLineIntegralShared(heightFalloff, rayDirectionY, rayOriginTermWithPossibleModifications) * rayLengthWithPossibleModifications;
}

// Calculate the amount of light that made it through the fog using the transmission equation.
float CalculateFogFactor(float fogAmount, float maxOpacity)
{
    return max(saturate(exp2(-fogAmount)), maxOpacity);
}

// Two layer height fog, based on Enviro 3.
half4 GetExponentialHeightFog(float3 positionWS, float rawDepth) 
{
    float3 cameraToReceiver = positionWS - _WorldSpaceCameraPos.xyz;
    float cameraToReceiverLengthSqr = dot(cameraToReceiver, cameraToReceiver);
    float cameraToReceiverLengthInv = rsqrt(cameraToReceiverLengthSqr);
    float cameraToReceiverLength = cameraToReceiverLengthSqr * cameraToReceiverLengthInv;
    
    float fogAmount = CalculateFogAmountForLayer(RayOriginTerm1, cameraToReceiver, cameraToReceiverLengthInv, cameraToReceiverLength, StartDistance1, HeightOffset1, HeightFalloff1, Density1);
    float fogFactor = CalculateFogFactor(fogAmount, MaxOpacity1);
    
    #if FOG_SECOND_LAYER_ENABLED
      float fogAmount2 = CalculateFogAmountForLayer(RayOriginTerm2, cameraToReceiver, cameraToReceiverLengthInv, cameraToReceiverLength, StartDistance2, HeightOffset2, HeightFalloff2, Density2);
      float fogFactor2 = CalculateFogFactor(fogAmount2, MaxOpacity2);

      // Selecting smallest fog factor as the most powerful according to Blend operation.
      fogFactor = min(fogFactor, fogFactor2);
    #endif
    
    #if FAR_FOG_ENABLED
        float heightFactor = 1 - InverseLerpFloat(FarFogVerticalStart, FarFogVerticalEnd, positionWS.y);
        float fogDistanceFactor = 1 - InverseLerpFloat(FarFogDistanceStart, FarFogDistanceEnd, cameraToReceiverLength) * heightFactor;
        fogFactor = min(fogFactor, fogDistanceFactor);
    #endif

    fogFactor = saturate(fogFactor);
    
    // Color
    half3 viewDirectionNormalized = cameraToReceiver * cameraToReceiverLengthInv;
    half3 sky = GetSkyColor(viewDirectionNormalized, fogFactor);

    float sunFactor = (dot(_SkyboxSunDirection, viewDirectionNormalized) + 1) / 2;
    sunFactor = 1 - saturate((1 - sunFactor) / FogNearSunFactor);

    #if UNITY_REVERSED_Z
        rawDepth = 1 - rawDepth;
    #endif
    
    half4 fogColor = (rawDepth == 1 || positionWS.y <= 0.01) ? lerp(_FogColor, _FogColorNearSun, sunFactor) : lerp(_FogColorObjects, _FogColorNearSunObjects, sunFactor);
    half3 inscatterColor = lerp(fogColor.xyz, sky, FogSkyBlend);
    
    // The smaller fog factor - the less visible scene, because of shader's Blend sceneColor.rgb * fogColor.a 
    return half4(inscatterColor * (1 - fogFactor), fogFactor);
}

half4 GetExponentialHeightFogForObjects(float3 positionWS)
{
    // Use fake rawDepth, that always result in 0. We can do it, because rawDepth only used for checking if it's skybox pixels.
    float rawDepth = 0;
    #if UNITY_REVERSED_Z
        rawDepth = 1 - rawDepth;
    #endif
    
    return GetExponentialHeightFog(positionWS, rawDepth);
}

half4 MixFogForObjects(float3 positionWS, half4 color)
{
    if(!_FogEnabled)
    {
        return color;
    }
    
    half4 fog = GetExponentialHeightFogForObjects(positionWS);
    // Alpha blend equal to KefirHeightFog shader blend.
    return half4(color.rgb * fog.a + fog.rgb, color.a);
}
#endif
