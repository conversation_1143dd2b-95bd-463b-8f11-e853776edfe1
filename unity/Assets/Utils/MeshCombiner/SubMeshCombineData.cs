using UnityEngine;

namespace Combiner
{
    public readonly struct SubMeshCombineData
    {
        public readonly GameObject GameObject;
        public readonly Mesh SharedMesh;
        public readonly Bounds Bounds;
        public readonly int SubMeshIndex;
        public readonly Matrix4x4 LocalToWorldMatrix;

        public SubMeshCombineData(GameObject gameObject, Mesh sharedMesh, int subMeshIndex, Matrix4x4 localToWorldMatrix, Bounds bounds)
        {
            GameObject = gameObject;
            SharedMesh = sharedMesh;
            SubMeshIndex = subMeshIndex;
            LocalToWorldMatrix = localToWorldMatrix;
            Bounds = bounds;
        }
    }
}