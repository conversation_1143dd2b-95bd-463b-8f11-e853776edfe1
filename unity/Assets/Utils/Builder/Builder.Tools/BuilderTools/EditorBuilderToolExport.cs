using Builder.Editor.Helper;
using UnityEditor;
using UnityEngine;

namespace Builder.Editor
{
    public class EditorBuilderToolExport : EditorBuilderTool
    {
        private bool _showSettingsTerrain;
        private bool _showSettingsRoad;


        public override int SceneInspectorGUIWidth => 0;
        public override int SceneInspectorGUIHeight => 0;
        
        public EditorBuilderToolExport(ControllerBuilder builder) : base(builder)
        {
        }
        

        public override void OnInspector(SerializedObject serializedObject)
        {
            if (Builder.Terrain == null)
            {
                EditorGUILayout.HelpBox("There is no terrain for export.", MessageType.Warning);
            }
            else
            {
                DrawExportSettingsTerrain(serializedObject);
                DrawExportSettingsRoad(serializedObject);
            }

            DrawExport(serializedObject);
        }

        public override void OnToolWindowInspector()
        {
        }

        public override void OnSceneInspector()
        {
        }

        public override void OnSceneButtonsAndTools()
        {
        }

        public override void OnSceneCreationMode()
        {
        }

        public override void OnDelete()
        {
        }


        private void DrawExportSettingsTerrain(SerializedObject serializedObject)
        {
            EditorGUILayout.BeginVertical(BuilderEditorGUILayout.StyleInfo);

            _showSettingsTerrain = BuilderEditorGUILayout.DrawFoldout(_showSettingsTerrain, new GUIContent("Terrain Export Settings", "Настройки экспорта террейна"));

            if (_showSettingsTerrain)
            {
                EditorGUILayout.Space();
                
                EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(Builder.ExportTerrainLodMapsSize)), new GUIContent("Lod Maps Size"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(Builder.ExportTerrainMeshCompression)), new GUIContent("Mesh Compression"));
                
                EditorGUILayout.Space();
                
                EditorGUILayout.BeginHorizontal();
                GUILayout.Space(14);
                EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(Builder.ExportTerrainLodLevels)), new GUIContent("Lod Levels"));
                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.EndVertical();
        }
        
        private void DrawExportSettingsRoad(SerializedObject serializedObject)
        {
            EditorGUILayout.BeginVertical(BuilderEditorGUILayout.StyleInfo);

            _showSettingsRoad = BuilderEditorGUILayout.DrawFoldout(_showSettingsRoad, new GUIContent("Road Export Settings", "Настройки экспорта дорог и перекрестков"));

            if (_showSettingsRoad)
            {
                EditorGUILayout.Space();
                
                EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(Builder.RoadsUVUnvrapIndex)));
                EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(Builder.RoadsUVUnwrapPadding)));
            }

            EditorGUILayout.EndVertical();
        }

        
        private void DrawExport(SerializedObject serializedObject)
        {
            EditorGUILayout.BeginVertical(BuilderEditorGUILayout.StyleInfo);
            EditorGUILayout.PropertyField(serializedObject.FindProperty(nameof(Builder.NeedCreateExportedScript)), new GUIContent("Need Create Exported Script"));
            EditorGUILayout.BeginHorizontal();
            
            if (GUILayout.Button(new GUIContent("Export", "Экспорт всех данных в указанныю папку")))
            {
                HelperBuilderExport.Export(Builder);
            }

            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();
        }
    }
}