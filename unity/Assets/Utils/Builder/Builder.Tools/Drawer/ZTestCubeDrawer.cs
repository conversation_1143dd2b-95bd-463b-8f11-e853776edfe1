using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

namespace Builder.Editor.Drawer
{
    public class ZTestCubeDrawer
    {
        public static void DrawCube(Vector3 center, Vector3 size)
        {
            Handles.color = new Color(0, 0, 0, 0.5f);
            Handles.matrix = Matrix4x4.TRS(center, Quaternion.identity, size);
            var zTest = Handles.zTest;
            Handles.zTest = CompareFunction.Greater;
            Handles.CubeHandleCap(0, Vector3.zero, Quaternion.identity, 1, EventType.Repaint);
            Graphics.DrawMeshNow(GetCubeMesh(), Handles.matrix);
            Handles.matrix = Matrix4x4.identity;
            Handles.zTest = zTest;
        }

        private static Mesh _cubeMesh;
        
        private static Mesh GetCubeMesh()
        {
            if (!_cubeMesh)
            {
                var sharedMesh = Resources.GetBuiltinResource<Mesh>("Cube.fbx");

                var triangles = sharedMesh.triangles;
                for (var i = 0; i < triangles.Length; i += 3)
                {
                    var a = triangles[i];
                    var b = triangles[i + 1];
                    var c = triangles[i + 2];

                    triangles[i] = c;
                    triangles[i + 1] = b;
                    triangles[i + 2] = a;
                }
              
                _cubeMesh = new Mesh();
                _cubeMesh.SetVertices(sharedMesh.vertices);
                _cubeMesh.SetTriangles(triangles, 0);
            }

            return _cubeMesh;
        }
    }
}