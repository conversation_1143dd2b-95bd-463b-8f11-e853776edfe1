#ifndef WATER_CAUSTICS_INCLUDED
#define WATER_CAUSTICS_INCLUDED
#include "Assets/Utils/Builder/Content/Water/Shaders/Reused/WaterReusedCaustics.hlsl"

half3 CalculateCaustics(float3 positionWS, float3 reconstructedWorldPositionAfterDistortion, float2 distortionUV, Light mainLight, float cameraBasedDepthAfterDistortion)
{
    #if defined(_WATER_CAUSTICS_LOW_QUALITY) || defined(_WATER_CAUSTICS_HIGH_QUALITY)
    half3 caustics = float3(0, 0, 0);
    CalculateCaustics(_CausticsSize, _CausticsBlendDistanceY, positionWS.y, distortionUV, reconstructedWorldPositionAfterDistortion, _CausticsTexture, sampler_CausticsTexture, mainLight.color, caustics);
    half causticsVisibilityFactor = smoothstep(_CausticsVisibilityDepth, _CausticsVisibilityDepth + _CausticsVisibilityDepthBlend, cameraBasedDepthAfterDistortion);
    caustics = caustics * (1 - causticsVisibilityFactor);

    // Simulate as if shadow goes under water to hide deep caustics.
    half shadowForCaustics = min(GetMainLight(TransformWorldToShadowCoord(reconstructedWorldPositionAfterDistortion), reconstructedWorldPositionAfterDistortion, 0).shadowAttenuation, mainLight.shadowAttenuation);
    return caustics * _CausticsPower * shadowForCaustics;
    #else
    return half3(0, 0, 0);
    #endif
}
#endif