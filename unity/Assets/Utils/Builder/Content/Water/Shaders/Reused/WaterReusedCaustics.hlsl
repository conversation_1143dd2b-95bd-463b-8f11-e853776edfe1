#ifndef WATER_REUSED_CAUSTICS_INCLUDED
#define WATER_REUSED_CAUSTICS_INCLUDED

#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
#if _DEPTH_NORMAL_TEXTURE_AVAILABLE
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareNormalsTexture.hlsl"
#else
    #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/DeclareDepthTexture.hlsl"
#endif

half4x4 _WaterCausticsMainLightDir;

float2 CausticUVs(float causticsSize, float2 rawUV, float2 offset)
{
    float2 uv = rawUV * causticsSize;
    return uv + offset * 0.1;
}

// Naive normal reconstruction using depth based on https://gist.github.com/bgolus/a07ed65602c009d5e2f753826e8078a0
float3 ViewSpacePosAtScreenUV(float2 uv)
{
    float3 viewSpaceRay = mul(unity_CameraInvProjection, float4(uv * 2.0 - 1.0, 1.0, 1.0) * _ProjectionParams.z).xyz;
    float rawDepth = SampleSceneDepth(uv);
    return viewSpaceRay * Linear01Depth(rawDepth, _ZBufferParams);
}

half3 ViewNormalAtPixelPosition(float2 uv)
{
    // Get current pixel's view space position.
    half3 viewSpacePos_c = ViewSpacePosAtScreenUV(uv);
    
    // For some bizarre reason _CameraDepthTexture_TexelSize.xy calculates as if _CameraDepthTexture 64x64.
    float2 texelSize = 1 / _ScreenParams.xy;
    
    // Get view space position at 1 pixel offsets in each major direction.
    half3 viewSpacePos_r = ViewSpacePosAtScreenUV(uv + float2( texelSize.x, 0.0));
    half3 viewSpacePos_u = ViewSpacePosAtScreenUV(uv + float2( 0.0, texelSize.y));

    // Get the difference between the current and each offset position.
    half3 hDeriv = viewSpacePos_r - viewSpacePos_c;
    half3 vDeriv = viewSpacePos_u - viewSpacePos_c;

    // Get view space normal from the cross product of the diffs.
    half3 viewNormal = normalize(cross(hDeriv, vDeriv));

    return viewNormal;
}

void CalculateCaustics(float causticsSize, float blendDistanceY, float waterLevelY, float2 sceneUV, float3 reconstructedPositionWS, Texture2D causticsTexture, SamplerState causticsTextureSampler, float3 mainLightColor, out half3 caustics)
{
    #if _WATER_CAUSTICS_HIGH_QUALITY
        // Depth normal texture available only if HBAO or some other render feature requires it, if it's not available we
        // need to reconstruct normal from depth texture.
        #if _DEPTH_NORMAL_TEXTURE_AVAILABLE
            float3 sceneNormal = SampleSceneNormals(sceneUV);
        #else
            float3 sceneNormal = mul((float3x3)unity_MatrixInvV, ViewNormalAtPixelPosition(sceneUV));
        #endif
    
        sceneNormal = pow(abs(sceneNormal), 64);
        sceneNormal = round(sceneNormal / (dot(sceneNormal, float3(1, 1, 1))));
        
        // Single tap triplanar projection.
        float2 rawUV = lerp(reconstructedPositionWS.xz, reconstructedPositionWS.yz, sceneNormal.x);
        rawUV = lerp(rawUV, reconstructedPositionWS.xy, sceneNormal.z);
        float2 waveOffsetUV = rawUV;
    #else
        // Get light direction and use it to rotate the world position.
        float3 lightUVs = mul(float4(reconstructedPositionWS, 1), _WaterCausticsMainLightDir).xyz;
        float2 rawUV = lightUVs.xy;
        float2 waveOffsetUV = lightUVs.xz;
    #endif
    
    float time = _Time.x;
    // Read wave texture for noise to offset cautics UVs.
    waveOffsetUV = waveOffsetUV * 0.025 + time * 0.25;
    float waveOffset = SAMPLE_TEXTURE2D(causticsTexture, causticsTextureSampler, waveOffsetUV).w - 0.5;
    
    float2 causticUV = CausticUVs(causticsSize, rawUV, waveOffset);

    float distanceFactor = smoothstep(25, 100, distance(_WorldSpaceCameraPos, reconstructedPositionWS));
    float lodLevel = abs(reconstructedPositionWS.y - waterLevelY) * 4 / blendDistanceY;
    // Hide caustics if we far from surface with caustics.
    lodLevel = lerp(lodLevel, 5, distanceFactor);
    half4 causticsA = SAMPLE_TEXTURE2D_LOD(causticsTexture, causticsTextureSampler, causticUV + time, lodLevel);
    half4 causticsB = SAMPLE_TEXTURE2D_LOD(causticsTexture, causticsTextureSampler, causticUV * 2.0, lodLevel);
    
    half causticsDriver = (causticsA.z * causticsB.z) * 10 + causticsA.z + causticsB.z;
    
    // Mask caustics from above water and fade below.
    half upperMask = saturate(-reconstructedPositionWS.y + waterLevelY);
    half lowerMask = saturate((reconstructedPositionWS.y - waterLevelY)/ blendDistanceY + blendDistanceY);
    causticsDriver *= min(upperMask, lowerMask);
    
    // Fake light dispersion.
    caustics = causticsDriver * half3(causticsA.w * 0.5, causticsB.w * 0.75, causticsB.x) * mainLightColor;
}
#endif