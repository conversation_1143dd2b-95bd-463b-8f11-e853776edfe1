#ifndef WATER_FOAM_INCLUDED
#define WATER_FOAM_INCLUDED

half FakeFoamWaves(float2 topDownUV, float noise, float3 positionWS, float waterDeepness)
{
    float4 voronoiNoises = GetVoronoiNoises(topDownUV);
    half waterDistanceToShore = GetDistanceToShore(topDownUV) + noise;
    float noiseBigScale = GeneratePositionBasedNoise(positionWS.xz * 0.1);
    float limitMaxDeepness = smoothstep(_FakeWavesDeepnessFadeStartEnd.y, _FakeWavesDeepnessFadeStartEnd.x, waterDeepness);

    float distanceToShore = waterDistanceToShore / 10 + (_Time.y + voronoiNoises.x * _FakeWavesFoamTimeOffsetNoiseMultiplier) * _FoamWavesSpeed;
    float fracDistanceToShore = frac(distanceToShore) * ((uint)(distanceToShore) % 2 == 1 ? 1 : 0);
    float limitMaxDistance = smoothstep(20 + voronoiNoises.x * _FakeWavesFoamStartDistanceFromShoreNoiseMultiplier, 15, waterDistanceToShore);
    float createGapsInWaves = smoothstep(_FoamWavesGapsInWaves.x, _FoamWavesGapsInWaves.y, voronoiNoises.y * noise);
    half distanceFromShoreFoam = smoothstep(_FoamWavesFirstWavesSize.x, _FoamWavesFirstWavesSize.y, fracDistanceToShore) * smoothstep(
        _FoamWavesFirstWavesSize.z, _FoamWavesFirstWavesSize.w, fracDistanceToShore) * limitMaxDistance * createGapsInWaves;

    // Waves based on second noise to fill gaps in first waves and make all waves less repetitive.
    float distanceToShore2 = waterDistanceToShore / 10 + (_Time.y + voronoiNoises.z * _FakeWavesFoamTimeOffsetNoiseMultiplier) * _FoamWavesSpeed + _FoamWavesSecondWavesOffset;
    float fracDistanceToShore2 = frac(distanceToShore2) * ((uint)(distanceToShore2) % 2 == 0 ? 1 : 0);
    float limitMaxDistance2 = smoothstep(20 + voronoiNoises.z * _FakeWavesFoamStartDistanceFromShoreNoiseMultiplier, 15, waterDistanceToShore);
    float createGapsInWaves2 = smoothstep(_FoamWavesGapsInWaves.z, _FoamWavesGapsInWaves.w, voronoiNoises.w * noise);
    half distanceFromShoreFoam2 = smoothstep(_FoamWavesSecondWavesSize.x, _FoamWavesSecondWavesSize.y, fracDistanceToShore2) * smoothstep(
        _FoamWavesSecondWavesSize.z, _FoamWavesSecondWavesSize.w, fracDistanceToShore2) * limitMaxDistance2 * createGapsInWaves2;

    return max(distanceFromShoreFoam, distanceFromShoreFoam2) * noiseBigScale * 1.5 * limitMaxDeepness;
}

void CalculateFoam(float3 positionWS, float cameraBasedDepth, float noise, float normalizedWaveHeight, Light mainLight, float3 GI,
    out half3 foam, out half3 foamWaves, out half foamMask, out half foamWavesMask)
{
    half2 topDownUV = GetTopDownTextureUV(positionWS);
    float terrainHeight = GetTerrainHeight(topDownUV);

    // Texture based deepness that works in a places where we don't have terrain mesh.
    float waterDeepness = TerrainHeightToWaterDeepness(terrainHeight, positionWS.y);

    // Foam.
    half3 foamMap = SAMPLE_TEXTURE2D(_FoamMap, sampler_FoamMap, positionWS.xz * _FoamUVScale + noise * 0.1).rgb;
    half deepnessBasedFoam = saturate(1 - waterDeepness / _FoamDeepness);
    half depthIntersectionFoam = saturate(1 - cameraBasedDepth / _FoamDepthIntersection);

    // Prevent depth intersection foam when camera is high, so there are no MSAA artifacts on trees leafs.
    depthIntersectionFoam *= 1 - smoothstep(_FoamHidingCameraHeightStartEnd.x, _FoamHidingCameraHeightStartEnd.y, _WorldSpaceCameraPos.y);
    
    // Foam that moves to shore(like fake waves).
    #if defined(_WATER_FAKE_WAVES)
        float combinedFoamWaves = FakeFoamWaves(topDownUV, noise, positionWS, waterDeepness);
        half foamBlendMask =  saturate(max(max(deepnessBasedFoam, combinedFoamWaves), depthIntersectionFoam));
    #else
        half foamBlendMask = saturate(max(deepnessBasedFoam, depthIntersectionFoam));
    #endif

    half3 foamBlend = SampleFoamRamp(foamBlendMask);
    foamMask = saturate(length(foamMap * foamBlend) * 1.5);
    foam = foamMask.xxx * (mainLight.shadowAttenuation * mainLight.color + GI);

    // Waves foam.
    half3 foamWavesMap = SAMPLE_TEXTURE2D(_FoamMap, sampler_FoamMap, positionWS.xz * _FoamWavesUVScale + noise * 0.1).rgb;
    half foamWavesFactor = normalizedWaveHeight * _FoamWavesPower * noise;
    half3 foamWavesBlend = SampleFoamRamp(foamWavesFactor);
    foamWavesMask = saturate(length(foamWavesMap * foamWavesBlend) * 1.5);
    foamWaves = foamWavesMask.xxx * (mainLight.shadowAttenuation * mainLight.color + GI);
}
#endif