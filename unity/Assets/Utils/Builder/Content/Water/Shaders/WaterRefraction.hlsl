#ifndef WATER_REFRACTION_INCLUDED
#define WATER_REFRACTION_INCLUDED

void CalculateRefractionUV(float4 positionSS, float2 screenUV, float3 normalWS, float cameraBasedDepth, float3 reconstructedWorldPosition, out half2 distortionUV,
                           out float cameraBasedDepthAfterDistortion, out float3 reconstructedWorldPositionAfterDistortion)
{
    #if defined(_WATER_REFRACTION)
        distortionUV = (positionSS.xy + DistortionUVs(normalWS)) / positionSS.w;
        float rawDepthAfterDistortion = GetRawDepthMin(distortionUV);
        cameraBasedDepthAfterDistortion = DepthDistanceFromFragmentToScene(rawDepthAfterDistortion, positionSS.w);
        reconstructedWorldPositionAfterDistortion = ComputeWorldSpacePosition(distortionUV, rawDepthAfterDistortion, UNITY_MATRIX_I_VP);
        // Prevent distortion of objects above water.
        if (cameraBasedDepthAfterDistortion < 0)
        {
            distortionUV = screenUV;
            cameraBasedDepthAfterDistortion = cameraBasedDepth;
            reconstructedWorldPositionAfterDistortion = reconstructedWorldPosition;
        }
    #else
        distortionUV = screenUV;
        cameraBasedDepthAfterDistortion = cameraBasedDepth;
        reconstructedWorldPositionAfterDistortion = reconstructedWorldPosition;
    #endif
}

// Under water noise to simulate seaweed, debris, etc.
void CalculateUnderwaterNoise(float3 reconstructedWorldPositionAfterDistortion, float absorptionFactor, out float4 underWaterNoiseColor, out float underWaterNoiseFactor)
{
    float2 underWaterUVPosition = reconstructedWorldPositionAfterDistortion.xz;
    underWaterNoiseFactor = SAMPLE_TEXTURE2D(_UnderWaterNoise, sampler_UnderWaterNoise, underWaterUVPosition / _UnderWaterNoiseScale0).r;
    underWaterNoiseFactor *= SAMPLE_TEXTURE2D(_UnderWaterNoise, sampler_UnderWaterNoise, (underWaterUVPosition + _UnderWaterNoiseMoveSpeed0 * _Time.y) / _UnderWaterNoiseScale1).r;
    underWaterNoiseFactor = PositivePow(underWaterNoiseFactor, _UnderWaterNoisePower);
    underWaterNoiseFactor *= smoothstep(_UnderWaterNoiseVisibleDeepnessStart, _UnderWaterNoiseVisibleDeepnessStart + _UnderWaterNoiseVisibleDeepnessBlend,
                                        -reconstructedWorldPositionAfterDistortion.y) * (1 - absorptionFactor);

    float colorBlendFactor = smoothstep(_UnderWaterNoiseColorBlendDeepnessStart, _UnderWaterNoiseColorBlendDeepnessStart + _UnderWaterNoiseColorBlendDeepnessBlend,
                                        -reconstructedWorldPositionAfterDistortion.y);
    underWaterNoiseColor = lerp(_UnderWaterNoiseColorShallow, _UnderWaterNoiseColorDeep, colorBlendFactor);
}

half3 CalculateRefraction(float3 reconstructedWorldPosition, float3 reconstructedWorldPositionAfterDistortion, float3 positionWS, half3 opaqueTexture, float cameraBasedDepthAfterDistortion, float noise)
{
    // Use noise to prevent visible gradient being the same over underwater terrain with same height.
    float cameraBasedDepthAfterDistortionForAbsorption = cameraBasedDepthAfterDistortion - noise;
    float absorptionFactor = saturate(cameraBasedDepthAfterDistortionForAbsorption / _ShallowWaterDistance);
    half3 absorptionTerrain = SampleAbsorptionTerrainRamp(absorptionFactor);
    half3 absorptionObjects = SampleAbsorptionObjectsRamp(absorptionFactor);
    half3 absorptionWhereGeometryNotVisible = SampleAbsorptionWhereGeometryNotVisible(absorptionFactor);

    // Texture based deepness for reconstructed position, this texture contains only terrain, and we can use it to distinguish objects and terrain for different absorption.
    half2 topDownUVForReconstructedPosition = GetTopDownTextureUV(reconstructedWorldPosition);
    float terrainHeightForReconstructedPosition = GetTerrainHeight(topDownUVForReconstructedPosition);
    float waterDeepnessForReconstructedPosition = TerrainHeightToWaterDeepness(terrainHeightForReconstructedPosition, positionWS.y);
    
    half4 underWaterNoiseColor;
    float underWaterNoiseFactor;
    CalculateUnderwaterNoise(reconstructedWorldPositionAfterDistortion, absorptionFactor, underWaterNoiseColor, underWaterNoiseFactor);
    absorptionTerrain = lerp(absorptionTerrain, underWaterNoiseColor.rgb, underWaterNoiseFactor * underWaterNoiseColor.a);
    
    float absorptionForObjectsFactor = saturate((waterDeepnessForReconstructedPosition - cameraBasedDepthAfterDistortion) / _AbsorbationTerranAndActualWaterDeepnessDifference);
    half3 absorption = lerp(absorptionTerrain, absorptionObjects, absorptionForObjectsFactor);

    float opaqueTextureVisibilityFactor = smoothstep(_RefractionVisibilityDepth, _RefractionVisibilityDepth + _RefractionVisibilityDepthBlend, cameraBasedDepthAfterDistortionForAbsorption);
    half3 refraction = lerp(opaqueTexture * absorption, absorptionWhereGeometryNotVisible, opaqueTextureVisibilityFactor);

    // Deep water.
    half waterDeepnessFactor = SAMPLE_TEXTURE2D(_DeepWaterMap, sampler_DeepWaterMap, (positionWS.xz - _DeepWaterOffset) / _DeepWaterSize).a;
    refraction = lerp(refraction, SampleDeepness(waterDeepnessFactor), waterDeepnessFactor);
    return refraction;
}
#endif