#ifndef WATER_COMMON_INCLUDED
#define WATER_COMMON_INCLUDED

#define SHADOWS_SCREEN 0

#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"
#include "WaterInput.hlsl"
#include "WaterCommonUtilities.hlsl"
#include "WaterGerstnerWaves.hlsl"
#include_with_pragmas "WaterReflections.hlsl"
#include "WaterLighting.hlsl"
#include "WaterTopDownTextures.hlsl"
#include "WaterRefraction.hlsl"
#include "WaterFoam.hlsl"
#include "WaterCaustics.hlsl"
#include "Assets/Utils/Builder/Content/Water/Shaders/FFT/WaterFFTModule.hlsl"

WaterVertexOutput WaterVertex(WaterVertexInput i)
{
    WaterVertexOutput o;
    UNITY_SETUP_INSTANCE_ID(i);
    UNITY_TRANSFER_INSTANCE_ID(i, o);
    UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

    o.positionWS = TransformObjectToWorld(float3(i.vertex.x, 0, i.vertex.z));

    #if !defined(_FFT_NORMALS)
        // Detail waves UVs.
        float time = _Time.y * _DetailTimeMultiplier;
        float noise = ((GenerateNoise((o.positionWS.xz * 0.5) + time) + GenerateNoise((o.positionWS.xz * 1) + time)) * 0.25 - 0.5) + 1;
        o.detailUVs.zw = o.positionWS.xz * _DetailTextureTiling0 + time * _DetailTextureSpeed0 * _DetailTextureDirection0.xy + noise * 0.05;
        o.detailUVs.xy = o.positionWS.xz * _DetailTextureTiling1 + time * _DetailTextureSpeed1 * _DetailTextureDirection1.xy + noise * 0.2;

        // Far normals.
        o.farNormalsUVs = DualAnimatedUV(o.positionWS.xz * 0.1, _NormalMapFarTilings, _NormalMapFarSpeeds);
    #endif

    // Waves.
    #if defined(_FFT_WAVES) || defined(_WATER_GERSTNER_WAVES)
        float smoothlyStopWavesByDistanceFactor = smoothstep(_WaterMaxGerstnerWavesDistanceSqrAndBlend.y, _WaterMaxGerstnerWavesDistanceSqrAndBlend.x, DistanceSqr(o.positionWS.xz, _WorldSpaceCameraPos.xz));
        half2 topDownUV = GetTopDownTextureUV(o.positionWS);
        float waterDeepness = TerrainHeightToWaterDeepness(GetTerrainHeight(topDownUV), o.positionWS.y);
    #endif
    
    #if defined(_FFT_WAVES)
        float3 displacement = SampleFFTWaves(o.positionWS, topDownUV, waterDeepness);
        o.positionWS += displacement;
        o.packedData.y = CalculateNormalizedWaveHeight(displacement.y, o.positionWS, smoothlyStopWavesByDistanceFactor);
        // Not used in FFT Waves.
        o.packedData.w = 0; 
    #elif defined(_WATER_GERSTNER_WAVES)
        float3 wavePosition;
        float3 waveNormal;
        float waveHeight;
        SampleWaves(GetDistanceToShore(topDownUV), waterDeepness, o.positionWS.xz, wavePosition, waveNormal, waveHeight);
        o.positionWS += wavePosition * smoothlyStopWavesByDistanceFactor;
        o.normalWS = waveNormal;
        o.packedData.y = CalculateNormalizedWaveHeight(waveHeight, o.positionWS, smoothlyStopWavesByDistanceFactor);
        o.packedData.w = waterDeepness;
    #else
        o.normalWS = float3(0, 1, 0);
        o.packedData.yw = 0;
    #endif

    o.positionCS = TransformWorldToHClip(o.positionWS);
    o.positionSS = ComputeScreenPos(o.positionCS);

    o.packedData.z = saturate(o.positionSS.w / _NormalFarDistance);
    o.packedData.x = 0; // Currently empty.
    return o;
}

half4 WaterFragment(WaterVertexOutput i) : SV_Target
{
    UNITY_SETUP_INSTANCE_ID(i);

    float3 positionWS = i.positionWS;
    float3 viewDirectionWS = SafeNormalize(_WorldSpaceCameraPos - positionWS);
    
    #if defined(_FFT_NORMALS)
        float4x4 derivatives = SampleDerivatives(i.positionWS.xz);
        float3 normalWS = NormalFromDerivatives(derivatives);
    #else
        float3 normalWS = SimpleScrollingTexturesNormal(i.packedData.w, i.packedData.z, i.normalWS, i.detailUVs, i.farNormalsUVs, viewDirectionWS);
    #endif

    half normalsSmoothingByDistanceFactor = smoothstep(_NormalsSmoothingByDistanceStart, _NormalsSmoothingByDistanceEnd, distance(_WorldSpaceCameraPos.xz, positionWS.xz));
    normalWS = lerp(normalWS, half3(0, 1, 0), min(normalsSmoothingByDistanceFactor, _NormalsSmoothingByDistanceMaxSmoothness));
    
    half2 screenUV = i.positionSS.xy / i.positionSS.w;
    float rawDepth = GetRawDepthMin(screenUV);
    float cameraBasedDepth = DepthDistanceFromFragmentToScene(rawDepth, i.positionSS.w);
    float3 reconstructedWorldPosition = ComputeWorldSpacePosition(screenUV, rawDepth, UNITY_MATRIX_I_VP);

    // Refraction UV.
    half2 distortionUV;
    float cameraBasedDepthAfterDistortion;
    float3 reconstructedWorldPositionAfterDistortion;
    CalculateRefractionUV(i.positionSS, screenUV, normalWS, cameraBasedDepth, reconstructedWorldPosition, distortionUV, cameraBasedDepthAfterDistortion, reconstructedWorldPositionAfterDistortion);

    // Specular and SSS.
    Light mainLight = GetMainLight(TransformWorldToShadowCoord(positionWS), positionWS, 0);
    half3 GI = SampleSH(normalWS);

    float normalizedWaveHeight = i.packedData.y;
    half3 spec;
    half3 sss;
    CalculateSpecularAndSSS(mainLight, viewDirectionWS, positionWS, normalWS, normalizedWaveHeight, GI, spec, sss);

    // Foam.
    float noise = GeneratePositionBasedNoise(positionWS.xz);
    half3 foam;
    half3 foamWaves;
    float foamMask;
    float foamWavesMask;
    CalculateFoam(positionWS, cameraBasedDepth, noise, normalizedWaveHeight, mainLight, GI, foam, foamWaves, foamMask, foamWavesMask);

    // Refraction.
    half3 opaqueTexture = SampleOpaqueTexture(distortionUV, cameraBasedDepthAfterDistortion);

    half3 refraction = CalculateRefraction(reconstructedWorldPosition, reconstructedWorldPositionAfterDistortion, positionWS, opaqueTexture, cameraBasedDepthAfterDistortion, noise);

    // Reflection.
    half3 reflection = CalculateReflection(positionWS, normalWS, viewDirectionWS, mainLight.shadowAttenuation);

    // Final Color.
    half3 finalColor = refraction + reflection + spec + sss;
    finalColor = lerp(finalColor, foam, foamMask);
    finalColor = lerp(finalColor, foamWaves, foamWavesMask);

    // Transparency.
    float preciseWaterDeepness = max(0, positionWS.y - reconstructedWorldPositionAfterDistortion.y);
    ApplyTransparency(preciseWaterDeepness, opaqueTexture, finalColor);

    // Shadow.
    ApplyShadow(mainLight, cameraBasedDepthAfterDistortion, finalColor);

    // Caustics.
    finalColor += CalculateCaustics(positionWS, reconstructedWorldPositionAfterDistortion, distortionUV, mainLight, cameraBasedDepthAfterDistortion);

    return half4(finalColor, 1);
}
#endif
