#ifndef WATER_REFLECTIONS_INCLUDED
#define WATER_REFLECTIONS_INCLUDED

#pragma multi_compile_fragment _ SKYBOX_IS_CUBEMAP

// Use only relevant pragma from SkyboxCommon.hlsl.
#include "Assets/Utils/SkySystem/Shaders/SkyboxCommon.hlsl"
#include "Assets/Utils/Builder/Content/Water/Shaders/Reused/WaterReusedSSR.hlsl"



half CalculateFresnelTerm(half3 normalWS, half3 viewDirectionWS, half fresnelPower)
{
    return saturate(PositivePow(1.0 - dot(normalWS, viewDirectionWS), fresnelPower));
}

half3 SampleReflections(half3 positionWS, half3 normalWS, half3 viewDirectionWS, half fresnel, half shadow, half3 fakeReflectionsSkyboxColorInShadow)
{
        float3 reflectionNormal = normalWS;
        reflectionNormal.xz *= half2(_ReflectionNormalPowerX, _ReflectionNormalPowerZ);
        reflectionNormal = normalize(reflectionNormal);
        half3 reflectVector = reflect(-viewDirectionWS, reflectionNormal);
        // Prevent sampling from lower hemisphere.
        reflectVector.y  = abs(reflectVector.y);

        half skyboxIntensity = lerp(_CubeMapReflectionIntensityCloseFar.x, _CubeMapReflectionIntensityCloseFar.y, fresnel);
        half3 skyboxReflection = CalculateSkyboxColorWithoutSun(reflectVector).rgb * skyboxIntensity;
        skyboxReflection = lerp(fakeReflectionsSkyboxColorInShadow * skyboxReflection, skyboxReflection, shadow);
        #if defined(_WATER_SCREEN_SPACE_REFLECTIONS)
            half3 ssrReflections;
            CalculateSSR(positionWS, reflectVector, skyboxReflection, ssrReflections);
            return ssrReflections;
        #else
            return skyboxReflection;
        #endif
}

float CalculateFresnel(float3 normalWS, float3 viewDirectionWS)
{
    // Decrease fresnel power to make water more reflective when flying on helicopter.
    half fresnelPower = lerp(_FresnelPowerLowHigh.x, _FresnelPowerLowHigh.y, InverseLerp(_FresnelHeightLowHigh.x, _FresnelHeightLowHigh.y, _WorldSpaceCameraPos.y));
    return  CalculateFresnelTerm(normalWS, viewDirectionWS, fresnelPower);
}

half3 CalculateReflection(float3 positionWS, float3 normalWS, float3 viewDirectionWS, half shadow)
{
    float fresnel = CalculateFresnel(normalWS, viewDirectionWS);
    half3 reflection = SampleReflections(positionWS, normalWS, viewDirectionWS, fresnel, shadow, _FakeReflectionsSkyboxColorInShadow);
    return reflection * _ReflectionIntensity;
}
#endif