#ifndef WATER_COMMON_UTILITIES_INCLUDED
#define WATER_COMMON_UTILITIES_INCLUDED

struct WaterVertexInput
{
    float4 vertex : POSITION;
    UNITY_VERTEX_INPUT_INSTANCE_ID
};

struct WaterVertexOutput
{
    float3 positionWS : TEXCOORD0;
    float4 positionSS : TEXCOORD1;
    half4 packedData : TEXCOORD2;
    #if !defined(_FFT_WAVES)
        half3 normalWS : TEXCOORD3;
    #endif
    #if !defined(_FFT_NORMALS)
        float4 detailUVs : TEXCOORD4;
        float4 farNormalsUVs : TEXCOORD5;
    #endif
    float4 positionCS : SV_POSITION;
    UNITY_VERTEX_INPUT_INSTANCE_ID
    UNITY_VERTEX_OUTPUT_STEREO
};

// Simple 2D Random noise from thebookofshaders.com.
float2 GenerateRandom(float2 st)
{
    st = float2(dot(st, float2(127.1, 311.7)), dot(st, float2(269.5, 183.3)));
    return -1.0 + 2.0 * frac(sin(st) * 43758.5453123);
}

// 2D Noise based on <PERSON> McGuire @morgan3d https://www.shadertoy.com/view/4dS3Wd.
float GenerateNoise(float2 st)
{
    float2 i = floor(st);
    float2 f = frac(st);

    float2 u = f * f * (3.0 - 2.0 * f);
    return lerp(lerp(dot(GenerateRandom(i), f),
                     dot(GenerateRandom(i + float2(1.0, 0.0)), f - float2(1.0, 0.0)), u.x),
                lerp(dot(GenerateRandom(i + float2(0.0, 1.0)), f - float2(0.0, 1.0)),
                     dot(GenerateRandom(i + float2(1.0, 1.0)), f - float2(1.0, 1.0)), u.x), u.y);
}

float GeneratePositionBasedNoise(float2 positionXZ)
{
    float time = _Time.y * _NoiseTimeMultiplier;
    return ((GenerateNoise((positionXZ * 0.5) + time) + GenerateNoise((positionXZ * 1) + time)) * 0.25 - 0.5) + 1;
}

float CalculateNormalizedWaveHeight(float waveHeight, float3 positionWS, float smoothlyStopWavesByDistanceFactor)
{
    // Prevent flickering of foam waves when water is viewed from sharp angle.
    float3 viewDirectionWS = SafeNormalize(_WorldSpaceCameraPos - positionWS);
    float waterWaveHeightSmoothingAngleFactor = 1 - smoothstep(_WaterWaveHeightSmoothingAngle, 0, abs(dot(float3(0, 1, 0), viewDirectionWS)));
    return saturate(waveHeight / _WavesHeightToConsiderHigh) * smoothlyStopWavesByDistanceFactor * waterWaveHeightSmoothingAngleFactor;
}

float InverseLerp(float a, float b, float t)
{
    return saturate((t - a) / (b - a));
}

float DistanceSqr(float2 pt1, float2 pt2)
{
    float2 v = pt2 - pt1;
    return dot(v, v);
}

float4 DualAnimatedUV(float2 uv, float4 tilings, float4 speeds)
{
    float4 coords;
    coords.xy = uv * tilings.xy;
    coords.zw = uv * tilings.zw;
    coords += speeds * _Time.x;
    return coords;
}

half2 DistortionUVs(float3 normalWS)
{
    half3 viewNormal = mul((float3x3)GetWorldToHClipMatrix(), -normalWS).xyz;
    return viewNormal.xz * _DistortionPower;
}

float3 SimpleScrollingTexturesNormal(float deepnessFactor, float farNormalsFactor, float3 normalWS, float4 detailUVs, float4 farNormalsUVs, float3 viewDirectionWS)
{
    // Detail waves.
    half2 detailBump1 = SAMPLE_TEXTURE2D(_DetailsTexture, sampler_DetailsTexture, detailUVs.zw).xy * 2 - 1;
    half2 detailBump2 = SAMPLE_TEXTURE2D(_DetailsTexture, sampler_DetailsTexture, detailUVs.xy).xy * 2 - 1;
    half2 detailBump = (detailBump1 + detailBump2 * 0.5) * saturate(deepnessFactor * 0.5 + 0.25);

    // Far normals.
    half2 farNormal1 = SAMPLE_TEXTURE2D(_NormalMapFar, sampler_NormalMapFar, farNormalsUVs.xy).xy * 2 - 1;
    half2 farNormal2 = SAMPLE_TEXTURE2D(_NormalMapFar, sampler_NormalMapFar, farNormalsUVs.zw).xy * 2 - 1;
    half2 farNormal = farNormal1 + farNormal2;

    float3 farNormalWithIntensity = half3(farNormal.x, 0, farNormal.y) * _NormalMapFarIntensity;

    float3 resultNormal = normalWS + lerp(half3(detailBump.x, 0, detailBump.y) * _DetailTexturesPower + farNormalWithIntensity * farNormalsFactor, farNormalWithIntensity, farNormalsFactor);
    // Prevent flickering of normals on when water is viewed from sharp angle.
    float normalStraighteningAngleFactor = smoothstep(_NormalStraighteningAngle, 0, abs(dot(float3(0, 1, 0), viewDirectionWS)));
    return normalize(lerp(resultNormal, float3(0, 1, 0), normalStraighteningAngleFactor));
}

float GetRawDepthMin(half2 screenSpaceUV)
{
    float rawD = SampleSceneDepth(screenSpaceUV);
    #if !SHADER_API_MOBILE
    // For some bizarre reason _CameraDepthTexture_TexelSize.xy calculates as if _CameraDepthTexture 64x64.
    float2 texelSize = 1 / _ScreenParams.xy;
    float rawD1 = SampleSceneDepth(screenSpaceUV + float2(1.0, 0.0) * texelSize);
    float rawD2 = SampleSceneDepth(screenSpaceUV + float2(-1.0, 0.0) * texelSize);
    float rawD3 = SampleSceneDepth(screenSpaceUV + float2(0.0, 1.0) * texelSize);
    float rawD4 = SampleSceneDepth(screenSpaceUV + float2(0.0, -1.0) * texelSize);

    // Search for farthest depth, this prevents MSAA artifacts similar to described in this thread:
    // https://forum.unity.com/threads/fixing-screen-space-directional-shadows-and-anti-aliasing.379902/
    rawD = min(rawD, min(min(rawD1, rawD2), min(rawD3, rawD4)));
    #endif
    return rawD;
}

float DepthDistanceFromFragmentToScene(float rawDepth, float fragmentDepth)
{
    return LinearEyeDepth(rawDepth, _ZBufferParams) - fragmentDepth;
}

void ApplyTransparency(float preciseWaterDeepness, half3 opaqueTexture, inout half3 finalColor)
{
    half transparencyFactor = smoothstep(0, _WaterTransparencyDepth, preciseWaterDeepness);
    finalColor = lerp(opaqueTexture, finalColor, transparencyFactor);
}

void ApplyShadow(Light mainLight, float cameraBasedDepthAfterDistortion, inout half3 finalColor)
{
    half shadow = mainLight.shadowAttenuation;
    shadow = lerp(1, shadow, _ShadowIntensity);
    shadow = lerp(1, shadow, saturate(cameraBasedDepthAfterDistortion / _ShadowDecreaseNearGeometry));
    finalColor = finalColor * shadow;
}

#endif
