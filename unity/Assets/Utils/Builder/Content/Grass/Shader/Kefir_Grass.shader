Shader "Kefir/Grass"
{
    Properties
    {
        [NoScaleOffset]_BaseMap("Base Color Map", 2D) = "white" {}
        [NoScaleOffset]_MaskMap("MSA Map", 2D) = "black" {}
        [NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        [Toggle(_NO_NORMAL_MAP)] _NoNormalMap("No Normal Map", Float) = 0
        
        _ShadowNormalBias("Shadow Normal Bias", Range(-0.2, 0.2)) = 0.03
        
        _TerrainNormalImportance("Terrain Normal Importance", Range(0, 1)) = 0.8
        _TerrainNormalUniformness ("Terrain Normal Uniformness", Range(0, 1)) = 0.8

        _Cutoff("Alpha Clip Threshold", Range(0, 1)) = 0.5
        [Enum(UnityEngine.Rendering.CullMode)] _Cull ("Cull", Float) = 0

        [Header(BlendWithGround)]
        _BlendWithGroundMaxIntensity ("Blend With Ground Max Intensity", Range(0, 1)) = 0.5
        _BlendWithGroundHeight ("Blend With Ground Height", Range(0, 1)) = 0.1
        _BlendWithGroundHeightSmoothBorder ("Blend With Ground Height Smooth Border", Range(0, 1)) = 0.1
        _BlendWithGroundColorMultiplier("Blend With Ground Color Multiplier", Range(0, 10)) = 5
        
        [Header(Dusting)]
        [Toggle(_DUSTING)] _Dusting("Dusting", Float) = 0
        _DustingFactor ("Dusting Factor", Range(0, 1)) = 0.5
        _DustingHeight ("Dusting Height", Range(0, 2)) = 0.5
        _DustingHeightSmoothBorder ("Dusting Height Smooth Border", Range(0, 1)) = 0.2

        [Header(SmoothAppearing)]
        _GrassHeight ("Grass Height", Range(0, 1)) = 0.5

        [Header(SimpleLightingOnly)]
        _AOPower ("AO Power", Range(0, 1)) = 1

        [Header(Wind)]
        [NoScaleOffset] _WindTexture ("Wind Texture", 2D) = "white" {}
        _WindFrequency("Wind Frequency", Range(0, 0.25)) = 0
        _WindScale ("Wind Scale", Range(0, 0.5)) = 0.5
        _WindTurbulence("Wind Turbulence", Range(0, 1)) = 0
    }
    SubShader
    {
        Tags
        {
            "RenderPipeline"="UniversalPipeline"
            "RenderType"="Opaque"
            "Queue"="AlphaTest"
            "UniversalMaterialType" = "Lit" "IgnoreProjector" = "True"
        }

        HLSLINCLUDE
        #pragma multi_compile_instancing
        // assumeuniformscaling - prevents the need of WorldToObject matrix.
        #pragma instancing_options procedural:setup assumeuniformscaling 
        #pragma multi_compile_vertex _ _GRASS_WIND
        #pragma multi_compile _ _SIMPLE_LIGHTING
        #if !defined(_SIMPLE_LIGHTING)
            #pragma shader_feature _NO_NORMAL_MAP
            #pragma shader_feature_fragment _DUSTING
        #endif
        
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
        #include "Assets/Content/ShaderLibrary/GlobalWindUniforms.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
        
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
        void LODFadeCrossFadeGrass(float4 positionCS, float lodFadeFactor)
        {
            half2 uv = positionCS.xy * _DitheringTextureInvSize;
            half d = SAMPLE_TEXTURE2D(_DitheringTexture, sampler_PointRepeat, uv).a;
            d = lodFadeFactor.x - CopySign(d, lodFadeFactor.x);
            clip(d);
        }
        
        CBUFFER_START(UnityPerMaterial)
        float _ShadowNormalBias;
        half _Cutoff;
        half _AOPower;
        half _BlendWithGroundMaxIntensity;
        half _BlendWithGroundHeight;
        half _BlendWithGroundHeightSmoothBorder;
        half _BlendWithGroundColorMultiplier;
        half _DustingFactor;
        half _DustingHeight;
        half _DustingHeightSmoothBorder;
        half _TerrainNormalImportance;
        half _TerrainNormalUniformness;
        half _GrassHeight;
        half _WindFrequency;
        half _WindScale;
        half _WindTurbulence;
        CBUFFER_END

        TEXTURE2D(_BaseMap);
        SAMPLER(sampler_BaseMap);

        TEXTURE2D(_MaskMap);
        SAMPLER(sampler_MaskMap);

        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);

        TEXTURE2D(_WindTexture);
        SAMPLER(sampler_WindTexture);

        #if defined(PROCEDURAL_INSTANCING_ON)
            struct MeshProperties
            {
                // Use BRG like packed matrix, to save on bandwidth.
                float4x3 ObjectToWorldPacked;
                // Don't provide per instance WorldToObject to safe on bandwidth, because we don't use it for our calculations.
                float4 Color;
            };

            StructuredBuffer<MeshProperties> _PropertiesBuffer;
            ByteAddressBuffer  _VisibleInstancesIdsBuffer;
            StructuredBuffer<float> _SmoothAppearFactors;
            StructuredBuffer<float> _LODFadeFactors;
        #endif

        #if defined(INSTANCING_ON)
            UNITY_INSTANCING_BUFFER_START(Props)
              UNITY_DEFINE_INSTANCED_PROP(float4, _InstancedColor)
            UNITY_INSTANCING_BUFFER_END(Props)
        #endif

        // Similar to BRG's LoadDOTSInstancedData_float4x4_from_float3x4 unpacks float4x3 into correct TRS matrix.
        float4x4 UnpackedMatrix(float4x3 packedMatrix)
        {
            return float4x4(
            packedMatrix._m00, packedMatrix._m30, packedMatrix._m21, packedMatrix._m12,
            packedMatrix._m10, packedMatrix._m01, packedMatrix._m31, packedMatrix._m22,
            packedMatrix._m20, packedMatrix._m11, packedMatrix._m02, packedMatrix._m32,
            0.0,  0.0,  0.0,  1.0);
        }
        
        float3 CalculatePositionWS(uint unity_InstanceID, float3 positionOS)
        {
           float3 positionWS = TransformObjectToWorld(positionOS.xyz);

            // Wind.
            #if defined(_GRASS_WIND)
                float2 windUV = positionWS.xz  * _WindScale + _Time.y * _WindFrequency;
                half2 wind = SAMPLE_TEXTURE2D_LOD(_WindTexture, sampler_WindTexture, windUV, 0).rg * 2 - 1;
                half3 windMain = _GlobalWindDirection *  (wind.r * _GlobalWindStrength);
            
                half3 windTurbulent = _GlobalWindCrossDirection * (_WindTurbulence * _GlobalWindStrength * wind.g);
                half3 combinedWind = (windMain + windTurbulent) * positionOS.y;
                positionWS += combinedWind;
            #endif
            
            // Smooth Appear.
            #if defined(PROCEDURAL_INSTANCING_ON)
                float smoothAppearFactor = _SmoothAppearFactors[unity_InstanceID];
                float3 upDirectionWS = float3(unity_ObjectToWorld[0][1], unity_ObjectToWorld[1][1], unity_ObjectToWorld[2][1]);
                positionWS = lerp(positionWS, positionWS - upDirectionWS * _GrassHeight, smoothAppearFactor);
            #endif
            return positionWS;
        }

        float3 CalculateNormalWS(uint unity_InstanceID, float3 positionWS, float3 positionOS, float3 normalOS, float4 tangentOS, out float3 tangentWS, out float3 bitangentWS)
        {
            float3 bitangent = cross(normalOS, tangentOS.xyz) * tangentOS.w;
            const float offset = 0.01;
            float3 bitangentOffsetOS = positionOS + bitangent * offset;
            float3 bitangentOffsetWS = CalculatePositionWS(unity_InstanceID, bitangentOffsetOS);
            bitangentWS = SafeNormalize(bitangentOffsetWS - positionWS);

            // SpeedTree8Common SpeedTreeVert billboard lighting use this.
            tangentWS = cross(GetWorldSpaceNormalizeViewDir(positionWS), bitangentWS);
            float3 normalWS = cross(tangentWS, bitangentWS);
            return normalWS;
        }

        float3 CalculateNormalWS(uint unity_InstanceID, float3 positionWS, float3 positionOS, float3 normalOS, float4 tangentOS)
        {
            float3 tangentWS;
            float3 bitangentWS;
            return CalculateNormalWS(unity_InstanceID, positionWS, positionOS.xyz, normalOS, tangentOS, tangentWS, bitangentWS);
        }
        
        void setup()
        {
            // Do nothing cause we calculate everything in CustomSetup.
        }

        void CustomSetup(uint unity_InstanceID, out half4 instanceColor)
        {
            #if defined(PROCEDURAL_INSTANCING_ON)
                uint id = _VisibleInstancesIdsBuffer.Load(unity_InstanceID * 4);
                MeshProperties properties = _PropertiesBuffer[id];
                // In DX12 you can't override unity_ObjectToWorld because it's readonly.
                unity_ObjectToWorld = UnpackedMatrix(properties.ObjectToWorldPacked);
                instanceColor = properties.Color;
            #elif defined(INSTANCING_ON)
                // You can't do customObjectToWorld = unity_ObjectToWorld; there because it's not correct.
                instanceColor = UNITY_ACCESS_INSTANCED_PROP(Props, _InstancedColor);
            #else
                instanceColor = half4(1, 1, 1, 1);
            #endif
        }
    
        ENDHLSL

        Pass
        {
            Name "ForwardLit"
            Tags
            {
                "LightMode" = "UniversalForward"
            }

            ZWrite On
            ZTest LEqual
            Cull[_Cull]

            HLSLPROGRAM
            #pragma vertex GrassPassVertex
            #pragma fragment GrassPassFragment

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            
            #if defined(_DUSTING)
                #define NO_SHADER_GRAPH 1
                #include "Assets/ContentDraft/ShaderLibrary/Dusting.hlsl"
            #endif

            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                #if defined(INSTANCING_ON)
                   UNITY_VERTEX_INPUT_INSTANCE_ID
                #endif
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float3 uvAndPositionOSY: TEXCOORD0;
                float4 instanceColor : TEXCOORD1;
                float3 normalWS : TEXCOORD2;
                float4 positionWSAndLODFadeFactor : TEXCOORD3;
                DECLARE_SH(sh, 4)
                #if !defined(_SIMPLE_LIGHTING) && !defined(_NO_NORMAL_MAP)
                    float3 tangentWS : TEXCOORD5;
                    float3 bitangentWS : TEXCOORD6;
                #endif
                #if defined(INSTANCING_ON)
                   UNITY_VERTEX_INPUT_INSTANCE_ID
                #endif
            };
            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"
            
            Varyings GrassPassVertex(Attributes input, uint unity_InstanceID: SV_InstanceID)
            {
                Varyings output;

                #if defined(INSTANCING_ON)
                    UNITY_SETUP_INSTANCE_ID(input);
                    UNITY_TRANSFER_INSTANCE_ID(input, output);
                #endif

                half4 instanceColor;
                CustomSetup(unity_InstanceID, instanceColor);

                float3 positionWS = CalculatePositionWS(unity_InstanceID, input.positionOS.xyz);

                output.positionCS = TransformWorldToHClip(positionWS);
                output.positionWSAndLODFadeFactor.xyz = positionWS;

                #if defined(PROCEDURAL_INSTANCING_ON)
                    output.positionWSAndLODFadeFactor.w = _LODFadeFactors[unity_InstanceID];
                #else
                    output.positionWSAndLODFadeFactor.w = 1;
                #endif

                #if defined(_SIMPLE_LIGHTING)
                    // Fake view dependent normal.
                    float3 normalWS = normalize(lerp(GetWorldSpaceNormalizeViewDir(positionWS), float3(0, 1, 0), _TerrainNormalUniformness));
                #else
                    float3 tangentWS;
                    float3 bitangentWS;
                    float3 normalWS = CalculateNormalWS(unity_InstanceID, positionWS, input.positionOS.xyz, input.normalOS, input.tangentOS, tangentWS, bitangentWS);
                    #if !defined(_NO_NORMAL_MAP)
                        output.tangentWS = tangentWS;
                        output.bitangentWS = bitangentWS;
                    #endif
                #endif

                output.uvAndPositionOSY = float3(input.uv.xy, input.positionOS.y);
                output.normalWS = normalWS;
                instanceColor.a = min(instanceColor.a, _BlendWithGroundMaxIntensity);
                output.instanceColor = instanceColor;
                OUTPUT_VERTEX_SH(normalWS, output.sh)
                return output;
            }

            half3 GrassCalculateDiffuse(float3 normalWS, Light light)
            {
                half3 attenuatedLightColor = light.color * (light.distanceAttenuation * light.shadowAttenuation);
                half3 lightDiffuseColor = LightingLambert(attenuatedLightColor, light.direction, normalWS);
                return lightDiffuseColor;
            }

            half3 GrassFragmentDiffuse(float3 albedo, float3 ambientColor, float ambientOcclusion, float3 positionWS, float3 normalWS, float4 shadowCoord)
            {
                half3 lightingColor = ambientColor * ambientOcclusion;

                Light mainLight = GetMainLight(shadowCoord, positionWS, 0);
                lightingColor += GrassCalculateDiffuse(normalWS, mainLight);

                #ifdef _ADDITIONAL_LIGHTS
                    uint numAdditionalLights = GetAdditionalLightsCount();
                    for (uint lightI = 0; lightI < numAdditionalLights; lightI++)
                    {
                        Light light = GetAdditionalLight(lightI, positionWS);
                        lightingColor += GrassCalculateDiffuse(normalWS, light);
                    }
                #endif

                return lightingColor * albedo;
            }

            half4 SimpleLighting(Varyings input, float3 positionWS, float3 normalWS, float3 albedo, float alpha)
            {
                float2 screenSpaceUV = GetNormalizedScreenSpaceUV(input.positionCS);
                float ambientOcclusion = lerp(1, CalculateAmbientOcclusionFactor(screenSpaceUV, positionWS, 1, 1), _AOPower);
                float4 shadowCoord = TransformWorldToShadowCoordBlendCascade(positionWS, input.positionCS.xy);
                half3 ambientColor = COMBINE_VERTEX_FRAGMENT_SH(normalWS, input.sh);
                return half4(GrassFragmentDiffuse(albedo, ambientColor, ambientOcclusion, positionWS, normalWS, shadowCoord), alpha);
            }
            
            float GetInstanceScale()
            {
                // Grass is uniformly scaled.
                return length(unity_ObjectToWorld._m01_m11_m21);
            }

            half4 GrassPassFragment(Varyings input) : SV_Target
            {
                #if defined(INSTANCING_ON)
                    UNITY_SETUP_INSTANCE_ID(input);
                #endif
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFadeGrass(input.positionCS, input.positionWSAndLODFadeFactor.w);
                #endif

                float2 uv = input.uvAndPositionOSY.xy;
                float positionOSY = input.uvAndPositionOSY.z;
                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, uv);
                clip(baseMap.a - _Cutoff);
                
                float instanceScale = GetInstanceScale();
                half3 albedo = lerp(baseMap.rgb, input.instanceColor.rgb * baseMap.g * _BlendWithGroundColorMultiplier, input.instanceColor.a * smoothstep((_BlendWithGroundHeightSmoothBorder + _BlendWithGroundHeight) * instanceScale, _BlendWithGroundHeight * instanceScale, positionOSY));
                float3 positionWS = input.positionWSAndLODFadeFactor.xyz;
                float3 normalWS = input.normalWS.xyz;
                
                #if defined(_SIMPLE_LIGHTING)
                    half4 model = SimpleLighting(input, positionWS, normalWS, albedo, baseMap.a);
                #else

                    half3 msa = SAMPLE_TEXTURE2D(_MaskMap, sampler_MaskMap, uv).rgb;

                    #if defined(_DUSTING)
                        half3 resultAldebo;
                        // Don't care about smoothness.
                        half resultSmoothness;
                        Dusting(sampler_BaseMap, _DustingFactor, _DustingHeight, _DustingHeightSmoothBorder, positionWS, normalWS, albedo, msa.g, resultAldebo, resultSmoothness);
                        albedo = resultAldebo;
                    #endif
                
                    #if defined(_NO_NORMAL_MAP)
                         SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, baseMap.a), msa);
                         InputData inputData = InitializeInputData(input, positionWS, GetFakeTBN(normalWS), normalWS);
                    #else
                        half3 normalTS =  UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, uv));
                        SurfaceData surfaceData = InitializeSurfaceData(half4(albedo, baseMap.a), msa, normalTS);
                        InputData inputData = InitializeInputData(input, positionWS, surfaceData.normalTS, half3x3(input.tangentWS, input.bitangentWS, normalWS));
                    #endif

                    // Fake view dependent normal.
                    float3 fakeNormalWS = normalize(lerp(GetWorldSpaceNormalizeViewDir(positionWS), float3(0, 1, 0), _TerrainNormalUniformness));
                    inputData.normalWS = lerp(inputData.normalWS, fakeNormalWS, _TerrainNormalImportance);

                    half4 model = UniversalFragmentPBR(inputData, surfaceData);
                #endif
                // Prevent MSAA artifacts.
                return saturate(model);
            }
            ENDHLSL
        }

        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #define SHADOW_CASTER_DEFAULT_INCLUDED

            #pragma target 2.0

            // -------------------------------------
            // Shader Stages
            #pragma vertex ShadowPassVertex
            #pragma fragment ShadowPassFragment

            #pragma multi_compile_fragment _ LOD_FADE_CROSSFADE

            // This is used during shadow map generation to differentiate between directional and punctual light shadows, as they use different formulas to apply Normal Bias
            #pragma multi_compile_vertex _ _CASTING_PUNCTUAL_LIGHT_SHADOW
            
            // Shadow Casting Light geometric parameters. These variables are used when applying the shadow Normal Bias and are set by UnityEngine.Rendering.Universal.ShadowUtils.SetupShadowCasterConstantBuffer in com.unity.render-pipelines.universal/Runtime/ShadowUtils.cs
            // For Directional lights, _LightDirection is used when applying shadow Normal Bias.
            // For Spot lights and Point lights, _LightPosition is used to compute the actual light direction because it is different at each shadow caster geometry vertex.
            float3 _LightDirection;
            float3 _LightPosition;

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
                #if defined(INSTANCING_ON)
                   UNITY_VERTEX_INPUT_INSTANCE_ID
                #endif
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float3 uvAndLodFadeFactor  : TEXCOORD0;
                #if defined(INSTANCING_ON)
                   UNITY_VERTEX_INPUT_INSTANCE_ID
                #endif
            };

            float3 ApplyShadowBiasGrass(float3 positionWS, float3 normalWS, float3 lightDirection)
            {
                float invNdotL = 1.0 - saturate(dot(lightDirection, normalWS));
                float scale = invNdotL * _ShadowBias.y;

                // normal bias is negative since we want to apply an inset normal offset
                positionWS = lightDirection * _ShadowBias.xxx + positionWS;
                // Add additional offset to lower peterpenning of grass shadow.
                positionWS = normalWS * (scale.xxx + _ShadowNormalBias) + positionWS;
                return positionWS;
            }
            
            float4 GetShadowPositionHClip(float3 positionWS, float3 normalWS)
            {
                #if _CASTING_PUNCTUAL_LIGHT_SHADOW
                        float3 lightDirectionWS = normalize(_LightPosition - positionWS);
                #else
                        float3 lightDirectionWS = _LightDirection;
                #endif

                float4 positionCS = TransformWorldToHClip(ApplyShadowBiasGrass(positionWS, normalWS, lightDirectionWS));

                #if UNITY_REVERSED_Z
                        positionCS.z = min(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #else
                        positionCS.z = max(positionCS.z, UNITY_NEAR_CLIP_VALUE);
                #endif

                return positionCS;
            }

            Varyings ShadowPassVertex(Attributes input, uint unity_InstanceID: SV_InstanceID)
            {
                Varyings output;

                #if defined(INSTANCING_ON)
                    UNITY_SETUP_INSTANCE_ID(input);
                    UNITY_TRANSFER_INSTANCE_ID(input, output);
                #endif

                half4 instanceColor;
                CustomSetup(unity_InstanceID, instanceColor);

                float3 positionWS = CalculatePositionWS(unity_InstanceID, input.positionOS.xyz);
                float3 normalWS = CalculateNormalWS(unity_InstanceID, positionWS, input.positionOS.xyz, input.normalOS, input.tangentOS);
                output.positionCS = GetShadowPositionHClip(positionWS, normalWS);
                output.uvAndLodFadeFactor.xy = input.uv;

                #if defined(PROCEDURAL_INSTANCING_ON)
                    output.uvAndLodFadeFactor.z = _LODFadeFactors[unity_InstanceID];
                #else
                    output.uvAndLodFadeFactor.z = 1;
                #endif
                
                return output;
            }

            half4 ShadowPassFragment(Varyings input) : SV_TARGET
            {
                #if defined(INSTANCING_ON)
                    UNITY_SETUP_INSTANCE_ID(input);
                #endif

                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFadeGrass(input.positionCS, input.uvAndLodFadeFactor.z);
                #endif
                
                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uvAndLodFadeFactor.xy);
                clip(baseMap.a - _Cutoff);
                return 0;
            }

            ENDHLSL
        }

        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode"="DepthNormals"
            }

            ZWrite On
            ZTest LEqual
            Cull[_Cull]

            HLSLPROGRAM
            #pragma vertex GrassDepthNormalsVertex
            #pragma fragment GrassDepthNormalsFragment

            #pragma multi_compile_fragment _ LOD_FADE_CROSSFADE

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float4 tangentOS : TANGENT;
                float2 uv : TEXCOORD0;
                #if defined(INSTANCING_ON)
                   UNITY_VERTEX_INPUT_INSTANCE_ID
                #endif
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float3 uvAndLodFadeFactor : TEXCOORD0;
                float3 normalWS : TEXCOORD1;
                #if defined(INSTANCING_ON)
                   UNITY_VERTEX_INPUT_INSTANCE_ID
                #endif
            };

            Varyings GrassDepthNormalsVertex(Attributes input, uint unity_InstanceID: SV_InstanceID)
            {
                Varyings output;

                #if defined(INSTANCING_ON)
                    UNITY_SETUP_INSTANCE_ID(input);
                    UNITY_TRANSFER_INSTANCE_ID(input, output);
                #endif

                float4 instanceColor;
                CustomSetup(unity_InstanceID, instanceColor);

                float3 positionWS = CalculatePositionWS(unity_InstanceID, input.positionOS.xyz);
                float3 normalWS = CalculateNormalWS(unity_InstanceID, positionWS, input.positionOS.xyz, input.normalOS, input.tangentOS);

                output.uvAndLodFadeFactor.xy = input.uv.xy;
                #if defined(PROCEDURAL_INSTANCING_ON)
                    output.uvAndLodFadeFactor.z = _LODFadeFactors[unity_InstanceID];
                #else
                    output.uvAndLodFadeFactor.z = 1;
                #endif
                output.positionCS = TransformWorldToHClip(positionWS);
                output.normalWS = normalWS;
                return output;
            }

            void GrassDepthNormalsFragment(Varyings input, out half4 outNormalWS : SV_Target0)
            {
                #if defined(INSTANCING_ON)
                    UNITY_SETUP_INSTANCE_ID(input);
                #endif

                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFadeGrass(input.positionCS, input.uvAndLodFadeFactor.z);
                #endif
                
                half4 baseMap = SAMPLE_TEXTURE2D(_BaseMap, sampler_BaseMap, input.uvAndLodFadeFactor.xy);
                clip(baseMap.a - _Cutoff);

                #if defined(_GBUFFER_NORMALS_OCT)
                    float3 normalWS = normalize(input.normalWS);
                    float2 octNormalWS = PackNormalOctQuadEncode(normalWS);           // values between [-1, +1], must use fp32 on some platforms.
                    float2 remappedOctNormalWS = saturate(octNormalWS * 0.5 + 0.5);   // values between [ 0,  1]
                    half3 packedNormalWS = PackFloat2To888(remappedOctNormalWS);      // values between [ 0,  1]
                    outNormalWS = half4(packedNormalWS, 0.0);
                #else
                float3 normalWS = NormalizeNormalPerPixel(input.normalWS);
                outNormalWS = half4(normalWS, 0.0);
                #endif
            }
            ENDHLSL
        }
    }
}