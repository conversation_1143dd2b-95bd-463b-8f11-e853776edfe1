using System.Collections.Generic;
using System.Linq;
using Builder.Scriptable;
using System;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Splines;
using Utils.Builder.Scripts.Features.Road;
using Utils.Editor;

namespace Builder.Helper
{
    public static class HelperBuilderRoadMesh
    {
        private class SubMeshCollection
        {
            public readonly List<Vector3> Vertices = new();
            public readonly List<Vector3> Normals = new();
            public readonly List<Vector4> Tangents = new();
            public readonly Dictionary<int, List<Vector2>> UVs = new();
            public readonly List<Vector2> UvToUnwrap = new();
            public readonly List<int> Triangles = new();
            public readonly List<int> VerticesToCorrectNormalsAndTangents = new();
            public readonly List<Color> Colors = new();
        }
        
        public static (Mesh mesh, Material material, IReadOnlyList<int> verticesToCorrectNormalsAndTangents, List<Vector2> uvToUnwrap, bool castShadows)[] CreateSceneMeshes(RoadDataAsset road)
        {
            if (!road.HasMeshRender)
            {
                return Array.Empty<(Mesh mesh, Material material, IReadOnlyList<int> verticesToCorrectNormalsAndTangents, List<Vector2> uvToUnwrap, bool castShadows)>();
            }
            
            var subMeshesData = new Dictionary<(Material material, bool castShadows), SubMeshCollection>();
            float splineLength = road.Spline.GetLength();

            foreach (var layer in road.Layers)
            {
                for (int i = 0; i < road.SlotsCount; i++)
                {
                    if (layer.Slots[i].TileL) CollectMeshData(subMeshesData, layer.Slots[i].TileL.SceneMesh, layer.Slots[i].TileL.CastShadows, road.SplineScale, road.Spline, splineLength, i, layer.Slots[i].TileLOffset, layer.Slots[i].TileLMaterial, false, false);
                    if (layer.Slots[i].TileR) CollectMeshData(subMeshesData, layer.Slots[i].TileR.SceneMesh, layer.Slots[i].TileR.CastShadows, road.SplineScale, road.Spline, splineLength, i, layer.Slots[i].TileROffset, layer.Slots[i].TileRMaterial, true, false);
                }
            }

            return CreateMeshes("road", subMeshesData);
        }
        
        public static void CreateColliderMesh(RoadDataAsset road, out Mesh meshCollider)
        {
            var subMeshesData = new Dictionary<(Material material, bool castShadows), SubMeshCollection>();
            float splineLength = road.Spline.GetLength();

            foreach (var layer in road.Layers)
            {
                for (int i = 0; i < road.SlotsCount; i++)
                {
                    if (layer.Slots[i].TileL) CollectMeshData(subMeshesData, layer.Slots[i].TileL.ColliderMesh, false, road.SplineScale, road.Spline, splineLength, i, layer.Slots[i].TileLOffset, null, false, true);
                    if (layer.Slots[i].TileR) CollectMeshData(subMeshesData, layer.Slots[i].TileR.ColliderMesh, false, road.SplineScale, road.Spline, splineLength, i, layer.Slots[i].TileROffset, null, true, true);
                }
            }

            CreateMesh("road_collider", subMeshesData.Values, out meshCollider);
        }
        
        public static Mesh CreateMapMesh(RoadDataAsset road)
        {
            var subMeshesData = new Dictionary<(Material material, bool castShadows), SubMeshCollection>();
            float splineLength = road.Spline.GetLength();

            foreach (var layer in road.Layers)
            {
                for (int i = 0; i < road.SlotsCount; i++)
                {
                    if (layer.Slots[i].TileL) CollectMeshData(subMeshesData, layer.Slots[i].TileL.MapMesh, false, road.SplineScale, road.Spline, splineLength, i, layer.Slots[i].TileLOffset, null, false, false);
                    if (layer.Slots[i].TileR) CollectMeshData(subMeshesData, layer.Slots[i].TileR.MapMesh, false, road.SplineScale, road.Spline, splineLength, i, layer.Slots[i].TileROffset, null, true, false);
                }
            }

            CreateMesh("road_map", subMeshesData.Values, out var mesh);
            return mesh;
        }
        
        private static void CreateMesh(string name, ICollection<SubMeshCollection> subMeshesData, out Mesh mesh)
        {
            var vertices = new List<Vector3>();
            var normals = new List<Vector3>();
            var tangents = new List<Vector4>();
            var uvsByIndex = new Dictionary<int, List<Vector2>>();
            var colors = new List<Color>();
            var triangles = new List<int>();

            foreach (var subMeshData in subMeshesData)
            {
                vertices.AddRange(subMeshData.Vertices);
                normals.AddRange(subMeshData.Normals);
                tangents.AddRange(subMeshData.Tangents);
                foreach ((int uvIndex, var uvList) in subMeshData.UVs)
                {
                    if (!uvsByIndex.TryGetValue(uvIndex, out var resultUvs))
                    {
                        resultUvs = new List<Vector2>();
                        uvsByIndex.Add(uvIndex, resultUvs);
                    }
                    resultUvs.AddRange(uvList);
                }
                colors.AddRange(subMeshData.Colors);
            }

            int globalIndex = 0;
            foreach (var subMeshData in subMeshesData)
            {
                triangles.AddRange(subMeshData.Triangles.Select(t => t + globalIndex));
                globalIndex += subMeshData.Vertices.Count;
            }

            mesh = new Mesh
            {
                name = name,
                indexFormat = vertices.Count > ushort.MaxValue ? IndexFormat.UInt32 : IndexFormat.UInt16,
            };

            mesh.indexBufferTarget |= GraphicsBuffer.Target.Raw;
            mesh.vertexBufferTarget |= GraphicsBuffer.Target.Raw;

            mesh.SetVertices(vertices);
            mesh.SetNormals(normals);
            mesh.SetTangents(tangents);
            foreach ((int uvIndex, var uvs) in uvsByIndex)
            {
                mesh.SetUVs(uvIndex, uvs);
            }
            mesh.SetColors(colors);
            mesh.SetTriangles(triangles.ToArray(), 0);

            mesh.RecalculateBounds();
            mesh.RecalculateUVDistributionMetrics();
        }
        
        private static (Mesh mesh, Material material, IReadOnlyList<int> verticesToCorrectNormalsAndTangents, List<Vector2> uvToUnwrap, bool castShadows)[] CreateMeshes(string name, Dictionary<(Material material, bool castShadows), SubMeshCollection> subMeshesData)
        {
            var meshes = new (Mesh mesh, Material material, IReadOnlyList<int> verticesToCorrectNormalsAndTangents, List<Vector2> uvToUnwrap, bool castShadows)[subMeshesData.Count];

            int index = 0;
            foreach (((var material, bool castShadows), var meshCollection) in subMeshesData)
            {
                var mesh = new Mesh
                {
                    name = FileName.Combine(name, index),
                    indexFormat = meshCollection.Vertices.Count > ushort.MaxValue ? IndexFormat.UInt32 : IndexFormat.UInt16,
                };
                
                mesh.indexBufferTarget |= GraphicsBuffer.Target.Raw;
                mesh.vertexBufferTarget |= GraphicsBuffer.Target.Raw;

                mesh.SetVertices(meshCollection.Vertices);
                mesh.SetNormals(meshCollection.Normals);
                mesh.SetTangents(meshCollection.Tangents);

                foreach ((int uvIndex, var uvs) in meshCollection.UVs)
                {
                    mesh.SetUVs(uvIndex, uvs);
                }

                mesh.SetColors(meshCollection.Colors);
                mesh.SetTriangles(meshCollection.Triangles, 0);

                mesh.RecalculateBounds();
                mesh.RecalculateUVDistributionMetrics();

                meshes[index++] = (mesh, material, meshCollection.VerticesToCorrectNormalsAndTangents, meshCollection.UvToUnwrap, castShadows);
            }

            return meshes;
        }
        
        private static void CollectMeshData(Dictionary<(Material material, bool castShadows), SubMeshCollection> subMeshesData, CachedMeshData tileMeshData, bool castShadows, float splineScale, ISpline spline, float splineLength,
            int index, float tileLOffset, Material tileMaterial, bool mirror, bool isCollider)
        {
            tileMeshData.CacheData();

            for (var subMeshIndex = 0; subMeshIndex < tileMeshData.SubMeshDescriptors.Length; subMeshIndex++)
            {
                (Material material, bool castShadows) materialWithShadow = tileMaterial ? (tileMaterial, castShadows) : (tileMeshData.SubmeshData[subMeshIndex].Material, castShadows);
                if (!subMeshesData.ContainsKey(materialWithShadow))
                {
                    subMeshesData.Add(materialWithShadow, new SubMeshCollection());
                }
                CollectSubMeshData(subMeshesData[materialWithShadow], tileMeshData, splineScale, spline, splineLength, index, tileLOffset, mirror, isCollider, subMeshIndex);
            }
        }
        
        private static void CollectSubMeshData(SubMeshCollection meshCollection, CachedMeshData tileMeshData, float splineScale, ISpline spline, float splineLength, int index, float tileLOffset, bool mirror, bool isCollider,
            int subMeshIndex)
        {
            var verticesCount = meshCollection.Vertices.Count;
            var secondVertex = mirror ? 2 : 1;
            var thirdVertex = mirror ? 1 : 2;

            var firstTriangleIndex = tileMeshData.SubMeshDescriptors[subMeshIndex].indexStart;
            var lastTriangleIndex = firstTriangleIndex + tileMeshData.SubMeshDescriptors[subMeshIndex].indexCount;
            var firstVertexIndex = tileMeshData.SubMeshDescriptors[subMeshIndex].firstVertex;
            var lastVertexIndex = firstVertexIndex + tileMeshData.SubMeshDescriptors[subMeshIndex].vertexCount;
            
            var correctBorderTangentsAndNormals = tileMeshData.SubmeshData[subMeshIndex].NeedCorrectBorderTangentsAndNormals;
            
            for (var i = firstTriangleIndex; i < lastTriangleIndex; i += 3)
            {
                meshCollection.Triangles.Add(verticesCount + tileMeshData.Triangles[i] - firstVertexIndex);
                meshCollection.Triangles.Add(verticesCount + tileMeshData.Triangles[i + secondVertex] - firstVertexIndex);
                meshCollection.Triangles.Add(verticesCount + tileMeshData.Triangles[i + thirdVertex] - firstVertexIndex);
            }
            
            var mirrorScale = mirror ? -1 : 1;
            var offset = index * RoadDataAsset.TileLength;

            for (var i = firstVertexIndex; i < lastVertexIndex; i++)
            {
                var sharedVertex = tileMeshData.Vertices[i];
                var vertexSplineZ = offset + sharedVertex.z;
                var pathProgress = vertexSplineZ * splineScale;
                
                var fromPathProgress = (vertexSplineZ - vertexSplineZ % RoadDataAsset.HalfTileLength) * splineScale;
                var fromPathPoint = (Vector3)spline.EvaluatePosition(fromPathProgress);
                var fromPathRotation = Quaternion.LookRotation(spline.EvaluateTangent(fromPathProgress));
                var fromVertexPoint = fromPathPoint + fromPathRotation * new Vector3(mirrorScale * (sharedVertex.x + tileLOffset), 0, 0);
                
                var toPathProgress = (vertexSplineZ - vertexSplineZ % RoadDataAsset.HalfTileLength + RoadDataAsset.HalfTileLength) * splineScale;
                var toPathPoint = (Vector3)spline.EvaluatePosition(toPathProgress);
                var toPathRotation = Quaternion.LookRotation(spline.EvaluateTangent(toPathProgress));
                var toVertexPoint = toPathPoint + toPathRotation * new Vector3(mirrorScale * (sharedVertex.x + tileLOffset), 0, 0);

                var halfTileProgress = Mathf.InverseLerp(fromPathProgress, toPathProgress, pathProgress);
                var vertexPosition = Vector3.Lerp(fromVertexPoint, toVertexPoint, halfTileProgress);
                
                if (!isCollider) vertexPosition.y += sharedVertex.y;
                meshCollection.Vertices.Add(vertexPosition);

                var normal = tileMeshData.Normals[i];
                if (mirror)
                {
                    normal.x *= -1;
                }
                
                var pathRotation = Quaternion.LookRotation(spline.EvaluateTangent(pathProgress));
                meshCollection.Normals.Add(pathRotation * normal);
                meshCollection.Tangents.Add(tileMeshData.Tangents[i]);
                foreach ((int uvIndex, var tileUvs) in tileMeshData.Uvs)
                {
                    if (!meshCollection.UVs.TryGetValue(uvIndex, out var uvs))
                    {
                        uvs = new List<Vector2>();
                        meshCollection.UVs.Add(uvIndex, uvs);
                    }
                    uvs.Add(tileUvs[i]);
                }
                meshCollection.UvToUnwrap.Add(new Vector2(splineLength * pathProgress, sharedVertex.x * mirrorScale));
                meshCollection.Colors.Add(i < tileMeshData.Colors.Length ? tileMeshData.Colors[i] : default);
                if (correctBorderTangentsAndNormals) meshCollection.VerticesToCorrectNormalsAndTangents.Add(verticesCount + i);
            }
        }
    }
}