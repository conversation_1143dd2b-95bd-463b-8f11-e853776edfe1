using Builder.Helper;
using System.Collections.Generic;
using System.Linq;
using Unity.Collections;
using Unity.Jobs;
using UnityEngine;
using UnityEngine.Rendering;
using Utils.Builder.Scripts.Helper;
using Utils.Editor;

namespace Builder.Editor.Helper
{
    public static class HelperBuilderExportRoadChunksSplitting
    {
        public static Dictionary<Vector2Int, List<(Material material, bool castShadows, Mesh chunkMesh)>> Execute(ControllerBuilder builder,
            List<(Material material, bool castShadows, Mesh mesh)> combinedRoads)
        {
            var result = new Dictionary<Vector2Int, List<(Material material, bool castShadows, Mesh chunkMesh)>>();

            foreach ((var material, bool castShadows, var mesh) in combinedRoads)
            {
                using var meshDataArray = Mesh.AcquireReadOnlyMeshData(mesh);
                var meshData = meshDataArray[0];

                using var meshVertices = new NativeArray<Vector3>(meshData.vertexCount, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
                using var meshNormals = new NativeArray<Vector3>(meshData.vertexCount, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
                using var meshTangents = new NativeArray<Vector4>(meshData.vertexCount, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
                using var meshColors = new NativeArray<Color>(meshData.vertexCount, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
                var meshUVsByIndex = new Dictionary<int, NativeArray<Vector2>>();
                using var meshIndices = new NativeArray<int>((int)mesh.GetIndexCount(0), Allocator.TempJob, NativeArrayOptions.UninitializedMemory);

                var attributes = new List<VertexAttributeDescriptor>(mesh.vertexAttributeCount);
                mesh.GetVertexAttributes(attributes);
                var attributesHash = attributes.Select(a => a.attribute).ToHashSet();

                meshData.GetVertices(meshVertices);
                meshData.GetNormals(meshNormals);
                meshData.GetTangents(meshTangents);

                if (attributesHash.Contains(VertexAttribute.Color)) meshData.GetColors(meshColors);

                for (int i = 0; i < 8; i++)
                {
                    if (attributesHash.Contains(VertexAttribute.TexCoord0 + i))
                    {
                        var uvs = new NativeArray<Vector2>(meshData.vertexCount, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
                        meshData.GetUVs(i, uvs);
                        meshUVsByIndex[i] = uvs;
                    }
                }

                meshData.GetIndices(meshIndices, 0);

                for (int chunkIndex = 0; chunkIndex < builder.Terrain.CellsChunksCount; chunkIndex++)
                {
                    var chunkCoords = BuilderTerrainPosition.GetCoordsByIndex(chunkIndex, builder.Terrain.CellsChunksGridSize);
                    var chunkRect = BuilderTerrainPosition.GetChunkRect(builder.Terrain, chunkCoords);
                    var chunkPosition = BuilderTerrainPosition.GetPositionByUV(BuilderTerrainPosition.GetUVByCoords(chunkCoords, builder.Terrain.CellsChunksGridSize), builder.Terrain.TerrainScale);

                    string chunkName = $"chunk({chunkCoords.x},{chunkCoords.y})";

                    using var meshUsingData = meshVertices.AsReadOnly().GetUsingData(meshIndices.AsReadOnly(), chunkRect, HelperBuilderMeshClipping.TriangleEntryLevel.AnyVertex);


                    if (meshUsingData.UsingVerticesArray.Length > 0)
                    {
                        var chunkMesh = new Mesh
                        {
                            name = FileName.Combine(chunkName, mesh.name),
                        };

                        meshUsingData.GetUsingVertexData(meshVertices.AsReadOnly(), out var chunkVertices);
                        new HelperBuilderTerrainVertices.TransformVerticesJob
                            {
                                TRS = Matrix4x4.TRS(-chunkPosition, Quaternion.identity, Vector3.one),
                                Vertices = chunkVertices,
                            }
                            .Schedule(chunkVertices.Length, 256)
                            .Complete();
                        chunkMesh.SetVertices(chunkVertices);
                        chunkVertices.Dispose();

                        meshUsingData.GetUsingVertexData(meshNormals.AsReadOnly(), out var chunkNormals);
                        chunkMesh.SetNormals(chunkNormals);
                        chunkNormals.Dispose();

                        meshUsingData.GetUsingVertexData(meshTangents.AsReadOnly(), out var chunkTangents);
                        chunkMesh.SetTangents(chunkTangents);
                        chunkTangents.Dispose();

                        if (attributesHash.Contains(VertexAttribute.Color))
                        {
                            meshUsingData.GetUsingVertexData(meshColors.AsReadOnly(), out var chunkColors);
                            chunkMesh.SetColors(chunkColors);
                            chunkColors.Dispose();
                        }

                        meshUsingData.GetUsingIndices(meshIndices.AsReadOnly(), out var chunkIndices);
                        chunkMesh.SetIndices(chunkIndices, MeshTopology.Triangles, 0);
                        chunkIndices.Dispose();

                        foreach ((int uvIndex, var uvs) in meshUVsByIndex)
                        {
                            meshUsingData.GetUsingVertexData(uvs.AsReadOnly(), out var chunkUVs);
                            chunkMesh.SetUVs(uvIndex, chunkUVs);
                            chunkUVs.Dispose();
                        }

                        chunkMesh.RecalculateBounds();

                        if (!result.TryGetValue(chunkCoords, out var meshesByChunk))
                        {
                            meshesByChunk = new List<(Material material, bool castShadows, Mesh chunkMesh)>();
                            result.Add(chunkCoords, meshesByChunk);
                        }

                        meshesByChunk.Add((material, castShadows, chunkMesh));
                    }
                }

                foreach (var meshUV in meshUVsByIndex.Values)
                {
                    meshUV.Dispose();
                }
            }

            return result;
        }
    }
}