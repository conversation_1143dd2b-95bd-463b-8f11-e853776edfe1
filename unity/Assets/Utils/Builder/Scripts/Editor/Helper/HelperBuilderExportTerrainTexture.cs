using Builder.Helper;
using Core;
using Game.Extensions.Editor;
using System.Collections.Generic;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using Utils.Builder.Scripts.Features;

namespace Builder.Editor.Helper
{
    public static class HelperBuilderExportTerrainTexture
    {
        public static Texture2D GetBakedTexture(ControllerBuilder builder, params (DebugMaterialMode materialMode, float3 colorsChannelsFactors)[] renderModes)
        {
            int size = (int)builder.ExportTerrainLodMapsSize;
            var mapTexture = new Texture2D(size, size, TextureFormat.RGB24, true);

            var pixels = new Color[size * size];

            foreach (var (materialMode, colorsChannelsFactors) in renderModes)
            {
                var bakedPixels = GetBakedPixelsByRenderMode(builder, size, materialMode);
                
                for (int i = 0; i < pixels.Length; i++)
                {
                    var p1 = pixels[i];
                    var p2 = bakedPixels[i];

                    for (int channelIndex = 0; channelIndex < 3; channelIndex++)
                    {
                        p1[channelIndex] = Mathf.Lerp(p1[channelIndex], p2[channelIndex], colorsChannelsFactors[channelIndex]);
                    }

                    p1.a = 1f;

                    pixels[i] = p1;
                }
            }

            mapTexture.SetPixels(pixels);

            return mapTexture;
        }

        private static Color[] GetBakedPixelsByRenderMode(ControllerBuilder builder, int size, DebugMaterialMode materialMode)
        {
            int increasedSize = size * 4;

            float worldSize = builder.Terrain.TerrainScale;
            float worldOffset = builder.Terrain.TerrainScale / 2;

            float orthographicSize = worldSize / 2;
            float cameraPositionXZ = orthographicSize - worldOffset;

            var bakingCamera = new GameObject().AddComponent<Camera>();

            var cameraTransform = bakingCamera.transform;
            cameraTransform.position = new Vector3(cameraPositionXZ, 200, cameraPositionXZ);
            cameraTransform.forward = Vector3.down;

            GetMaterialsAndCameraRenderers(builder, out var materials, out var cameraRendererViews);

            foreach (var cameraRendererView in cameraRendererViews) cameraRendererView.EnableToCameraRender();

            bakingCamera.cullingMask = LayerId.ToMask(LayerId.TerrainBuilderCameraRenderLayer);

            var renderTexture = new RenderTexture(increasedSize, increasedSize, 24, RenderTextureFormat.ARGB32)
            {
                wrapMode = TextureWrapMode.Clamp,
            };

            HelperBuilderCamera.SetupTerrainTextureBlitCamera(bakingCamera, 0.01f, 400, orthographicSize);

            using (new GizmosToggler(false))
            {
                MaterialEditorExtension.ChangeMaterialsDebugMode(materials, materialMode);
                bakingCamera.targetTexture = renderTexture;
                bakingCamera.Render();
                MaterialEditorExtension.ChangeMaterialsDebugMode(materials, DebugMaterialMode.None);
            }

            foreach (var cameraRendererView in cameraRendererViews) cameraRendererView.DisableToCameraRender();

            var bakedTexture = new Texture2D(increasedSize, increasedSize, TextureFormat.RGBA32, true);
            RenderTexture.active = renderTexture;
            bakedTexture.ReadPixels(new Rect(0, 0, increasedSize, increasedSize), 0, 0);
            bakedTexture.Apply();
            RenderTexture.active = null;

            var bakedPixels = bakedTexture.GetPixels(2);

            if (materialMode is DebugMaterialMode.NormalTangentSpace or DebugMaterialMode.Smoothness or DebugMaterialMode.AmbientOcclusion)
            {
                for (int i = 0; i < bakedPixels.Length; i++)
                {
                    bakedPixels[i] = bakedPixels[i].linear;
                }
            }

            Object.DestroyImmediate(renderTexture);
            Object.DestroyImmediate(bakingCamera.gameObject);
            Object.DestroyImmediate(bakedTexture);

            return bakedPixels;
        }

        private static void GetMaterialsAndCameraRenderers(ControllerBuilder builder, out HashSet<Material> materials, out HashSet<ICameraRendererView> cameraRendererViews)
        {
            materials = new HashSet<Material>();
            cameraRendererViews = new HashSet<ICameraRendererView>();

            foreach (var roadData in builder.RoadDataList.List)
            {
                if (!roadData.Model.HasMeshRender) continue;

                var view = builder.RoadViewList[roadData];
                cameraRendererViews.Add(view);
                foreach (var (_, material, _, _, _) in view.GetMeshes()) materials.Add(material);
            }

            foreach (var crossroadData in builder.CrossroadDataList.List)
            {
                if (!crossroadData.Model.TerrainSticking) continue;

                var view = builder.CrossroadViewList[crossroadData];
                cameraRendererViews.Add(view);
                foreach (var material in view.GetMaterials()) materials.Add(material);
            }

            for (int chunkIndex = 0; chunkIndex < builder.TerrainChunkViewList.Length; chunkIndex++)
            {
                var view = builder.TerrainChunkViewList[chunkIndex];
                cameraRendererViews.Add(view);
                materials.Add(view.GetMaterial());
            }

            foreach (var view in builder.PlotSpotTerrainViewList.List)
            {
                cameraRendererViews.Add(view);
                materials.Add(view.GetMaterial());
            }

            foreach (var coverageData in builder.CoverageDataList.List)
            {
                var view = builder.CoverageViewList[coverageData];
                cameraRendererViews.Add(view);
                materials.Add(coverageData.Model.Material);
            }
        }
    }
}