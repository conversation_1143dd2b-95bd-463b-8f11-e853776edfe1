using Builder.Helper;
using Game.Extensions.Editor;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using Utils.Builder.Scripts;
using Utils.Builder.Scripts.Features.Crossroad;
using Utils.Builder.Scripts.Features.Road;
using Utils.Editor;
using Object = UnityEngine.Object;

namespace Builder.Editor.Helper
{
    public static class HelperBuilderExportRoad
    {
        public static void Export(ControllerBuilder builder, string builderExportPath, string editorExportPath, Transform parent, Dictionary<Vector2Int, LODGroup> lodGroups, Material terrainMaterialLOD)
        {
            if (builder.RoadsUVUnvrapIndex >= 0)
            {
                HelperBuilderExportRoadSystemPacking.UnwrapUV2(builder);
            }

            string meshesExportPath = Path.Combine(builderExportPath, "Road");
            Directory.CreateDirectory(meshesExportPath);
            var meshesDirectoryCleaner = new DirectoryCleaner(meshesExportPath);

            var roadsGo = new GameObject("roads")
            {
                transform =
                {
                    parent = parent,
                },
            };
            var roadsMapGo = new GameObject("roads_map")
            {
                transform =
                {
                    parent = parent,
                },
            };

            try
            {
                AssetDatabase.StartAssetEditing();

                var roads = builder.RoadDataList.List;
                for (int i = 0; i < roads.Count; i++)
                {
                    if (!roads[i].Model.HasMeshRender) continue;
                    SaveMapRoad(roads[i], i, meshesExportPath, roadsMapGo.transform, meshesDirectoryCleaner);
                }

                foreach (var crossroadData in builder.CrossroadDataList.List)
                {
                    SaveCrossroad(crossroadData.Model, roadsGo.transform);
                }

                var emptyList = new List<(Material material, bool castShadows, Mesh chunkMesh)>();
                var combinedChunkedRoadsMeshes = new Dictionary<Vector2Int, List<(Material material, bool castShadows, Mesh chunkMesh)>>();
                var combinedChunkedRoadsColliders = new Dictionary<Vector2Int, List<(Material material, bool castShadows, Mesh chunkMesh)>>();

                if (builder.ExportTerrainLodLevels.Any(lodLevel => !lodLevel.RoadsUsesMaterialLOD))
                {
                    var combinedRoadsMeshes = HelperBuilderExportRoadCombining.GetCombinedMeshes(builder);
                    var mesh = new Mesh() { name = "Combine" };
                    mesh.CombineMeshes(combinedRoadsMeshes.Select(tupple => new CombineInstance()
                    {
                        mesh = tupple.mesh,
                        transform = Matrix4x4.identity
                    }).ToArray());
                    
                    string roadNameAsset = FileName.WithExtension("roads_combine", AssetType.Mesh);
                    string roadPath = Path.Combine(editorExportPath, roadNameAsset);
                    MeshEditorExtensions.CreateMeshAssetIfChanged(roadPath, mesh, 0.01f);

                    combinedChunkedRoadsMeshes = HelperBuilderExportRoadChunksSplitting.Execute(builder, combinedRoadsMeshes);
                }

                if (builder.ExportTerrainLodLevels.Any(lodLevel => lodLevel.RoadsUsesMaterialLOD))
                {
                    var combinedRoadsColliders = HelperBuilderExportRoadCombining.GetCombinedColliders(builder, terrainMaterialLOD);
                    combinedChunkedRoadsColliders = HelperBuilderExportRoadChunksSplitting.Execute(builder, combinedRoadsColliders);
                }

                for (int chunkIndex = 0; chunkIndex < builder.Terrain.CellsChunksCount; chunkIndex++)
                {
                    var chunkCoords = BuilderTerrainPosition.GetCoordsByIndex(chunkIndex, builder.Terrain.CellsChunksGridSize);
                    var chunkUV = BuilderTerrainPosition.GetUVByCoords(chunkCoords, builder.Terrain.CellsChunksGridSize);
                    var chunkPosition = BuilderTerrainPosition.GetPositionByUV(chunkUV, builder.Terrain.TerrainScale);

                    var chunkMeshData = combinedChunkedRoadsMeshes.GetValueOrDefault(chunkCoords, emptyList);
                    var chunkColliderData = combinedChunkedRoadsColliders.GetValueOrDefault(chunkCoords, emptyList);
                    
                    if (chunkMeshData.Count == 0 && chunkColliderData.Count == 0) continue;

                    var roadsRenderers = new List<Renderer>();
                    var roadsLODRenderers = new List<Renderer>();
                    GameObject chunkParent;

                    if (lodGroups.TryGetValue(chunkCoords, out var lodGroup))
                    {
                        chunkParent = lodGroup.gameObject;
                    }
                    else
                    {
                        string chunkName = $"chunk({chunkCoords.x},{chunkCoords.y})";
                        var chunkObject = new GameObject(chunkName);
                        chunkObject.transform.SetParent(roadsGo.transform, false);
                        chunkObject.transform.position = chunkPosition;

                        chunkParent = chunkObject;
                        
                        lodGroup = chunkObject.AddComponent<LODGroup>();
                        lodGroup.SetLODs(builder.ExportTerrainLodLevels.Select(level => new LOD(level.DisableWeight, Array.Empty<Renderer>())).ToArray());
                        lodGroup.size = builder.Terrain.TerrainScale / builder.Terrain.CellsChunksGridSize;
                    }

                    foreach ((var material, bool castShadows, var chunkMesh) in chunkMeshData)
                    {
                        SaveCombinedRoad(builder, material, castShadows, chunkMesh, meshesExportPath, chunkParent.transform, meshesDirectoryCleaner, roadsRenderers, "m");
                    }

                    foreach ((var material, bool castShadows, var chunkMesh) in chunkColliderData)
                    {
                        SaveCombinedRoad(builder, material, castShadows, chunkMesh, meshesExportPath, chunkParent.transform, meshesDirectoryCleaner, roadsLODRenderers, "c");
                    }

                    var terrainLods = lodGroup.GetLODs();
                    for (int lodIndex = 0; lodIndex < builder.ExportTerrainLodLevels.Length; lodIndex++)
                    {
                        if (!builder.ExportTerrainLodLevels[lodIndex].RoadsUsesMaterialLOD)
                        {
                            var renderers = new List<Renderer>(terrainLods[lodIndex].renderers);
                            renderers.AddRange(roadsRenderers);
                            terrainLods[lodIndex].renderers = renderers.ToArray();
                        }
                        else
                        {
                            var renderers = new List<Renderer>(terrainLods[lodIndex].renderers);
                            renderers.AddRange(roadsLODRenderers);
                            terrainLods[lodIndex].renderers = renderers.ToArray();
                        }
                    }

                    lodGroup.SetLODs(terrainLods);
                    lodGroup.size = builder.Terrain.TerrainScale / builder.Terrain.CellsChunksGridSize;
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }

            meshesDirectoryCleaner.RemoveAssets();
        }


        private static void SaveMapRoad(RoadController road, int index, string exportPath, Transform mapParent, DirectoryCleaner meshesDirectoryCleaner)
        {
            var roadMapMesh = HelperBuilderRoadMesh.CreateMapMesh(road.Model);
            string roadMapName = FileName.Combine("road_map", index);
            string roadMapNameAsset = FileName.WithExtension(roadMapName, AssetType.Mesh);
            string roadMapPath = Path.Combine(exportPath, roadMapNameAsset);
            meshesDirectoryCleaner.DontRemove(roadMapNameAsset);
            MeshEditorExtensions.CreateMeshAssetIfChanged(roadMapPath, roadMapMesh, 0.01f);
            roadMapMesh = AssetDatabase.LoadAssetAtPath<Mesh>(roadMapPath);

            var mapGo = new GameObject(roadMapName)
            {
                transform =
                {
                    parent = mapParent,
                },
            };
            var roadMapEditor = mapGo.AddComponent<RoadMapEditor>();
            roadMapEditor.RoadIndex = road.Model.RoadMapIndex;
            mapGo.AddComponent<MeshFilter>().sharedMesh = roadMapMesh;
        }

        private static void SaveCrossroad(CrossroadDataAsset crossroad, Transform parent)
        {
            var copy = Object.Instantiate(crossroad.Instance.Prefab, parent);
            copy.transform.SetPositionAndRotation(crossroad.Position, crossroad.Rotation);
            if (crossroad.TerrainSticking)
            {
                Object.DestroyImmediate(copy.GetComponent<MeshRenderer>());
                Object.DestroyImmediate(copy.GetComponent<MeshFilter>());
            }
        }

        private static void SaveCombinedRoad(ControllerBuilder builder, Material material, bool castShadows, Mesh mesh, string exportPath, Transform parent, DirectoryCleaner meshesDirectoryCleaner, List<Renderer> roadsRenderers,
            string postfix)
        {
            string roadName = FileName.Combine(mesh.name, postfix);
            string roadNameAsset = FileName.WithExtension(roadName, AssetType.Mesh);
            string roadPath = Path.Combine(exportPath, roadNameAsset);
            meshesDirectoryCleaner.DontRemove(roadNameAsset);
            mesh.UploadMeshData(true);
            MeshUtility.Optimize(mesh);
            MeshEditorExtensions.CreateMeshAssetIfChanged(roadPath, mesh, 0.01f);
            var resultMesh = AssetDatabase.LoadAssetAtPath<Mesh>(roadPath);

            var roadGo = new GameObject(resultMesh.name)
            {
                transform =
                {
                    parent = parent,
                    localPosition = Vector3.zero,
                    localRotation = Quaternion.identity,
                },
            };
            GameObjectUtility.SetStaticEditorFlags(roadGo, builder.Settings.DefualtStaticEditorFlags);
            var meshRenderer = roadGo.AddComponent<MeshRenderer>();
            meshRenderer.sharedMaterial = material;
            meshRenderer.shadowCastingMode = castShadows ? ShadowCastingMode.On : ShadowCastingMode.Off;
            meshRenderer.lightProbeUsage = builder.Settings.DefaultLightProbeUsage;

            var meshFilter = roadGo.AddComponent<MeshFilter>();
            meshFilter.sharedMesh = resultMesh;

            var meshCollider = roadGo.AddComponent<MeshCollider>();
            meshCollider.sharedMesh = resultMesh;

            roadsRenderers.Add(meshRenderer);
        }
    }
}