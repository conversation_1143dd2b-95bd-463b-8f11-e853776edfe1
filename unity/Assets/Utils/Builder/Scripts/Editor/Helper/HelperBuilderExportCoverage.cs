using Game.Extensions.Editor;
using System.Collections.Generic;
using System.IO;
using Unity.Collections;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityMeshSimplifier;
using Utils.Builder.Scripts.Features.Coverage;
using Utils.Builder.Scripts.Helper;
using Utils.Editor;

namespace Builder.Editor.Helper
{
    public static class HelperBuilderExportCoverage
    {
        public static void Export(ControllerBuilder builder, string exportPath, string editorExportPath, GameObject exportedGameObject)
        {
            string meshesExportPath = Path.Combine(exportPath, "Coverage");
            Directory.CreateDirectory(meshesExportPath);
            var directoryCleaner = new DirectoryCleaner(meshesExportPath);
            
            string comparerExportPath = Path.Combine(editorExportPath, "Coverage");
            Directory.CreateDirectory(comparerExportPath);
            var comparerDirectoryCleaner = new DirectoryCleaner(comparerExportPath);
            
            try
            {
                AssetDatabase.StartAssetEditing();
                
                for(int i = 0; i < builder.CoverageDataList.List.Count; i ++)
                {
                    var coverageData = builder.CoverageDataList.List[i];
                    SaveCoverage(builder, coverageData, builder.CoverageViewList[coverageData], i, directoryCleaner, meshesExportPath, comparerDirectoryCleaner, comparerExportPath, exportedGameObject.transform);
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }
            
            directoryCleaner.RemoveAssets();
        }

        private static void SaveCoverage(ControllerBuilder builder, CoverageController coverageController, CoverageView coverageView, int index, DirectoryCleaner directoryCleaner, string meshesExportPath,
            DirectoryCleaner comparerDirectoryCleaner, string comparerExportPath, Transform parent)
        {
            if (!coverageController.Model.NeedRenderer) return;
            
            var gameObject = new GameObject(FileName.Combine("coverage", index));
            gameObject.transform.SetParent(parent);
            GameObjectUtility.SetStaticEditorFlags(gameObject, builder.Settings.DefualtStaticEditorFlags);

            var lodLevels = builder.ExportTerrainLodLevels;
            
            var lods = new List<LOD>();
            var meshes = new Mesh[lodLevels.Length];

            var meshForCompare = Object.Instantiate(coverageView.GetMesh());

            var meshVertices = new NativeArray<Vector3>(meshForCompare.vertices, Allocator.TempJob);
            var meshIndices = new NativeArray<int>(meshForCompare.GetIndices(0), Allocator.TempJob);
            var meshUVs = new NativeArray<Vector2>(meshForCompare.uv, Allocator.TempJob);
            var meshNormals = new NativeArray<Vector3>(meshForCompare.normals, Allocator.TempJob);
            var meshTangents = new NativeArray<Vector4>(meshForCompare.tangents, Allocator.TempJob);
            
            var (sortedVertices, sortedIndices, sortedNormals, sortedTangents, sortedUVs) = HelperBuilderSameMesh.SortMeshVertices(meshVertices, meshIndices, meshNormals, meshTangents, meshUVs, Allocator.TempJob);
            
            meshForCompare.SetVertices(sortedVertices);
            meshForCompare.SetIndices(sortedIndices, MeshTopology.Triangles, 0);
            meshForCompare.SetUVs(0, sortedUVs);
            meshForCompare.SetNormals(sortedNormals);
            meshForCompare.SetTangents(sortedTangents);
            
            sortedVertices.Dispose();
            sortedIndices.Dispose();
            sortedNormals.Dispose();
            sortedTangents.Dispose();
            sortedUVs.Dispose();
            
            string meshForCompareName = FileName.Combine("coverage", index, "compare");
            string meshForCompareFileName = FileName.WithExtension(meshForCompareName, AssetType.Mesh);
            string meshForComparePath = Path.Combine(comparerExportPath, meshForCompareFileName);
            
            comparerDirectoryCleaner.DontRemove(meshForCompareFileName);
            bool isSameMesh = !MeshEditorExtensions.CreateMeshAssetIfChanged(meshForComparePath, meshForCompare, 0.1f);
            
            var meshLod0 = Object.Instantiate(meshForCompare);
            meshLod0.name = FileName.Combine("coverage", index, "quality", 100);
            MeshEditorExtensions.OptimizeMesh(meshLod0, builder.ExportTerrainMeshCompression);
                
            if (meshLod0.vertexCount == 0) return;

            meshes[0] = meshLod0;

            for (int i = 0; i < lodLevels.Length; i++)
            {
                float quality = lodLevels[i].MeshQuality;

                string meshName = FileName.Combine("coverage", index, "quality", (int)(quality * 100));
                string meshFileName = FileName.WithExtension(meshName, AssetType.Mesh);
                string meshPath = Path.Combine(meshesExportPath, meshFileName);

                if (isSameMesh && AssetDatabase.LoadAssetAtPath<Mesh>(meshPath) is { } assetMesh)
                {
                    meshes[i] = assetMesh;
                }
                else
                {
                    if (i > 0)
                    {
                        meshes[i] = SimplifyMesh(meshLod0, quality);
                    }

                    meshes[i].RecalculateTangents();
                    meshes[i].RecalculateBounds();
                    meshes[i].RecalculateUVDistributionMetrics();
                    meshes[i].name = meshName;
                    
                    meshes[i].UploadMeshData(true);
                    MeshUtility.Optimize(meshes[i]);
                    AssetDatabase.CreateAsset(meshes[i], meshPath);
                    meshes[i] = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
                }
                
                directoryCleaner.DontRemove(meshFileName);
            }

            var meshCollider = gameObject.AddComponent<MeshCollider>();
            meshCollider.sharedMesh = meshes[0];

            for (int i = 0; i < meshes.Length; i++)
            {
                if (i > 0 && meshes[i] == meshes[i - 1])
                {
                    var lod = lods[^1];
                    lod.screenRelativeTransitionHeight = lodLevels[i].DisableWeight;
                    lods[^1] = lod;
                    continue;
                }
            
                var lodObject = new GameObject(meshes[i].name);
                lodObject.transform.SetParent(gameObject.transform, false);
                GameObjectUtility.SetStaticEditorFlags(lodObject, builder.Settings.DefualtStaticEditorFlags);

                var meshFilter = lodObject.AddComponent<MeshFilter>();
                meshFilter.sharedMesh = meshes[i];
        
                var meshRenderer = lodObject.AddComponent<MeshRenderer>();
                meshRenderer.sharedMaterial = coverageController.Model.Material;
                meshRenderer.lightProbeUsage = builder.Settings.DefaultLightProbeUsage;
                meshRenderer.shadowCastingMode = coverageController.Model.CastShadows ? ShadowCastingMode.On : ShadowCastingMode.Off;
        
                lods.Add(new LOD(lodLevels[i].DisableWeight, new Renderer[] { meshRenderer }));
            }
        
            var lodGroup = gameObject.AddComponent<LODGroup>();
            lodGroup.SetLODs(lods.ToArray());
            lodGroup.size = builder.Terrain.TerrainScale / builder.Terrain.CellsChunksGridSize;
        }

        private static Mesh SimplifyMesh(Mesh meshForSimplify, float meshQuality)
        {
            var meshSimplifier = new MeshSimplifier(meshForSimplify);
            var options = SimplificationOptions.Default;
            options.PreserveBorderEdges = true;
            options.PreserveSurfaceCurvature = true;
            options.Agressiveness = 5;
            meshSimplifier.SimplificationOptions = options;
            meshSimplifier.SimplifyMesh(meshQuality);
            var simplifiedMesh = meshSimplifier.ToMesh();
            return simplifiedMesh;
        }
    }
}