using Builder.Scriptable;
using Core;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.Rendering;
using Object = UnityEngine.Object;

namespace Utils.Builder.Scripts.Features.Crossroad
{
    public class CrossroadView : IDisposable, ICameraRendererView
    {
        public event Action<Rect> BecameDirty;

        private readonly CrossroadController _controller;

        private readonly GameObject _gameObject;
        private readonly MeshRenderer _meshRenderer;
        private readonly MeshFilter _meshFilter;
        private readonly MeshCollider _meshCollider;
        private readonly List<Vector2> _uvsToUnwrap;

        private bool _isDirty = true;

        public CrossroadView(CrossroadController controller, BuilderSettingsViewDescription settings, Transform parent)
        {
            _controller = controller;

            _gameObject = Object.Instantiate(controller.Model.Instance.Prefab, parent);
            _gameObject.name = _controller.Model.name;
            GameObjectUtility.SetStaticEditorFlags(_gameObject, settings.DefualtStaticEditorFlags);

            _meshRenderer = _gameObject.GetComponent<MeshRenderer>();
            _meshFilter = _gameObject.GetComponent<MeshFilter>();
            _meshCollider = _gameObject.GetComponent<MeshCollider>();
            _meshCollider.enabled = false;
            _meshCollider.enabled = true;
            
            _meshCollider.hideFlags = HideFlags.HideInHierarchy;
            _meshRenderer.lightProbeUsage = settings.DefaultLightProbeUsage;
            _meshFilter.sharedMesh = Object.Instantiate(_meshFilter.sharedMesh);
            if (_meshFilter.sharedMesh.tangents.Length == 0)
            {
                _meshFilter.sharedMesh.RecalculateTangents();
            }
            _uvsToUnwrap = _meshFilter.sharedMesh.vertices.Select(v => v.Vector2()).ToList();
            _meshFilter.sharedMesh.name = _controller.Model.name;
            
            _controller.Changed += OnChanged;
        }

        private void OnChanged(Rect changeRect)
        {
            if (_controller.GetRect().Intersect(changeRect))
            {
                _isDirty = true;
                BecameDirty?.Invoke(changeRect);
            }
        }

        public Matrix4x4 GetTransform()
        {
            ClearDirty();
            return _gameObject.transform.localToWorldMatrix;
        }

        public (Mesh mesh, List<Vector2> uvsToUnwrap) GetMesh()
        {
            ClearDirty();
            return (_meshFilter.sharedMesh, _uvsToUnwrap);
        }

        public Material[] GetMaterials()
        {
            ClearDirty();
            return _meshRenderer.sharedMaterials;
        }

        public Mesh GetCollider()
        {
            ClearDirty();
            return _meshCollider.sharedMesh;
        }

        public int GetColliderInstanceID()
        {
            ClearDirty();
            return _meshCollider.GetInstanceID();
        }

        public void ClearDirty()
        {
            if (_isDirty)
            {
                _isDirty = false;
                Profiler.BeginSample(GetType().Name + ".Recalculate()");
                Recalculate();
                Profiler.EndSample();
            }
        }

        private void Recalculate()
        {
            int layer = _controller.Model.TerrainSticking ? LayerId.TerrainBuilderDefaultLayer : 0;
            if (_gameObject.layer != layer) _gameObject.layer = layer;
            
            _gameObject.transform.position = _controller.Model.Position;
            _gameObject.transform.rotation = _controller.Model.Rotation;
            _meshCollider.enabled = false;
            _meshCollider.enabled = true;
            _meshRenderer.shadowCastingMode = _controller.Model.CastShadows ? ShadowCastingMode.On : ShadowCastingMode.Off;
        }
        
        public void Dispose()
        {
            _controller.Changed -= OnChanged;

            Object.DestroyImmediate(_meshFilter.sharedMesh);
            Object.DestroyImmediate(_gameObject);
        }

        public void EnableToCameraRender()
        {
            _gameObject.layer = LayerId.TerrainBuilderCameraRenderLayer;
        }

        public void DisableToCameraRender()
        {
            _gameObject.layer = LayerId.TerrainBuilderDefaultLayer;
        }
    }
}