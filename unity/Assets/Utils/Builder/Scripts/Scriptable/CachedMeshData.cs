using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;

namespace Builder.Scriptable
{
    [Serializable]
    public class CachedMeshData
    {
        public CachedSubmeshData[] SubmeshData;
        public Mesh Mesh;
        
        private Mesh _cachedMesh;
        
        public Vector3[] Vertices { get; private set; }
        public Vector3[] Normals { get; private set; }
        public Vector4[] Tangents { get; private set; }
        public Dictionary<int, List<Vector2>> Uvs { get; private set; } = new();
        public int[] Triangles { get; private set; }
        public Color[] Colors { get; private set; }
        public SubMeshDescriptor[] SubMeshDescriptors { get; private set; }


        public void CacheData()
        {
            if (_cachedMesh == Mesh && Vertices is { Length: > 0 })
            {
                return;
            }
            _cachedMesh = Mesh;
            
            DisposeData();

            if (Mesh == null)
            {
                return;
            }

            Vertices = Mesh.vertices;
            Normals = Mesh.normals;
            Tangents = Mesh.tangents;
            for (int i = 0; i < 8; i++)
            {
                if (Mesh.HasVertexAttribute(VertexAttribute.TexCoord0 + i))
                {
                    var uvs = new List<Vector2>();
                    Mesh.GetUVs(i, uvs);
                    Uvs.Add(i, uvs);
                }

            }
            Triangles = Mesh.triangles;
            Colors = Mesh.colors;
            SubMeshDescriptors = new SubMeshDescriptor[Mesh.subMeshCount];

            for (var subMeshIndex = 0; subMeshIndex < SubMeshDescriptors.Length; subMeshIndex++)
            {
                var subMesh = Mesh.GetSubMesh(subMeshIndex);
                SubMeshDescriptors[subMeshIndex] = subMesh;
            }
        }

        public void DisposeData()
        {
            Vertices = Array.Empty<Vector3>();
            Normals = Array.Empty<Vector3>();
            Uvs.Clear();
            Triangles = Array.Empty<int>();
            Colors = Array.Empty<Color>();
            SubMeshDescriptors = Array.Empty<SubMeshDescriptor>();
        }
    }
}