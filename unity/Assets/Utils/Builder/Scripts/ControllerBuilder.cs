using Builder.Scriptable;
using Core;
using System;
using System.IO;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.SceneManagement;
using Utils.AssetsUtilityEditor;
using Utils.Builder.Scripts;
using Utils.Builder.Scripts.Features.BorderEdges;
using Utils.Builder.Scripts.Features.Coastlines;
using Utils.Builder.Scripts.Features.Coverage;
using Utils.Builder.Scripts.Features.Crossroad;
using Utils.Builder.Scripts.Features.DirtyRectGizmos;
using Utils.Builder.Scripts.Features.Foundation;
using Utils.Builder.Scripts.Features.Grass;
using Utils.Builder.Scripts.Features.Hole;
using Utils.Builder.Scripts.Features.PlotSpot;
using Utils.Builder.Scripts.Features.Road;
using Utils.Builder.Scripts.Features.Stamp;
using Utils.Builder.Scripts.Features.Terrain;
using Utils.Builder.Scripts.Features.TerrainBrush;
using Utils.Builder.Scripts.Features.TerrainLayers;
using Utils.Builder.Scripts.Features.UndoRedo;
using Utils.Builder.Scripts.Features.WaterZone;
using Utils.Builder.Scripts.InstancesGenerator;
using Utils.BuilderPlotSpots;
using Utils.Editor;
using TerrainData = Utils.Builder.Scripts.Features.Terrain.TerrainData;

namespace Builder
{
    [SelectionBase]
    [ExecuteInEditMode]
    [RequireComponent(typeof(BuilderPlotSpotView))]
    public class ControllerBuilder : MonoBehaviour
    {
        public bool ShowGrass { get; set; } = true;
        public bool ShowDirtyRects { get; set; } = false;
        public bool NeedCreateExportedScript = true;

        [Tooltip("Разрешение текстур, которые будут созданы для MaterialLOD, используемого на последних лодах чанков террейна")] public EnumBuilderTextureSize ExportTerrainLodMapsSize = EnumBuilderTextureSize._2048x2048;

        [Tooltip("Сила компрессии меша террейна")] public ModelImporterMeshCompression ExportTerrainMeshCompression = ModelImporterMeshCompression.Off;

        [Tooltip("Уровни лодов чанков террейна")]
        public LodLevel[] ExportTerrainLodLevels =
        {
            new() { MeshQuality = 1.0f, DisableWeight = 0.95F, UseMaterialLOD = false, RoadsUsesMaterialLOD = false }, new() { MeshQuality = 0.8f, DisableWeight = 0.6F, UseMaterialLOD = false },
            new() { MeshQuality = 0.4f, DisableWeight = 0.3F, UseMaterialLOD = false, RoadsUsesMaterialLOD = false }, new() { MeshQuality = 0.05f, DisableWeight = 0, UseMaterialLOD = true }
        };

        public static event Action OnBuilderExported;

        internal static void InvokeOnBuilderExported()
        {
            OnBuilderExported?.Invoke();
        }

        [Tooltip("Создает для дорог уникальную развертку UV для указанной UV")] public int RoadsUVUnvrapIndex = -1;
        [Tooltip("Паддинг между дорогами (в метрах)")] public float RoadsUVUnwrapPadding = 4f;

        [field: SerializeField] public DataBuilderSelector Selector { get; private set; } = new();

        public BuilderSettingsViewDescription Settings => ScriptableObjectInstance.Get<BuilderSettingsViewDescription>();

        private Transform _transform;
        public Transform Transform => _transform ? _transform : _transform = transform;

        public UndoRedoController UndoRedoController { get; private set; }

        public TerrainDataAsset Terrain { get; private set; }

        public BuilderPlotSpotView BuilderPlotSpotView { get; private set; }

        public RoadDataList RoadDataList { get; private set; }
        public RoadViewList RoadViewList { get; private set; }

        public CrossroadDataList CrossroadDataList { get; private set; }
        public CrossroadViewList CrossroadViewList { get; private set; }
        public CrossroadTerrainBlendViewList CrossroadTerrainBlendViewList { get; private set; }

        public StampDataList StampDataList { get; private set; }
        public HoleDataList HoleDataList { get; private set; }

        public FoundationDataList FoundationDataList { get; private set; }
        public FoundationViewList FoundationViewList { get; private set; }

        public CoverageDataList CoverageDataList { get; private set; }
        public CoverageViewList CoverageViewList { get; private set; }

        public PlotSpotDataList PlotSpotDataList { get; private set; }
        public PlotSpotViewList PlotSpotViewList { get; private set; }
        public PlotSpotTerrainViewList PlotSpotTerrainViewList { get; private set; }

        public BorderEdgesData BorderEdgesData { get; private set; }

        public TerrainController TerrainController { get; private set; }
        public TerrainBrushController TerrainBrushController { get; private set; }
        public LayersBrushController LayersBrushController { get; private set; }
        public CoverageBrushController CoverageBrushController { get; private set; }
        public TerrainData TerrainData { get; private set; }
        public RaycastedTerrainData RaycastedTerrainData { get; private set; }
        public TerrainCoveragesData TerrainCoveragesData { get; private set; }
        public TerrainTrianglesData TerrainTrianglesData { get; private set; }
        public TerrainGridView TerrainGridView { get; private set; }
        public TerrainChunkViewList TerrainChunkViewList { get; private set; }
        public TerrainSplatMapViewList TerrainSplatMapViewList { get; private set; }
        public TerrainBaseSplatMapView TerrainBaseSplatMapView { get; private set; }
        public TerrainPreparedSplatMapView TerrainPreparedSplatMapView { get; private set; }
        public TerrainLayersDataAsset TerrainLayers { get; private set; }
        public SplatMapVariantsByChunksData SplatMapVariantsByChunksData { get; private set; }
        public SplatMapVariantDataList SplatMapVariantDataList { get; private set; }
        public CompositeTerrainSplatMapView CompositeTerrainSplatMapView { get; private set; }
        public WaterZoneDataList WaterZoneDataList { get; private set; }
        public CoastlineDataList CoastlineDataList { get; private set; }
        public RoadViewNormalsAndTangentsList RoadViewNormalsAndTangentsList { get; private set; }
        public GrassChunkedAssetsList GrassChunkedAssetsList { get; private set; }
        public GrassChunkedDataList GrassChunkedDataList { get; private set; }
        public GrassChunkedViewList GrassChunkedViewList { get; private set; }
        public InstancesPregenerationDataAsset InstancesPregeneration { get; private set; }
        public DirtyRectGizmosList DirtyRectGizmosList { get; private set; }

        private void OnEnable()
        {
            BuilderPlotSpotView = GetComponent<BuilderPlotSpotView>();

            var undoRedoController = new UndoRedoController(Transform);
            
            string workingDirectory = Path.Combine(Path.GetDirectoryName(SceneManager.GetActiveScene().path), gameObject.name);
            Directory.CreateDirectory(workingDirectory);

            string terrainAssetPath = Path.Combine(workingDirectory, FileName.WithExtension("terrain", AssetType.Asset));
            var terrain = AssetDatabase.LoadAssetAtPath<TerrainDataAsset>(terrainAssetPath);
            if (!terrain)
            {
                terrain = ScriptableObject.CreateInstance<TerrainDataAsset>();
                AssetDatabase.CreateAsset(terrain, terrainAssetPath);
            }

            var terrainController = new TerrainController(terrain);

            string terrainLayersAssetPath = Path.Combine(workingDirectory, FileName.WithExtension("terrain_layers", AssetType.Asset));
            var terrainLayers = AssetDatabase.LoadAssetAtPath<TerrainLayersDataAsset>(terrainLayersAssetPath);
            if (!terrainLayers)
            {
                terrainLayers = ScriptableObject.CreateInstance<TerrainLayersDataAsset>();
                AssetDatabase.CreateAsset(terrainLayers, terrainLayersAssetPath);
            }

            string instancesPregenerationAssetPath = Path.Combine(workingDirectory, FileName.WithExtension("instances_pregeneration", AssetType.Asset));
            var instancesPregeneration = AssetDatabase.LoadAssetAtPath<InstancesPregenerationDataAsset>(instancesPregenerationAssetPath);
            if (!instancesPregeneration)
            {
                instancesPregeneration = ScriptableObject.CreateInstance<InstancesPregenerationDataAsset>();
                AssetDatabase.CreateAsset(instancesPregeneration, instancesPregenerationAssetPath);
            }

            var roadDataList = new RoadDataList(workingDirectory, terrain, undoRedoController);
            var roadViewList = new RoadViewList(roadDataList, Settings, Transform);

            var crossroadDataList = new CrossroadDataList(workingDirectory, roadDataList, terrain, undoRedoController);
            var crossroadViewList = new CrossroadViewList(crossroadDataList, Settings, Transform);

            var stampDataList = new StampDataList(workingDirectory, undoRedoController, terrain);
            var holeDataList = new HoleDataList(workingDirectory, undoRedoController);

            var foundationDataList = new FoundationDataList(workingDirectory, terrain, undoRedoController);
            var foundationViewList = new FoundationViewList(foundationDataList, Transform);

            var coverageDataList = new CoverageDataList(workingDirectory, undoRedoController);

            var plotSpotDataList = new PlotSpotDataList(workingDirectory, terrain, undoRedoController);
            var plotSpotViewList = new PlotSpotViewList(plotSpotDataList, Settings, Transform);

            var borderEdgesData = new BorderEdgesData(roadDataList, roadViewList, crossroadDataList, crossroadViewList, coverageDataList);

            var raycastedTerrainData = new RaycastedTerrainData(terrainController, roadDataList, roadViewList, crossroadDataList, crossroadViewList, foundationDataList, foundationViewList, plotSpotDataList, plotSpotViewList);

            var terrainBrushData = new TerrainBrushController(workingDirectory, terrainController);
            var layersBrushData = new LayersBrushController(workingDirectory, terrainController, terrainLayers);
            var coverageBrushData = new CoverageBrushController(terrain, workingDirectory);
            var terrainData = new TerrainData(terrainController, terrainBrushData, stampDataList, holeDataList, borderEdgesData, raycastedTerrainData);

            var coverageViewList = new CoverageViewList(coverageDataList, Settings, terrainController, terrainData, raycastedTerrainData, Transform);
            var terrainCoveragesData = new TerrainCoveragesData(terrainController, coverageViewList);
            var terrainTrianglesData = new TerrainTrianglesData(terrainController, terrainData, raycastedTerrainData, terrainCoveragesData);

            var splatMapVariantDataList = new SplatMapVariantDataList(workingDirectory, undoRedoController, terrainController);
            var splatMapVariantsByChunksData = new SplatMapVariantsByChunksData(workingDirectory, terrain);

            var terrainGridView = new TerrainGridView(terrainData, terrainBrushData, terrainTrianglesData, terrainController, Settings, Transform);

            var terrainBaseSplatMapView = new TerrainBaseSplatMapView(terrainData, layersBrushData, terrainLayers, terrainGridView, terrainController, Settings);
            var terrainPreparedSplatMapView = new TerrainPreparedSplatMapView(terrainController, terrainLayers);

            var terrainSplatMapViewList = new TerrainSplatMapViewList(terrainData, layersBrushData, terrainLayers, splatMapVariantDataList, terrainGridView, terrainController, Settings);

            var compositeTerrainSplatMapView = new CompositeTerrainSplatMapView(terrainPreparedSplatMapView, terrainBaseSplatMapView, splatMapVariantsByChunksData, splatMapVariantDataList, terrainSplatMapViewList, terrain);
            
            var plotSpotTerrainViewList = new PlotSpotTerrainViewList(plotSpotDataList, terrainData, terrainTrianglesData, raycastedTerrainData, terrainCoveragesData, terrainGridView, terrainController, compositeTerrainSplatMapView, Settings, Transform);
            var terrainChunkViewList = new TerrainChunkViewList(terrainData, terrainTrianglesData, terrainGridView, terrain, compositeTerrainSplatMapView, terrainBrushData, Settings, Transform);


            var waterZoneDataList = new WaterZoneDataList(workingDirectory, undoRedoController);
            var coastlineDataList = new CoastlineDataList(workingDirectory, undoRedoController);
            var roadViewNormalsAndTangentsList = new RoadViewNormalsAndTangentsList(roadViewList, terrainController, borderEdgesData, terrainGridView);
            var crossroadTerrainBlendViewList = new CrossroadTerrainBlendViewList(crossroadDataList, terrainController, terrainGridView, Settings, Transform);

            var grassChunkedAssetsList = new GrassChunkedAssetsList(terrain, workingDirectory);
            var grassChunkedDataList = new GrassChunkedDataList(grassChunkedAssetsList, terrainData, terrainGridView, terrainController, compositeTerrainSplatMapView, Settings);
            var grassChunkedViewList = new GrassChunkedViewList(grassChunkedDataList, Settings.PrototypesSettingsViewDescriptions);

            var dirtyRectGizmosList = new DirtyRectGizmosList(roadViewList, crossroadViewList, coverageViewList, terrainChunkViewList, plotSpotTerrainViewList, grassChunkedDataList);

            UndoRedoController = undoRedoController;
            Terrain = terrain;
            TerrainController = terrainController;
            RoadDataList = roadDataList;
            RoadViewList = roadViewList;
            CrossroadDataList = crossroadDataList;
            CrossroadViewList = crossroadViewList;
            StampDataList = stampDataList;
            HoleDataList = holeDataList;
            FoundationDataList = foundationDataList;
            FoundationViewList = foundationViewList;
            CoverageDataList = coverageDataList;
            PlotSpotDataList = plotSpotDataList;
            PlotSpotViewList = plotSpotViewList;
            BorderEdgesData = borderEdgesData;
            RaycastedTerrainData = raycastedTerrainData;
            TerrainBrushController = terrainBrushData;
            LayersBrushController = layersBrushData;
            CoverageBrushController = coverageBrushData;
            TerrainData = terrainData;
            CoverageViewList = coverageViewList;
            TerrainCoveragesData = terrainCoveragesData;
            TerrainTrianglesData = terrainTrianglesData;
            TerrainLayers = terrainLayers;
            InstancesPregeneration = instancesPregeneration;
            CompositeTerrainSplatMapView = compositeTerrainSplatMapView;
            SplatMapVariantsByChunksData = splatMapVariantsByChunksData;
            SplatMapVariantDataList = splatMapVariantDataList;
            TerrainGridView = terrainGridView;
            TerrainChunkViewList = terrainChunkViewList;
            WaterZoneDataList = waterZoneDataList;
            CoastlineDataList = coastlineDataList;
            RoadViewNormalsAndTangentsList = roadViewNormalsAndTangentsList;
            CrossroadTerrainBlendViewList = crossroadTerrainBlendViewList;
            PlotSpotTerrainViewList = plotSpotTerrainViewList;
            TerrainPreparedSplatMapView = terrainPreparedSplatMapView;
            TerrainBaseSplatMapView = terrainBaseSplatMapView;
            TerrainSplatMapViewList = terrainSplatMapViewList;
            GrassChunkedAssetsList = grassChunkedAssetsList;
            GrassChunkedDataList = grassChunkedDataList;
            GrassChunkedViewList = grassChunkedViewList;
            DirtyRectGizmosList = dirtyRectGizmosList;
        }

        private void OnDisable()
        {
            DirtyRectGizmosList.Dispose();
            GrassChunkedViewList.Dispose();
            GrassChunkedDataList.Dispose();
            CompositeTerrainSplatMapView.Dispose();
            TerrainSplatMapViewList.Dispose();
            TerrainPreparedSplatMapView.Dispose();
            TerrainBaseSplatMapView.Dispose();
            PlotSpotTerrainViewList.Dispose();
            CrossroadTerrainBlendViewList.Dispose();
            RoadViewNormalsAndTangentsList.Dispose();
            CoastlineDataList.Dispose();
            WaterZoneDataList.Dispose();
            TerrainChunkViewList.Dispose();
            TerrainGridView.Dispose();
            SplatMapVariantDataList.Dispose();
            SplatMapVariantsByChunksData.Dispose();
            TerrainTrianglesData.Dispose();
            TerrainCoveragesData.Dispose();
            CoverageViewList.Dispose();
            TerrainData.Dispose();
            LayersBrushController.Dispose();
            TerrainBrushController.Dispose();
            RaycastedTerrainData.Dispose();
            BorderEdgesData.Dispose();
            PlotSpotViewList.Dispose();
            PlotSpotDataList.Dispose();
            CoverageDataList.Dispose();
            FoundationViewList.Dispose();
            FoundationDataList.Dispose();
            HoleDataList.Dispose();
            StampDataList.Dispose();
            CrossroadViewList.Dispose();
            CrossroadDataList.Dispose();
            RoadViewList.Dispose();
            RoadDataList.Dispose();
            UndoRedoController.Dispose();

            DirtyRectGizmosList = null;
            GrassChunkedViewList = null;
            GrassChunkedDataList = null;
            GrassChunkedAssetsList = null;
            CompositeTerrainSplatMapView = null;
            TerrainSplatMapViewList = null;
            TerrainPreparedSplatMapView = null;
            TerrainBaseSplatMapView = null;
            PlotSpotTerrainViewList = null;
            CrossroadTerrainBlendViewList = null;
            TerrainChunkViewList = null;
            TerrainGridView = null;
            SplatMapVariantDataList = null;
            SplatMapVariantsByChunksData = null;
            TerrainTrianglesData = null;
            TerrainCoveragesData = null;
            CoverageViewList = null;
            TerrainData = null;
            LayersBrushController = null;
            TerrainBrushController = null;
            RaycastedTerrainData = null;
            BorderEdgesData = null;
            PlotSpotViewList = null;
            PlotSpotDataList = null;
            CoverageDataList = null;
            FoundationViewList = null;
            FoundationDataList = null;
            HoleDataList = null;
            StampDataList = null;
            CrossroadViewList = null;
            CrossroadDataList = null;
            RoadViewList = null;
            RoadDataList = null;
            RoadViewNormalsAndTangentsList = null;
            UndoRedoController = null;

            _isFirstUpdate = true;
        }

        private bool _isFirstUpdate = true;
        
        private void Update()
        {
            if (_isFirstUpdate)
            {
                _isFirstUpdate = false;
                return;
            }
            
            foreach (var road in RoadViewList.List)
            {
                road.ClearDirty();
            }

            foreach (var crossroad in CrossroadViewList.List)
            {
                crossroad.ClearDirty();
            }

            foreach (var roadTerrainBlends in RoadViewNormalsAndTangentsList.List)
            {
                roadTerrainBlends.ClearDirty();
            }

            foreach (var crossroadTerrainBlendView in CrossroadTerrainBlendViewList.List)
            {
                crossroadTerrainBlendView.ClearDirty();
            }

            foreach (var coverage in CoverageViewList.List)
            {
                coverage.ClearDirty();
            }

            TerrainGridView.ClearDirty();

            for (int i = 0; i < TerrainChunkViewList.Length; i++)
            {
                TerrainChunkViewList[i].ClearDirty();
            }

            
            foreach (var plotSpotTerrainView in PlotSpotTerrainViewList.List)
            {
                plotSpotTerrainView.ClearDirty();
            }

            foreach (var terrainSplatMapView in TerrainSplatMapViewList.List)
            {
                terrainSplatMapView.ClearDirty();
            }

            foreach (var grassChunkedData in GrassChunkedDataList.List)
            {
                grassChunkedData.ClearDirty();
            }

            if (ShowGrass)
            {
                foreach (var grassChunkedView in GrassChunkedViewList.List)
                {
                    grassChunkedView.Update();
                }
            }
        }

        private void LateUpdate()
        {
            foreach (var grassChunkedView in GrassChunkedViewList.List)
            {
                grassChunkedView.LateUpdate();
            }
        }

        public Action DrawGizmosSelected;
        
        private void OnDrawGizmosSelected()
        {
            if (ShowDirtyRects)
            {
                var wasZTest = Handles.zTest;
                Handles.zTest = CompareFunction.Always;
                Handles.color = Color.blue;
                foreach (var dirtyRect in DirtyRectGizmosList.GetDirtyRects())
                {
                    Handles.DrawWireCube(dirtyRect.center.Vector3(), dirtyRect.size.Vector3());
                }

                Handles.zTest = wasZTest;
            }
            DrawGizmosSelected?.Invoke();
        }
    }
}