using ReactCore;
using UnityEditor.UIElements;
using UnityEngine.UIElements;

namespace Utils.BalanceEditor.ReactCore
{
    public sealed class PropertyBindToObjectFieldController<T> : IController
        where T : UnityEngine.Object
    {
        private readonly ObjectField _view;
        private readonly Property<T> _model;

        public PropertyBindToObjectFieldController(Property<T> model, ObjectField view)
        {
            _model = model;
            _view = view;
        }

        public void Deactivate()
        {
            _view.UnregisterValueChangedCallback(OnFieldChanged);
            _model.Changed -= OnValueChanged;
        }

        public void Activate()
        {
            _view.objectType = typeof(T);
            _view.RegisterValueChangedCallback(OnFieldChanged);
            _model.Changed += OnValueChanged;

            _view.SetValueWithoutNotify(_model.Value);
        }

        private void OnFieldChanged(ChangeEvent<UnityEngine.Object> evt)
        {
            _model.Value = evt.newValue as T;
        }

        private void OnValueChanged(T oldValue, T newValue)
        {
            _view.SetValueWithoutNotify(newValue);
        }
    }
}