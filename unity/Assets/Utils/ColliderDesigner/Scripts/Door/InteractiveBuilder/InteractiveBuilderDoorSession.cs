using UnityEditor;
using UnityEngine;

namespace Utils.ColliderDesigner.Scripts.Door.InteractiveBuilder
{
    public class InteractiveBuilderDoorSession : MonoBehaviour
    {
        [SerializeField, HideInInspector] public InteractiveBuilderDoorState State = new();
        
        public void Increment()
        {
            State.StepIndex++;
        }
        
        public void Decrement()
        {
            State.StepIndex--;
        }

        public InteractiveBuilderDoor.Step GetCurrentStep()
        {
            return State.StepIndex switch
            {
                <= 0 => new InteractiveBuilderDoor.Step2(gameObject, this),
                >= 1 => new InteractiveBuilderDoor.Step3(gameObject, this),
            };
        }

        [CustomEditor(typeof(InteractiveBuilderDoorSession))]
        private class Editor : UnityEditor.Editor
        {
            public override void OnInspectorGUI()
            {
                base.OnInspectorGUI();
                
                EditorGUILayout.HelpBox("Этот скрипт не может попасть в билд. Нужно завершить настройку или удалить скрипт", MessageType.Warning);
                
                if (GUILayout.Button("Продолжить настройку", GUILayout.Height(100)))
                {
                    var wnd = EditorWindow.GetWindow<InteractiveBuilderDoor>();
                    InteractiveBuilderDoorSession session = (InteractiveBuilderDoorSession)target;
                    wnd.SetNextStep(session.GetCurrentStep());
                }
                GUILayout.Space(5f);
                if (GUILayout.Button("Прервать и удалить скрипт", GUILayout.Height(100)))
                {
                    EditorUtility.SetDirty(((MonoBehaviour)target).gameObject);
                    DestroyImmediate(target);
                    
                }
            }
        } 
    }
}