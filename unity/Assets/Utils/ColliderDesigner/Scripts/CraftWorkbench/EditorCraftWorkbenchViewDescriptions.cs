using UnityEngine;
using Utils.ColliderDesigner.Editor.Scene.EditorViews;

namespace Utils.ColliderDesigner.Editor.Scene.ViewDescriptions
{
    [CreateAssetMenu(fileName = "EditorCraftWorkbenchViewDescriptions", menuName = "ScriptableObjects/ViewDescription/Editor/EditorCraftWorkbenchViewDescriptions")]
    public class EditorCraftWorkbenchViewDescriptions : ScriptableObject
    {
        [field: SerializeField] public CraftWorkbenchEditorView EntityView { get; private set; }
    }
}