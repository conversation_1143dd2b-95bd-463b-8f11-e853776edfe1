using Core;
using Game.Battle;
using Models.References.Cargo;
using UnityEditor;
using UnityEngine;
using Utils.AssetsUtilityEditor;
using Utils.ColliderDesigner.Editor.Model.ExportModels;
using Utils.ColliderDesigner.Editor.Scene.EditorViews;
using Utils.ColliderDesigner.Editor.Scene.ViewDescriptions;
using Utils.TypeCastExtensions;

namespace Utils.ColliderDesigner.Editor.Scene.StaticObjects
{
    [ExecuteInEditMode]
    [SelectionBase]
    public class CargoExchangerLevelEditor : LevelEditorView
    {
        [SerializeField] [HideInInspector] public string _id;
        
        private bool _isInited;

        public override void ClearLevelEditorView()
        {
            DestroyImmediate(this);
        }
        
        private void Update()
        {
            if (!_isInited)
            {
                _isInited = true;
                Redraw(_id);
            }
        }

        private void Redraw(string id)
        {
            var parent = PrefabUtility.GetNearestPrefabInstanceRoot(transform);
            if (parent != null)
            {
                return;
            }
            
            var description = PropertySetIdentified<CargoExchangerDescription>.Property(id);
            var viewDescriptions = ScriptableObjectInstance.Get<EditorCargoExchangerViewDescriptions>();
            var view = viewDescriptions[description].EntityView;

            if (this.NeedReInstantiatePrefab(view))
            {
                for (var i = transform.childCount - 1; i >= 0; i--)
                {
                    DestroyImmediate(transform.GetChild(i).gameObject);
                }

                if (transform && view)
                {
                    PrefabUtility.InstantiatePrefab(view, transform);
                }

                EditorUtility.SetDirty(gameObject);
            }
        }
        
        public void RefreshName(string id)
        {
            name = "CargoExchanger_" + id;
        }

        public LocationCargoExchangerExportModel GetExportModel(int index, int entityId)
        {
            var editorView = GetComponentInChildren<CargoExchangerEditorView>();
            return new LocationCargoExchangerExportModel(
                editorView.entityExchangerView,
                index,
                entityId,
                PropertySetIdentified<CargoExchangerDescription>.Property(_id),
                transform.position.ToSystemVector3(),
                transform.GetComponentInChildren<BoxCollider>().bounds.center.ToSystemVector3(),
                editorView.Colliders
            );
        }

        [CustomEditor(typeof(CargoExchangerLevelEditor))]
        internal class CargoSellEditor : UnityEditor.Editor
        {
            private SerializedProperty _id;
            private CargoExchangerLevelEditor _cargoExchanger;

            void OnEnable()
            {
                _cargoExchanger = (CargoExchangerLevelEditor)target;
                _id = serializedObject.FindProperty(nameof(CargoExchangerLevelEditor._id));
            }

            public override void OnInspectorGUI()
            {
                serializedObject.Update();
                
                _id.stringValue = ColliderGUILayout.LabelPopup<CargoExchangerDescription>("CargoExchanger", _id.stringValue);
                
                if (serializedObject.hasModifiedProperties)
                {
                    serializedObject.ApplyModifiedProperties();
                    
                    _cargoExchanger.Redraw(_id.stringValue);
                    _cargoExchanger.RefreshName(_id.stringValue);
                }
            }
        }
    }
}