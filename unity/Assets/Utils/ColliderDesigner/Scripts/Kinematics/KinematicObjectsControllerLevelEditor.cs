using Core;
using Game.Battle.Target;
using Models.MathUtils;
using Models.References.KinematicObject;
using System;
using System.Collections.Generic;
using System.Linq;
using Game.Locations;
using UnityEditor;
using UnityEngine;
using UnityEngine.Localization;
using Utils.ColliderDesigner.Editor;
using Utils.ColliderDesigner.Editor.OBBScope;
using Utils.ColliderDesigner.Editor.Scene;
using Utils.ColliderDesigner.Editor.Scene.Collider;
using Utils.TypeCastExtensions;

namespace Utils.ColliderDesigner.Scripts.Kinematics
{
    [ExecuteInEditMode]
    [SelectionBase]
    public class KinematicObjectsControllerLevelEditor : LevelEditorView, ISerializationCallbackReceiver
    {
        [SerializeField] [HideInInspector] public string _stateId = KinematicObjectMovementStateDescription.Enum[0].Id;
        
        [field: SerializeField] public Transform MarkerParent { get; private set; }
        [field: SerializeField] public LocalizedString Name { get; private set; }
        [field: SerializeField] public Collider Collider { get; private set; }
        
        [field: SerializeField] private IdentifiedProperty<KinematicObjectMovementStateDescription> _portalClosedState = new(KinematicObjectMovementStateDescription.Point1.Id);
        [field: SerializeField] public KinematicObjectMovementStateDescription PortalClosedState { get; private set; }
        [field: SerializeField] public TargetHintPositionView TargetHintPositionView { get; private set; }
        
        [field: SerializeField] public KinematicObjectsControllerKinematicObjectLevelEditorData[] KinematicObjects { get; private set; }
        [field: SerializeField] public int StateChangeDuration { get; private set; }
        [field: SerializeField] public int StateChangeCooldown { get; private set; }
        [field: SerializeField] public bool InstantReset { get; private set; }
        [field: SerializeField] public InteractAngle InteractAngle { get; private set; } = new(150f);
        
        [field: SerializeField] public ObbScope VehicleInteractionAreas { get; private set; }
        [field: SerializeField] public ObbScope CharacterInteractionAreas { get; private set; }

        [field: HideInInspector, SerializeField] private int SearchObstaclesTransitionsMask;
        [field: HideInInspector, SerializeField] private string[] SearchObstaclesTransitionsOptions;
        
        private IEnumerable<OBB> VehicleInteractionAreaOBBs => VehicleInteractionAreas ? VehicleInteractionAreas.GetScope() : Array.Empty<OBB>();
        private IEnumerable<OBB> CharacterInteractionAreaOBBs => CharacterInteractionAreas ? CharacterInteractionAreas.GetScope() : Array.Empty<OBB>();
        
        public void OnBeforeSerialize() { }
        public void OnAfterDeserialize()
        {
            if (KinematicObjectMovementStateDescription.Enum.TryFromId(_portalClosedState.Id, out var state) == false)
            {
                state = KinematicObjectMovementStateDescription.Point1;
            }
            PortalClosedState = state;
        }

        public override void ClearLevelEditorView()
        {
            DestroyImmediate(this);
        }
        
        public KinematicObjectsControllerDescription GetKinematicObjectsControllerDescription(Dictionary<KinematicObjectLevelEditor, HashSet<ColliderLevelEditor>> kinematicColliders)
        {
            var transformMatrix = transform.localToWorldMatrix;

            int kinematicObjectsCount = KinematicObjects.Length;
            var kinematicObjects = new KinematicObjectDescription[kinematicObjectsCount];
            var kinematicObjectsMovement = new KinematicObjectMovementDescription[kinematicObjectsCount];
            for (int kinematicObjectIndex = 0; kinematicObjectIndex < kinematicObjectsCount; kinematicObjectIndex++)
            {
                ref readonly var kinematicObjectData = ref KinematicObjects[kinematicObjectIndex];

                var colliders = kinematicColliders.GetValueOrDefault(kinematicObjectData.KinematicObject);

                var localPositionFrom = kinematicObjectData.PositionFrom;
                var localRotationFrom = Quaternion.Euler(kinematicObjectData.RotationFrom);
                var localPositionTo = kinematicObjectData.PositionTo;
                var localRotationTo = Quaternion.Euler(kinematicObjectData.RotationTo);
                
                kinematicObjects[kinematicObjectIndex] = kinematicObjectData.KinematicObject.GetKinematicObjectDescription(colliders);
                kinematicObjectsMovement[kinematicObjectIndex] = new KinematicObjectMovementDescription(
                    transformMatrix.MultiplyPoint3x4(localPositionFrom).ToSystemVector3(),
                    (transformMatrix.rotation * localRotationFrom).ToSystemQuaternion(),
                    transformMatrix.MultiplyPoint3x4(localPositionTo).ToSystemVector3(),
                    (transformMatrix.rotation * localRotationTo).ToSystemQuaternion()
                );
            }
            
            var kinematicObjectsControllerDescription = new KinematicObjectsControllerDescription(
                kinematicObjects,
                kinematicObjectsMovement,
                StateChangeDuration,
                StateChangeCooldown,
                PropertySetIdentified<KinematicObjectMovementStateDescription>.Property(_stateId),
                InstantReset,
                CharacterInteractionAreaOBBs.ToArray(),
                VehicleInteractionAreaOBBs.ToArray(),
                GetSearchObstaclesTransitions()
            );
            return kinematicObjectsControllerDescription;
        }

        private IReadOnlyCollection<KinematicObjectMovementStateDescription> GetSearchObstaclesTransitions()
        {
            HashSet<KinematicObjectMovementStateDescription> result = new HashSet<KinematicObjectMovementStateDescription>();
            for (int i = 0; i < KinematicObjectMovementStateDescription.Enum.Count; i++)
            {
                if ((SearchObstaclesTransitionsMask & (1 << i)) != 0 &&
                    KinematicObjectMovementStateDescription.Enum.TryFromId(SearchObstaclesTransitionsOptions[i], out KinematicObjectMovementStateDescription state))
                {
                    result.Add(state);
                }
            }
            return result;
        }
        
        [CustomEditor(typeof(KinematicObjectsControllerLevelEditor))]
        internal class KinematicObjectsControllerLevelEditorEditor : UnityEditor.Editor
        {
            private SerializedProperty _stateId;
            private KinematicObjectsControllerLevelEditor _kinematicObjectsController;

            private float _animationProgress;
            private bool _isPlaying;

            private KinematicObjectMovementStateDescription StateDescription => PropertySetIdentified<KinematicObjectMovementStateDescription>.Property(_stateId.stringValue);

            void OnEnable()
            {
                _kinematicObjectsController = (KinematicObjectsControllerLevelEditor)target;
                _stateId = serializedObject.FindProperty(nameof(KinematicObjectsControllerLevelEditor._stateId));
                _kinematicObjectsController.SearchObstaclesTransitionsOptions = KinematicObjectMovementStateDescription.Enum.Select(item => item.Id).ToArray();
            }

            public override void OnInspectorGUI()
            {
                base.OnInspectorGUI();

                serializedObject.Update();

                _kinematicObjectsController.SearchObstaclesTransitionsMask = EditorGUILayout.MaskField("SearchObstaclesTransitions: ", _kinematicObjectsController.SearchObstaclesTransitionsMask, _kinematicObjectsController.SearchObstaclesTransitionsOptions);

                GUILayout.Space(15f);

                EditorGUI.BeginChangeCheck();
                _stateId.stringValue = ColliderGUILayout.LabelPopup<KinematicObjectMovementStateDescription>("State", _stateId.stringValue);
                if (EditorGUI.EndChangeCheck())
                {
                    Stop();
                    UpdateView();
                }

                serializedObject.ApplyModifiedProperties();

                EditorGUI.BeginChangeCheck();
                _animationProgress = EditorGUILayout.Slider("Animation progress", _animationProgress, 0f, 1f);
                if (EditorGUI.EndChangeCheck())
                {
                    Pause();
                    UpdateView();
                }

                EditorGUILayout.BeginHorizontal();
                EditorGUI.BeginDisabledGroup(_isPlaying);

                if (GUILayout.Button("►"))
                {
                    Play();
                }

                EditorGUI.EndDisabledGroup();
                EditorGUI.BeginDisabledGroup(!_isPlaying);

                if (GUILayout.Button("▮▮"))
                {
                    Pause();
                    UpdateView();
                }

                EditorGUI.EndDisabledGroup();
                EditorGUI.BeginDisabledGroup(_animationProgress == GetStartProgress());

                if (GUILayout.Button("▇"))
                {
                    Stop();
                    UpdateView();
                }

                EditorGUI.EndDisabledGroup();
                EditorGUILayout.EndHorizontal();
            }

            private void OnDisable()
            {
                if (_kinematicObjectsController)
                {
                    Stop();
                    UpdateView();
                }
            }

            private void Play()
            {
                _isPlaying = true;
                EditorApplication.update += UpdateAnimation;
            }

            private void Pause()
            {
                _isPlaying = false;
                EditorApplication.update -= UpdateAnimation;
            }

            private void Stop()
            {
                Pause();
                _animationProgress = GetStartProgress();
            }

            private void UpdateAnimation()
            {
                _animationProgress = GetProgress();

                if (IsEndOfAnimation())
                {
                    Pause();
                }

                UpdateView();
                Repaint();
            }

            private void UpdateView()
            {
                int kinematicObjectsCount = _kinematicObjectsController.KinematicObjects?.Length ?? 0;
                var kinematicObjects = _kinematicObjectsController.KinematicObjects;
                
                for(int kinematicObjectIndex = 0; kinematicObjectIndex < kinematicObjectsCount; kinematicObjectIndex++)
                {
                    ref readonly var kinematicObjectData = ref kinematicObjects[kinematicObjectIndex];
                    
                    var kinematicObjectTransform = kinematicObjects[kinematicObjectIndex].KinematicObject.transform;

                    var localPosition = Vector3.Lerp(kinematicObjectData.PositionFrom, kinematicObjectData.PositionTo, _animationProgress);
                    var localRotation = Quaternion.Lerp(Quaternion.Euler(kinematicObjectData.RotationFrom), Quaternion.Euler(kinematicObjectData.RotationTo), _animationProgress);

                    var transformMatrix = _kinematicObjectsController.transform.localToWorldMatrix;

                    kinematicObjectTransform.position = transformMatrix.MultiplyPoint3x4(localPosition);
                    kinematicObjectTransform.rotation = transformMatrix.rotation * localRotation;
                }
            }

            private float GetProgress()
            {
                if (StateDescription == KinematicObjectMovementStateDescription.Point2)
                {
                    return Mathf.Clamp01(_animationProgress - (Time.deltaTime * 1000 / _kinematicObjectsController.StateChangeDuration));
                }

                return Mathf.Clamp01(_animationProgress + (Time.deltaTime * 1000 / _kinematicObjectsController.StateChangeDuration));
            }

            private bool IsEndOfAnimation()
            {
                if (StateDescription == KinematicObjectMovementStateDescription.Point2)
                {
                    return _animationProgress <= 0;
                }

                return _animationProgress >= 1;
            }

            private float GetStartProgress()
            {
                if (StateDescription == KinematicObjectMovementStateDescription.Point2)
                {
                    return 1;
                }

                return 0;
            }
        }
    }
}