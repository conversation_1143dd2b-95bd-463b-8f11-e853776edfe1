using UnityEngine;
using Utils.ColliderDesigner.Editor;
using Utils.ColliderDesigner.Editor.OBBScope;
using Utils.ColliderDesigner.Editor.Scene;
using Utils.ColliderDesigner.Editor.Scene.Collider;

namespace Utils.ColliderDesigner.Scripts.ExportPlot
{
    public static class ClearLevelEditorHandler
    {
        public static void Execute(Component component)
        {
            BakedLightHandler.ClearBakedLights(component.GetComponentsInChildren<Light>());
            
            ColliderUtils.UnpackAndDestroy(component.GetComponentsInChildren<ObbScope>(true));
            ColliderUtils.UnpackAndDestroy(component.GetComponentsInChildren<ObbSingle>(true));
            
            ColliderUtils.ClearMapColliders(component.GetComponentsInChildren<MapLevelEditor>(true));
            ColliderUtils.ClearLevelEditorView(component.GetComponentsInChildren<LevelEditorView>(true));
        }
    }
}