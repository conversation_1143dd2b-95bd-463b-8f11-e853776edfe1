using SnappingHandle.Editor;
using UnityEditor;
using UnityEngine;

namespace Utils.ColliderDesigner.Editor.OBBScope
{
    public class ObbSingle : MonoBehaviour
    {
        [SerializeField] public OBBBox Box = new ();
        
        [CustomEditor(typeof(ObbSingle))]
        private class BoxCollidersScopeEditor : UnityEditor.Editor
        {
            
            private ObbSingle _scope;
            private SerializedProperty _box;

            private void OnEnable()
            {
                _scope = (ObbSingle)target;
                _box = serializedObject.FindProperty(nameof(Box));
            }

            private void OnSceneGUI()
            {
                if (Event.current.type == EventType.Repaint)
                {
                    if (Selection.activeGameObject != _scope.gameObject) return;
                    var (center, rotation, size) = _scope.Box.ToGlobal(_scope.transform);
                    EditorObbUtils.DrawGizmos(center, rotation, size);
                }
            }

            public override void OnInspectorGUI()
            {
                EditorGUILayout.EditorToolbarForTarget(EditorGUIUtility.TrTempContent("Edit OBB Single"), this);
                serializedObject.Update();
                EditorGUILayout.PropertyField(_box);
                serializedObject.ApplyModifiedProperties();
            }
        }
    }
}