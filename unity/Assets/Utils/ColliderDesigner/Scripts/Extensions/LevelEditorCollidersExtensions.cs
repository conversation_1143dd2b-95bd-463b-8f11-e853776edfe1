using Models.Physics.Shapes.Data.Hull;
using Models.References.Colliders;
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Utils.ColliderDesigner.Editor;
using Utils.ColliderDesigner.Editor.Scene.Collider;
using Utils.ColliderDesigner.Scripts.Kinematics;
using Utils.ColliderDesigner.Scripts.Utils;

namespace Utils.ColliderDesigner.Scripts.Extensions
{
    public static class LevelEditorCollidersExtensions
    {
        public static LevelEditorColliders CollectColliders(LevelEditorEntities levelEditorEntities)
        {
            var mainColliders = ColliderUtils.FindSortedObjectsOfType<ColliderLevelEditor>().ToHashSet();
            
            var plotBuilderPanelColliders = new HashSet<ColliderLevelEditor>();
            if (levelEditorEntities.PlotBuilderPanel != null)
            {
                plotBuilderPanelColliders.UnionWith(levelEditorEntities.PlotBuilderPanel.GetComponentsInChildren<ColliderLevelEditor>(false));
            }
            mainColliders.ExceptWith(plotBuilderPanelColliders);
            
            Dictionary<KinematicObjectLevelEditor, HashSet<ColliderLevelEditor>> kinematicColliders = new();
            foreach (var kinematicObjectsController in levelEditorEntities.KinematicObjectsControllers)
            {
                foreach (var kinematicObjectData in kinematicObjectsController.KinematicObjects)
                {
                    var kinematicObject = kinematicObjectData.KinematicObject;
                    
                    if (kinematicObject.ColliderHolder == null)
                    {
                        continue;
                    }
                    
                    var kinematicObjectColliders = CollectCollidersFromRoot(kinematicObject.ColliderHolder);
                    kinematicColliders.Add(kinematicObject, kinematicObjectColliders);
                    
                    mainColliders.ExceptWith(kinematicObjectColliders);
                }
            }
            
            var bakedColliders = new HashSet<ColliderLevelEditor>();
            bakedColliders.UnionWith(mainColliders);
            bakedColliders.UnionWith(plotBuilderPanelColliders);
            
            return new LevelEditorColliders
            {
                BakedColliders = bakedColliders,
                
                MainColliders = mainColliders,
                KinematicColliders = kinematicColliders,
                PlotBuilderPanelColliders = plotBuilderPanelColliders
            };
        }
        
        public static LevelEditorColliders CollectCollidersFromRoot(GameObject root, LevelEditorEntities levelEditorEntities)
        {
            var mainColliders = root.GetComponentsInChildren<ColliderLevelEditor>().ToHashSet();
            
            var plotBuilderPanelColliders = new HashSet<ColliderLevelEditor>();
            if (levelEditorEntities.PlotBuilderPanel != null)
            {
                plotBuilderPanelColliders.UnionWith(levelEditorEntities.PlotBuilderPanel.GetComponentsInChildren<ColliderLevelEditor>(false));
            }
            mainColliders.ExceptWith(plotBuilderPanelColliders);
            
            Dictionary<KinematicObjectLevelEditor, HashSet<ColliderLevelEditor>> kinematicColliders = new();
            foreach (var kinematicObjectsController in levelEditorEntities.KinematicObjectsControllers)
            {
                foreach (var kinematicObjectData in kinematicObjectsController.KinematicObjects)
                {
                    var kinematicObject = kinematicObjectData.KinematicObject;
                    
                    if (kinematicObject.ColliderHolder == null)
                    {
                        continue;
                    }
                    
                    var kinematicObjectColliders = CollectCollidersFromRoot(kinematicObject.ColliderHolder);
                    kinematicColliders.Add(kinematicObject, kinematicObjectColliders);
                    
                    mainColliders.ExceptWith(kinematicObjectColliders);
                }
            }

            var bakedColliders = new HashSet<ColliderLevelEditor>();
            bakedColliders.UnionWith(mainColliders);
            bakedColliders.UnionWith(plotBuilderPanelColliders);
            
            return new LevelEditorColliders
            {
                BakedColliders = bakedColliders,
                
                MainColliders = mainColliders,
                KinematicColliders = kinematicColliders,
                PlotBuilderPanelColliders = plotBuilderPanelColliders
            };
        }
        
        private static HashSet<ColliderLevelEditor> CollectCollidersFromRoot(GameObject root)
        {
            HashSet<ColliderLevelEditor> result = new();
            result.UnionWith(root.GetComponentsInChildren<BoxColliderLevelEditor>(false));
            result.UnionWith(root.GetComponentsInChildren<TriangleColliderLevelEditor>(false));
            result.UnionWith(root.GetComponentsInChildren<CapsuleColliderLevelEditor>(false));
            result.UnionWith(HullEditorExtensions.CollectHullColliderEditorComponentsFromGameObjectChildren(root));
            return result;
        }

        public static CollidersDescription BuildCollidersDescriptionFromLevelEditorColliders(Transform root, HashSet<ColliderLevelEditor> colliders)
        {
            BoxColliderDescription[] boxesDescriptions = colliders is { Count: > 0 }
                ? colliders
                    .OfType<BoxColliderLevelEditor>()
                    .Select(boxColliderLevelEditor => boxColliderLevelEditor.GetDescriptionFromRoot(root))
                    .ToArray()
                : Array.Empty<BoxColliderDescription>();
            
            TriangleColliderDescription[] trianglesDescriptions = colliders is { Count: > 0 }
                ? colliders
                    .OfType<TriangleColliderLevelEditor>()
                    .Select(triangleColliderLevelEditor => triangleColliderLevelEditor.GetDescriptionFromRoot(root))
                    .ToArray()
                : Array.Empty<TriangleColliderDescription>();
            
            CapsuleColliderDescription[] capsulesDescriptions = colliders is { Count: > 0 }
                ? colliders
                    .OfType<CapsuleColliderLevelEditor>()
                    .Select(capsuleColliderLevelEditor => capsuleColliderLevelEditor.GetDescriptionFromRoot(root))
                    .ToArray()
                : Array.Empty<CapsuleColliderDescription>();
            
            HullColliderDescription[] hulls;
            HullShapeData[] hullData;
            
            if (colliders is { Count: > 0 })
            {
                (hulls, hullData) = HullEditorExtensions.BuildHullCollidersFromRootLevelEditorList(root, colliders.OfType<BaseHullColliderLevelEditor>().ToList());
            }
            else
            {
                (hulls, hullData) = (Array.Empty<HullColliderDescription>(), Array.Empty<HullShapeData>());
            }
            
            return new CollidersDescription(
                boxes: boxesDescriptions,
                triangles: trianglesDescriptions,
                capsules: capsulesDescriptions,
                hulls: hulls,
                hullData: hullData
            );
        }
    }
}