using Game.Battle.Entities.KinematicObjectsController.Views;
using System.Collections.Generic;
using Game.Battle.Entities.Switch.Views;
using Models.References.KinematicObject;
using Models.References.Switch;
using Utils.ColliderDesigner.Scripts.Kinematics;

namespace Utils.ColliderDesigner.Scripts.Triggers
{
    public class LocationTriggerSystemExportModel
    {
        public List<(KinematicObjectsControllerDescription description, KinematicObjectsControllerView view, KinematicObjectsControllerLevelEditor editor)> KinematicObjectsControllers { get; init; }
        public List<(SwitchDescription description, SwitchView view)> Switches { get; init; }
    }
}