using System.Collections.Generic;
using System.Linq;
using System.Text;
using Core;
using Models.Physics.PhysicsBodies;
using Models.References.SecurityPanel;
using Utils.ColliderDesigner.Editor.Model.ExportModels;
using Utils.ColliderDesigner.Editor.Scene.Collider;
using Utils.ColliderDesigner.Scripts.SecurityPanel;
using Utils.ColliderDesigner.Scripts.Utils;

namespace Utils.ColliderDesigner.Editor.Generators
{
    public class LocationSecurityPanelsFile
    {
        public void Build(StringBuilder stringBuilder, List<LocationSecurityPanelExportModel> securityAlarmPanels, Dictionary<ColliderLevelEditor, PhysicsBodyId> colliderBodies)
        {
            foreach (var exportModel in securityAlarmPanels)
            {
                stringBuilder.AppendLine(exportModel.ToStringFieldInitialization(colliderBodies));
            }

            stringBuilder
                .AppendLine("        var securityPanels = new Enum<LocationPanelDescription>()")
                .AppendLine("        {");

            foreach (var alarmPanel in securityAlarmPanels)
            {
                stringBuilder.AppendLine($"            {alarmPanel.ServerFieldName},");
            }

            stringBuilder.AppendLine("        };");
        }
    }
}