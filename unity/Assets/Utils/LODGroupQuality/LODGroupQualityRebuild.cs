using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace LODGroupQuality
{
    [RequireComponent(typeof(LODGroup))]
    public class LODGroupQualityRebuild : MonoBehaviour
    {
        [SerializeField] private LODGroupQualitySetups _lodGroupQualitySetups;
        [SerializeField, HideInInspector] private bool _hadRemovedFirstLod;

        private readonly HashSet<Renderer> _offRenderers = new();
        private LODGroup _lodGroup;
        private LOD[] _initialLODS;
        private Renderer[] _allRenderers;
        private Dictionary<Renderer, uint> _rendererToDefaultRenderingLayer;

        private void Awake()
        {
            _lodGroup = GetComponent<LODGroup>();
            _initialLODS = _lodGroup.GetLODs();
            _allRenderers = _initialLODS
                .SelectMany(x => x.renderers)
                .Distinct()
                .ToArray();

            _rendererToDefaultRenderingLayer = new Dictionary<Renderer, uint>(_allRenderers.Length);
            foreach (var item in _allRenderers)
            {
                _rendererToDefaultRenderingLayer.Add(item, item.renderingLayerMask);
            }
        }

        private void OnEnable()
        {
            if (_lodGroupQualitySetups && _lodGroupQualitySetups.TryGetCategory(out LODQualityCategory category))
            {
                LODQualitySettings.NotifyQualityChanged += TryRebuild;
                int quality = LODQualitySettings.Get(category);
                TryRebuild(category, quality);
            }
        }

        private void OnDisable()
        {
            LODQualitySettings.NotifyQualityChanged -= TryRebuild;
        }

        public void SetLODSetup(LODGroupQualitySetups setups)
        {
            OnDisable();
            _lodGroupQualitySetups = setups;

            if (enabled && gameObject.activeInHierarchy)
            {
                OnEnable();
            }
        }

        private void TryRebuild(LODQualityCategory category, int quality)
        {
            if (!_lodGroupQualitySetups.TryGetCategory(out var ownCategory) || ownCategory != category) return;
            
            LODGroupSetup setup = _lodGroupQualitySetups.GetSetup(quality, out ReadOnlySpan<int> indices);
            int lodCount = _hadRemovedFirstLod ? indices.Length - 1 : indices.Length;
            LOD[] newLods = new LOD[lodCount];
            RefillRenderersSet();
            setup.SetupGroup(_lodGroup);
            for (int i = 0; i < lodCount; i++)
            {
                int indicesIndex = _hadRemovedFirstLod ? i + 1 : i;
                int lodIndex = indices[indicesIndex];
                if (_initialLODS.Length <= lodIndex)
                {
                    Debug.LogError($"IndexOutOfRangeException: {category.name}/{_lodGroupQualitySetups.name} indices don't fit into parents {_lodGroup.name} lods");
                    return;
                }
                LOD lod = _initialLODS[lodIndex];
                foreach (Renderer rend in lod.renderers)
                {
                    // Return default Rendering Layer to Renderer so it renders again.
                    uint defaultRenderLayer = _rendererToDefaultRenderingLayer[rend];
                    if (rend.renderingLayerMask != defaultRenderLayer)
                    {
                        rend.renderingLayerMask = defaultRenderLayer;
                    }
                    _offRenderers.Remove(rend);
                }
                setup.SetupLod(ref lod, indicesIndex);
                newLods[i] = lod;
            }
            TurnOffRenderersSet();
            
            _lodGroup.SetLODs(newLods);
            _lodGroup.RecalculateBounds();
        }

        private void RefillRenderersSet()
        {
            foreach (Renderer rend in _allRenderers)
            {
                _offRenderers.Add(rend);
            }
        }

        private void TurnOffRenderersSet()
        {
            foreach (Renderer rend in _offRenderers)
            {
                // Use renderingLayerMask 0(Nothing) instead of forceRenderingOff to prevent incorrect behaviour with RenderersCullingController which uses forceRenderingOff.
                rend.renderingLayerMask = 0;
            }
            _offRenderers.Clear();
        }

        public void SetHadRemovedFirstLod()
        {
            _hadRemovedFirstLod = true;
        }
        
    }
}