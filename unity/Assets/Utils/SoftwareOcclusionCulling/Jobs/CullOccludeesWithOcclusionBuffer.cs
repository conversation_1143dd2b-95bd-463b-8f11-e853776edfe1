using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using Utils.Geometry;
using Utils.SoftwareOcclusionCulling.Data;

namespace Utils.SoftwareOcclusionCulling.Jobs
{
    [BurstCompile]
    public struct CullOccludeesWithOcclusionBuffer : IJobParallelFor
    {
        [ReadOnly] public float AlwaysShowRenderersInRadius;
        [ReadOnly] public NativeArray<Plane> CameraPlanes;
        [ReadOnly] public NativeArray<OcclusionObject> Occludees;
        [ReadOnly] public float4x4 CurrentCameraView;
        [ReadOnly] public float4x4 CurrentCameraProjection;
        [ReadOnly] public NativeArray<float4> OBBVertices;
        [ReadOnly] public float3 CameraPosition;
        [ReadOnly] public float FarClipPlane;
        [ReadOnly] public float NearClipPlane;
        [ReadOnly] public NativeArray<OccludeeScreenSizeCullingMode> OccludeesScreenSizeCullingMode;
        [ReadOnly] public float CameraFieldOfView;
        [ReadOnly] public float OrdinaryObjectsCullScreenSize;
        [ReadOnly] public float OrdinaryObjectsCullScreenSizeMinDistance;
        [ReadOnly] public float HidingObjectsCullScreenSize;
        [ReadOnly] public float HidingObjectsCullScreenSizeMinDistance;
        
        // Buffer 0
        [ReadOnly] public float4x4 OcclusionBufferViewport0;
        [ReadOnly] public int2 OcclusionBufferSize0;
        [ReadOnly] public NativeArray<float> OcclusionBuffer0;
        
        // Buffer 1
        [ReadOnly] public float4x4 OcclusionBufferViewport1;
        [ReadOnly] public int2 OcclusionBufferSize1;
        [ReadOnly] public NativeArray<float> OcclusionBuffer1;
        
        // Buffer 2
        [ReadOnly] public float4x4 OcclusionBufferViewport2;
        [ReadOnly] public int2 OcclusionBufferSize2;
        [ReadOnly] public NativeArray<float> OcclusionBuffer2;
        
        [ReadOnly] public NativeArray<bool> RenderersVisible;
       
        [WriteOnly] public NativeList<int>.ParallelWriter RenderersToEnable;
        [WriteOnly] public NativeList<int>.ParallelWriter RenderersToDisable;

        public void Execute(int index)
        {
            var occludee = Occludees[index];
            bool previouslyVisible = RenderersVisible[index];
            var bounds = occludee.GetBounds();
            float2 boundsPosition = occludee.Position.xz;
            float distance = math.distance(boundsPosition, CameraPosition.xz);
            if (distance <= AlwaysShowRenderersInRadius)
            {
                if (!previouslyVisible)
                {
                    RenderersToEnable.AddNoResize(index);
                }
                return;
            }

            // Cull small objects by screen size.
            var cullingMode = OccludeesScreenSizeCullingMode[index];
            if (cullingMode != OccludeeScreenSizeCullingMode.None)
            {
                float cullScreenSize;
                float cullScreenSizeMinDistance;
                if (cullingMode == OccludeeScreenSizeCullingMode.CulledHidingObject)
                {
                    cullScreenSize = OrdinaryObjectsCullScreenSize;
                    cullScreenSizeMinDistance = OrdinaryObjectsCullScreenSizeMinDistance;
                }
                else
                {
                    cullScreenSize = HidingObjectsCullScreenSize;
                    cullScreenSizeMinDistance = HidingObjectsCullScreenSizeMinDistance;
                }
                
                if (distance > cullScreenSizeMinDistance && GeometryUtils.GetScreenRelativeHeight(bounds.size.y, distance, CameraFieldOfView) < cullScreenSize)
                {
                    if (previouslyVisible)
                    {
                        RenderersToDisable.AddNoResize(index);
                    }
                    return;
                }
            }
            
            if (!GeometryUtils.TestPlanesAABB(in CameraPlanes, bounds))
            {
                if (previouslyVisible)
                {
                    RenderersToDisable.AddNoResize(index);
                }
                return;
            }
            
            if (occludee.Contains(CameraPosition))
            {
                if (!previouslyVisible)
                {
                    RenderersToEnable.AddNoResize(index);
                }
                
                return;
            }
            
            var modelViewProjection = math.mul(CurrentCameraProjection, math.mul(CurrentCameraView, occludee.CalculateTRS()));

            bool needToChangeVisibilityState = SoftwareOcclusionCullingUtils.CullObjectAgainstOcclusionBuffer(
                previouslyVisible, modelViewProjection, NearClipPlane, FarClipPlane, OBBVertices,
                OcclusionBufferSize0, OcclusionBufferViewport0, OcclusionBuffer0,
                OcclusionBufferSize1, OcclusionBufferViewport1, OcclusionBuffer1,
                OcclusionBufferSize2, OcclusionBufferViewport2, OcclusionBuffer2);

            if (!needToChangeVisibilityState)
            {
                return;
            }
            
            if (previouslyVisible)
            {
                RenderersToDisable.AddNoResize(index);
            }
            else
            {
                RenderersToEnable.AddNoResize(index);
            }
        }
    }
}