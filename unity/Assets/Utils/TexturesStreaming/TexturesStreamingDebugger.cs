using System.Text;
using TMPro;
using UnityEngine;

public class TexturesStreamingDebugger : MonoBehaviour
{
    private const int _byteToMegaByte = 1024 * 1024;
    
    [SerializeField] private TextMeshProUGUI _texturesInfo;
    
    private readonly StringBuilder _stringBuilder = new(200);

    private void Awake()
    {
        DontDestroyOnLoad(gameObject);
    }

    private void Update()
    {
        _stringBuilder.Clear();
        _stringBuilder.AppendLine("Texture Memory Usage:");
        // Set the Memory Budget value slightly higher than the Texture.desiredTextureMemory value.
        _stringBuilder.AppendLine($"Desired(If there were no constraints) {Texture.desiredTextureMemory / _byteToMegaByte}");
        _stringBuilder.AppendLine($"Current(Streaming + NonStreaming) {Texture.currentTextureMemory / _byteToMegaByte}");
        _stringBuilder.AppendLine($"Target(Current moves to it) {Texture.targetTextureMemory / _byteToMegaByte}");
        
        _stringBuilder.AppendLine();
        _stringBuilder.AppendLine($"NonStreaming {Texture.nonStreamingTextureMemory / _byteToMegaByte}");
        _stringBuilder.AppendLine($"Total(If all textures load at mip 0) {Texture.totalTextureMemory / _byteToMegaByte}");
        
        _stringBuilder.AppendLine();
        _stringBuilder.AppendLine("Texture Count:");
        _stringBuilder.AppendLine($"Streaming Uploads Count {Texture.streamingMipmapUploadCount}");
        _stringBuilder.AppendLine($"Streaming Renderers Count {Texture.streamingRendererCount}");
        _stringBuilder.AppendLine($"Streaming Textures Count {Texture.streamingTextureCount}");
        _stringBuilder.AppendLine($"NonStreaming Textures Count {Texture.nonStreamingTextureCount}");
            
        _texturesInfo.text = _stringBuilder.ToString();
    }
}
