using Core;
using Game.Realm.Map.Camera;
using Game.Realm.SettlementBuilding;
using UnityEditor;
using UnityEngine;
using Utils.AssetsUtilityEditor;
using Utils.ColliderDesigner.Scripts.ExportRealm;
using Utils.Editor;
using Object = UnityEngine.Object;

namespace Utils.ColliderDesigner.Scripts.Realm
{
    public class RealmSectorsLevelEditor : MonoBehaviour
    {
        [field: SerializeField] public Object PrefabAsset { get; private set; }
        [field: SerializeField] public GameObject TextAsset { get; private set; }
        [field: SerializeField] public Material SectorsMaterial { get; private set; }
        [field: SerializeField] public Material SectorCellHoverMaterial { get; private set; }
        [field: SerializeField] public Material SectorCellSelectionMaterial { get; private set; }
        [field: SerializeField] public Material SectorCellDotsMaterial { get; private set; }
        [field: SerializeField] public Material SectorCellSpacesMaterial { get; private set; }
        [field: SerializeField] public RealmBuildingsMarkerView BuildingsMarkerAsset { get; private set; }
        
        [CustomEditor(typeof(RealmSectorsLevelEditor))]
        private class Editor : UnityEditor.Editor
        {
            private RealmSectorsLevelEditor _editorView;
            private static readonly ExportRealmCommandHandler _commandHandler = new();
            private MapCameraSettingsViewDescription _cameraSettingsViewDescription;
            private SerializedObject _settingsSerializedObject;
            private SerializedProperty _borderCenter;
            private SerializedProperty _bordersSize;

            private void OnEnable()
            {
                _editorView = (RealmSectorsLevelEditor)target;
                _cameraSettingsViewDescription = ScriptableObjectInstance.Get<MapCameraSettingsViewDescription>();
                _settingsSerializedObject = new SerializedObject(_cameraSettingsViewDescription);
                _borderCenter = _settingsSerializedObject.FindProperty(nameof(_cameraSettingsViewDescription.BordersCenter).ToBackingField());
                _bordersSize = _settingsSerializedObject.FindProperty(nameof(_cameraSettingsViewDescription.BordersSize).ToBackingField());
            }

            public override void OnInspectorGUI()
            {
                base.OnInspectorGUI();
                
                EditorGUI.BeginDisabledGroup(!_editorView.PrefabAsset);
                
                if (GUILayout.Button("Export"))
                {
                    _commandHandler.OnExportClicked(_editorView);
                }
                
                EditorGUI.EndDisabledGroup();
                
                _settingsSerializedObject.Update();

                EditorGUILayout.PropertyField(_borderCenter);
                EditorGUILayout.PropertyField(_bordersSize);

                if (_settingsSerializedObject.hasModifiedProperties)
                {
                    _settingsSerializedObject.ApplyModifiedProperties();
                }
            }

            private void OnSceneGUI()
            {
                Handles.color = Color.green;
                Handles.DrawWireCube(_cameraSettingsViewDescription.BordersCenter.Vector3(), _cameraSettingsViewDescription.BordersSize.Vector3());
            }
        }
    }
}