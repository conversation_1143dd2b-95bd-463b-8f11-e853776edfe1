using Models.Physics.Manifolds.Tests;
using Models.Physics.Types;
using Models.Physics.Utils;
using System;
using UnityEngine;
using Utils.Debugs.PhysicsDebug.PhysicsDebugScenes.Settings;
using Utils.GizmosExtensions;
using Vector3 = System.Numerics.Vector3;
using Quaternion = System.Numerics.Quaternion;

namespace Utils.Debugs.PhysicsDebug.PhysicsDebugScenes
{
    public class PlayerPlaneCollisionTestPhysicsDebugScene : PhysicsDebugScene<PlayerPlaneCollisionTestPhysicsDebugSceneSettings>
    {
        private GameObject _marker1;
        private GameObject _marker21;
        private GameObject _marker22;
        
        protected override void Draw(PlayerPlaneCollisionTestPhysicsDebugSceneSettings settings, PhysicsDebugSceneContext context)
        {
            Vector3 position1 = GetMarkerPosition(_marker1);
            float length1 = settings.Length1;
            float radius1 = settings.Radius1;
            float margin1 = settings.Margin1;
            
            GizmosShapesExtensions.DrawCapsule(position1, Quaternion.Identity, radius1, length1, Color.green);

            Vector3 planePoint = GetMarkerPosition(_marker21);
            Vector3 planeNormal = GetMarkerPosition(_marker22) - planePoint;

            if (planeNormal.LengthSquared() > 0.000001f)
            {
                planeNormal = Vector3.Normalize(planeNormal);
            }
            else
            {
                planeNormal = Vector3.UnitX;
            }

            GizmosShapesExtensions.DrawPlane(planePoint, planeNormal, 5.0f, Color.cyan);

            Span<ManifoldPoint> manifolds = stackalloc ManifoldPoint[1];

            int contactsCount = PlayerPlaneManifoldTest.Test(position1, length1, radius1, SurfaceType.Default, margin1, planePoint, planeNormal, SurfaceType.Default, manifolds);

            bool debugLocals = settings.DebugLocals;
            
            for (int i = 0; i < contactsCount; i++)
            {
                ref ManifoldPoint manifold = ref manifolds[i];

                Vector3 worldPositionOnA;
                Vector3 worldPositionOnB;
                Vector3 worldNormalOnA;
                Vector3 worldNormalOnB;

                if (debugLocals)
                {
                    Vector3 localPositionOnA = manifold.LocalPositionOnA;
                    Vector3 localPositionOnB = manifold.LocalPositionOnB;
                    Vector3 localNormalOnA = manifold.LocalNormalOnA;
                    Vector3 localNormalOnB = manifold.LocalNormalOnB;

                    worldPositionOnA = position1 + localPositionOnA;
                    worldPositionOnB = planePoint + localPositionOnB;
                    worldNormalOnA = localNormalOnA;
                    worldNormalOnB = localNormalOnB;
                }
                else
                {
                    worldPositionOnA = manifold.WorldPositionOnA;
                    worldPositionOnB = manifold.WorldPositionOnB;
                    worldNormalOnA = -manifold.WorldNormalOnB;
                    worldNormalOnB = manifold.WorldNormalOnB;
                }

                float distance = manifold.Distance;

                if (settings.DebugA)
                {
                    GizmosShapesExtensions.DrawSphere(worldPositionOnA, 0.1f, Color.red);
                    GizmosShapesExtensions.DrawLine(worldPositionOnA, worldPositionOnA + worldNormalOnA * distance, Color.blue);
                }

                if (settings.DebugB)
                {
                    GizmosShapesExtensions.DrawSphere(worldPositionOnB, 0.1f, Color.magenta);
                    GizmosShapesExtensions.DrawLine(worldPositionOnB, worldPositionOnB + worldNormalOnB * distance, Color.yellow);
                }
            }
        }
        
        protected override void Initialize(PlayerPlaneCollisionTestPhysicsDebugSceneSettings settings, PhysicsDebugSceneContext context)
        {
            _marker1 = CreateMarker("player", settings.transform, -5.0f, 0.0f, 0.0f);
            _marker21 = CreateMarker("plane_point", settings.transform, 0.0f, 0.0f, 0.0f);
            _marker22 = CreateMarker("plane_direction", settings.transform, 0.0f, 0.0f, 1.0f);
        }

        protected override void Free(PlayerPlaneCollisionTestPhysicsDebugSceneSettings settings, PhysicsDebugSceneContext context)
        {
            DestroyMarker(_marker1);
            DestroyMarker(_marker21);
            DestroyMarker(_marker22);
        }
    }
}