using System.Globalization;
using UnityEngine;

namespace Utils.PhysicsDebug.Tools
{
    public class PhysicsDebugCodeGenMeshVertices : MonoBehaviour
    {
        public bool LoadMesh;
        public bool DoubleSided;

        private void Update()
        {
            if (LoadMesh)
            {
                Mesh mesh = this.GetComponent<MeshFilter>().mesh;
                var vertices = mesh.vertices;
                var triangles = mesh.triangles;
                var normals = mesh.normals;

                string result = "private readonly IList<Vector3> mesh = new List<Vector3> { ";
                for (int i = 0; i < triangles.Length; i += 3)
                {
                    if (i > 0)
                    {
                        result += ", ";
                    }

                    Vector3 a = vertices[triangles[i + 0]];
                    Vector3 b = vertices[triangles[i + 1]];
                    Vector3 c = vertices[triangles[i + 2]];

                    if (!DoubleSided)
                    {
                        Vector3 normal = normals[i / 3];

                        Vector3 cross = Vector3.Cross(b - a, c - a);
                        float dot = Vector3.Dot(cross, normal);
                        if (dot < 0)
                        {
                            result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f),", a.x, a.y, a.z);
                            result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f),", b.x, b.y, b.z);
                            result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f)", c.x, c.y, c.z);
                        }
                        else
                        {
                            result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f),", a.x, a.y, a.z);
                            result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f),", c.x, c.y, c.z);
                            result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f)", b.x, b.y, b.z);
                        }
                    }
                    else
                    {
                        result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f),", a.x, a.y, a.z);
                        result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f),", b.x, b.y, b.z);
                        result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f),", c.x, c.y, c.z);
                        result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f),", a.x, a.y, a.z);
                        result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f),", c.x, c.y, c.z);
                        result += string.Format(CultureInfo.InvariantCulture, "new Vector3({0}f, {1}f, {2}f)", b.x, b.y, b.z);
                    }
                }
                result += " };\n";
            
                Debug.LogError(result);
            }
            LoadMesh = false;
        }
    }
}