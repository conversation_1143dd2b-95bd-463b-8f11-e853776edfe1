using System;
using UnityEngine;

namespace Utils.UiState
{
    [Serializable]
    public class StateTransformInfo : StateInfo<Transform>
    {
        [SerializeField] private Vector3 _localPosition;
        [SerializeField] private Vector3 _localVector3Rotation;
        [SerializeField] private Vector3 _localScale;

        public Vector3 LocalPosition
        {
            get => _localPosition;
            set => _localPosition = value;
        }
        
        public Vector3 LocalScale 
        {
            get => _localScale;
            set => _localScale = value;
        }

        public override void Apply(Transform target)
        {
            target.localPosition = _localPosition;
            target.localRotation = Quaternion.Euler(_localVector3Rotation);
            target.localScale = _localScale;
        }

        public override void Take(Transform target)
        {
            _localPosition = target.localPosition;
            _localVector3Rotation = target.localRotation.eulerAngles;
            _localScale = target.localScale;
        }
    }
}