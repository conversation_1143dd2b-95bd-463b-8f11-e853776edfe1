using System.IO;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;
using Utils.Editor;
using Utils.Geometry;

namespace Utils.LODTextureBaker
{
    public class BakedIntoObject
    {
        private readonly MeshFilter _meshFilter;
        private readonly Mesh _originalMesh;
        private readonly int[] _originalTriangles;
        private readonly Vector3[] _originalVertices;
        private readonly int[] _trianglesToProjectedSide;
        private readonly Vector2[] _newUV;

        public BakedIntoObject(MeshFilter meshFilter, Vector3[] directions)
        {
            _meshFilter = meshFilter;
            _originalMesh = _meshFilter.sharedMesh;
            _originalTriangles = _originalMesh.triangles;
            _originalVertices = _originalMesh.vertices;
            _trianglesToProjectedSide = FindDirectionOfProjectionForAllTriangles(_originalTriangles, _originalVertices, directions);

            _newUV = new Vector2[_originalVertices.Length];
        }

        public void CalculateNewUVForTrianglesInDirection(Camera bakeCamera, int bakeDirection, Vector2 scale, Vector2 offset)
        {
            for (int i = 0; i < _trianglesToProjectedSide.Length; i++)
            {
                if (_trianglesToProjectedSide[i] != bakeDirection)
                {
                    continue;
                }

                int index0 = _originalTriangles[i * 3];
                int index1 = _originalTriangles[i * 3 + 1];
                int index2 = _originalTriangles[i * 3 + 2];
                
                var vertex0 = _originalVertices[index0];
                var vertex1 = _originalVertices[index1];
                var vertex2 = _originalVertices[index2];

                _newUV[index0] = bakeCamera.WorldToViewportPoint(vertex0) * scale + offset;
                _newUV[index1] = bakeCamera.WorldToViewportPoint(vertex1) * scale + offset;
                _newUV[index2] = bakeCamera.WorldToViewportPoint(vertex2) * scale + offset;
            }
        }

        public void UpdateObjectWithBakedMesh(LODTextureBaker lodTextureBaker, string directoryPath)
        {
            lodTextureBaker.OriginalMeshes.Add(_originalMesh);
            var newMesh = Object.Instantiate(_originalMesh);
            newMesh.uv = _newUV;
            newMesh.name = newMesh.name.Replace("(Clone)", "");
            _meshFilter.sharedMesh = SaveMesh(newMesh, $"{newMesh.name}_Baked", directoryPath);
        }

        private static Mesh SaveMesh(Mesh mesh, string name, string directoryPath)
        {
            Directory.CreateDirectory(directoryPath);
            var pathToAsset = Path.Combine(directoryPath, FileName.WithExtension(name, AssetType.Mesh));
            AssetDatabase.CreateAsset(mesh, pathToAsset);

            return AssetDatabase.LoadAssetAtPath<Mesh>(pathToAsset);
        }

        private static int[] FindDirectionOfProjectionForAllTriangles(int[] originalTriangles, Vector3[] originalVertices, Vector3[] directions)
        {
            // Split triangles by sides.
            var trianglesToProjectedSide = new int[originalTriangles.Length / 3];
            for (int i = 0; i < originalTriangles.Length; i += 3)
            {
                var index0 = originalTriangles[i];
                var index1 = originalTriangles[i + 1];
                var index2 = originalTriangles[i + 2];

                var vertex0 = originalVertices[index0];
                var vertex1 = originalVertices[index1];
                var vertex2 = originalVertices[index2];

                // Find where triangle facing most, so we project texture from there.
                var normal = GeometryUtils.GetTriangleNormal(vertex0, vertex1, vertex2);
                int directionID = -1;
                float minValue = float.MaxValue;

                for (int j = 0; j < directions.Length; j++)
                {
                    float directionDot = math.dot(normal, directions[j]);
                    if (directionDot < minValue)
                    {
                        directionID = j;
                        minValue = directionDot;
                    }
                }

                trianglesToProjectedSide[i / 3] = directionID;
            }

            return trianglesToProjectedSide;
        }
    }
}