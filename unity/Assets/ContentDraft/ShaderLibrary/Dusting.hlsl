#ifndef DUSTING_SUPPORT_INCLUDED
#define DUSTING_SUPPORT_INCLUDED

#include "Assets/Utils/Builder/Content/TerrainTopDownTextures/Shaders/TerrainTopDownTextures.hlsl"
#include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"
#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Texture.hlsl"

TEXTURE2D(_DustingColorTexture);
TEXTURE2D(_DustingHeightNoise);
TEXTURE2D(_DustingBlendNoiseTexture);

float4 _DustingSettings0;
#define DustingColorTextureScale _DustingSettings0.x
#define DustingColorTextureBlend _DustingSettings0.y
#define DustingHeightNoiseScale _DustingSettings0.z
#define DustingHeightNoisePower _DustingSettings0.w

float4 _DustingSettings1;
#define DustingBlendNoiseScale _DustingSettings1.x
#define DustingBlendNoiseBlend _DustingSettings1.y
#define EnableDusting _DustingSettings1.z

float4 _DustingColor;

void Dusting(SamplerState samplerState, float dustingFactor, float dustingHeight, float dustingSmoothBorder, float3 positionWS, float3 normalWS, half3 albedo, half smoothness,
             out half3 resultAlbedo,
             out half resultSmoothness)
{
    dustingFactor *= EnableDusting;
    if (dustingFactor == 0)
    {
        resultAlbedo = albedo;
        resultSmoothness = smoothness;
        return;
    }

    float2 terrainUV = GetTopDownTextureUV(positionWS);
    float terrainHeight = GetTerrainHeight(terrainUV);
    float heightNoise = SAMPLE_TEXTURE2D(_DustingHeightNoise, samplerState, positionWS.xz * DustingHeightNoiseScale).r;

    float pixelDustLevel = positionWS.y + lerp(0, DustingHeightNoisePower, heightNoise);
    if (pixelDustLevel > terrainHeight)
    {
        float blendNoise = Triplanar(_DustingBlendNoiseTexture, samplerState, positionWS, normalWS, DustingBlendNoiseScale, DustingBlendNoiseBlend).x;
        float blend = 1 - smoothstep(dustingHeight, dustingHeight + dustingSmoothBorder + blendNoise * 0.5, pixelDustLevel - terrainHeight);
        blend *= dustingFactor;

        half3 dusting = Triplanar(_DustingColorTexture, samplerState, positionWS, normalWS, DustingColorTextureScale, DustingColorTextureBlend).rgb;

        resultAlbedo = saturate(lerp(albedo, dusting * _DustingColor.rgb, blend));
        resultSmoothness = lerp(smoothness, dusting.r * blend * 0.5, blend);
    }
    else
    {
        resultAlbedo = albedo;
        resultSmoothness = smoothness;
    }
}

void Dusting_float(UnitySamplerState reusedSampler, float dustingFactor, float dustingHeight, float dustingSmoothBorder, float3 positionWS, float3 normalWS, half3 albedo, half smoothness,
                   out half3 resultAlbedo,
                   out half resultSmoothness)
{
    Dusting(reusedSampler.samplerstate, dustingFactor, dustingHeight, dustingSmoothBorder, positionWS, normalWS, albedo, smoothness, resultAlbedo, resultSmoothness);
}
#endif
