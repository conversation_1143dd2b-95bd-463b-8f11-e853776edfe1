Shader "Kefir/Kefir_Settlement_Road"
{
    Properties
    {
        [Header(BaseMap (UV1))]
        _Base_Color_A("Base Color (A)", Color) = (1, 1, 1, 0)
        _BaseColor("Base Color (B)", Color) = (1, 1, 1, 1)
        [NoScaleOffset]_BaseMap("Base Map", 2D) = "white" {}
        [Normal][NoScaleOffset]_BumpMap("Normal Map", 2D) = "bump" {}
        [NoScaleOffset]_MaskMap("Metal(R) Smooth(G) AO(B)", 2D) = "black" {}

        [Header(Layers (UV0))]
        [KeywordEnum(None, One, Two, Three, Four)]_LAYERS("Layers", Float) = 0
        [NoScaleOffset]_SplatMap("SplatMap", 2D) = "white" {}

        [NoScaleOffset]_SplatMapNoiseMap("NoiseMap", 2D) = "white" {}
        _SplatMapNoisePower("Noise Power", Vector) = (1, 1, 1, 1)
        _SpaltMapNoiseTiling("Noise Tiling", Float) = 1

        [Header(LayerR (UV2))]
        _LayerRScale("LayerRScale", Float) = 1
        _LayerRColor("LayerRColor", Color) = (1, 1, 1, 1)
        [NoScaleOffset]_LayerRBaseMap("LayerRBaseMap", 2D) = "white" {}
        [Normal][NoScaleOffset]_LayerRBumpMap("LayerRBumpMap", 2D) = "bump" {}
        [NoScaleOffset]_LayerRMaskMap("LayerRMaskMap", 2D) = "black" {}

        [Header(LayerG (UV3))]
        _LayerGScale("LayerGScale", Float) = 1
        _LayerGColor("LayerGColor", Color) = (1, 1, 1, 1)
        [NoScaleOffset]_LayerGBaseMap("LayerGBaseMap", 2D) = "white" {}
        [Normal][NoScaleOffset]_LayerGBumpMap("LayerGBumpMap", 2D) = "bump" {}
        [NoScaleOffset]_LayerGMaskMap("LayerGMaskMap", 2D) = "black" {}

        [Header(LayerB (UV4))]
        _LayerBScale("LayerBScale", Float) = 1
        _LayerBColor("LayerBColor", Color) = (1, 1, 1, 1)
        [NoScaleOffset]_LayerBBaseMap("LayerBBaseMap", 2D) = "white" {}
        [Normal][NoScaleOffset]_LayerBBumpMap("LayerBBumpMap", 2D) = "bump" {}
        [NoScaleOffset]_LayerBMaskMap("LayerBMaskMap", 2D) = "black" {}

        [Header(LayerA (UV5))]
        _LayerAScale("LayerAScale", Float) = 1
        _LayerAColor("LayerAColor", Color) = (1, 1, 1, 1)
        [NoScaleOffset]_LayerABaseMap("LayerABaseMap", 2D) = "white" {}
        [Normal][NoScaleOffset]_LayerABumpMap("LayerABumpMap", 2D) = "bump" {}
        [NoScaleOffset]_LayerAMaskMap("LayerAMaskMap", 2D) = "black" {}
    }

    SubShader
    {
        Tags
        {
            "RenderType" = "Opaque" "RenderPipeline" = "UniversalPipeline" "Queue" = "Geometry+100"
        }

        HLSLINCLUDE
        #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
        #include "Assets/Content/ShaderLibrary/UtilityFunctions.hlsl"

        TEXTURE2D(_BaseMap);

        TEXTURE2D(_BumpMap);
        SAMPLER(sampler_BumpMap);

        TEXTURE2D(_MaskMap);
        SAMPLER(sampler_MaskMap);

        TEXTURE2D(_SplatMap);
        SAMPLER(sampler_SplatMap);

        TEXTURE2D(_SplatMapNoiseMap);
        SAMPLER(sampler_SplatMapNoiseMap);

        TEXTURE2D(_LayerRBaseMap);
        SAMPLER(sampler_LayerRBaseMap);

        TEXTURE2D(_LayerRBumpMap);
        SAMPLER(sampler_LayerRBumpMap);

        TEXTURE2D(_LayerRMaskMap);
        SAMPLER(sampler_LayerRMaskMap);

        TEXTURE2D(_LayerGBaseMap);
        SAMPLER(sampler_LayerGBaseMap);
        
        TEXTURE2D(_LayerGBumpMap);
        TEXTURE2D(_LayerGMaskMap);
        TEXTURE2D(_LayerBBaseMap);
        TEXTURE2D(_LayerBBumpMap);
        TEXTURE2D(_LayerBMaskMap);
        TEXTURE2D(_LayerABaseMap);
        TEXTURE2D(_LayerABumpMap);
        TEXTURE2D(_LayerAMaskMap);

        CBUFFER_START(UnityPerMaterial)
            float4 _SplatMapNoisePower;
            float _SpaltMapNoiseTiling;
            float _LayerRScale;
            float _LayerGScale;
            float _LayerBScale;
            float _LayerAScale;
            half4 _BaseColor;
            half4 _Base_Color_A;
            half4 _LayerRColor;
            half4 _LayerGColor;
            half4 _LayerBColor;
            half4 _LayerAColor;
        CBUFFER_END

        struct CustomAttributes
        {
            float4 positionOS : POSITION;
            float3 normalOS : NORMAL;
            float4 tangentOS : TANGENT;
            #if !defined(_LAYERS_NONE)
                float2 uv0 : TEXCOORD0;
            #endif

            float2 uv1 : TEXCOORD1;

            #if defined(_LAYERS_ONE) || defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                float2 uv2 : TEXCOORD2;
            #endif

            #if defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                float2 uv3 : TEXCOORD3;
            #endif

            #if defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                float2 uv4 : TEXCOORD4;
            #endif

            #if defined(_LAYERS_FOUR)
                float2 uv5 : TEXCOORD5;
            #endif
        };

        void ReusedSplatmapSampling(float2 splatMapUV, float2 layerRUV, float2 layerGUV, float2 layerBUV, float2 layerAUV, out float4 layerFactors, out float blend)
        {
            float4 splatMapValue = SAMPLE_TEXTURE2D_LOD(_SplatMap, sampler_SplatMap, splatMapUV, 0);
            float4 splatMapNoiseValue = SAMPLE_TEXTURE2D_LOD(_SplatMapNoiseMap, sampler_SplatMapNoiseMap, splatMapUV * _SpaltMapNoiseTiling, 0);

            splatMapValue += lerp(float4(1, 1, 1, 1), splatMapNoiseValue, _SplatMapNoisePower) * splatMapValue;
            splatMapValue = saturate(splatMapValue);

            // Use transparency of base texture to decrease weight, so we can correctly blend between combined layer textures and base texture.
            float4 splatMapValueWithNoiseAndTransparency = splatMapValue;

            // Blending of layers similar to TerrainSplatmapCommon.cginc from builtin shaders.
            half weight = dot(splatMapValue, float4(1, 1, 1, 1));
            splatMapValue /= (weight + 1e-3f);
            layerFactors = 0;

            #if defined(_LAYERS_ONE) || defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                half4 layerRColorMap = SAMPLE_TEXTURE2D(_LayerRBaseMap, sampler_LayerRBaseMap, layerRUV);
                layerFactors.r = splatMapValue.r * layerRColorMap.a;
                splatMapValueWithNoiseAndTransparency.r *= layerRColorMap.a;
            #endif

            #if defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                half4 layerGColorMap = SAMPLE_TEXTURE2D(_LayerGBaseMap, sampler_LayerGBaseMap, layerGUV);
                layerFactors.g = splatMapValue.g * layerGColorMap.a;
                splatMapValueWithNoiseAndTransparency.g *= layerGColorMap.a;
            #endif

            #if defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                half4 layerBColorMap = SAMPLE_TEXTURE2D(_LayerBBaseMap, sampler_LayerRBaseMap, layerBUV);
                layerFactors.b = splatMapValue.b * layerBColorMap.a;
                splatMapValueWithNoiseAndTransparency.b *= layerBColorMap.a;
            #endif

            #if defined(_LAYERS_FOUR)
                half4 layerAColorMap = SAMPLE_TEXTURE2D(_LayerABaseMap, sampler_LayerGBaseMap, layerAUV);
                layerFactors.a = splatMapValue.a * layerBColorMap.a;
                splatMapValueWithNoiseAndTransparency.a *= layerBColorMap.a;
            #endif

            blend = saturate(dot(splatMapValueWithNoiseAndTransparency, float4(1, 1, 1, 1)));
        }

        float3 ReusedNormalMixing(half3 normalTS, float2 layerRUV, float2 layerGUV, float2 layerBUV, float2 layerAUV, float4 layerFactors, float blend)
        {
            half3 combinedLayersNormal = 0;
            #if defined(_LAYERS_ONE) || defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                combinedLayersNormal += UnpackNormal(SAMPLE_TEXTURE2D(_LayerRBumpMap, sampler_LayerRBumpMap, layerRUV)) * layerFactors.r;
            #endif

            #if defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                combinedLayersNormal += UnpackNormal(SAMPLE_TEXTURE2D(_LayerGBumpMap, sampler_BumpMap, layerGUV)) * layerFactors.g;
            #endif

            #if defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                combinedLayersNormal += UnpackNormal(SAMPLE_TEXTURE2D(_LayerBBumpMap, sampler_LayerRBumpMap, layerBUV)) * layerFactors.b;
            #endif

            #if defined(_LAYERS_FOUR)
                combinedLayersNormal += UnpackNormal(SAMPLE_TEXTURE2D(_LayerABumpMap, sampler_BumpMap, layerAUV)) * layerFactors.a;
            #endif

            // To avoid nan after normalizing.
            combinedLayersNormal.z += 1e-5f;

            return lerp(normalTS, normalize(combinedLayersNormal), blend);
        }
        
        half3 KefirSampleNormalTS(float2 uv, float2 uv1, float2 uv2, float2 uv3, float2 uv4, float2 uv5)
        {
            float4 layerFactors;
            float blend;
            ReusedSplatmapSampling(uv, uv2, uv3, uv4, uv5, layerFactors, blend);
            half3 normalTS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, uv1));
            return ReusedNormalMixing(normalTS, uv2, uv3, uv4, uv5, layerFactors, blend);
        }
        ENDHLSL

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma shader_feature_local _LAYERS_NONE _LAYERS_ONE _LAYERS_TWO _LAYERS_THREE _LAYERS_FOUR

            #pragma multi_compile_fragment _ LOD_FADE_CROSSFADE
            #if defined(LOD_FADE_CROSSFADE)
                #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #endif

            #include_with_pragmas "Assets/Content/ShaderLibrary/URPKeywords.hlsl"
            #include "Assets/Content/ShaderLibrary/Lighting/KefirLighting.hlsl"
            
            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float4x3 tbn_positionWS : TEXCOORD0;
                DECLARE_SH(sh, 3)

                #if defined(_LAYERS_NONE)
                float2 packedUV0 : TEXCOORD4;
                #else
                    float4 packedUV0 : TEXCOORD4;
                #endif

                #if defined(_LAYERS_ONE)
                    float2 packedUV1 : TEXCOORD5;
                #elif defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                    float4 packedUV1 : TEXCOORD5;
                #endif

                #if defined(_LAYERS_THREE)
                    float2 packedUV2 : TEXCOORD6;
                #elif defined(_LAYERS_FOUR)
                    float4 packedUV2 : TEXCOORD6;
                #endif
            };

            #include "Assets/Content/ShaderLibrary/Lighting/Functions/KefirLitInputHelpers.hlsl"

            Varyings vert(CustomAttributes i)
            {
                Varyings o;

                VertexPositionInputs vertexInput = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInput.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInput, normalInputs, o.tbn_positionWS);

                #if defined(_LAYERS_NONE)
                o.packedUV0 = i.uv1;
                #else
                    o.packedUV0 = float4(i.uv1, i.uv0);
                #endif

                #if defined(_LAYERS_ONE)
                    o.packedUV1 = i.uv2 * _LayerRScale;
                #elif defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                    o.packedUV1 = float4(i.uv2 * _LayerRScale, i.uv3 * _LayerGScale);
                #endif

                #if defined(_LAYERS_THREE)
                    o.packedUV2 = i.uv4 * _LayerBScale;
                #elif defined(_LAYERS_FOUR)
                    o.packedUV2 = float4(i.uv4 * _LayerBScale, i.uv5 * _LayerAScale);
                #endif
                
                OUTPUT_VERTEX_SH(normalInputs.normalWS, o.sh)
                return o;
            }

            half4 frag(Varyings i) : SV_Target
            {
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(i.positionCS);
                #endif
                
                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                float2 baseUV = i.packedUV0.xy;
                half3 baseColor = SAMPLE_TEXTURE2D(_BaseMap, sampler_MaskMap, baseUV).rgb;
                half3 msa = SAMPLE_TEXTURE2D(_MaskMap, sampler_MaskMap, baseUV).rgb;
                half3 normalTS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, baseUV));

                #if !defined(_LAYERS_NONE)
                    float2 layerRUV = 0;
                    float2 layerGUV = 0;
                    float2 layerBUV = 0;
                    float2 layerAUV = 0;

                    #if defined(_LAYERS_ONE) || defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                        layerRUV = i.packedUV1.xy;
                    #endif

                    #if defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                        layerGUV = i.packedUV1.zw;
                    #endif

                    #if defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                        layerBUV = i.packedUV2.xy;
                    #endif

                    #if defined(_LAYERS_FOUR)
                        layerAUV = i.packedUV2.zw;
                    #endif

                    float4 layerFactors;
                    float blend;
                    ReusedSplatmapSampling(baseUV, layerRUV, layerGUV, layerBUV, layerAUV, layerFactors, blend);

                    half3 combinedLayersColor = 0;
                    half3 combinedLayersMSA = 0;

                    #if defined(_LAYERS_ONE) || defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                        combinedLayersColor += SAMPLE_TEXTURE2D(_LayerRBaseMap, sampler_LayerRBaseMap, layerRUV).rgb * _LayerRColor.rgb * layerFactors.r;
                        combinedLayersMSA += SAMPLE_TEXTURE2D(_LayerRMaskMap, sampler_LayerRMaskMap, layerRUV).rgb * layerFactors.r;
                    #endif

                    #if defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                        combinedLayersColor += SAMPLE_TEXTURE2D(_LayerGBaseMap, sampler_LayerGBaseMap, layerGUV).rgb * _LayerGColor.rgb * layerFactors.g;
                        combinedLayersMSA += SAMPLE_TEXTURE2D(_LayerGMaskMap, sampler_MaskMap, layerGUV).rgb * layerFactors.g;
                    #endif

                    #if defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                        combinedLayersColor += SAMPLE_TEXTURE2D(_LayerBBaseMap, sampler_LayerRBaseMap, layerBUV).rgb * _LayerBColor.rgb * layerFactors.b;
                        combinedLayersMSA += SAMPLE_TEXTURE2D(_LayerBMaskMap, sampler_LayerRMaskMap, layerBUV).rgb * layerFactors.b;
                    #endif

                    #if defined(_LAYERS_FOUR)
                        combinedLayersColor += SAMPLE_TEXTURE2D(_LayerABaseMap, sampler_LayerGBaseMap, layerAUV).rgb * _LayerAColor.rgb * layerFactors.a;
                        combinedLayersMSA += SAMPLE_TEXTURE2D(_LayerAMaskMap, sampler_MaskMap, layerAUV).rgb * layerFactors.a;
                    #endif
                
                    baseColor = lerp(baseColor, combinedLayersColor, blend);
                    msa = lerp(msa, combinedLayersMSA, blend);
                    normalTS = ReusedNormalMixing(normalTS, layerRUV, layerGUV, layerBUV, layerAUV, layerFactors, blend);
                #endif

                SurfaceData surfaceData = InitializeSurfaceData(half4(baseColor, 1), msa, normalTS);
                InputData inputData = InitializeInputData(i, positionWS, surfaceData.normalTS, tbn);
                return UniversalFragmentPBR(inputData, surfaceData);
            }
            ENDHLSL
        }

        Pass
        {
            Name "DepthNormals"
            Tags
            {
                "LightMode" = "DepthNormals"
            }

            ZWrite On

            HLSLPROGRAM
            
            #pragma target 2.0

            #pragma vertex DepthNormalsVertex
            #pragma fragment DepthNormalsFragment
            #pragma multi_compile_fragment _ LOD_FADE_CROSSFADE
            #pragma shader_feature_local _LAYERS_NONE _LAYERS_ONE _LAYERS_TWO _LAYERS_THREE _LAYERS_FOUR
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            
            #if defined(LOD_FADE_CROSSFADE)
                #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/LODCrossFade.hlsl"
            #endif
            
            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float4x3 tbn_positionWS : TEXCOORD0;
                
                #if defined(_LAYERS_NONE)
                    float2 packedUV0 : TEXCOORD3;
                #else
                    float4 packedUV0 : TEXCOORD3;
                #endif

                #if defined(_LAYERS_ONE)
                    float2 packedUV1 : TEXCOORD4;
                #elif defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                    float4 packedUV1 : TEXCOORD4;
                #endif

                #if defined(_LAYERS_THREE)
                    float2 packedUV2 : TEXCOORD5;
                #elif defined(_LAYERS_FOUR)
                    float4 packedUV2 : TEXCOORD5;
                #endif
            };

            Varyings DepthNormalsVertex(CustomAttributes i)
            {
                Varyings o = (Varyings)0;

                VertexPositionInputs vertexInput = GetVertexPositionInputs(i.positionOS.xyz);
                o.positionCS = vertexInput.positionCS;

                VertexNormalInputs normalInputs = GetVertexNormalInputs(i.normalOS, i.tangentOS);

                PackTBNAndPositionWS(vertexInput, normalInputs, o.tbn_positionWS);

                #if defined(_LAYERS_NONE)
                o.packedUV0 = i.uv1;
                #else
                    o.packedUV0 = float4(i.uv1, i.uv0);
                #endif

                #if defined(_LAYERS_ONE)
                    o.packedUV1 = i.uv2 * _LayerRScale;
                #elif defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                    o.packedUV1 = float4(i.uv2 * _LayerRScale, i.uv3 * _LayerGScale);
                #endif

                #if defined(_LAYERS_THREE)
                    o.packedUV2 = i.uv4 * _LayerBScale;
                #elif defined(_LAYERS_FOUR)
                    o.packedUV2 = float4(i.uv4 * _LayerBScale, i.uv5 * _LayerAScale);
                #endif
                
                return o;
            }

            void DepthNormalsFragment(Varyings i, out half4 outNormalWS : SV_Target0
                #ifdef _WRITE_RENDERING_LAYERS
                    , out float4 outRenderingLayers : SV_Target1
                #endif
            )
            {
                #if defined(LOD_FADE_CROSSFADE)
                    LODFadeCrossFade(i.positionCS);
                #endif

                half3x3 tbn;
                float3 positionWS;
                UnpackTBNAndPositionWS(i.tbn_positionWS, tbn, positionWS);

                float2 baseUV = i.packedUV0.xy;
                half3 normalTS = UnpackNormal(SAMPLE_TEXTURE2D(_BumpMap, sampler_BumpMap, baseUV));
                
                #if !defined(_LAYERS_NONE)
                    float2 layerRUV = 0;
                    float2 layerGUV = 0;
                    float2 layerBUV = 0;
                    float2 layerAUV = 0;
                    
                    #if defined(_LAYERS_ONE) || defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                        layerRUV = i.packedUV1.xy;
                    #endif

                    #if defined(_LAYERS_TWO) || defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                        layerGUV = i.packedUV1.zw;
                    #endif

                    #if defined(_LAYERS_THREE) || defined(_LAYERS_FOUR)
                        layerBUV = i.packedUV2.xy;
                    #endif

                    #if defined(_LAYERS_FOUR)
                        layerAUV = i.packedUV2.zw;
                    #endif

                    float4 layerFactors;
                    float blend;
                    ReusedSplatmapSampling(baseUV, layerRUV, layerGUV, layerBUV, layerAUV, layerFactors, blend);
                    normalTS = ReusedNormalMixing(normalTS, layerRUV, layerGUV, layerBUV, layerAUV, layerFactors, blend);
                #endif
                
                outNormalWS = half4(TransformUnpackedToWorldNormal(normalTS, tbn), 0.0);
                
                #ifdef _WRITE_RENDERING_LAYERS
                    uint renderingLayers = GetMeshRenderingLayer();
                    outRenderingLayers = float4(EncodeMeshRenderingLayer(renderingLayers), 0, 0, 0);
                #endif
            }
            ENDHLSL
        }
        Pass
        {
            Name "ShadowCaster"
            Tags
            {
                "LightMode" = "ShadowCaster"
            }

            ZWrite On
            ZTest LEqual
            ColorMask 0
            Cull[_Cull]

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ShadowCaster.hlsl"
            ENDHLSL
        }
        Pass
        {
            Name "Object Motion Vectors"
            Tags
            {
                "LightMode" = "MotionVectors"
            }

            HLSLPROGRAM
            #include_with_pragmas "Assets/Content/ShaderLibrary/Passes/ObjectMotionVectors.hlsl"
            ENDHLSL
        }
    }
}