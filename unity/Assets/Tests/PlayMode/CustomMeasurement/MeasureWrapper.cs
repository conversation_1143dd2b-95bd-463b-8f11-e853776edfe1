using System;
using System.Collections;
using System.Collections.Generic;
using Tests.PlayMode.ReporterDataCollector;
using Tests.PlayMode.Utility;
using Unity.PerformanceTesting;

namespace Tests.PlayMode.CustomMeasurement
{
    public static class MeasureWrapper
    {
        private const int _defaultMeasurementCount = 1000;
        private const int _defaultWarmupCount = 100;
        
        public static void CustomMeasurement(string name, double measure, bool onlyAggregations, List<string> aggregationList, string location, string comment, bool isFromSetup = false)
        {
            if (PerformanceTest.Active != null)
            {
                Measure.Custom(name, measure);
                ReportDataCollector.AddMeasurementComment(onlyAggregations, aggregationList, comment, location, false, isFromSetup);
            }
        }
        
        public static void CustomMeasurementList(string name, List<double> measures, bool onlyAggregations, List<string> aggregationList, string location, string comment, bool isFromSetup = false)
        {
            if (PerformanceTest.Active != null)
            {
                foreach (double measure in measures)
                {
                    Measure.Custom(name, measure);
                }
                ReportDataCollector.AddMeasurementComment(onlyAggregations, aggregationList, comment, location, false, isFromSetup);
            }
        }
        
        public static IEnumerator FramesMeasurement(bool needMeasureFps, bool onlyAggregations, List<string> aggregationList, string location, string comment, string name = "Time", int measurementCount = _defaultMeasurementCount, int warmupCount = _defaultWarmupCount)
        {
            return PerformanceTest.Active != null ? Measure.Scope(name).RunMeasure(needMeasureFps, location, comment, name, onlyAggregations, aggregationList, measurementCount, warmupCount) : null;
        }

        public static IDisposable ScopedFrameMetricsMeasurement(bool needMeasureFps, bool onlyAggregations, List<string> aggregationList, string location, string comment, string name = "Time", bool isFromSetup = false)
        {
            return PerformanceTest.Active != null ? Measure.Frames().Scope(name).WithComment(location, onlyAggregations, aggregationList, comment, needMeasureFps, isFromSetup) : null;
        }

        public static IDisposable ScopeMeasurement(bool onlyAggregations, List<string> aggregationList, string location, string comment, string name = "Time", bool isFromSetup = false)
        {
            return PerformanceTest.Active != null ? Measure.Scope(name).WithComment(location, onlyAggregations, aggregationList, comment, isFromSetup) : null;
        }

        public static Measurements Measurements(params MeasurementDescription[] measurementDescriptions)
        {
            return PerformanceTest.Active != null ? new Measurements(measurementDescriptions) : null;
        }
    }
}