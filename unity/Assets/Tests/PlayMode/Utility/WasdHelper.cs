using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Controls;

namespace Tests.PlayMode.Utility
{
    public class WasdHelper
    {
        private readonly Dictionary<Vector3, HashSet<KeyControl>> _directionKeys;

        public WasdHelper(Keyboard keyboard)
        {
            _directionKeys = new Dictionary<Vector3, HashSet<KeyControl>>
            {
                { Vector3.right, new HashSet<KeyControl>(new[] { keyboard.dKey }) },
                { Vector3.forward, new HashSet<KeyControl>(new[] { keyboard.wKey }) },
                { Vector3.left, new HashSet<KeyControl>(new[] { keyboard.aKey }) },
                { Vector3.back, new HashSet<KeyControl>(new[] { keyboard.sKey }) }
            };
        }
        
        public IEnumerable<KeyControl> GetKeys(Vector3 currentPoint, Vector3 targetPoint, float angle)
        {
            Quaternion rotation = Quaternion.AngleAxis(angle, Vector3.up);
            Vector3 resultingPoint = Move(targetPoint, currentPoint);
            
            return GetKeysByDirection(resultingPoint, rotation);
        }

        private Vector3 Move(Vector3 point, Vector3 centerPoint)
        {
            return new Vector3(point.x - centerPoint.x, point.y - centerPoint.y, point.z - centerPoint.z);
        }

        private ISet<KeyControl> GetKeysByDirection(Vector3 direction, Quaternion rotation)
        {
            ISet<KeyControl> keys = _directionKeys.OrderBy(pair => Math.Abs(Vector3.SignedAngle(direction, rotation * pair.Key, Vector3.up))).First().Value;
            
            return keys;
        }
    }
}