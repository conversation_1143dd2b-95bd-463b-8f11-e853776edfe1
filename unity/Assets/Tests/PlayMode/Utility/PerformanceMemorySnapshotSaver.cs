using System;
using Unity.Profiling.Memory;
using UnityEngine;

namespace Tests.PlayMode.Utility
{
    public class PerformanceMemorySnapshotSaver
    {
        static Action<string, bool> finishCallback = (path, success) =>
        {
            if (!success)
            {
                Debug.LogError("Failed to save memory snapshot.");
            }
            else
            {
                Debug.Log("Memory snapshot saved at: " + path);
            }
        };

        public static void TakeSnapshot(string snapshotPath)
        {
            MemoryProfiler.TakeSnapshot(snapshotPath, finishCallback);
        }
    }
}