using Models.Cheats;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Models.Messages;
using Models.References.Car;
using Models.References.Clan;
using Models.References.ClanBattle;
using Tests.PlayMode.Adapter;
using Tests.PlayMode.CustomMeasurement;
using Tests.PlayMode.InputWrapper;
using Tests.PlayMode.TestStep.Ui.MouseKeyboard;
using Tests.PlayMode.Utility;
using Tests.PlayMode.YieldInstruction;
using UnityEngine;
using Utils.TypeCastExtensions;
using Quaternion = System.Numerics.Quaternion;
using ReturnToStartPositionStep = Tests.PlayMode.TestStep.Ui.MouseKeyboard.ReturnToStartPositionStep;

namespace Tests.PlayMode.TestStep.Ui.TestCase.MouseKeyboard
{
    public class GvGTest : UiStep<IMouseKeyboardInput>
    {
        private const int _delay = 3;

        public GvGTest(IUiAdapter<IMouseKeyboardInput> adapter) : base(adapter)
        {
        }

        protected override IEnumerator<IEnumerator> AdapterActions(IUiAdapter<IMouseKeyboardInput> adapter)
        {
            float distanceCount;
            int i;
            int vehicleId;
            yield return new FindRealmStep(adapter);
            yield return new LeaveBattleCheatStep(adapter);
            yield return new CreateClanCheatStep(adapter, new RandomizeHelper().GetRandomClanName(), new RandomizeHelper().GetRandomClanTag(), new RandomizeHelper().GetRandomClanDescription(), ClanEmblemDescription.Emblem0, ClanLanguageDescription.Ru, ClanEnterTypeDescription.Open);
            yield return new ClanBattlesCheatStep(adapter, ClanConquestObjectTypeDescription.CityStripClub, ClanBattleTeamDescription.Defender);
            yield return new WaitForSecondsRealtime(_delay);

            distanceCount = 5;
            var botAttacker = SpawnGvgModelPresets.BotAttackerOnBulettiThunderXModel;
            for (i = 0; i < ClanBattleMemberSlotDescription.Enum.Count; i++)
            {
                botAttacker.CustomizationInfo = CustomizationInfoBotPresets.GetRandomPresets();
                yield return new SpawnBotStep(adapter, BotFlagsPresets.IsDrivingForwardBackwardCar, botAttacker, position: PerformanceTeleportRobberyPoints.CrossroadStripClubPoint + new Vector3(distanceCount, 0, 2 * i));
                distanceCount += 2.5f;
            }
            
            distanceCount = -3;
            var botDefender = SpawnGvgModelPresets.BotDefenderModel;
            for (i = 0; i < ClanBattleMemberSlotDescription.Enum.Count - 1; i++)
            {
                botDefender.CustomizationInfo = CustomizationInfoBotPresets.GetRandomPresets();
                yield return new SpawnBotStep(adapter, BotFlagsPresets.IsFiringAndRotateAndMoving, botDefender, position: PerformanceTeleportRobberyPoints.CrossroadStripClubPoint + new Vector3(distanceCount, 0, 3 * i));
                distanceCount += 2.5f;
            }

            distanceCount = 1;
            for (i = 0; i < ClanBattleMemberSlotDescription.Enum.Count - 1; i++)
            {
                var carsValues = adapter.GameModel.BattleModel.BattleEntitiesModel.WorldEntitiesModel.Cars.Values.Count();
                var (spawnPosition, spawnOrientation) = CheatSpawnVehicleExtensions.CalculateSpawnPosition(CheatSpawnVehicleTransformType.CustomTransform, adapter.GameModel.BattleModel.LocationDescription,
                    adapter.GameModel.BattleModel.BattleEntitiesModel.Player.ServerModel,
                    PerformanceTeleportRobberyPoints.ResortRegionCarWarpPosition.ToSystemVector3() + new Vector3(distanceCount, 0, distanceCount).ToSystemVector3(), Quaternion.Identity);
                yield return new SendBattleCheatStep(adapter, new CheatSpawnCarMessage(CarDescription.LonghornTitan, spawnPosition, spawnOrientation));
                yield return new WaitForCondition(() => adapter.GameModel.BattleModel.BattleEntitiesModel.WorldEntitiesModel.Cars.Values.Count() > carsValues);
                distanceCount += 2.5f;
            }

            yield return new SpawnVehicleCheatStep(adapter, CarDescription.FukurouSpark);
            vehicleId = adapter.GameModel.BattleModel.BattleEntitiesModel.WorldEntitiesModel.Cars.Values.Max(c => c.Id);
            yield return new EnterInVehicleStep(adapter, vehicleId);
            yield return new WarpWithCarStep(adapter, PerformanceTeleportRobberyPoints.ResortRegionCarWarpPosition - new Vector3(0, 0, 5), PerformanceTeleportRobberyPoints.GvgStripClubCarSettingAngle);
            yield return new CheatLookAtStep(adapter, PerformanceTeleportRobberyPoints.CrossroadStripClubCameraDirectionPoint, accuracy: 10f);
            yield return new WaitForSecondsRealtime(_delay);

            yield return new CustomMetrics(adapter, "ClanBattle");    
            yield return new WaitForSecondsRealtime(_delay);
            yield return MeasureWrapper.FramesMeasurement(true, true, FrameMeasurementHelper.AggregationList, "ClanBattle", "GvGStripClub", "FPS");
            yield return new ReturnToStartPositionStep(adapter);
        }
    }
}