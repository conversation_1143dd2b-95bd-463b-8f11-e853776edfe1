using System.Collections;
using System.Collections.Generic;
using Tests.PlayMode.Adapter;
using Tests.PlayMode.CustomMeasurement;
using Tests.PlayMode.InputWrapper;
using Tests.PlayMode.TestStep.Ui.MouseKeyboard;
using Tests.PlayMode.Utility;
using UnityEngine;

namespace Tests.PlayMode.TestStep.Ui.TestCase.MouseKeyboard
{
    public class FpsSettlementHangarRunning : UiStep<IMouseKeyboardInput>
    {
        private float _delay = 3f;

        public FpsSettlementHangarRunning(IUiAdapter<IMouseKeyboardInput> adapter) : base(adapter)
        {
        }

        protected override IEnumerator<IEnumerator> AdapterActions(IUiAdapter<IMouseKeyboardInput> adapter)
        {
            yield return new FindRealmStep(adapter);
            yield return new CheatTeleportStep(adapter, PerformanceTeleportSettlementPoints.HangarPoint, PerformanceCameraRotationTeleportPoints.SettlementCameraRotationPoints.HangarCameraRotation);
            yield return new WaitForSecondsRealtime(_delay);
            using (MeasureWrapper.ScopedFrameMetricsMeasurement(true, true, FrameMeasurementHelper.AggregationList, "Settlement", "HangarRunning180D", "FPS"))
            {
                yield return new PressButtonStep(adapter, adapter.InputWrapper.Keyboard.leftShiftKey);
                yield return new MoveToPointStep(adapter, PerformanceTeleportSettlementPoints.HangarCameraDirectionPoint, 5f, 30000);
                yield return new ReleaseButtonStep(adapter, adapter.InputWrapper.Keyboard.leftShiftKey);
            }
            yield return new ReturnToStartPositionStep(adapter);
        }
    }
}