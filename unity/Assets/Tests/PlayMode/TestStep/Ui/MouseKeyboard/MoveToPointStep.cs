using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Framework.Core.Now;
using Game.Battle.EntityModel.BattleCharacter.Movement;
using Tests.PlayMode.Adapter;
using Tests.PlayMode.InputWrapper;
using Tests.PlayMode.Utility;
using Tests.PlayMode.YieldInstruction;
using UnityEngine;
using UnityEngine.InputSystem.Controls;

namespace Tests.PlayMode.TestStep.Ui.MouseKeyboard
{
    public class MoveToPointStep : UiStep<IMouseKeyboardInput>
    {
        private readonly Vector3 _point;
        private readonly float _accuracy;
        private readonly double _timeout;

        private readonly INow _now = new RealNow();
        private readonly HashSet<KeyControl> _pressedKeys = new();
        private readonly HashSet<KeyControl> _releasedKeys = new();
        private readonly WasdHelper _wasdHelper;
        private double _startTime;

        public MoveToPointStep(IUiAdapter<IMouseKeyboardInput> adapter, Vector3 point, float accuracy, double timeout) : base(adapter)
        {
            _point = point;
            _accuracy = accuracy;
            _timeout = timeout;
            _wasdHelper = new WasdHelper(adapter.InputWrapper.Keyboard);
        }

        protected override IEnumerator<IEnumerator> AdapterActions(IUiAdapter<IMouseKeyboardInput> adapter)
        {
            _startTime = _now.Get;
            IMovementModel movement = adapter.GameModel.BattleModel.BattleEntitiesModel?.Player.ClientModel.Movement;
            float angle = GameObject.Find("PlayerCameraRoot").transform.rotation.eulerAngles.y;
            
            bool isDone = false;
            var location = adapter.GameModel.GlobalModel.BattleRoomModel.Room;
            while (!isDone)
            {
                Vector3 currentPosition = new Vector3(movement.X, movement.Y, movement.Z);
                Vector3 direction = _point - currentPosition;

                if (direction.magnitude <= _accuracy || location != adapter.GameModel.GlobalModel.BattleRoomModel.Room )
                {
                    foreach (var pressedKey in _pressedKeys)
                    {
                        yield return new ReleaseButtonStep(adapter, pressedKey);
                    }
                    _pressedKeys.Clear();
                    isDone = true;
                }
                else
                {
                    var keys = _wasdHelper.GetKeys(currentPosition, _point, angle).ToList();

                    foreach (var pressedKey in _pressedKeys)
                    {
                        if (!keys.Contains(pressedKey))
                        {
                            yield return new ReleaseButtonStep(adapter, pressedKey);
                            _releasedKeys.Add(pressedKey);
                        }
                    }

                    foreach (var releasedKey in _releasedKeys)
                    {
                        _pressedKeys.Remove(releasedKey);
                    }
                    _releasedKeys.Clear();
                    
                    foreach (var key in keys)
                    {
                        if (!_pressedKeys.Contains(key))
                        {
                            yield return new PressButtonStep(adapter, key);
                            _pressedKeys.Add(key);
                        }
                    }

                    yield return new WaitForCondition(() => _now.Get - _startTime <= _timeout, "_now.Get - _startTime <= _timeout");
                }
            }
        }
    }
}
