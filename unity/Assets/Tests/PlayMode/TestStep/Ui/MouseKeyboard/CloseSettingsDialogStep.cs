using System.Collections;
using System.Collections.Generic;
using Game.Global.Settings;
using Game.Global.SettingsDialog.Views;
using Game.Shared.Models.Screens;
using Tests.PlayMode.Adapter;
using Tests.PlayMode.InputWrapper;
using Tests.PlayMode.Utility;
using Tests.PlayMode.YieldInstruction;
using UnityEngine;

namespace Tests.PlayMode.TestStep.Ui.MouseKeyboard
{
    public class CloseSettingsDialogStep : TestStep
    {
        private readonly IUiAdapter<IMouseKeyboardInput> _adapter;
        public CloseSettingsDialogStep(IUiAdapter<IMouseKeyboardInput> adapter) 
        {
            _adapter = adapter;
        }
        protected override IEnumerator<IEnumerator> Actions()
        {
            var settingsDialogView = Object.FindObjectOfType<SettingsDialogView>();
            var confirmSettingsButton = settingsDialogView.ConfirmButton;
            if (confirmSettingsButton.IsInteractable)
            {
                var confirmSettingsButtonTransform = settingsDialogView.ConfirmButton.transform;
                var confirmSettingsButtonPosition = CoordinateHelper.GetButtonPosition(confirmSettingsButtonTransform);
                yield return new MovePointerStep(_adapter, _adapter.InputWrapper.Mouse.position, confirmSettingsButtonPosition);
                yield return new PressAndReleaseButtonStep(_adapter, _adapter.InputWrapper.Mouse.leftButton);
            }
            else
            {
                yield return new PressAndReleaseButtonStep(_adapter, _adapter.InputWrapper.Keyboard.escapeKey);
            }
            yield return new WaitForCondition(() => _adapter.GameModel.GlobalModel.SettingsDialogModel.ScreenState == ScreenState.Closed, "SettingsDialogModel.ScreenState == ScreenState.Closed");
        }
    }
}