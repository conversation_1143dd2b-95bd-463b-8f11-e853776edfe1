using Models.Messages;
using Models.References.Realm;
using System.Collections;
using System.Collections.Generic;
using Tests.PlayMode.Adapter;
using Tests.PlayMode.Utility;
using Tests.PlayMode.YieldInstruction;

namespace Tests.PlayMode.TestStep
{
    public class CheatJoinRealmStep : TestStep
    {
        private readonly IGameAdapter _adapter;
        private readonly int _realmId;

        public CheatJoinRealmStep(IGameAdapter adapter, int realmId) 
        {
            _adapter = adapter;
            _realmId = realmId;
        }
        
        public CheatJoinRealmStep(IGameAdapter adapter) : this (adapter, new RandomizeHelper().GetRandomCityId())
        {
        }
        
        protected override IEnumerator<IEnumerator> Actions()
        {
            yield return new SendRegionMessageStep(_adapter, new CheatJoinRealmMessage(_realmId));
            var cityModel = _adapter.GameModel.GlobalModel.CityModel;
            yield return new WaitForCondition(() => !RealmId.IsEmpty(cityModel.RealmId) && cityModel.RealmId == _realmId && !cityModel.IsJoining && !cityModel.IsLeaving,
                "!RealmId.IsEmpty(cityModel.CityId) && cityModel.CityId == _realmId && !cityModel.IsJoining && !cityModel.IsLeaving", timeout: 15000);
        }
    }
}