using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.Models.BattleCharacterModel;
using Models.Models.BattleCharacterStateModel;
using Models.Models.CharacterInterruptModel;
using Models.Models.CharacterValidationModel;
using Models.Models.IntentionModel;
using Models.Models.InterruptArrestingModel;
using Models.Models.RequestModel;
using Models.Models.TransformModel;
using Models.Models.WeaponReloadModel;
using Models.Models.WorldEntitiesCollectionModel;
using Models.Models.WorldInterruptModel;
using Models.References;
using Models.References.Input;
using Models.Utils.InteractRules;

namespace Server.Systems
{
    public class StartInterruptArrestingSystem : System<(int id, IRequestModel<(InterruptArrestingInputDescription, int)> interruptArrestingRequest, ICharacterValidationModel validationModel, IInterruptArrestingModel interruptArrestingModel, ITransformModel transformModel, ICharacterInterruptModel interruptModel, IWeaponReloadModel weaponReloadModel, IIntentionModel reloadIntention)>
    {
        private readonly IWorldEntitiesCollectionModel<IBattleCharacterModel> _battleCharacters;
        private readonly IWorldInterruptModel _worldInterruptModel;

        public StartInterruptArrestingSystem(IWorldEntitiesCollectionModel<IBattleCharacterModel> battleCharacters, IWorldInterruptModel worldInterruptModel)
        {
            _battleCharacters = battleCharacters;
            _worldInterruptModel = worldInterruptModel;
        }
    
        protected override void Tick(ITick tick)
        {
            long now = tick.Ts;
            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var id = component.id;
                var interruptArrestingRequest = component.interruptArrestingRequest;
                var validationModel = component.validationModel;
                var interruptArrestingModel = component.interruptArrestingModel;
                var transformModel = component.transformModel;
                var interruptModel = component.interruptModel;
                var weaponReloadModel = component.weaponReloadModel;
                var reloadIntention = component.reloadIntention;

                if (!interruptArrestingRequest.IsRequested) continue;

                if (validationModel.CanPerformIdleActions())
                {
                    var (interruptArrestingInput, targetEntityId) = interruptArrestingRequest.Arg;

                    if (interruptArrestingInput == InterruptArrestingInputDescription.Start && !interruptArrestingModel.IsActive)
                    {
                        if (_battleCharacters.TryGetModel(targetEntityId, out var targetEntity)
                            && !weaponReloadModel.IsInProcess && !reloadIntention.HasIntention
                            && targetEntity.StateModel.Value == BattleCharacterState.Idle
                            && !targetEntity.KnockdownModel.IsActive
                            && targetEntity.ArrestTargetModel.IsArrestingActive
                            && InteractRules.CheckDistance(transformModel, targetEntity.Transform, BattleDistancesDescription.ArrestSqrDistance))
                        {
                            interruptModel.InterruptWhenInterruptArresting(now, _worldInterruptModel);
                        
                            interruptArrestingModel.Start(now);
                            interruptArrestingModel.TargetEntityId = targetEntityId;
                            targetEntity.ArrestTargetModel.StartInterruptArresting(now, id);
                        }
                    }
                    else if (interruptArrestingInput == InterruptArrestingInputDescription.Stop && interruptArrestingModel.IsActive)
                    {
                        _worldInterruptModel.StopInterruptArresting(id, interruptArrestingModel);
                    }
                }

                interruptArrestingRequest.Clear();
            }
        }
    }
}