using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.Models.PhysicsSceneModel;
using Models.Models.SnapshotsModel;
using Models.Physics.Scene;
using Models.Physics.Snapshots;

namespace Server.Systems
{
    public class PhysicsSnapshotSystem : IUpdateSystem
    {
        private readonly IPhysicsSceneModel _physicsSceneModel;
        private readonly ISnapshotsModel<PhysicsData> _physicsSnapshots;

        public PhysicsSnapshotSystem(IPhysicsSceneModel physicsSceneModel, ISnapshotsModel<PhysicsData> physicsSnapshots)
        {
            _physicsSceneModel = physicsSceneModel;
            _physicsSnapshots = physicsSnapshots;
        }

        public void Update(ITick tick)
        {
            PhysicsData sourceData = _physicsSceneModel.Data;
            PhysicsData destinationData = _physicsSnapshots.Add(tick.TickIndex);
            PhysicsSnapshots.MakeQueriesSnapshotForPhysicsData(sourceData, destinationData);
        }
    }
}