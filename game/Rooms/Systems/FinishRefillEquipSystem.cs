using System.Numerics;
using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.Inventory;
using Models.Models.BreachEntityModel;
using Models.Models.BuildingModel;
using Models.Models.CharacterBuilderSlots;
using Models.Models.EquipAmmoModel;
using Models.Models.EquipSlotsModel;
using Models.Models.HealthModel;
using Models.Models.InventoryModel;
using Models.Models.PocketsModel;
using Models.Models.RefillCopEquipModel;
using Models.Models.RefillEquipModel;
using Models.Models.TransformModel;
using Models.Models.VehicleModel;
using Models.Models.WorldEntitiesCollectionModel;
using Models.References;
using Models.References.Vehicle.Trunk;
using Models.Utils.Extensions;
using Models.Utils.Extensions.Builder;
using Server.Common.Models.AnalyticsModel;
using Server.References;

namespace Server.Systems
{
    public class FinishRefillEquipSystem : System<(int id, IRefillEquipModel refillEquipModel, IHealthModel healthModel, IInventoryModel inventoryModel, IEquipSlotsModel equipSlotsModel, IEquipAmmoModel equipAmmoModel, IPocketsModel pocketsModel, ITransformModel transformModel, ICharacterBuilderSlotsModel characterBuilderSlotsModel)>
    {
        private readonly IRefillCopEquipModel _refillCopEquipModel;
        private readonly IWorldEntitiesCollectionModel<IVehicleModel> _vehicles;
        private readonly IWorldEntitiesCollectionModel<IBuildingModel> _buildings;
        private readonly IWorldEntitiesCollectionModel<IBreachEntityModel> _breachEntities;
        private readonly IBattleAnalyticsModel _analyticsModel;

        public FinishRefillEquipSystem(IRefillCopEquipModel refillCopEquipModel, 
            IWorldEntitiesCollectionModel<IVehicleModel> vehicles, 
            IWorldEntitiesCollectionModel<IBuildingModel> buildings,
            IWorldEntitiesCollectionModel<IBreachEntityModel> breachEntities,
            IBattleAnalyticsModel analyticsModel)
        {
            _refillCopEquipModel = refillCopEquipModel;
            _vehicles = vehicles;
            _buildings = buildings;
            _breachEntities = breachEntities;
            _analyticsModel = analyticsModel;
        }

        protected override void Tick(ITick tick)
        {
            long now = tick.Ts;

            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var id = component.id;
                var refillEquipModel = component.refillEquipModel;
                var healthModel = component.healthModel;
                var inventoryModel = component.inventoryModel;
                var equipSlotsModel = component.equipSlotsModel;
                var equipAmmoModel = component.equipAmmoModel;
                var pocketsModel = component.pocketsModel;
                var transformModel = component.transformModel;
                var characterBuilderSlotsModel = component.characterBuilderSlotsModel;

                if (refillEquipModel.IsInProgress)
                {
                    if (!CanInteract(refillEquipModel.EntityId, transformModel, out RefillCopEquipSourceDescription refillSource))
                    {
                        refillEquipModel.Stop();
                    }
                    else if (now >= refillEquipModel.EndTs)
                    {
                        refillEquipModel.Stop();
                        var equipPreset = BuilderSlotsExtensions.GetCopEquip(characterBuilderSlotsModel).EquipPreset;
                        var weaponPreset = BuilderSlotsExtensions.GetCopWeapon(characterBuilderSlotsModel).WeaponPreset;
                        _refillCopEquipModel.Refill(equipPreset, weaponPreset, healthModel, inventoryModel, equipSlotsModel, equipAmmoModel, pocketsModel);

                        _analyticsModel.RefillCopEquip(id, refillSource, equipPreset, weaponPreset, transformModel.ToVector3());
                    }
                }
            }
        }

        private bool CanInteract(int entityId, ITransformModel transformModel, out RefillCopEquipSourceDescription source)
        {
            var vehicleId = VehicleTrunkSettingsDescription.GetVehicleEntityId(entityId);
            if (_vehicles.TryGetModel(vehicleId, out var vehicleModel))
            {
                var trunkEntityId = VehicleTrunkSettingsDescription.GetVehicleTrunkEntityId(vehicleId);
                var hasBreached = _breachEntities.TryGetModel(trunkEntityId, out var breachEntityModel) && breachEntityModel.BreachStateEntityModel.IsActive;
                if (vehicleModel.CanRefillCopAmmo && !hasBreached)
                {
                    var trunkPosition = vehicleModel.TrunkPosition;
                    var position = new Vector3(trunkPosition.X, trunkPosition.Y, trunkPosition.Z);
                    source = RefillCopEquipSourceDescription.VehicleTrunk;
                    return InventoryRules.CheckInteractDistance(transformModel, position, BattleDistancesDescription.TrunkInteractSqrDistance);
                }
            }
            else if (_buildings.TryGetModel(entityId, out var buildingModel) && buildingModel.Description.CanCopRefillAmmo)
            {
                var position = buildingModel.Description.CopRefillAmmoPoint.Position;
                source = RefillCopEquipSourceDescription.PoliceStation;
                return InventoryRules.CheckInteractDistance(transformModel, position);
            }

            source = null;
            return false;
        }
    }
}