using System;
using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.ClientNotification;
using Models.ClientNotification.ClientNotificationsCollection;
using Models.Inventory;
using Models.Models.CharacterValidationModel;
using Models.Models.HangarOwnerModel;
using Models.Models.InventoryModel;
using Models.Models.IPartyMemberAccessModel;
using Models.Models.PlotBuildingDefenseBlockPrivateModel;
using Models.Models.PlotBuildingDefenseBlockPublicModel;
using Models.Models.ProlongedInteractionModel;
using Models.Models.RequestModel;
using Models.Models.TransformModel;
using Models.Models.WorldEntitiesCollectionModel;
using Models.Models.WorldEntitiesModel;
using Models.References;
using Models.References.Input;
using Models.References.Inventory;
using Models.References.PlotBuildingDefenseBlock;
using Models.Replication;
using Server.Battle.Analytics.Extensions;
using Server.Battle.Models.SettlementPlotsServerModel;
using Server.Common.Models.AnalyticsModel;

namespace Server.Systems
{
    public class FinishRaidDefenseBlockSystem : System<(
        int id, 
        IRequestModel<(RaidDefenseBlockInputDescription, int)> raidDefenseBlockRequest, 
        IProlongedInteractionModel raidDefenseBlockModel,
        IInventoryModel inventoryModel,
        ICharacterValidationModel validationModel,
        ITransformModel transformModel,
        IClientNotificationsCollection clientNotifications )>
    {
        
        private readonly IWorldEntitiesCollectionModel<IPlotBuildingDefenseBlockPublicModel> _plotBuildingDefenseBlockPublicModels;
        private readonly IWorldEntitiesCollectionModel<IPlotBuildingDefenseBlockPrivateModel> _plotBuildingDefenseBlockPrivateModels;

        private readonly IWorldEntitiesModel _worldEntitiesModel;
        private readonly IHangarOwnerModel _hangarOwnerModel;
        private readonly IPartyMemberAccessModel _partyAccess;
        private readonly IBattleAnalyticsModel _analyticsModel;
        private readonly ISettlementPlotsServerModel _plotsModel;

        public FinishRaidDefenseBlockSystem(IWorldEntitiesModel worldEntitiesModel, IHangarOwnerModel hangarOwnerModel, IPartyMemberAccessModel partyAccess, IBattleAnalyticsModel analyticsModel, ISettlementPlotsServerModel plotsModel)
        {
            _worldEntitiesModel = worldEntitiesModel;
            _plotBuildingDefenseBlockPublicModels = worldEntitiesModel.PlotBuildingDefenseBlockPublicModels;
            _plotBuildingDefenseBlockPrivateModels = worldEntitiesModel.PlotBuildingDefenseBlockPrivateModels;
            _hangarOwnerModel = hangarOwnerModel;
            _partyAccess = partyAccess;
            _analyticsModel = analyticsModel;
            _plotsModel = plotsModel;
        }

        protected override void Tick(ITick tick)
        {
            long now = tick.Ts;
            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var characterId = component.id;
                var raidRequest = component.raidDefenseBlockRequest;
                var raidModel = component.raidDefenseBlockModel;
                var inventoryModel = component.inventoryModel;
                var validationModel = component.validationModel;
                var transformModel = component.transformModel;
                var clientNotifications = component.clientNotifications;

                if (!raidModel.IsInProcess) continue;
                
                if (!validationModel.CanPerformIdleActions() ||
                    !_plotBuildingDefenseBlockPublicModels.TryGetModel(raidModel.EntityId, out var defenseBlockPublicModel) || 
                    !InventoryRules.CheckInteractDistance(transformModel, defenseBlockPublicModel.DescriptionModel.Value.Position))
                {
                    raidModel.Stop();
                    continue;
                }
                
                if (now < raidModel.EndTs)
                    continue;

                var privateModelEntityId = defenseBlockPublicModel.DescriptionModel.Value.EntityIds.PrivateModelEntityId;
                if (!_plotBuildingDefenseBlockPrivateModels.TryGetModel(privateModelEntityId, out var defenseBlockPrivateModel) ||
                    !PlotBuildingDefenseRules.CanRaidDefenseBlock(characterId, defenseBlockPublicModel, _worldEntitiesModel, _hangarOwnerModel, _partyAccess) ||
                    !PlotBuildingDefenseRules.TryGetDecoderItemSlot(inventoryModel, out var decoderSlot))
                {
                    raidModel.Stop();
                    continue;
                }
                
                var currentDecoder = decoderSlot.ItemModel.Value;

                var decodingAnalyticsState = defenseBlockPublicModel.RaidLockTs.Value == ReplicationUtil.MinTs
                    ? PlotBuildingRaidStateDescription.Started
                    : PlotBuildingRaidStateDescription.InProgress;

                var attackPoints = RaidDefenseBlockSettingsDescription.CalculateAttackPoints(defenseBlockPrivateModel.DecoderAttackPoints.Value, currentDecoder.PlotBuildingDecoderDescription.DefensePoints);
                defenseBlockPrivateModel.DecoderAttackPoints.Value = attackPoints;

                var raidLockTime = RaidDefenseBlockSettingsDescription.CalculateRaidLockTime(now, defenseBlockPublicModel.RaidLockTs.Value);
                defenseBlockPublicModel.RaidLockTs.Value = now + raidLockTime;
                
                if (defenseBlockPrivateModel.CalculateDefensePoints() <= attackPoints)
                {
                    defenseBlockPublicModel.IsRaided.Value = true;
                    decodingAnalyticsState = PlotBuildingRaidStateDescription.Finished;
                }
                
                if (decoderSlot.StackModel.Value > 1)
                {
                    decoderSlot.StackModel.Value -= 1;
                }
                else
                {
                    decoderSlot.Unset();
                }
                clientNotifications.Add(new ClientNotification.InventoryItemUsed(currentDecoder));

                HandleAnalytics(characterId, raidModel.EntityId, defenseBlockPrivateModel, currentDecoder, raidLockTime, decodingAnalyticsState);
                    
                if (!defenseBlockPublicModel.IsRaided.Value && PlotBuildingDefenseRules.TryGetDecoderItemSlot(inventoryModel, out _))
                    raidRequest.SetRequest((RaidDefenseBlockInputDescription.Start, raidModel.EntityId));

                raidModel.Stop();
            }
        }

        private void HandleAnalytics(int characterId, int entityId, IPlotBuildingDefenseBlockPrivateModel defenseBlockPrivateModel, InventoryItemDescription decoder, long raidLockTime, PlotBuildingRaidStateDescription decodingState)
        {
            var plotSlotDescription = _worldEntitiesModel.PlotSlotByEntity[entityId];
            var settlementPlotServerModel = _plotsModel.Slots[plotSlotDescription];
            var ownerPrivateId = settlementPlotServerModel.OwnerPrivateId;
            var plotModel = _worldEntitiesModel.SlotPlotModels[plotSlotDescription];

            BreachAnalyticsExtension.SendUseDecoderAnalytics(_analyticsModel, plotModel, defenseBlockPrivateModel, ownerPrivateId, characterId, decoder.PlotBuildingDecoderDescription, raidLockTime, decodingState);
        }
    }
}