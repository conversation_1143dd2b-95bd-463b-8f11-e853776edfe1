using Framework.Replication.Data;
using Framework.Replication.Data.Primitive;
using Models.References;
using Models.References.Location;
using Models.References.Realm;

namespace Server.Messages.Party
{
    public class PartyMemberUpdateGlobalMapStateAndPlotAddressMessage : CompositeData
    {
        private readonly IPrimitiveData<BattleModeDescription> _battleMode = new ReferenceData<BattleModeDescription>(BattleModeDescription.Enum);
        private readonly IPrimitiveData<LocationDescription> _location = new ReferenceData<LocationDescription>(LocationDescription.Enum);
        private readonly IPrimitiveData<SectorDescription> _sector = new ReferenceData<SectorDescription>(SectorDescription.Enum);
        private readonly IPrimitiveData<SettlementDescription> _settlement = new ReferenceData<SettlementDescription>(SettlementDescription.Enum);
        private readonly IPrimitiveData<SectorDescription> _plotSector = new ReferenceData<SectorDescription>(SectorDescription.Enum);
        private readonly IPrimitiveData<SettlementDescription> _plotSettlement = new ReferenceData<SettlementDescription>(SettlementDescription.Enum);

        public PartyMemberUpdateGlobalMapStateAndPlotAddressMessage(BattleModeDescription battleMode, LocationDescription location, SectorDescription sector, SettlementDescription settlement, SectorDescription plotSector, SettlementDescription plotSettlement) : this()
        {
            _battleMode.Value = battleMode;
            _location.Value = location;
            _sector.Value = sector;
            _settlement.Value = settlement;
            _plotSector.Value = plotSector;
            _plotSettlement.Value = plotSettlement;
        }

        public PartyMemberUpdateGlobalMapStateAndPlotAddressMessage()
        {
            Add("battle_mode", _battleMode);
            Add("location", _location);
            Add("sector", _sector);
            Add("settlement", _settlement);
            Add("plot_sector", _plotSector);
            Add("plot_settlement", _plotSettlement);
        }

        public BattleModeDescription BattleMode => _battleMode.Value;
        public LocationDescription Location => _location.Value;
        public SectorDescription Sector => _sector.Value;
        public SettlementDescription Settlement => _settlement.Value;
        public SectorDescription PlotSector => _plotSector.Value;
        public SettlementDescription PlotSettlement => _plotSettlement.Value;
    }
}