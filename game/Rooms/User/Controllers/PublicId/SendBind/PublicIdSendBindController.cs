using Framework.Cluster.Dispatcher;
using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Value.Values;
using Server.Common.Utils;
using Server.Messages;
using Server.PublicId.Requests;
using Server.References;

namespace Server.User.Controllers.PublicId.SendBind
{
    public class PublicIdSendBindController : IPublicIdSendBindController
    {
        private readonly IDispatcher _dispatcher;
        
        public PublicIdSendBindController(IDispatcher dispatcher)
        {
            _dispatcher = dispatcher;
        }

        public async IAwaiter<IEither<IValue, IValue>> Send(string uid, long publicId, string name)
        {
            var room = PublicIdDescription.GetBindRoom();
            var message = new BindPublicIdMessage(uid, name, publicId);

            var dispatcherResult = await _dispatcher.Send(PublicIdSearchRequests.BindPublicId, Roles.PublicIdSearch, room, message);

            if (dispatcherResult.IsLeft)
            {
                return new Left<IValue, IValue>(dispatcherResult.AsLeft);
            }

            return dispatcherResult.AsRight;
        }
    }
}