using System;
using System.Collections.Generic;
using Framework.Async.Awaiter;
using Server.Common.Controllers.DatabaseController;
using Server.References;
using Server.RegionAdmin.Requests.AdminDatabaseLoad;

namespace Server.RegionAdmin.Controllers.RegionAdminDatabaseController
{
    public class RegionAdminDatabaseController : IRegionAdminDatabaseController
    {
        private readonly IDatabaseController _databaseController;
        private readonly HashSet<string> _datasets;

        public RegionAdminDatabaseController(IDatabaseController databaseController, HashSet<string> datasets)
        {
            _databaseController = databaseController;
            _datasets = datasets;
        }

        public async IAwaiter<AdminDatabaseLoadResult> Load(DatasetDescription datasetDescription, string key)
        {
            if (!_datasets.Contains(datasetDescription.Id))
            {
                return new AdminDatabaseLoadResult.DatasetNotFound();
            }
            
            var result = await _databaseController.Load(datasetDescription, key);
            switch (result)
            {
                case DatabaseLoadResult.LoadError { Value: var value }:
                    return new AdminDatabaseLoadResult.LoadFailed(value);
                case DatabaseLoadResult.NotFoundError:
                    return new AdminDatabaseLoadResult.KeyNotFound();
                case DatabaseLoadResult.Ok { Value: var value }:
                    return new AdminDatabaseLoadResult.Ok(value);
                default:
                    throw new ArgumentOutOfRangeException(nameof(result));
            }
        }
    }
}