using Framework.Replication.Data;
using Framework.Replication.Data.Primitive;
using Framework.Replication.Enum;
using Models.Data.Clan;

namespace Server.Clan.Requests.ClanAppointmentFinished
{
    public class ClanAppointmentFinishedMessage<T> : CompositeData where T : class
    {
        private readonly IPrimitiveData<T> _item;
        private readonly IPrimitiveData<int> _periodIndex = new ClanBattlePeriodIndexData();
        private readonly IPrimitiveData<bool> _isWin = new BoolData();

        public ClanAppointmentFinishedMessage(IEnum<T> @enum, T item, int periodIndex, bool isWin) : this(@enum)
        {
            _item.Value = item;
            _periodIndex.Value = periodIndex;
            _isWin.Value = isWin;
        }

        public ClanAppointmentFinishedMessage(IEnum<T> @enum)
        {
            _item = Add("item", new ReferenceData<T>(@enum));
            Add("period_index", _periodIndex);
            Add("is_win", _isWin);
        }

        public T Item => _item.Value;
        public int PeriodIndex => _periodIndex.Value;
        public bool IsWin => _isWin.Value;
    }
}