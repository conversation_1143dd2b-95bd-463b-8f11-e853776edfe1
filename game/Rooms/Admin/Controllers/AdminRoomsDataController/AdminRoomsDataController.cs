using Framework.Cluster.Dispatcher;
using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Core.Unit;
using Framework.Value.Values;
using Framework.Utility.Logger;
using Framework.Utility.Loggers;
using Models.References;
using Server.Admin.Models;
using Server.Battle.Requests;
using Server.Common.Utils;
using Server.RegionUser.Requests;
using Server.User.Requests;

namespace Server.Admin.Controllers.AdminRoomsDataController
{
    public class AdminRoomsDataController : IAdminRoomsDataController
    {
        private readonly IDispatcher _dispatcher;
        private readonly ILogger _logger;

        public AdminRoomsDataController(IDispatcher dispatcher, ILoggers loggers)
        {
            _dispatcher = dispatcher;
            _logger = loggers.Build(nameof(AdminRoomsDataController));
        }

        public async IAwaiter<IEither<IValue, IValue>> GetUserRoomData(string privateId)
        {
            if (string.IsNullOrEmpty(privateId))
            {
                return AdminAsyncResponses.InvalidArgumentEither;
            }

            var result = await _dispatcher.UnsafeSend(UserRequests.AdminGetRoomData, Roles.User, privateId, Unit.unit);

            if (result.IsLeft)
            {
                _logger.Info("Get user {0} failed, {1}", privateId, result.AsLeft);
            }

            return result;
        }

        public async IAwaiter<IEither<IValue, IValue>> GetRegionUserRoomData(string regionId, string privateId)
        {
            if (string.IsNullOrEmpty(privateId) || string.IsNullOrEmpty(regionId) || !ClusterDescription.Enum.TryFromId(regionId, out _))
            {
                return AdminAsyncResponses.InvalidArgumentEither;
            }

            var result = await _dispatcher.UnsafeRemoteSend(RegionUserRequests.AdminGetRoomData, regionId, Roles.RegionUser, privateId, Unit.unit);

            if (result.IsLeft)
            {
                _logger.Info("Get region ({0}) user ({1}) failed, {2}", regionId, privateId, result.AsLeft);
            }

            return result;
        }

        public async IAwaiter<IEither<IValue, IValue>> GetBattleRoomData(string regionId, bool isShooterRole, string roomId)
        {
            if (string.IsNullOrEmpty(roomId) || string.IsNullOrEmpty(regionId) || !ClusterDescription.Enum.TryFromId(regionId, out _))
            {
                return AdminAsyncResponses.InvalidArgumentEither;
            }

            var destination = isShooterRole ? Roles.Shooter : Roles.Settlement;
            var result = await _dispatcher.UnsafeRemoteSend(BattleRequests.AdminGetRoomData, regionId, destination, roomId, Unit.unit);

            if (result.IsLeft)
            {
                _logger.Info("Get region ({0}) isShooter ({1}) battle ({2}) failed, {3}", regionId, isShooterRole, roomId, result.AsLeft);
            }

            return result;
        }
    }
}