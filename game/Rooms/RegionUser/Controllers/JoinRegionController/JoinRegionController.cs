using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Value.Values;
using Framework.Utility.Logger;
using Framework.Utility.Loggers;
using Models.References;
using Models.References.Inventory;
using Server.Common.Controllers.SaveController;
using Server.Common.Utils;
using Server.Messages;
using Server.RegionUser.Models.RegionUserModel;

namespace Server.RegionUser.Controllers.JoinRegionController
{
    public class JoinRegionController : IJoinRegionController
    {
        private readonly IEither<IValue, IValue> _waitingSaveError = "waiting_save_error".ToEitherError();
        private readonly IEither<IValue, IValue> _serviceError = "service_error".ToEitherError();

        private readonly IRegionUserModel _regionUserModel;
        private readonly ISaveController _saveController;
        private readonly ILogger _logger;
    
        public JoinRegionController(IRegionUserModel regionUserModel, ISaveController saveController, ILoggers loggers)
        {
            _regionUserModel = regionUserModel;
            _saveController = saveController;
            _logger = loggers.Build(nameof(JoinRegionController));
        }

        public async IAwaiter<IEither<IValue, IValue>> JoinRegion(JoinRegionMessage message)
        {
            var publicId = message.PublicId;
            var nickname = message.Nickname;
            var globalProgressBytes = message.GlobalProgressBytes;

            if (_regionUserModel.IsWaitingSaveWhileJoining)
            {
                return _waitingSaveError;
            }
        
            if (_regionUserModel.IsInRegion)
            {
                return AsyncResponses.OkEither;
            }
        
            _regionUserModel.JoinRegion(publicId, nickname);

            if (!_regionUserModel.GlobalProgressReplicateModel.Deserialize(globalProgressBytes))
            {
                _logger.Info("Read global progress fail");
                return _serviceError;
            }

            _regionUserModel.IsWaitingSaveWhileJoining = true;
        
            await _saveController.InstantSave();
        
            _regionUserModel.IsWaitingSaveWhileJoining = false;

            return AsyncResponses.OkEither;
        }
    }
}