using System;
using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Core.Timers;
using Framework.Value.Values;
using Framework.Utility.Logger;
using Framework.Utility.Loggers;
using Models.Models.ClanBattleAuctionTime;
using Models.Models.RegionUserClanModel;
using Models.References.Realm;
using Models.Utils;
using Server.Clan.Requests;
using Server.Clan.Requests.ClanAddBetClanBattle;
using Server.Common.Utils;
using Server.RegionUser.Models.RegionUserModel;
using Server.RegionUser.Models.States;

namespace Server.RegionUser.Controllers.Clan.RegionUserPlaceSectorBetController
{
    public class RegionUserPlaceSectorBetController : IRegionUserPlaceSectorBetController
    {
        private readonly IEither<IValue, IValue> _validationError = ServerResponses.Validation.ToEitherError();
        private readonly IEither<IValue, IValue> _serviceError = ServerResponses.ServiceError.ToEitherError();

        private readonly string _id;
        private readonly RegionUserRoomState _state;
        private readonly ITimers _timers;
        private readonly IRegionUserClanModel _clanModel;
        private readonly ILogger _logger;

        public RegionUserPlaceSectorBetController(string id, IRegionUserModel regionUserModel, RegionUserRoomState state, ILoggers loggers, ITimers timers)
        {
            _id = id;
            _state = state;
            _timers = timers;
            _clanModel = regionUserModel.ClanModel;
            _logger = loggers.Build(nameof(RegionUserPlaceSectorBetController));
        }

        public async IAwaiter<IEither<IValue, IValue>> PlaceBet(SectorDescription sector, int amount)
        {
            if (sector == null)
            {
                return _validationError;
            }

            var minAmount = sector.Tier.ClanBattleBetMinAmount;
            if (amount < minAmount)
            {
                return _validationError;
            }

            if (!_clanModel.CanPerformInClanActions ||
                _clanModel.IsRequesting ||
                !_state.IsClanLinked)
            {
                return _validationError;
            }

            if (!_clanModel.Rank.CanPlaceClanBattleBet)
            {
                return _validationError;
            }

            if (!ClanBattleAuctionTimeRules.IsBetsPhaseActive(_timers.Now))
            {
                return _validationError;
            }

            _clanModel.IsRequesting = true;

            var result = await _state.ClanLink.Send(ClanRequests.AddSectorBetClanBattle, new ClanAddBetClanBattleMessage<SectorDescription>(SectorDescription.Enum, _id, sector, amount));

            _clanModel.IsRequesting = false;

            if (result.IsLeft)
            {
                _logger.Info("Failed {0}", result.AsLeft);
                return _serviceError;
            }

            switch (result.AsRight)
            {
                case ClanAddBetClanBattleResult.Error:
                    return _serviceError;
                case ClanAddBetClanBattleResult.Ok:
                    return AsyncResponses.OkEither;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }
}