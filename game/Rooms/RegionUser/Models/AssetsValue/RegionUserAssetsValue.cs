using Models.Models.EquipAmmoModel;
using Models.Models.EquipSlotsModel;
using Models.Models.PocketsModel;
using Models.Utils.Extensions;
using Server.RegionUser.Models.RegionUserGlobalMapVehicleModel;
using Server.RegionUser.Models.RegionUserModel;
using Server.RegionUser.Models.RegionUserOwnedSettlementsModel;

namespace Server.RegionUser.Models.AssetsValue
{
    public static class RegionUserAssetsValue
    {
        public static int GetAssetsValue(IRegionUserModel regionUserModel, int clearCashRate, float taxCashModifier)
        {
            int value = 0;

            var battleModel = regionUserModel.BattleModel;

            value += battleModel.CoinsModel.Value;

            value += GetOwnedSettlementsAssetsValue(regionUserModel.OwnedSettlementsModel);

            value += GetGlobalMapVehicleAssetsValue(battleModel.GlobalMapVehicleModel, clearCashRate, taxCashModifier);

            value += RealmAssetsValueExtensions.GetInventoryAssetsValue(battleModel.Inventory, clearCashRate, taxCashModifier);
            value += GetEquipAssetsValue(battleModel.EquipSlots, battleModel.EquipAmmo);
            value += GetPocketsAssetsValue(battleModel.Pockets);

            return value;
        }

        private static int GetOwnedSettlementsAssetsValue(IRegionUserOwnedSettlementsModel ownedSettlementsModel)
        {
            int value = 0;

            foreach (var ownedSettlementModel in ownedSettlementsModel)
            {
                value += ownedSettlementModel.Sector.Tier.SettlementCoinsPrice;
            }

            return value;
        }

        private static int GetGlobalMapVehicleAssetsValue(IRegionUserGlobalMapVehicleModel globalMapVehicleModel, int clearCashRate, float taxCashModifier)
        {
            int value = 0;

            var hasVehicle = globalMapVehicleModel.TryGetVehicle(out var car, out var helicopter);
            if (hasVehicle)
            {
                var isCar = car != null;
                var hasTrunk = isCar ? car.HasTrunk : helicopter.HasTrunk;

                if (hasTrunk)
                {
                    value += RealmAssetsValueExtensions.GetInventoryAssetsValue(globalMapVehicleModel.VehicleTrunkInventory, clearCashRate, taxCashModifier);
                }
            }

            return value;
        }

        private static int GetEquipAssetsValue(IEquipSlotsModel equipSlots, IEquipAmmoModel equipAmmo)
        {
            int value = 0;

            foreach (var (equipSlotDescription, equipSlotModel) in equipSlots)
            {
                if (!equipSlotModel.ItemModel.IsEmpty)
                {
                    var item = equipSlotModel.ItemModel.Value;
                    var count = 1;
                    var ammo = equipSlotDescription.HasAmmo ? equipAmmo[equipSlotDescription.EquipAmmoOrArmorSlot].Value : 0;
                    var durability = equipSlotModel.DurabilityModel.Value;

                    value += RealmAssetsValueExtensions.GetItemAssetsValue(item, count, ammo, durability);
                }
            }

            return value;
        }

        private static int GetPocketsAssetsValue(IPocketsModel pockets)
        {
            int value = 0;

            foreach (var pocketSlotModel in pockets.Slots.Values)
            {
                if (!pocketSlotModel.ItemModel.IsEmpty)
                {
                    var item = pocketSlotModel.ItemModel.Value;
                    var count = pocketSlotModel.StackModel.Value;
                    var ammo = 0;
                    var durability = 0;

                    value += RealmAssetsValueExtensions.GetItemAssetsValue(item, count, ammo, durability);
                }
            }

            return value;
        }
    }
}