namespace Server.RegionUser.Models.RegionUserConquestSectorsModel
{
    public class RegionUserConquestSectorsModel : IRegionUserConquestSectorsModel
    {
        public bool HasData { get; set; }
        public byte[] Bytes { get; set; }
        public long Version { get; set; }
        public long RequestConquestSectorsCooldownEndTs { get; set; }

        public void Clear()
        {
            HasData = false;
            Bytes = null;
            Version = 0;
            RequestConquestSectorsCooldownEndTs = 0;
        }
    }
}