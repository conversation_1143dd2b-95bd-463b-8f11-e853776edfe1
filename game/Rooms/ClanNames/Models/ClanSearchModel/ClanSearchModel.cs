using System.Collections.Generic;
using Models.References.Clan;
using Server.ClanNames.Data;

namespace Server.ClanNames.Models.ClanSearchModel
{
    public class ClanSearchModel : IClanSearchModel
    {
        private readonly Dictionary<string, HashSet<ClanNameTagData>> _dict = new();
        private readonly List<string> _resultIds = new();

        public void Add(string name, ClanNameTagData clanData)
        {
            for (var len = ClanNameDescription.MinLength; len <= ClanNameDescription.MaxLength; len++)
            {
                for (var i = 0; i <= name.Length - len; i++)
                {
                    var key = name.Substring(i, len);
                    if (_dict.TryGetValue(key, out HashSet<ClanNameTagData> lst))
                    {
                        lst.Add(clanData);
                    }
                    else
                    {
                        _dict.Add(key, new HashSet<ClanNameTagData> {clanData});
                    }
                }
            }
        }

        public void Remove(string name, ClanNameTagData clanData)
        {
            for (var len = ClanNameDescription.MinLength; len <= ClanNameDescription.MaxLength; len++)
            {
                for (var i = 0; i <= name.Length - len; i++)
                {
                    var key = name.Substring(i, len);
                    if (_dict.TryGetValue(key, out var lst))
                    {
                        lst.Remove(clanData);
                        if (lst.Count == 0)
                        {
                            _dict.Remove(key);
                        }
                    }
                }
            }
        }

        public bool TryGet(string name, out List<string> result)
        {
            _resultIds.Clear();
            if (_dict.TryGetValue(name, out HashSet<ClanNameTagData> resultClans))
            {
                foreach (var item in resultClans)
                {
                    _resultIds.Add(item.Id.Value);
                }

                result = _resultIds;
                return true;
            }

            result = default;
            return false;
        }
    }
}