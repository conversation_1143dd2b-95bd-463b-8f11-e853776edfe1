using System;
using Framework.Cluster.RestartTime;
using Framework.Core.Timer;
using Framework.Core.Timers;
using Server.Common.Controllers.SaveController;
using Server.Common.Extensions;

namespace Server.Common.Controllers.RoomStopController
{
    public abstract class RoomStopController : IRoomStopController
    {
        private readonly ISaveController _saveController;
        private readonly long _timeoutTime;
        private readonly long _restartTimeoutTime;
        private readonly ITimers _timers;
        private readonly IRestartTime _restartTime;

        private readonly Random _random = new();

        private ITimer _timer;
        private bool _isReadyToStop;

        protected RoomStopController(ISaveController saveController, long timeoutTime, long restartTimeoutTime, ITimers timers, IRestartTime restartTime)
        {
            _saveController = saveController;
            _timeoutTime = timeoutTime;
            _restartTimeoutTime = restartTimeoutTime;
            _timers = timers;
            _restartTime = restartTime;
        }

        protected abstract bool CheckStop();
        protected abstract void SetStopped();

        public void Update()
        {
            bool isStopCondition = CheckStop() && _saveController is not {HasUnsavedChanges: true};
            bool isTimerStarted = _timer != null;

            if (isStopCondition && !isTimerStarted)
            {
                StartTimer();
            }
            else if (!isStopCondition && isTimerStarted)
            {
                StopTimer();
            }
        }

        protected async void StartTimer()
        {
            _timer = _timers.StartTimer(_timers.Now + GetTimeoutTime());
            await _timer;
            _isReadyToStop = true;

            if (_saveController is {HasUnsavedLowPriorityChanges: true})
            {
                await _saveController.InstantSave();
            }

            if (_isReadyToStop)
            {
                SetStopped();
            }
        }

        private void StopTimer()
        {
            _isReadyToStop = false;
            _timer.Stop();
            _timer = null;
        }

        protected virtual long GetTimeoutTime()
        {
            bool isRestartTime = _timers.Now >= _restartTime.Ts;
            return isRestartTime ? _timeoutTime + _random.NextInt64(_restartTimeoutTime) : _timeoutTime;
        }
    }
}