using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Value.Values;
using Models.References;
using Models.References.Location;
using Server.BattleMatchmaking.Models;
using Server.Messages;

namespace Server.Common.Controllers.RequestBattleController
{
    public abstract record FindBattleResult
    {
        public record ResponseSuccess(string RoomId, BattleSlotsResult BattleSlotsResult) : FindBattleResult;
        public record ResponseFail(string Reason) : FindBattleResult;
        public record LinkDown() : FindBattleResult;
    }

    public interface IRequestBattleController
    {
        IAwaiter<FindBattleResult> FindShooter(BattleModeDescription battleMode, LocationDescription location, PlatformDescription platform, int partySize, long realmLifetime);
        IAwaiter<FindBattleResult> FindPrison(BattleModeDescription battleMode, LocationDescription location, int slotsCount);
        void LinkDowned(string room);
        IAwaiter<IEither<IValue, IValue>> GetBattleRoomSuccess(GetBattleRoomSuccessMessage message);
        IAwaiter<IEither<IValue, IValue>> GetBattleRoomFail(GetBattleRoomFailMessage message);
    }
}