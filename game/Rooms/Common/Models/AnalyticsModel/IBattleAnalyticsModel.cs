using System.Numerics;
using Framework.Core.Identified;
using Models.Models.CustomizationModel;
using Models.References;
using Models.References.Bollard;
using Models.References.Builder.Breach;
using Models.References.Builder;
using Models.References.Building;
using Models.References.Camera;
using Models.References.Car;
using Models.References.Cargo;
using Models.References.CargoSeller;
using Models.References.Character;
using Models.References.Collection;
using Models.References.Container;
using Models.References.CraftWorkbench;
using Models.References.Door;
using Models.References.Grenade;
using Models.References.HangarStorage;
using Models.References.Helicopter;
using Models.References.HideObject;
using Models.References.Inventory;
using Models.References.KinematicObject;
using Models.References.Location.LocationTraders;
using Models.References.LootNode;
using Models.References.LootObject;
using Models.References.Plot;
using Models.References.PlotBuildingDefenseBlock;
using Models.References.PlotContainers;
using Models.References.Realm;
using Models.References.RobberQuest;
using Models.References.SecurityPanel;
using Models.References.Tattoo;
using Models.References.VigilantNpc;
using Models.References.Weapon;
using Server.References;

namespace Server.Common.Models.AnalyticsModel
{
    public interface IBattleAnalyticsModel
    {
        void UnlockContainer(int entityId, LocationContainerDescription container, bool isLockpickUsed);
        void UnlockDoor(int entityId, LocationDoorDescription door, bool isLockpickUsed);
        void UnlockLootNode(int entityId, LocationLootNodeDescription lootNode, bool isLockpickUsed);
        void HackPanel(int entityId, LocationPanelDescription panel);
        void KnockoutCharacter(int entityId, KnockoutReasonDescription reason, int killerEntityId, PlayerRoleDescription role, PlayerRoleDescription killerRole, WeaponDescription weapon, GrenadeDescription grenade, CharacterKnockoutOrDeathRobberStateDescription robberState, Vector3 position, Vector3 killerPosition);
        void ReviveCharacter(int entityId, int saverEntityId, PlayerRoleDescription role, PlayerRoleDescription saverRole, Vector3 position, bool isSameParty);
        void Death(int entityId, DeathReasonAnalyticsDescription reason, int killerEntityId, PlayerRoleDescription role, PlayerRoleDescription killerRole, WeaponDescription weapon, GrenadeDescription grenade, CharacterKnockoutOrDeathRobberStateDescription robberState, Vector3 position, int policeAttentionPoints);
        void RoleTime(int entityId, PlayerRoleDescription role, long time, int partySize);
        void PutMoneyWorkbench(int entityId, int amount, string ownerUid);
        void ExchangeMoneyWorkbench(int entityId, int amountDirty, int amountClean, string ownerUid);
        void SetCraftWorkbench(int entityId, LocationCraftWorkbenchDescription description);
        void UseBollard(int entityId, LocationBollardDescription bollard);
        void TakeLootNode(int entityId, LocationLootNodeDescription lootNode);
        void TakeStaticLootObject(int entityId, StaticLootObjectDescription lootObject);
        void TakeRandomLootObject(int entityId, RandomLootObjectDescription lootObject);
        void TakeLoot(int entityId, PlayerRoleDescription ownerRole);
        void TakeVehicleLoot(int entityId);
        void ArrestLoot(int entityId, PlayerRoleDescription lootOwnerRole, bool isTouched);
        void ArrestVehicleLoot(int entityId, bool isTouched);
        void LootRemoved(PlayerRoleDescription ownerRole, bool isTouched, LootRemoveReasonDescription reason, Vector3 position);
        void VehicleLootRemoved(bool isTouched, VehicleLootRemoveReasonDescription reason, Vector3 position);
        void FurnitureLootRemoved(FurnitureTypeDescription furnitureType, IIdentified furnitureId, FurnitureLootRemoveReasonDescription reason, bool isTouched);
        void BuildingLootRemoved(BuilderBuildingTypeDescription buildingType, BuildingLootRemoveReasonDescription reason, bool isTouched);
        void EnterHideObject(int entityId, LocationHideObjectDescription hideObject, PlayerRoleDescription role, bool result);
        void CheckHideObject(int entityId, LocationHideObjectDescription hideObject, PlayerRoleDescription role, bool checkResult);
        void KinematicObjectChanged(int kinematicObjectsControllerEntityId, KinematicObjectsControllerDescription kinematicObjectsController, int entityId, PlayerRoleDescription role);
        void TakeBuildingCargo(int entityId, int cargoEntityId, CargoDescription cargo, Vector3 cargoPosition);
        void CargoRemoved(CargoDescription cargo, Vector3 cargoPosition, CargoRemoveReasonDescription reason);
        void SetCargoWorkbench(int entityId, CargoDescription cargo);
        void ExchangeCargo(int entityId, CargoDescription cargo);
        void BuyCargo(int entityId, LocationCargoSellerDescription cargoSeller);
        void BreakCollider(int entityId, PlayerRoleDescription role,  int breakableColliderEntityId, Vector3 position);
        void SetDrill(int entityId, SafeDoorDescription description);
        void ArrestDrill(int entityId, SafeDoorDescription description);
        void CameraTriggered(int entityId, LocationCameraDescription camera);
        void BreakCamera(int entityId, LocationCameraDescription camera);
        void BuildingReset(LocationBuildingDescription building);
        void VigilantNpcStartCall(int entityId, LocationVigilantNpcDescription vigilantNpc);
        void VigilantNpcFinishCall(int entityId, LocationVigilantNpcDescription vigilantNpc);
        void KnockoutVigilantNpc(int entityId, LocationVigilantNpcDescription vigilantNpc);
        void ReviveVigilantNpc(int entityId, LocationVigilantNpcDescription vigilantNpc, PlayerRoleDescription role);
        void ThreatenVigilantNpc(int entityId, LocationVigilantNpcDescription vigilantNpc, VigilantNpcThreatMethodDescription threatMethod);
        void RobberQuestComplete(int entityId, int line, int step, RobberQuestTaskDescription task);
        void BuyInventoryItem(int entityId, LocationNpcDescription npc, InventoryItemDescription item, int amount);
        void SellInventoryItem(int entityId, LocationNpcDescription npc, InventoryItemDescription item, int amount);
        void CollectionFinished(int entityId, CollectionDescription collection);
        void Surrender(int entityId, Vector3 position);
        void Handcuff(int entityId, int targetEntityId, Vector3 position);
        void RemoveHandcuff(int entityId, int uncuffedEntityId, bool isTeammate, Vector3 position);
        void Arrested(int entityId, int targetEntityId, bool isForced, int policeAttentionPoints, Vector3 position);
        void IncreaseCoins(int entityId, CoinsSourceDescription source, int amount, bool isAoe);
        void DecreaseCoins(int entityId, DecreaseCoinsReasonDescription reason, int amount);
        void BuyCarSlot(int entityId, CarSlotDescription carSlot);
        void BuyHelicopterSlot(int entityId, HelicopterSlotDescription helicopterSlot);
        void TeleportCar(int entityId, int spawnPointEntityId, Vector3 spawnPointPosition, PlayerRoleDescription role, CarDescription car, bool isSpawnedVehicleTrunkEmpty);
        void TeleportHelicopter(int entityId, int spawnPointEntityId, Vector3 spawnPointPosition, PlayerRoleDescription role, HelicopterDescription helicopter, bool isSpawnedVehicleTrunkEmpty);
        void BorderExit(int entityId, PlayerRoleDescription role, CarDescription car, HelicopterDescription helicopter, int policeAttentionPoints, Vector3 position, string vehicleOwnerPrivateId);
        void PlayerLeaveBattle(int entityId, PlayerRoleDescription role, int policeAttentionPoints, Vector3 position, PlayerLeaveBattleReasonDescription reason);
        void RefillCopEquip(int entityId, RefillCopEquipSourceDescription source, EquipPresetDescription equipPreset, WeaponPresetDescription weaponPreset, Vector3 position);
        void Repair(int entityId, InventoryItemDescription item);
        void BecomeRobber(int entityId, BecomeRobberStateDescription state, int equipCost, Vector3 position);
        void RobberyCoinsDiff(int entityId, int inventoryCostDiff, int vehicleCostDiff);
        void UseItem(int entityId, PlayerRoleDescription role, InventoryItemDescription item, bool isFromPocket, Vector3 position);
        void GrenadeRemovedByLimit(GrenadeDescription grenade);
        void PutCargoInExchanger(int entityId, LocationCargoExchangerDescription cargoExchanger, CargoDescription cargo);
        void CargoExchange(int entityId, LocationCargoExchangerDescription cargoExchanger, int cargoCount, int cargoCost);
        void ArrestCargoExchangerCargos(int entityId, LocationCargoExchangerDescription cargoExchanger, int cargoCost);
        void ArrestCargoExchangerCash(int entityId, LocationCargoExchangerDescription cargoExchanger, int cargoCost);
        void TakeSettlementTax(int entityId, LocationSettlementCashDescription description, int cashAmount);
        void GetClanConquestItemsReward(int entityId, LocationSettlementCashDescription description, int cashAmount);
        void UseEmotion(int entityId, string emoteId, PlayerRoleDescription role);
        void BecomeCopAttempt(int entityId, BecomeCopAttemptResultAnalyticsDescription result);
        void ConvertRealmSessionToYears(int entityId, int beforePrisonRealmId, int prisonEnterTypeYears, int realmAssetsYears, int killsYears, int lootedExchangedCargoMoneyYears, int producedDrugCargosYears, int robberyLootedItemsYears, int totalYears);
        void UnlockTattoo(int entityId, TattooDescription tattooDescription, int grade);
        void ChangeCar(int entityId, CarDescription car);
        void ChangeHelicopter(int entityId, HelicopterDescription helicopter);
        void SendChangedCustomize(int entityId, CustomizationInfo oldInfo, CustomizationInfo info, int tattooGrade, int tattooFGrade);
        void CollectMoneyWorkbench(int entityId, int amount, string ownerUid);
        void SetBuilding(int entityId, PlotDescription plot, PlotBuilderSlotTypeDescription buildingSlotType, IIdentified buildingSlotId, InventoryItemDescription item);
        void UnsetBuilding(int entityId, PlotDescription plot, PlotBuilderSlotTypeDescription buildingSlotType, IIdentified buildingSlotId, InventoryItemDescription item);
        void SetFurniture(int entityId, PlotBuilderSlotTypeDescription parentSlotType, IIdentified parentSlotId, IIdentified parentValueId, PlotBuilderSlotTypeDescription furnitureSlotType, IIdentified furnitureSlotId, InventoryItemDescription item);
        void SetFurnitureUpgrade(int entityId, PlotBuilderSlotTypeDescription parentSlotType, IIdentified parentSlotId, IIdentified parentValueId, PlotBuilderSlotTypeDescription upgradeSlotType, IIdentified upgradeSlotId, InventoryItemDescription item);
        void ThrowGrenade(int entityId, PlayerRoleDescription role, GrenadeDescription grenade, Vector3 throwPosition);
        void GrenadeExplosion(int entityId, PlayerRoleDescription role, GrenadeDescription grenade, Vector3 position);
        void DefenseStateOnOwnerEnter(int entityId, int defensePointsLeft, int defensePointsTotal, int uncommonModsCount, int epicModsCount, int legendModsCount);
        void UseDecoder(int entityId, string plotOwnerUid, string buildingId, PlotBuildingDecoderDescription decoderDescription, int defensePointsLeft, int defensePointsTotal, int uncommonModsCount, int epicModsCount, int legendModsCount, long raidLockTimeLeft, PlotBuildingRaidStateDescription decodingState);
        void BreachStorage(int entityId, string plotOwnerUid, string buildingId, StorageDescription storageDescription);
        void BreachPlotContainer(int entityId, string plotOwnerUid, string buildingId, MainBuildingPlotContainerSlotDescription slotDescription);
        void BreachPlotVehicleTrunk(int entityId, string plotOwnerUid, string buildingId, string carId);
        void LootMoneyWorkbench(int entityId, string plotOwnerUid, int amount);
        void TutorialStep(int entityId, int step, long timeSinceLastStep);
        void SetPlotCar(int entityId, IIdentified buildingId, InventoryItemDescription item);
        void UnsetPlotCar(int entityId, IIdentified buildingId, InventoryItemDescription item, bool isTrunkEmpty, bool isUpgradesEmpty, bool isVehicleOnLocation);
        void SetPlotHelicopter(int entityId, IIdentified buildingId, InventoryItemDescription item);
        void UnsetPlotHelicopter(int entityId, IIdentified buildingId, InventoryItemDescription item, bool isTrunkEmpty, bool isVehicleOnLocation);
        void EnterBattle(int entityId, CarDescription car, HelicopterDescription helicopter, bool isVehicleOwner, bool isEnterWithVehicle, bool isPartyEnter, bool isPlotEnterPoint);
        void SpawnPlotCar(int entityId, IIdentified buildingId, CarDescription car, bool isActiveVehicleTrunkDropped, bool isActiveVehicleNotOnLocation);
        void SpawnPlotHelicopter(int entityId, IIdentified buildingId, HelicopterDescription helicopter, bool isActiveVehicleTrunkDropped, bool isActiveVehicleNotOnLocation);
        void TeleportPlotCar(int entityId, IIdentified buildingId, CarDescription car, bool isActiveVehicleTrunkDropped, bool isActiveVehicleNotOnLocation);
        void TeleportPlotHelicopter(int entityId, IIdentified buildingId, HelicopterDescription helicopter, bool isActiveVehicleTrunkDropped, bool isActiveVehicleNotOnLocation);
        void BreachCarTrunk(int entityId, int vehicleOwnerEntityId, PlayerRoleDescription vehicleOwnerRole, CarDescription car, Vector3 position);
        void StealCarTrunkItem(int entityId, int vehicleOwnerEntityId, PlayerRoleDescription vehicleOwnerRole, CarDescription car, InventoryItemDescription item, int count, Vector3 position);
        void StealCarTrunkCargo(int entityId, int vehicleOwnerEntityId, PlayerRoleDescription vehicleOwnerRole, CarDescription car, CargoDescription cargo, Vector3 position);
        void StealShelvingCargo(int entityId, string plotOwnerUid, CargoDescription cargoDescription);
        void StealCollectionCargo(int entityId, string plotOwnerUid, CargoDescription cargoDescription);
        void StealCargoWorkbenchCargo(int entityId, string plotOwnerUid, CargoDescription cargoDescription);
        void StealCargoWorkbenchItem(int entityId, string plotOwnerUid, InventoryItemDescription itemDescription, int count);
        void StealPlotCarTrunkItem(int entityId, string plotOwnerUid, string buildingId, string carId, InventoryItemDescription itemDescription, int count);
        void StealPlotCarTrunkCargo(int entityId, string plotOwnerUid, string buildingId, string carId, CargoDescription cargoDescription);
        void StealStorageItem(int entityId, string plotOwnerUid, string buildingId, StorageSlotDescription slotDescription, InventoryItemDescription itemDescription, int count);
        void StealPlotContainerItem(int entityId, string plotOwnerUid, string buildingId, MainBuildingPlotContainerSlotDescription slotDescription, InventoryItemDescription itemDescription, int count);
        void SetCarUpgrade(int entityId, CarDescription car, InventoryItemDescription upgradeItem);
        void MoveBaseLocally(int entityId, SectorDescription sector, SettlementDescription settlement, int settlersCount, string settlementOwnerUid);
        void CumulativeItemCharging(int entityId, InventoryItemDescription chargingItem, int cumulativePoints, CumulativeItemChargeSourceDescription chargingSource, string chargingSourcePayload, Vector3 chargingSourcePosition);
        void LocationEventFinish(LocationEventDescription locationEvent, long eventDuration, LocationEventFinishReasonDescription locationEventFinishReason);
        void LocationEventStart(LocationEventDescription locationEvent);
        void StartShooterBattle();
        void StopShooterBattle();
        void StartPlayersDisconnectTimer(StartPlayersDisconnectTimerReasonDescription reason);
        void ShooterMatchmakingGroups(long timeSinceRoomStart, string matchmakingGroupsId, int soloCitizensCount, int duoCitizensCount, int trioCitizensCount, int soloRobbersCount, int duoRobbersCount, int trioRobbersCount, int soloCopsCount, int duoCopsCount, int trioCopsCount);
    }
}