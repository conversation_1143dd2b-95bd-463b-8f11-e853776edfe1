using System.Numerics;
using Framework.Analytics.Analytics;
using Framework.Analytics.AnalyticsRequest;
using Framework.Core.Identified;
using Models.Data;
using Models.MathUtils;
using Models.Models.ClanBattleModel;
using Models.Models.CustomizationModel;
using Models.Models.Parties;
using Models.Models.WorldModel.VehiclesWorldModel;
using Models.References;
using Models.References.Bollard;
using Models.References.Builder.Breach;
using Models.References.Builder;
using Models.References.Building;
using Models.References.Camera;
using Models.References.Car;
using Models.References.Cargo;
using Models.References.CargoSeller;
using Models.References.Character;
using Models.References.Collection;
using Models.References.Container;
using Models.References.CraftWorkbench;
using Models.References.Door;
using Models.References.Grenade;
using Models.References.HangarStorage;
using Models.References.Helicopter;
using Models.References.HideObject;
using Models.References.Inventory;
using Models.References.KinematicObject;
using Models.References.Location;
using Models.References.Location.LocationTraders;
using Models.References.LootNode;
using Models.References.LootObject;
using Models.References.Plot;
using Models.References.PlotBuildingDefenseBlock;
using Models.References.PlotContainers;
using Models.References.Realm;
using Models.References.RobberQuest;
using Models.References.SecurityPanel;
using Models.References.Tattoo;
using Models.References.VigilantNpc;
using Models.References.Weapon;
using Server.Battle.Analytics.Requests;
using Server.Battle.Models.States;
using Server.Common.Extensions;
using Server.Common.Utils;
using Server.References;

namespace Server.Common.Models.AnalyticsModel
{
    public class BattleAnalyticsModel : IBattleAnalyticsModel
    {
        private readonly IAnalytics _analytics;
        private readonly BattleRoomState _state;
        private readonly LocationDescription _location;
        private readonly string _roomId;
        private readonly IParties _parties;
        private readonly IVehiclesWorldModel _vehicles;
        private readonly int _realmId;
        private readonly long _realmStartTs;
        private readonly string _sectorId;
        private readonly string _settlementId;
        private readonly string _clanConquestObjectId;
        private readonly string _clanConquestSectorId;
        private readonly ICustomizationAnalyticsModel _customizationModel; 
        
        public BattleAnalyticsModel(IAnalytics analytics, BattleRoomState state, LocationDescription location, string roomId, IParties parties, SectorDescription sector, SettlementDescription settlement, IVehiclesWorldModel vehicles, IClanBattleModel clanBattleModel, int realmId, long realmStartTs)
        {
            _analytics = analytics;
            _state = state;
            _location = location;
            _roomId = roomId;
            _parties = parties;
            _vehicles = vehicles;
            _realmId = realmId;
            _realmStartTs = realmStartTs;

            _customizationModel = new CustomizationAnalyticsModel(analytics);
            _sectorId = GetNullableIdentifiedId(sector);
            _settlementId = GetNullableIdentifiedId(settlement);
            _clanConquestObjectId = GetNullableIdentifiedId(clanBattleModel.ConquestObjectTypeModel.Value);
            _clanConquestSectorId = GetNullableIdentifiedId(clanBattleModel.ConquestSectorModel.Value);
        }

        public void UnlockContainer(int entityId, LocationContainerDescription container, bool isLockpickUsed)
        {
            Send(new UnlockContainerAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Container = container, IsLockpickUsed = isLockpickUsed, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void UnlockDoor(int entityId, LocationDoorDescription door, bool isLockpickUsed)
        {
            Send(new UnlockDoorAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Door = door, IsLockpickUsed = isLockpickUsed, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void UnlockLootNode(int entityId, LocationLootNodeDescription lootNode, bool isLockpickUsed)
        {
            Send(new UnlockLootNodeAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, LootNode = lootNode, IsLockpickUsed = isLockpickUsed, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void HackPanel(int entityId, LocationPanelDescription panel)
        {
            Send(new HackPanelAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Panel = panel, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId)});
        }

        public void KnockoutCharacter(int entityId, KnockoutReasonDescription reason, int killerEntityId, PlayerRoleDescription role, PlayerRoleDescription killerRole, WeaponDescription weapon, GrenadeDescription grenade, CharacterKnockoutOrDeathRobberStateDescription robberState, Vector3 position, Vector3 killerPosition)
        {
            var killerPartySize = killerEntityId != BattleEntityIdData.Empty ? GetPartySize(killerEntityId) : 0;
            Send(new KnockoutCharacterAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Reason = reason, KillerUid = GetUid(killerEntityId), Role = role, KillerRoleId = GetNullableIdentifiedId(killerRole), WeaponId = GetNullableIdentifiedId(weapon), GrenadeId = GetNullableIdentifiedId(grenade), RobberState = robberState, Position = position, KillerPosition = killerPosition, PartySize = GetPartySize(entityId), KillerPartySize = killerPartySize, ClanConquestObjectId = _clanConquestObjectId, ClanConquestSectorId = _clanConquestSectorId, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void ReviveCharacter(int entityId, int saverEntityId, PlayerRoleDescription role, PlayerRoleDescription saverRole, Vector3 position, bool isSameParty)
        {
            Send(new ReviveCharacterAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SaverUid = GetUid(saverEntityId), Role = role, SaverRole = saverRole, Position = position, PartySize = GetPartySize(entityId), SaverPartySize = GetPartySize(saverEntityId), IsSameParty = isSameParty, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void Death(int entityId, DeathReasonAnalyticsDescription reason, int killerEntityId, PlayerRoleDescription role, PlayerRoleDescription killerRole, WeaponDescription weapon, GrenadeDescription grenade, CharacterKnockoutOrDeathRobberStateDescription robberState, Vector3 position, int policeAttentionPoints)
        {
            var killerPartySize = killerEntityId != BattleEntityIdData.Empty ? GetPartySize(killerEntityId) : 0;
            Send(new DeathAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Reason = reason, Role = role, KillerUid = GetUid(killerEntityId), KillerRoleId = GetNullableIdentifiedId(killerRole), WeaponId = GetNullableIdentifiedId(weapon), GrenadeId = GetNullableIdentifiedId(grenade), RobberState = robberState, Position = position, PoliceAttentionPoints = policeAttentionPoints, PartySize = GetPartySize(entityId), KillerPartySize = killerPartySize, ClanConquestObjectId = _clanConquestObjectId, ClanConquestSectorId = _clanConquestSectorId, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId)});
        }

        public void RoleTime(int entityId, PlayerRoleDescription role, long time, int partySize)
        {
            Send(new RoleTimeAnalyticsRequest { Uid = GetUid(entityId), Location  = _location, RoomId = _roomId, Role = role, Time = time, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void PutMoneyWorkbench(int entityId, int amount, string ownerUid)
        {
            Send(new PutMoneyWorkbenchAnalyticsRequest {Uid = GetUid(entityId), Location = _location, RoomId = _roomId, PartySize = GetPartySize(entityId), Amount = amount, SectorId = _sectorId, SettlementOwnerUid = ownerUid, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }
    
        public void ExchangeMoneyWorkbench(int entityId, int amountDirty, int amountClean, string ownerUid)
        {
            Send(new ExchangeMoneyWorkbenchAnalyticsRequest {Uid = GetUid(entityId), Location = _location, RoomId = _roomId, PartySize = GetPartySize(entityId), AmountDirty = amountDirty, AmountClean = amountClean, SectorId = _sectorId, SettlementOwnerUid = ownerUid, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId)});
        }

        public void SetCraftWorkbench(int entityId, LocationCraftWorkbenchDescription description)
        {
            Send(new SetCraftWorkbenchAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Description = description, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void UseBollard(int entityId, LocationBollardDescription bollard)
        {
            _location.TryGetKinematicObjectsController(bollard.KinematicObjectsControllerEntityId, out var locationKinematicObjectDescription);
            Send(new UseBollardAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Bollard = bollard, KinematicObjectsController = locationKinematicObjectDescription.Description, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void TakeLootNode(int entityId, LocationLootNodeDescription lootNode)
        {
            Send(new TakeLootNodeAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, LootNode = lootNode, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void TakeStaticLootObject(int entityId, StaticLootObjectDescription lootObject)
        {
            Send(new TakeStaticLootObjectAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, LootObject = lootObject, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void TakeRandomLootObject(int entityId, RandomLootObjectDescription lootObject)
        {
            Send(new TakeRandomLootObjectAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, LootObject = lootObject, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void TakeLoot(int entityId, PlayerRoleDescription ownerRole)
        {
            Send(new TakeLootAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, OwnerRole = ownerRole, PartySize = GetPartySize(entityId) });
        }

        public void TakeVehicleLoot(int entityId)
        {
            Send(new TakeVehicleLootAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, PartySize = GetPartySize(entityId) });
        }

        public void ArrestLoot(int entityId, PlayerRoleDescription lootOwnerRole, bool isTouched)
        {
            Send(new ArrestLootAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, LootOwnerRole = lootOwnerRole, IsTouched = isTouched, PartySize = GetPartySize(entityId) });
        }

        public void ArrestVehicleLoot(int entityId, bool isTouched)
        {
            Send(new ArrestVehicleLootAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, IsTouched = isTouched, PartySize = GetPartySize(entityId) });
        }

        public void LootRemoved(PlayerRoleDescription ownerRole, bool isTouched, LootRemoveReasonDescription reason, Vector3 position)
        {
            Send(new LootRemovedAnalyticsRequest { Location = _location, RoomId = _roomId, OwnerRole = ownerRole, IsTouched = isTouched, Reason = reason, Position = position});
        }

        public void VehicleLootRemoved(bool isTouched, VehicleLootRemoveReasonDescription reason, Vector3 position)
        {
            Send(new VehicleLootRemovedAnalyticsRequest { Location = _location, RoomId = _roomId, IsTouched = isTouched, Reason = reason, Position = position });
        }

        public void FurnitureLootRemoved(FurnitureTypeDescription furnitureType, IIdentified furnitureId, FurnitureLootRemoveReasonDescription reason, bool isTouched)
        {
            Send(new FurnitureLootRemovedAnalyticsRequest { Location = _location, RoomId = _roomId, FurnitureType = furnitureType, FurnitureId = furnitureId, Reason = reason, IsTouched = isTouched, RealmId = _realmId, RealmStartTs = _realmStartTs });
        }

        public void BuildingLootRemoved(BuilderBuildingTypeDescription buildingType, BuildingLootRemoveReasonDescription reason, bool isTouched)
        {
            Send(new BuildingLootRemovedAnalyticsRequest { Location = _location, RoomId = _roomId, BuildingType = buildingType, Reason = reason, IsTouched = isTouched, RealmId = _realmId, RealmStartTs = _realmStartTs });
        }

        public void EnterHideObject(int entityId, LocationHideObjectDescription hideObject, PlayerRoleDescription role, bool result)
        {
            Send(new EnterHideObjectAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, HideObject = hideObject, Role = role, Result = result, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId)});
        }

        public void CheckHideObject(int entityId, LocationHideObjectDescription hideObject, PlayerRoleDescription role, bool checkResult)
        {
            Send(new CheckHideObjectAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, HideObject = hideObject, CheckResult = checkResult, PartySize = GetPartySize(entityId), Role = role, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void KinematicObjectChanged(int kinematicObjectsControllerEntityId, KinematicObjectsControllerDescription kinematicObjectsController, int entityId, PlayerRoleDescription role)
        {
            Send(new KinematicObjectChangedAnalyticsRequest { Location = _location, RoomId = _roomId, EntityId = kinematicObjectsControllerEntityId, KinematicObjectsController = kinematicObjectsController, Uid = GetUid(entityId), Role = role, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void TakeBuildingCargo(int entityId, int cargoEntityId, CargoDescription cargo, Vector3 cargoPosition)
        {
            Send(new TakeBuildingCargoAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, EntityId = cargoEntityId, Cargo = cargo, Position = cargoPosition, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void CargoRemoved(CargoDescription cargo, Vector3 cargoPosition, CargoRemoveReasonDescription reason)
        {
            Send(new CargoRemovedAnalyticsRequest() { Location = _location, RoomId = _roomId, Cargo = cargo, Position = cargoPosition, Reason = reason});
        }

        public void SetCargoWorkbench(int entityId, CargoDescription cargo)
        {
            Send(new SetCargoWorkbenchAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Cargo = cargo, PartySize = GetPartySize(entityId), SectorId = _sectorId, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void ExchangeCargo(int entityId, CargoDescription cargo)
        {
            Send(new ExchangeCargoAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Cargo = cargo, PartySize = GetPartySize(entityId) });
        }

        public void BuyCargo(int entityId, LocationCargoSellerDescription cargoSeller)
        {
            Send(new BuyCargoAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, CargoSeller = cargoSeller, PartySize = GetPartySize(entityId), SectorId = _sectorId, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId), CarId = GetActiveCarId(entityId)});
        }

        public void BreakCollider(int entityId, PlayerRoleDescription role, int breakableColliderEntityId, Vector3 position)
        {
            Send(new BreakColliderAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Role = role, BreakableColliderEntityId = breakableColliderEntityId, Position = position, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void SetDrill(int entityId, SafeDoorDescription description)
        {
            Send(new SetDrillAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Description = description, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void ArrestDrill(int entityId, SafeDoorDescription description)
        {
            Send(new ArrestDrillAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Description = description, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void CameraTriggered(int entityId, LocationCameraDescription camera)
        {
            Send(new CameraTriggeredAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Camera = camera, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void BreakCamera(int entityId, LocationCameraDescription camera)
        {
            Send(new BreakCameraAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Camera = camera, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void BuildingReset(LocationBuildingDescription building)
        {
            Send(new BuildingResetAnalyticsRequest() { Location = _location, RoomId = _roomId, Building = building});
        }

        public void VigilantNpcStartCall(int entityId, LocationVigilantNpcDescription vigilantNpc)
        {
            Send(new VigilantNpcStartCallAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, VigilantNpc = vigilantNpc, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void VigilantNpcFinishCall(int entityId, LocationVigilantNpcDescription vigilantNpc)
        {
            Send(new VigilantNpcFinishCallAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, VigilantNpc = vigilantNpc, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void KnockoutVigilantNpc(int entityId, LocationVigilantNpcDescription vigilantNpc)
        {
            Send(new KnockoutVigilantNpcAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, VigilantNpc = vigilantNpc, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void ReviveVigilantNpc(int entityId, LocationVigilantNpcDescription vigilantNpc, PlayerRoleDescription role)
        {
            Send(new ReviveVigilantNpcAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, VigilantNpc = vigilantNpc, Role = role, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void ThreatenVigilantNpc(int entityId, LocationVigilantNpcDescription vigilantNpc, VigilantNpcThreatMethodDescription threatMethod)
        {
            Send(new ThreatenVigilantNpcAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, VigilantNpc = vigilantNpc, ThreatMethod = threatMethod, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void RobberQuestComplete(int entityId, int line, int step, RobberQuestTaskDescription task)
        {
            Send(new RobberQuestCompleteAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Line = line, Step = step, Task = task, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void BuyInventoryItem(int entityId, LocationNpcDescription npc, InventoryItemDescription item, int amount)
        {
            Send(new BuyInventoryItemAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Npc = npc, Item = item, Amount = amount, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void SellInventoryItem(int entityId, LocationNpcDescription npc, InventoryItemDescription item, int amount)
        {
            Send(new SellInventoryItemAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Npc = npc, Item = item, Amount = amount, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void CollectionFinished(int entityId, CollectionDescription collection)
        {
            Send(new CollectionFinishedAnalyticsRequest() { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Collection = collection, PartySize = GetPartySize(entityId), SectorId = _sectorId, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void Surrender(int entityId, Vector3 position)
        {
            Send(new SurrenderAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Position = position, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void Handcuff(int entityId, int targetEntityId, Vector3 position)
        {
            Send(new HandcuffAnalyticsRequest { Uid = GetUid(entityId), TargetUid = GetUid(targetEntityId), Location = _location, RoomId = _roomId, Position = position, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void RemoveHandcuff(int entityId, int uncuffedEntityId, bool isTeammate, Vector3 position)
        {
            Send(new RemoveHandcuffAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, IsTeammate = isTeammate, UncuffedUid = GetUid(uncuffedEntityId), Position = position, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void Arrested(int entityId, int targetEntityId, bool isForced, int policeAttentionPoints, Vector3 position)
        {
            Send(new ArrestedAnalyticsRequest { Uid = GetUid(entityId), TargetUid = GetUid(targetEntityId), Location = _location, RoomId = _roomId, IsForced = isForced, PoliceAttentionPoints = policeAttentionPoints, Position = position, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void IncreaseCoins(int entityId, CoinsSourceDescription source, int amount, bool isAoe)
        {
            Send(new IncreaseCoinsAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Source = source, Amount = amount, IsAoe = isAoe, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void DecreaseCoins(int entityId, DecreaseCoinsReasonDescription reason, int amount)
        {
            Send(new DecreaseCoinsAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Reason = reason, Amount = amount, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void BuyCarSlot(int entityId, CarSlotDescription carSlot)
        {
            Send(new BuyCarSlotAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, CarSlot = carSlot, PartySize = GetPartySize(entityId) });
        }

        public void BuyHelicopterSlot(int entityId, HelicopterSlotDescription helicopterSlot)
        {
            Send(new BuyHelicopterSlotAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, HelicopterSlot = helicopterSlot, PartySize = GetPartySize(entityId) });
        }

        public void TeleportCar(int entityId, int spawnPointEntityId, Vector3 spawnPointPosition, PlayerRoleDescription role, CarDescription car, bool isSpawnedVehicleTrunkEmpty)
        {
            Send(new TeleportCarAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SpawnPointEntityId = spawnPointEntityId, SpawnPointPosition = spawnPointPosition, Role = role, Car = car, IsSpawnedVehicleTrunkEmpty = isSpawnedVehicleTrunkEmpty, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void TeleportHelicopter(int entityId, int spawnPointEntityId, Vector3 spawnPointPosition, PlayerRoleDescription role, HelicopterDescription helicopter, bool isSpawnedVehicleTrunkEmpty)
        {
            Send(new TeleportHelicopterAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SpawnPointEntityId = spawnPointEntityId, SpawnPointPosition = spawnPointPosition, Role = role, Helicopter = helicopter, IsSpawnedVehicleTrunkEmpty = isSpawnedVehicleTrunkEmpty, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void ChangeCar(int entityId, CarDescription car)
        {
            _customizationModel.ChangeCar(GetUid(entityId), car);
        }

        public void ChangeHelicopter(int entityId, HelicopterDescription helicopter)
        {
            _customizationModel.ChangeHelicopter(GetUid(entityId), helicopter);
        }

        public void SendChangedCustomize(int entityId, CustomizationInfo oldInfo, CustomizationInfo info, int tattooGrade, int tattooFGrade)
        {
            _customizationModel.SendChangedCustomize(GetUid(entityId), oldInfo, info, tattooGrade, tattooFGrade);
        }

        public void SetBuilding(int entityId, PlotDescription plot, PlotBuilderSlotTypeDescription buildingSlotType, IIdentified buildingSlotId, InventoryItemDescription item)
        {
            Send(new SetBuildingAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotPosition = plot.Position, BuildingSlotType = buildingSlotType, BuildingSlotId = GetNullableIdentifiedId(buildingSlotId), Item = item, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void UnsetBuilding(int entityId, PlotDescription plot, PlotBuilderSlotTypeDescription buildingSlotType, IIdentified buildingSlotId, InventoryItemDescription item)
        {
            Send(new UnsetBuildingAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotPosition = plot.Position, BuildingSlotType = buildingSlotType, BuildingSlotId = GetNullableIdentifiedId(buildingSlotId), Item = item, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void SetFurniture(int entityId, PlotBuilderSlotTypeDescription parentSlotType, IIdentified parentSlotId, IIdentified parentValueId, PlotBuilderSlotTypeDescription furnitureSlotType, IIdentified furnitureSlotId, InventoryItemDescription item)
        {
            Send(new SetFurnitureAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, ParentSlotTypeId = GetNullableIdentifiedId(parentSlotType), ParentSlotId = GetNullableIdentifiedId(parentSlotId), ParentValueId = GetNullableIdentifiedId(parentValueId), FurnitureSlotType = furnitureSlotType, FurnitureSlotId = GetNullableIdentifiedId(furnitureSlotId), Item = item, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void SetFurnitureUpgrade(int entityId, PlotBuilderSlotTypeDescription parentSlotType, IIdentified parentSlotId, IIdentified parentValueId, PlotBuilderSlotTypeDescription upgradeSlotType, IIdentified upgradeSlotId, InventoryItemDescription item)
        {
            Send(new SetFurnitureUpgradeAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, ParentSlotTypeId = GetNullableIdentifiedId(parentSlotType), ParentSlotId = GetNullableIdentifiedId(parentSlotId), ParentValueId = GetNullableIdentifiedId(parentValueId), UpgradeSlotType = upgradeSlotType, UpgradeSlotId = GetNullableIdentifiedId(upgradeSlotId), Item = item, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void ThrowGrenade(int entityId, PlayerRoleDescription role, GrenadeDescription grenade, Vector3 throwPosition)
        {
            Send(new ThrowGrenadeAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Role = role, Grenade = grenade, ThrowPosition = throwPosition, ClanConquestObjectId = _clanConquestObjectId, ClanConquestSectorId = _clanConquestSectorId, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void GrenadeExplosion(int entityId, PlayerRoleDescription role, GrenadeDescription grenade, Vector3 position)
        {
            Send(new GrenadeExplosionAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, RoleId = GetNullableIdentifiedId(role), Grenade = grenade, Position = position, ClanConquestObjectId = _clanConquestObjectId, ClanConquestSectorId = _clanConquestSectorId, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void BorderExit(int entityId, PlayerRoleDescription role, CarDescription car, HelicopterDescription helicopter, int policeAttentionPoints, Vector3 position, string vehicleOwnerPrivateId)
        {
            var uid = GetUid(entityId);
            var isVehicleOwner = vehicleOwnerPrivateId == uid;
            Send(new BorderExitAnalyticsRequest { Uid = uid, Location = _location, RoomId = _roomId, Role = role, Car = car, Helicopter = helicopter, PoliceAttentionPoint = policeAttentionPoints, Position = position, PartySize = GetPartySize(entityId), IsVehicleOwner = isVehicleOwner, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void PlayerLeaveBattle(int entityId, PlayerRoleDescription role, int policeAttentionPoints, Vector3 position, PlayerLeaveBattleReasonDescription reason)
        {
            Send(new PlayerLeaveBattleAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Role = role, PoliceAttentionPoint = policeAttentionPoints, Position = position, PartySize = GetPartySize(entityId), LeaveReason = reason, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }
        
        public void RefillCopEquip(int entityId, RefillCopEquipSourceDescription source, EquipPresetDescription equipPreset, WeaponPresetDescription weaponPreset, Vector3 position)
        {
            Send(new RefillCopEquipAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Source = source, EquipPreset = equipPreset, WeaponPreset = weaponPreset, Position = position, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void Repair(int entityId, InventoryItemDescription item)
        {
            Send(new RepairAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Item = item, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void BecomeRobber(int entityId, BecomeRobberStateDescription state, int equipCost, Vector3 position)
        {
            Send(new BecomeRobberAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, State = state, Position = position, EquipCost = equipCost, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void RobberyCoinsDiff(int entityId, int inventoryCostDiff, int vehicleCostDiff)
        {
            Send(new RobberyCoinsDiffAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, InventoryCostDiff = inventoryCostDiff, VehicleCostDiff = vehicleCostDiff, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void UseItem(int entityId, PlayerRoleDescription role, InventoryItemDescription item, bool isFromPocket, Vector3 position)
        {
            Send(new UseItemAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Role = role, Item = item, IsFromPocket = isFromPocket, Position = position, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void GrenadeRemovedByLimit(GrenadeDescription grenade)
        {
            Send(new GrenadeRemovedByLimitAnalyticsRequest { Location = _location, RoomId = _roomId, Grenade = grenade});
        }
        
        public void UseEmotion(int entityId, string emoteId, PlayerRoleDescription role)
        {
            Send(new UseEmotionAnalyticsRequest { Uid = GetUid(entityId), EmoteId = emoteId, Role = role, Location = _location, RoomId = _roomId, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void BecomeCopAttempt(int entityId, BecomeCopAttemptResultAnalyticsDescription result)
        {
            Send(new BecomeCopAttemptAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, Result = result, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void PutCargoInExchanger(int entityId, LocationCargoExchangerDescription cargoExchanger, CargoDescription cargo)
        {
            Send(new PutCargoInExchangerAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, CargoExchanger = cargoExchanger, Cargo = cargo, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void CargoExchange(int entityId, LocationCargoExchangerDescription cargoExchanger, int cargoCount, int cargoCost)
        {
            Send(new CargoExchangeAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, CargoExchanger = cargoExchanger, CargoCount = cargoCount, CargoCost = cargoCost, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void ArrestCargoExchangerCargos(int entityId, LocationCargoExchangerDescription cargoExchanger, int cargoCost)
        {
            Send(new ArrestCargoExchangerCargosAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, CargoExchanger = cargoExchanger, CargoCost = cargoCost, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void ArrestCargoExchangerCash(int entityId, LocationCargoExchangerDescription cargoExchanger, int cargoCost)
        {
            Send(new ArrestCargoExchangerCashAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, CargoExchanger = cargoExchanger, CargoCost = cargoCost, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void TakeSettlementTax(int entityId, LocationSettlementCashDescription description, int cashAmount)
        {
            Send(new TakeSettlementTaxAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, Description = description, CashAmount = cashAmount, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void GetClanConquestItemsReward(int entityId, LocationSettlementCashDescription description, int cashAmount)
        {
            Send(new GetClanConquestItemsRewardAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, Description = description, CashAmount = cashAmount, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void ConvertRealmSessionToYears(int entityId, int beforePrisonRealmId, int prisonEnterTypeYears, int realmAssetsYears, int killsYears, int lootedExchangedCargoMoneyYears, int producedDrugCargosYears, int robberyLootedItemsYears, int totalYears)
        {
            Send(new ConvertRealmSessionToYearsAnalyticsRequest { Uid = GetUid(entityId), BeforePrisonRealmId = beforePrisonRealmId, PrisonEnterTypeYears = prisonEnterTypeYears, RealmAssetsYears = realmAssetsYears, KillsYears = killsYears, LootedExchangedCargoMoneyYears = lootedExchangedCargoMoneyYears, ProducedDrugCargosYears = producedDrugCargosYears, RobberyLootedItemsYears = robberyLootedItemsYears, TotalYears = totalYears });
        }

        public void UnlockTattoo(int entityId, TattooDescription tattooDescription, int grade)
        {
            Send(new UnlockTattooAnalyticsRequest { Uid = GetUid(entityId), TattooDescription = tattooDescription, Grade = grade });
        }
        
        public void CollectMoneyWorkbench(int entityId, int amount, string ownerUid)
        {
            Send(new CollectMoneyWorkbenchAnalyticsRequest() {Uid = GetUid(entityId), Location = _location, RoomId = _roomId, PartySize = GetPartySize(entityId), CashAmount = amount, SectorId = _sectorId, SettlementOwnerUid = ownerUid, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId)});
        }
        
        public void EnterBattle(int entityId, CarDescription car, HelicopterDescription helicopter, bool isVehicleOwner, bool isEnterWithVehicle, bool isPartyEnter, bool isPlotEnterPoint)
        {
            var fullPartySize = _parties.TryGetPartyByEntity(entityId, out var party) ? party.MemberPublicIds.Count : 1;
            Send(new EnterBattleAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, Location = _location, Car = car, Helicopter = helicopter, IsPartyEnter = isPartyEnter, IsEnterWithVehicle = isEnterWithVehicle, IsVehicleOwner = isVehicleOwner, IsPlotEnterPoint = isPlotEnterPoint, FullPartySize = fullPartySize, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId)});
        }
        
        public void DefenseStateOnOwnerEnter(int entityId, int defensePointsLeft, int defensePointsTotal, int uncommonModsCount, int epicModsCount, int legendModsCount)
        {
            Send(new DefenseStateOnOwnerEnterAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, DefensePointsLeft = defensePointsLeft, DefensePointsTotal = defensePointsTotal, UncommonModulesCount = uncommonModsCount, EpicModulesCount = epicModsCount, LegendModulesCount = legendModsCount, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void UseDecoder(int entityId, string plotOwnerUid, string buildingId, PlotBuildingDecoderDescription decoderDescription, int defensePointsLeft, int defensePointsTotal, int uncommonModsCount, int epicModsCount, int legendModsCount, long raidLockTimeLeft, PlotBuildingRaidStateDescription decodingState)
        {
            Send(new UseDecoderAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, BuildingId = buildingId, DecoderDescription = decoderDescription, DefensePointsLeft = defensePointsLeft, DefensePointsTotal = defensePointsTotal, UncommonModulesCount = uncommonModsCount, EpicModulesCount = epicModsCount, LegendModulesCount = legendModsCount, RaidLockTimeLeft = raidLockTimeLeft, DecodingState = decodingState, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void BreachStorage(int entityId, string plotOwnerUid, string buildingId, StorageDescription storageDescription)
        {
            Send(new BreachStorageAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, BuildingId = buildingId, StorageDescription = storageDescription, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void BreachPlotContainer(int entityId, string plotOwnerUid, string buildingId, MainBuildingPlotContainerSlotDescription slotDescription)
        {
            Send(new BreachPlotContainerAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, BuildingId = buildingId, PlotContainerSlotDescription = slotDescription, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void BreachPlotVehicleTrunk(int entityId, string plotOwnerUid, string buildingId, string carId)
        {
            Send(new BreachPlotVehicleTrunkAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, BuildingId = buildingId, CarId = carId, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void LootMoneyWorkbench(int entityId, string plotOwnerUid, int amount)
        {
            Send(new LootMoneyWorkbenchAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, Amount = amount, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }
        
        public void TutorialStep(int entityId, int step, long timeSinceLastStep)
        {
            Send(new TutorialStepAnalyticsRequest { Uid = GetUid(entityId), Step = step, TimeSinceLastStep = timeSinceLastStep });
        }
        
        public void SetPlotCar(int entityId, IIdentified buildingId, InventoryItemDescription item)
        {
            Send(new SetPlotCarAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, BuildingId = buildingId, Item = item, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void UnsetPlotCar(int entityId, IIdentified buildingId, InventoryItemDescription item, bool isTrunkEmpty, bool isUpgradesEmpty, bool isVehicleOnLocation)
        {
            Send(new UnsetPlotCarAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, BuildingId = buildingId, Item = item, IsTrunkEmpty = isTrunkEmpty, IsUpgradesEmpty = isUpgradesEmpty, IsVehicleOnLocation = isVehicleOnLocation, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void SetPlotHelicopter(int entityId, IIdentified buildingId, InventoryItemDescription item)
        {
            Send(new SetPlotHelicopterAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, BuildingId = buildingId, Item = item, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void UnsetPlotHelicopter(int entityId, IIdentified buildingId, InventoryItemDescription item, bool isTrunkEmpty, bool isVehicleOnLocation)
        {
            Send(new UnsetPlotHelicopterAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, BuildingId = buildingId, Item = item, IsTrunkEmpty = isTrunkEmpty, IsVehicleOnLocation = isVehicleOnLocation, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void SpawnPlotCar(int entityId, IIdentified buildingId, CarDescription car, bool isActiveVehicleTrunkDropped, bool isActiveVehicleNotOnLocation)
        {
            Send(new SpawnPlotCarAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, BuildingId = buildingId, Car = car, IsActiveVehicleTrunkDropped = isActiveVehicleTrunkDropped, IsActiveVehicleNotOnLocation = isActiveVehicleNotOnLocation, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void SpawnPlotHelicopter(int entityId, IIdentified buildingId, HelicopterDescription helicopter, bool isActiveVehicleTrunkDropped, bool isActiveVehicleNotOnLocation)
        {
            Send(new SpawnPlotHelicopterAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, BuildingId = buildingId, Helicopter = helicopter, IsActiveVehicleTrunkDropped = isActiveVehicleTrunkDropped, IsActiveVehicleNotOnLocation = isActiveVehicleNotOnLocation, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void TeleportPlotCar(int entityId, IIdentified buildingId, CarDescription car, bool isActiveVehicleTrunkDropped, bool isActiveVehicleNotOnLocation)
        {
            Send(new TeleportPlotCarAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, BuildingId = buildingId, Car = car, IsActiveVehicleTrunkDropped = isActiveVehicleTrunkDropped, IsActiveVehicleNotOnLocation = isActiveVehicleNotOnLocation, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void TeleportPlotHelicopter(int entityId, IIdentified buildingId, HelicopterDescription helicopter, bool isActiveVehicleTrunkDropped, bool isActiveVehicleNotOnLocation)
        {
            Send(new TeleportPlotHelicopterAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, BuildingId = buildingId, Helicopter = helicopter, IsActiveVehicleTrunkDropped = isActiveVehicleTrunkDropped, IsActiveVehicleNotOnLocation = isActiveVehicleNotOnLocation, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }
        
        public void BreachCarTrunk(int entityId, int vehicleOwnerEntityId, PlayerRoleDescription vehicleOwnerRole, CarDescription car, Vector3 position)
        {
            Send(new BreachCarTrunkAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, SettlementId = _settlementId, Car = car, VehicleOwnerUid = GetUid(vehicleOwnerEntityId), VehicleOwnerRoleId = GetNullableIdentifiedId(vehicleOwnerRole), Position = position, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void StealCarTrunkItem(int entityId, int vehicleOwnerEntityId, PlayerRoleDescription vehicleOwnerRole, CarDescription car, InventoryItemDescription item, int count, Vector3 position)
        {
            Send(new StealCarTrunkItemAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, SettlementId = _settlementId, Car = car, VehicleOwnerUid = GetUid(vehicleOwnerEntityId), VehicleOwnerRoleId = GetNullableIdentifiedId(vehicleOwnerRole), Item = item, Count = count, Position = position, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void StealCarTrunkCargo(int entityId, int vehicleOwnerEntityId, PlayerRoleDescription vehicleOwnerRole, CarDescription car, CargoDescription cargo, Vector3 position)
        {
            Send(new StealCarTrunkCargoAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, SettlementId = _settlementId, Car = car, VehicleOwnerUid = GetUid(vehicleOwnerEntityId), VehicleOwnerRoleId = GetNullableIdentifiedId(vehicleOwnerRole), Cargo = cargo, Position = position, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }
        
        public void StealShelvingCargo(int entityId, string plotOwnerUid, CargoDescription cargoDescription)
        {
            Send(new StealShelvingCargoAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, CargoDescription = cargoDescription, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void StealCollectionCargo(int entityId, string plotOwnerUid, CargoDescription cargoDescription)
        {
            Send(new StealCollectionCargoAnalyticsRequest() { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, CargoDescription = cargoDescription, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void StealCargoWorkbenchCargo(int entityId, string plotOwnerUid, CargoDescription cargoDescription)
        {
            Send(new StealCargoWorkbenchCargoAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, CargoDescription = cargoDescription, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void StealCargoWorkbenchItem(int entityId, string plotOwnerUid, InventoryItemDescription itemDescription, int count)
        {
            Send(new StealCargoWorkbenchItemAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, ItemDescription = itemDescription, Count = count, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void StealPlotCarTrunkItem(int entityId, string plotOwnerUid, string buildingId, string carId, InventoryItemDescription itemDescription, int count)
        {
            Send(new StealPlotCarTrunkItemAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, BuildingId = buildingId, CarId = carId, ItemDescription = itemDescription, Count = count, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void StealPlotCarTrunkCargo(int entityId, string plotOwnerUid, string buildingId, string carId, CargoDescription cargoDescription)
        {
            Send(new StealPlotCarTrunkCargoAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, BuildingId = buildingId, CarId = carId, CargoDescription = cargoDescription, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void StealStorageItem(int entityId, string plotOwnerUid, string buildingId, StorageSlotDescription slotDescription, InventoryItemDescription itemDescription, int count)
        {
            Send(new StealStorageItemAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, BuildingId = buildingId, SlotDescription = slotDescription, ItemDescription = itemDescription, Count = count, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }
        public void StealPlotContainerItem(int entityId, string plotOwnerUid, string buildingId, MainBuildingPlotContainerSlotDescription slotDescription, InventoryItemDescription itemDescription, int count)
        {
            Send(new StealPlotContainerItemAnalyticsRequest { Uid = GetUid(entityId), RoomId = _roomId, SettlementId = _settlementId, SectorId = _sectorId, PlotOwnerUid = plotOwnerUid, BuildingId = buildingId, SlotDescription = slotDescription, ItemDescription = itemDescription, Count = count, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void SetCarUpgrade(int entityId, CarDescription car, InventoryItemDescription upgradeItem)
        {
            Send(new SetCarUpgradeAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, SectorId = _sectorId, Car = car, UpgradeItem = upgradeItem, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }
        
        public void MoveBaseLocally(int entityId, SectorDescription sector, SettlementDescription settlement, int settlersCount, string settlementOwnerUid)
        {
            var hasSettlementBuilding = sector.HasBuildings && sector.Buildings.ContainsKey(settlement);
            Send(new MoveBaseAnalyticsRequest { Uid = GetUid(entityId), OldSectorId = sector.Id, OldSettlementId = settlement.Id, NewSectorId = sector.Id, NewSettlementId = settlement.Id, SettlementUsersCount = settlersCount, SettlementOwnerUid = settlementOwnerUid, HasSettlementBuilding = hasSettlementBuilding, ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }
        
        public void CumulativeItemCharging(int entityId, InventoryItemDescription chargingItem, int cumulativePoints, CumulativeItemChargeSourceDescription chargingSource, string chargingSourcePayload, Vector3 chargingSourcePosition)
        {
            chargingSourcePayload ??= AnalyticsUtils.EmptyValue;
            var chargePercentage = MathExtension.Clamp((float)cumulativePoints / chargingItem.CumulativeItemDescription.MaxPointsAmount, 0f, 1f);
            Send(new CumulativeItemChargingAnalyticsRequest { Uid = GetUid(entityId), Location = _location, RoomId = _roomId, ChargingItem = chargingItem, ChargePercentage = chargePercentage, ChargeSource = chargingSource, ChargeSourcePayload = chargingSourcePayload, ChargeSourcePosition = chargingSourcePosition, PartySize = GetPartySize(entityId), ClanId = GetClanId(entityId), RealmId = GetRealmId(entityId), RealmStartTs = GetRealmStartTs(entityId) });
        }

        public void LocationEventFinish(LocationEventDescription locationEvent, long eventDuration, LocationEventFinishReasonDescription locationEventFinishReason)
        {
            Send(new FinishLocationEventAnalyticsRequest() { Location = _location, RoomId = _roomId, LocationEvent = locationEvent, EventDuration = eventDuration, FinishReason = locationEventFinishReason});
        }

        public void LocationEventStart(LocationEventDescription locationEvent)
        {
            Send(new StartLocationEventAnalyticsRequest { Location = _location, RoomId = _roomId, LocationEvent = locationEvent });
        }

        public void StartShooterBattle()
        {
            Send(new StartShooterBattleAnalyticsRequest { Location = _location, RoomId = _roomId });
        }

        public void StopShooterBattle()
        {
            Send(new StopShooterBattleAnalyticsRequest { Location = _location, RoomId = _roomId });
        }

        public void StartPlayersDisconnectTimer(StartPlayersDisconnectTimerReasonDescription reason)
        {
            Send(new StartPlayersDisconnectTimerAnalyticsRequest { Reason = reason, Location = _location, RoomId = _roomId });
        }

        public void ShooterMatchmakingGroups(long timeSinceRoomStart, string matchmakingGroupsId, int soloCitizensCount, int duoCitizensCount, int trioCitizensCount, int soloRobbersCount, int duoRobbersCount, int trioRobbersCount, int soloCopsCount, int duoCopsCount, int trioCopsCount)
        {
            Send(new ShooterMatchmakingGroupsAnalyticsRequest { Location = _location, RoomId = _roomId, TimeSinceRoomStart = timeSinceRoomStart, MatchmakingGroupsId = matchmakingGroupsId, SoloCitizensCount = soloCitizensCount, DuoCitizensCount = duoCitizensCount, TrioCitizensCount = trioCitizensCount, SoloRobbersCount = soloRobbersCount, DuoRobbersCount = duoRobbersCount, TrioRobbersCount = trioRobbersCount, SoloCopsCount = soloCopsCount, DuoCopsCount = duoCopsCount, TrioCopsCount = trioCopsCount });
        }

        private void Send<T>(in T request) where T : IAnalyticsRequest
        {
            _analytics.Send(request);
        }

        private int GetPartySize(int entityId)
        {
            return _parties.TryGetPartyByEntity(entityId, out var partyModel)
                ? partyModel.MembersCount
                : 1;
        }

        private string GetUid(int entityId)
        {
            return _state.JoinedUsers.TryGetKey(entityId, out var uid) ? uid : AnalyticsUtils.EmptyValue;
        }

        private string GetClanId(int entityId)
        {
            if (_state.JoinedUsers.TryGetKey(entityId, out var uid) &&
                _state.PlayerStates.TryGetValue(uid, out var playerState))
            {
                var clanId = playerState.ClanId;
                if (!string.IsNullOrEmpty(clanId))
                {
                    return clanId;
                }
            }

            return AnalyticsUtils.EmptyValue;
        }

        private int GetRealmId(int entityId)
        {
            if (_state.JoinedUsers.TryGetKey(entityId, out var uid) &&
                _state.PlayerStates.TryGetValue(uid, out var playerState))
            {
                return playerState.RealmId;
            }

            return 0;
        }

        private long GetRealmStartTs(int entityId)
        {
            if (_state.JoinedUsers.TryGetKey(entityId, out var uid) &&
                _state.PlayerStates.TryGetValue(uid, out var playerState))
            {
                return playerState.RealmStartTs;
            }

            return 0;
        }

        private string GetActiveCarId(int entityId)
        {
            if (_vehicles.TryGetCharacterCar(entityId, out var car))
            {
                return car.Description.Id;
            }
            return AnalyticsUtils.EmptyValue;
        }

        private string GetNullableIdentifiedId(IIdentified identified)
        {
            return identified == null ? AnalyticsUtils.EmptyValue : identified.Id;
        }
    }
}