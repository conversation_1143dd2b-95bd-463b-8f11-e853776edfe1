using Framework.Core.Identified;

namespace Server.References
{
    public class BecomeRobberStateDescription : Identified
    {
        private BecomeRobberStateDescription(string id) : base(id)
        {
        }

        public static BecomeRobberStateDescription Idle { get; } = new("idle");
        public static BecomeRobberStateDescription InVehicle { get; } = new("in_vehicle");
        public static BecomeRobberStateDescription InHideObject { get; } = new("in_hide_object");
    }
}