using Framework.Core.BinaryStream;
using Framework.Replication.Data;
using Framework.Replication.Serializer.Binary.Write;
using Models.Models.BattleData;
using Models.Models.BattleDataCollections;
using Models.References;
using Server.Battle.Models.DiffHistoryModels;
using Server.Battle.Models.DiffsHistoryModel;
using Server.Battle.Replication.ReplicationModel;
using Server.Battle.Replication.RoleDataModel;

namespace Server.Battle.Replication.DataBytesProvider
{
    public class DataBytesProvider : IDataBytesProvider
    {
        private readonly IData _worldData;
        private readonly IBattleDataCollections _battleDataCollections;
        private readonly IDiffHistoryModels _otherDiffHistoryModels;
        private readonly IDiffsHistoryModel _worldDiffsHistoryModel;
        private readonly IDiffsHistoryModel _worldSettingsOverrideDataDiffsHistoryModel;
        private readonly IRoleDataModel _roleDataModel;
        private readonly IData _worldSettingsOverrideData;
        private readonly IData _shooterData;
        private readonly IData _hangarData;
        private readonly IData _clanBattleData;
        private readonly IData _clanBattleAttackTeamPositionsData;
        private readonly IData _clanBattleDefendTeamPositionsData;
        private readonly IDiffsHistoryModel _shooterDataDiffsHistoryModel;
        private readonly IDiffsHistoryModel _hangarDataDiffsHistoryModel;
        private readonly IDiffsHistoryModel _clanBattleDataDiffsHistoryModel;
        private readonly IDiffsHistoryModel _clanBattleAttackTeamPositionsDiffsHistoryModel;
        private readonly IDiffsHistoryModel _clanBattleDefendTeamPositionsDiffsHistoryModel;

        private readonly IWriteBinarySerializer _writeSerializer = new WriteBinarySerializer();

        public DataBytesProvider(IBattleData battleData, IBattleDataCollections battleDataCollections, IReplicationModel replicationModel, IRoleDataModel roleDataModel)
        {
            _worldData = battleData.WorldData;
            _battleDataCollections = battleDataCollections;
            _otherDiffHistoryModels = replicationModel.OtherDiffHistoryModels;
            _worldDiffsHistoryModel = replicationModel.WorldDataDiffsHistoryModel;
            _roleDataModel = roleDataModel;
            _worldSettingsOverrideData = battleData.WorldSettingsOverrideData;
            _worldSettingsOverrideDataDiffsHistoryModel = replicationModel.OverrideSettingsDiffsHistoryModel;
            _shooterData = battleData.ShooterData;
            _hangarData = battleData.HangarData;
            _clanBattleData = battleData.ClanBattleData;
            _clanBattleAttackTeamPositionsData = battleData.ClanBattleAttackTeamPositionsData;
            _clanBattleDefendTeamPositionsData = battleData.ClanBattleDefendTeamPositionsData;
            _shooterDataDiffsHistoryModel = replicationModel.ShooterDiffsHistoryModel;
            _hangarDataDiffsHistoryModel = replicationModel.HangarDiffsHistoryModel;
            _clanBattleDataDiffsHistoryModel = replicationModel.ClanBattleDiffsHistoryModel;
            _clanBattleAttackTeamPositionsDiffsHistoryModel = replicationModel.ClanBattleAttackTeamPositionsDiffsHistoryModel;
            _clanBattleDefendTeamPositionsDiffsHistoryModel = replicationModel.ClanBattleDefendTeamPositionsDiffsHistoryModel;
        }

        public void WriteEntityWhole(int entityId, IBinaryWriteStream stream)
        {
            var data = _battleDataCollections.Entities[entityId];
            _writeSerializer.Write(data, stream);
        }
        
        public void WriteEntityDiff(int entityId, long tickIndex, IBinaryWriteStream stream)
        {
            var diffsHistoryModel = _otherDiffHistoryModels[entityId];
            diffsHistoryModel.Write(tickIndex, stream);
        }
        
        public bool IsEntityChanged(int entityId, long tickIndex)
        {
            var diffsHistoryModel = _otherDiffHistoryModels[entityId];
            return diffsHistoryModel.IsChanged(tickIndex);
        }

        public void WriteWorldWhole(IBinaryWriteStream stream)
        {
            _writeSerializer.Write(_worldData, stream);
        }
        
        public void WriteWorldDiff(long tickIndex, IBinaryWriteStream stream)
        {
            _worldDiffsHistoryModel.Write(tickIndex, stream);
        }
        
        public bool IsWorldChanged(long tickIndex)
        {
            return _worldDiffsHistoryModel.IsChanged(tickIndex);
        }

        public void WriteWorldSettingsOverrideWhole(IBinaryWriteStream stream)
        {
            _writeSerializer.Write(_worldSettingsOverrideData, stream);
        }

        public void WriteWorldSettingsOverrideDiff(long tickIndex, IBinaryWriteStream stream)
        {
            _worldSettingsOverrideDataDiffsHistoryModel.Write(tickIndex, stream);
        }

        public bool IsWorldSettingsOverrideChanged(long tickIndex)
        {
            return _worldSettingsOverrideDataDiffsHistoryModel.IsChanged(tickIndex);
        }

        public void WriteRoleDataWhole(PlayerRoleDescription role, IBinaryWriteStream stream)
        {
            _roleDataModel.Get(role, out var roleData, out _);
            _writeSerializer.Write(roleData, stream);
        }
        
        public void WriteRoleDataDiff(PlayerRoleDescription role, long tickIndex, IBinaryWriteStream stream)
        {
            _roleDataModel.Get(role, out _, out var diffHistoryModel);
            diffHistoryModel.Write(tickIndex, stream);
        }
        
        public bool IsRoleDataChanged(PlayerRoleDescription role, long tickIndex)
        {
            _roleDataModel.TryGet(role, out _, out var diffsHistoryModel);
            return diffsHistoryModel.IsChanged(tickIndex);
        }

        public void WriteShooterWhole(IBinaryWriteStream stream)
        {
            _writeSerializer.Write(_shooterData, stream);
        }

        public void WriteShooterDiff(long tickIndex, IBinaryWriteStream stream)
        {
            _shooterDataDiffsHistoryModel.Write(tickIndex, stream);
        }

        public bool IsShooterChanged(long tickIndex)
        {
            return _shooterDataDiffsHistoryModel.IsChanged(tickIndex);
        }

        public void WriteHangarWhole(IBinaryWriteStream stream)
        {
            _writeSerializer.Write(_hangarData, stream);
        }

        public void WriteHangarDiff(long tickIndex, IBinaryWriteStream stream)
        {
            _hangarDataDiffsHistoryModel.Write(tickIndex, stream);
        }

        public bool IsHangarChanged(long tickIndex)
        {
            return _hangarDataDiffsHistoryModel.IsChanged(tickIndex);
        }
    
        public void WriteClanBattleWhole(IBinaryWriteStream stream)
        {
            _writeSerializer.Write(_clanBattleData, stream);
        }

        public void WriteClanBattleDiff(long tickIndex, IBinaryWriteStream stream)
        {
            _clanBattleDataDiffsHistoryModel.Write(tickIndex, stream);
        }

        public bool IsClanBattleChanged(long tickIndex)
        {
            return _clanBattleDataDiffsHistoryModel.IsChanged(tickIndex);
        }
    
        public void WriteClanBattleAttackTeamPositionsWhole(IBinaryWriteStream stream)
        {
            _writeSerializer.Write(_clanBattleAttackTeamPositionsData, stream);
        }

        public void WriteClanBattleAttackTeamPositionsDiff(long tickIndex, IBinaryWriteStream stream)
        {
            _clanBattleAttackTeamPositionsDiffsHistoryModel.Write(tickIndex, stream);
        }

        public bool IsClanBattleAttackTeamPositionsChanged(long tickIndex)
        {
            return _clanBattleAttackTeamPositionsDiffsHistoryModel.IsChanged(tickIndex);
        }
    
        public void WriteClanBattleDefendTeamPositionsWhole(IBinaryWriteStream stream)
        {
            _writeSerializer.Write(_clanBattleDefendTeamPositionsData, stream);
        }

        public void WriteClanBattleDefendTeamPositionsDiff(long tickIndex, IBinaryWriteStream stream)
        {
            _clanBattleDefendTeamPositionsDiffsHistoryModel.Write(tickIndex, stream);
        }

        public bool IsClanBattleDefendTeamPositionsChanged(long tickIndex)
        {
            return _clanBattleDefendTeamPositionsDiffsHistoryModel.IsChanged(tickIndex);
        }
    }
}