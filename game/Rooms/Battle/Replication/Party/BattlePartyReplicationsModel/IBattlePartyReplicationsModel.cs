using Models.Models.PartyModel;
using Server.Battle.Replication.Party.ReplicationModels;

namespace Server.Battle.Replication.Party.BattlePartyReplicationsModel
{
    public interface IBattlePartyReplicationsModel
    {
        void AddParty(IPartyModel partyModel);
        void RemoveParty(string partyId);
        bool TryGetReplicationModel(string partyId, out BattlePartyReplicationModel model);
    }
}