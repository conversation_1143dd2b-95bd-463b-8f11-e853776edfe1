using Framework.Replication.Data.Dictionary;
using Framework.Replication.PrimitiveSerializer;

namespace Server.Battle.Data
{
    public static class BattleCoinsTransactionsData
    {
        public static IDictionaryData<int, BattleCoinsTransactionData> Build() => new DictionaryData<int, BattleCoinsTransactionData>(int.MaxValue, new IntPrimitiveSerializer(0, int.MaxValue), _ => new BattleCoinsTransactionData());
    }
}