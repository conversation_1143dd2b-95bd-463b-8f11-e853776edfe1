using System;
using System.Numerics;

namespace Server.Battle.Extensions.SimpleDotPhysics.Settings
{
    public readonly struct SimpleDotPhysicsEntitySettings
    {
        public readonly float AirResistance;
        public readonly float Gravity;

        public readonly bool ShouldBreakBreakableColliders;

        public readonly int MaxReflectionsCount;
        public readonly int MaxPostNormalReflectionsCount;
        
        public readonly float VerticalReflectionPercent;
        public readonly float HorizontalReflectionPercent;
        public readonly float DynamicVerticalReflectionVelocity;
        public readonly float DynamicHorizontalReflectionVelocity;
        
        public readonly Quaternion[] DynamicReflectionsMods;
        
        public readonly Func<int, bool> RigidBodyTypeFilter;

        public SimpleDotPhysicsEntitySettings(float airResistance, float gravity,
            bool shouldBreakBreakableColliders,
            int maxReflectionsCount, int maxPostNormalReflectionsCount,
            float verticalReflectionPercent, float horizontalReflectionPercent, float dynamicVerticalReflectionVelocity, float dynamicHorizontalReflectionVelocity,
            Quaternion[] dynamicReflectionsMods,
            Func<int, bool> rigidBodyTypeFilter)
        {
            AirResistance = airResistance;
            Gravity = gravity;

            ShouldBreakBreakableColliders = shouldBreakBreakableColliders;
            
            MaxReflectionsCount = maxReflectionsCount;
            MaxPostNormalReflectionsCount = maxPostNormalReflectionsCount;
            
            VerticalReflectionPercent = verticalReflectionPercent;
            HorizontalReflectionPercent = horizontalReflectionPercent;
            DynamicVerticalReflectionVelocity = dynamicVerticalReflectionVelocity;
            DynamicHorizontalReflectionVelocity = dynamicHorizontalReflectionVelocity;
            
            DynamicReflectionsMods = dynamicReflectionsMods;
            
            RigidBodyTypeFilter = rigidBodyTypeFilter;
        }
    }
}