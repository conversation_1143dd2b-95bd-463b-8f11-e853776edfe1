using System;
using System.Collections.Generic;
using Models.Models.PlotBuildingDefenseBlockPrivateModel;
using Models.Models.PlotCarModel;
using Models.Models.PlotContainerModel;
using Models.Models.PlotModel;
using Models.Models.PlotVehicleModel;
using Models.Models.StorageBoxModel;
using Models.Models.WorldEntitiesModel;
using Models.References;
using Models.References.Builder;
using Models.References.PlotBuildingDefenseBlock;
using Models.Utils.Extensions;
using Server.Common.Models.AnalyticsModel;

namespace Server.Battle.Analytics.Extensions
{
    public static class BreachAnalyticsExtension
    {
        public static void SendDefenseStateOnOwnerEnter(
            int characterId,
            PlotSlotDescription plotSlotDescription,
            IBattleAnalyticsModel analyticsModel, 
            IWorldEntitiesModel worldEntitiesModel)
        {
            if (plotSlotDescription == null ||
                !worldEntitiesModel.SlotPlotModels.TryGetValue(plotSlotDescription, out var plotModel))
                return;
            
            var defenseBlockPrivateModelList = new List<IPlotBuildingDefenseBlockPrivateModel>();
            var plotDescription = plotModel.PlotDescription;
            if (!plotModel.MainBuildingSlot.IsEmpty() && plotModel.MainBuildingSlot.Value.DefenseSettings.CanBeRaided)
            {
                var entityId = plotDescription.MainBuildingEntityIds.DefenseBlockEntityIds.PrivateModelEntityId;
                defenseBlockPrivateModelList.Add(worldEntitiesModel.PlotBuildingDefenseBlockPrivateModels[entityId]);
            }
            if (!plotModel.CompoundWallSlot.IsEmpty() && plotModel.CompoundWallSlot.Value.DefenseSettings.CanBeRaided)
            {
                var entityId = plotDescription.CompoundWallEntityIds.DefenseBlockEntityIds.PrivateModelEntityId;
                defenseBlockPrivateModelList.Add(worldEntitiesModel.PlotBuildingDefenseBlockPrivateModels[entityId]);
            }
            foreach (var (outbuildingSlot, outbuildingDescriptionModel) in plotModel.OutbuildingSlots)
            {
                if (outbuildingDescriptionModel.IsEmpty() || !outbuildingDescriptionModel.Value.DefenseSettings.CanBeRaided) continue;
                var entityId = plotDescription.OutbuildingEntityIds[outbuildingSlot].DefenseBlockEntityIds.PrivateModelEntityId;
                defenseBlockPrivateModelList.Add(worldEntitiesModel.PlotBuildingDefenseBlockPrivateModels[entityId]); 
            }
                
            var (defensePoints, defensePointsTotal, uncommonCount, epicCount, legendCount) = GetDefenseBlockParams(defenseBlockPrivateModelList);
            
            analyticsModel.DefenseStateOnOwnerEnter(characterId, defensePoints, defensePointsTotal, uncommonCount, epicCount, legendCount);
        }
        
        public static void SendUseDecoderAnalytics(
            IBattleAnalyticsModel analyticsModel, 
            IPlotModel plotModel, 
            IPlotBuildingDefenseBlockPrivateModel defenseBlockPrivateModel, 
            string plotOwnerUid, 
            int characterId, 
            PlotBuildingDecoderDescription decoderDescription, 
            long raidLockTime, 
            PlotBuildingRaidStateDescription decodingState)
        {
            var buildingId = plotModel.CompoundWallSlot.Value.Id;
            var plotDescription = plotModel.PlotDescription;
            if (plotDescription.MainBuildingEntities.Contains(defenseBlockPrivateModel.Id))
            {
                buildingId = plotModel.MainBuildingSlot.Value.Id;
            }
            else if (plotDescription.OutbuildingsEntities.TryGetValue(defenseBlockPrivateModel.Id, out var outbuildingSlotDescription))
            {
                var outbuildingSlotModel = plotModel.OutbuildingSlots[outbuildingSlotDescription];
                buildingId = outbuildingSlotModel.Value.Id;
            }
            var (defensePoints, defensePointsTotal, uncommonCount, epicCount, legendCount) = GetDefenseBlockParams(defenseBlockPrivateModel);
            
            analyticsModel.UseDecoder(characterId, plotOwnerUid, buildingId, decoderDescription, defensePoints, defensePointsTotal, uncommonCount, epicCount, legendCount, raidLockTime, decodingState);
        }
        
        public static void SendBreachStorageAnalytics(IBattleAnalyticsModel analyticsModel, IPlotModel plotModel, string plotOwnerUid, int characterId, int storageEntityId, IStorageBoxModel storageBoxModel)
        {
            var storageDescription = storageBoxModel.DescriptionModel.Value.Description;
            
            var plotDescription = plotModel.PlotDescription;
            if (plotDescription.MainBuildingEntities.Contains(storageEntityId))
            {
                var buildingId = plotModel.MainBuildingSlot.Value.Id;
                
                analyticsModel.BreachStorage(characterId, plotOwnerUid, buildingId, storageDescription);
            }
            else if (plotDescription.OutbuildingsEntities.TryGetValue(storageEntityId, out var outbuildingSlotDescription))
            {
                var outbuildingSlotModel = plotModel.OutbuildingSlots[outbuildingSlotDescription];
                var buildingId = outbuildingSlotModel.Value.Id;

                analyticsModel.BreachStorage(characterId, plotOwnerUid, buildingId, storageDescription);
            }
        }
        
        public static void SendBreachPlotContainerAnalytics(IBattleAnalyticsModel analyticsModel, IPlotModel plotModel, string plotOwnerUid, int characterId, int plotContainerEntityId, IPlotContainerModel plotContainerModel)
        {
            var slotDescription = plotContainerModel.SlotDescription;
            
            var plotDescription = plotModel.PlotDescription;
            if (plotDescription.MainBuildingEntities.Contains(plotContainerEntityId))
            {
                var buildingId = plotModel.MainBuildingSlot.Value.Id;
                
                analyticsModel.BreachPlotContainer(characterId, plotOwnerUid, buildingId, slotDescription);
            }
        }

        public static void SendBreachPlotVehicleTrunkAnalytics(IBattleAnalyticsModel analyticsModel, IPlotModel plotModel, string plotOwnerUid, int characterId, IPlotVehicleModel plotVehicleModel)
        {
            var carId = GetCarId(plotVehicleModel);
            
            var plotDescription = plotModel.PlotDescription;
            if (plotDescription.MainBuildingEntities.Contains(plotVehicleModel.Id))
            {
                var buildingId = plotModel.MainBuildingSlot.Value.Id;
                
                analyticsModel.BreachPlotVehicleTrunk(characterId, plotOwnerUid, buildingId, carId);
            }
            else if (plotDescription.OutbuildingsEntities.TryGetValue(plotVehicleModel.Id, out var outbuildingSlotDescription))
            {
                var outbuildingSlotModel = plotModel.OutbuildingSlots[outbuildingSlotDescription];
                var buildingId = outbuildingSlotModel.Value.Id;

                analyticsModel.BreachPlotVehicleTrunk(characterId, plotOwnerUid, buildingId, carId);
            }
        }

        private static string GetCarId(IPlotVehicleModel plotVehicleModel)
        {
            return plotVehicleModel switch
            {
                IPlotCarModel plotCarModel => plotCarModel.VehicleDescription.Id,
                _ => throw new ArgumentOutOfRangeException(nameof(plotVehicleModel))
            };
        }

        private static (int, int, int, int, int) GetDefenseBlockParams(List<IPlotBuildingDefenseBlockPrivateModel> defenseBlockPrivateModels)
        {
            (int, int, int, int, int) sumBlockParams = default;
            foreach (var defensePrivateModel in defenseBlockPrivateModels)
            {
                var blockParams = GetDefenseBlockParams(defensePrivateModel);
                sumBlockParams.Item1 += blockParams.Item1;
                sumBlockParams.Item2 += blockParams.Item2;
                sumBlockParams.Item3 += blockParams.Item3;
                sumBlockParams.Item4 += blockParams.Item4;
                sumBlockParams.Item5 += blockParams.Item5;
            }
            return sumBlockParams;
        }
        
        private static (int, int, int, int, int) GetDefenseBlockParams(IPlotBuildingDefenseBlockPrivateModel defenseBlockPrivateModel)
        {
            var defensePointsTotal = defenseBlockPrivateModel.CalculateDefensePoints();
            var defensePoints = defensePointsTotal - defenseBlockPrivateModel.DecoderAttackPoints.Value;
            var uncommonModules = 0;
            var epicModules = 0;
            var legendModules = 0;
            foreach (var slot in defenseBlockPrivateModel.SlotsModel)
            {
                if (slot.IsEmpty) continue;
                var module = slot.Item.GetItem().PlotBuildingDefenseModuleDescription;
                if (module == PlotBuildingDefenseModuleDescription.Uncommon)
                    uncommonModules++;
                else if (module == PlotBuildingDefenseModuleDescription.Epic)
                    epicModules++;
                else if (module == PlotBuildingDefenseModuleDescription.Legendary)
                    legendModules++;
            }

            return (defensePoints, defensePointsTotal, uncommonModules, epicModules, legendModules);
        }
    }
}