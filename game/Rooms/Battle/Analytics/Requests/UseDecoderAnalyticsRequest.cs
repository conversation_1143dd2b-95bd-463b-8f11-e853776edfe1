using Framework.Analytics.AnalyticsRequest;
using Framework.Utility.Csv;
using Models.References.Builder;
using Models.References.PlotBuildingDefenseBlock;

namespace Server.Battle.Analytics.Requests
{
    public readonly struct UseDecoderAnalyticsRequest : IAnalyticsRequest
    {
        public string Tag => "use_decoder";
        public string Uid { get; init; }
        public string RoomId { get; init; }
        public string SettlementId { get; init; }
        public string SectorId { get; init; }
        public string PlotOwnerUid { get; init; }
        public string BuildingId { get; init; }
        public PlotBuildingDecoderDescription DecoderDescription { get; init; }
        public int DefensePointsLeft { get; init; }
        public int DefensePointsTotal { get; init; }
        public int UncommonModulesCount { get; init; }
        public int EpicModulesCount { get; init; }
        public int LegendModulesCount { get; init; }
        public long RaidLockTimeLeft { get; init; }
        public PlotBuildingRaidStateDescription DecodingState { get; init; }
        public int PartySize { get; init; }
        public string ClanId { get; init; }
        public int RealmId { get; init; }
        public long RealmStartTs { get; init; }
    
        public void Write(ICsv csv)
        {
            csv.Add(Uid);
            csv.Add(RoomId);
            csv.Add(SettlementId);
            csv.Add(SectorId);
            csv.Add(PlotOwnerUid);
            csv.Add(BuildingId);
            csv.Add(DecoderDescription.Id);
            csv.Add(DefensePointsLeft);
            csv.Add(DefensePointsTotal);
            csv.Add(UncommonModulesCount);
            csv.Add(EpicModulesCount);
            csv.Add(LegendModulesCount);
            csv.Add(RaidLockTimeLeft);
            csv.Add(DecodingState.Id);
            csv.Add(PartySize);
            csv.Add(ClanId);
            csv.Add(RealmId);
            csv.Add(RealmStartTs);
        }
    }
}