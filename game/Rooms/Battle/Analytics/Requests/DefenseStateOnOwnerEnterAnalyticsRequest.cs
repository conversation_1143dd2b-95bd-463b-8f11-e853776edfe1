using Framework.Analytics.AnalyticsRequest;
using Framework.Utility.Csv;

namespace Server.Battle.Analytics.Requests
{
    public readonly struct DefenseStateOnOwnerEnterAnalyticsRequest : IAnalyticsRequest
    {
        public string Tag => "defense_state_on_owner_enter";
        public string Uid { get; init; }
        public string RoomId { get; init; }
        public string SettlementId { get; init; }
        public string SectorId { get; init; }
        public int DefensePointsLeft { get; init; }
        public int DefensePointsTotal { get; init; }
        public int UncommonModulesCount { get; init; }
        public int EpicModulesCount { get; init; }
        public int LegendModulesCount { get; init; }
        public int PartySize { get; init; }
        public string ClanId { get; init; }
        public int RealmId { get; init; }
        public long RealmStartTs { get; init; }
    
        public void Write(ICsv csv)
        {
            csv.Add(Uid);
            csv.Add(RoomId);
            csv.Add(SettlementId);
            csv.Add(SectorId);
            csv.Add(DefensePointsLeft);
            csv.Add(DefensePointsTotal);
            csv.Add(UncommonModulesCount);
            csv.Add(EpicModulesCount);
            csv.Add(LegendModulesCount);
            csv.Add(PartySize);
            csv.Add(ClanId);
            csv.Add(RealmId);
            csv.Add(RealmStartTs);
        }
    }
}