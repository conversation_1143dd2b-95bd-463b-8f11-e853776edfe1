using System.Numerics;
using Framework.Analytics.AnalyticsRequest;
using Framework.Utility.Csv;
using Models.References.Cargo;
using Models.References.Location;

namespace Server.Battle.Analytics.Requests
{
    public readonly struct TakeBuildingCargoAnalyticsRequest : IAnalyticsRequest
    {
        public string Tag => "take_building_cargo";
        public string Uid { get; init; }
        public LocationDescription Location { get; init; }
        public string RoomId { get; init; }
        public int EntityId { get; init; }
        public CargoDescription Cargo { get; init; }
        public Vector3 Position { get; init; }
        public int PartySize { get; init; }
        public string ClanId { get; init; }
        public int RealmId { get; init; }
        public long RealmStartTs { get; init; }
    
        public void Write(ICsv csv)
        {
            csv.Add(Uid);
            csv.Add(Location.Id);
            csv.Add(RoomId);
            csv.Add(EntityId);
            csv.Add(Cargo.Id);
            csv.Add(Position.X);
            csv.Add(Position.Y);
            csv.Add(Position.Z);
            csv.Add(PartySize);
            csv.Add(ClanId);
            csv.Add(RealmId);
            csv.Add(RealmStartTs);
        }
    }
}