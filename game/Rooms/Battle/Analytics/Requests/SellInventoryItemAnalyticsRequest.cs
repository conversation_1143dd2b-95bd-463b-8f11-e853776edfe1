using Framework.Analytics.AnalyticsRequest;
using Framework.Utility.Csv;
using Models.References.Inventory;
using Models.References.Location;
using Models.References.Location.LocationTraders;

namespace Server.Battle.Analytics.Requests
{
    public readonly struct SellInventoryItemAnalyticsRequest : IAnalyticsRequest
    {
        public string Tag => "sell_inventory_item";
        public string Uid { get; init; }
        public LocationDescription Location { get; init; }
        public string RoomId { get; init; }
        public LocationNpcDescription Npc { get; init; }
        public InventoryItemDescription Item { get; init; }
        public int Amount { get; init; }
        public int PartySize { get; init; }
        public string ClanId { get; init; }
        public int RealmId { get; init; }
        public long RealmStartTs { get; init; }
    
        public void Write(ICsv csv)
        {
            csv.Add(Uid);
            csv.Add(Location.Id);
            csv.Add(RoomId);
            csv.Add(Npc.TraderInventory.Id);
            csv.Add(Npc.Position.X);
            csv.Add(Npc.Position.Y);
            csv.Add(Npc.Position.Z);
            csv.Add(Item.Id);
            csv.Add(Amount);
            csv.Add(PartySize);
            csv.Add(ClanId);
            csv.Add(RealmId);
            csv.Add(RealmStartTs);
        }
    }
}