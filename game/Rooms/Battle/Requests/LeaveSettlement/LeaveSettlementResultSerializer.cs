using System;
using Framework.Core.BinaryStream;
using Framework.Ecs.Serializer;

namespace Server.Battle.Requests.LeaveSettlement
{
    public class LeaveSettlementResultSerializer : ISerializer<LeaveSettlementResult>
    {
        public void Put(LeaveSettlementResult value, IBinaryWriteStream stream)
        {
            switch (value)
            {
                case LeaveSettlementResult.PlotIsUnderConstruction:
                    stream.WriteByte(0);
                    break;
                case LeaveSettlementResult.WaitingSave:
                    stream.WriteByte(1);
                    break;
                case LeaveSettlementResult.Ok:
                    stream.WriteByte(2);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(value));
            }
        }

        public bool TryGet(IBinaryReadStream stream, out LeaveSettlementResult value)
        {
            if (stream.TryReadByte(out var index))
            {
                switch (index)
                {
                    case 0:
                        value = new LeaveSettlementResult.PlotIsUnderConstruction();
                        return true;
                    case 1:
                        value = new LeaveSettlementResult.WaitingSave();
                        return true;
                    case 2:
                        value = new LeaveSettlementResult.Ok();
                        return true;
                }
            }

            value = default;
            return false;
        }
    }
}