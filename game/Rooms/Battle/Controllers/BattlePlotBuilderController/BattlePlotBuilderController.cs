using System;
using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Core.Identified;
using Framework.Value.Values;
using Framework.Ecs.Tick;
using Framework.Utility.Loggers;
using Models.Inventory;
using Models.Inventory.ItemActionModel;
using Models.Inventory.ItemActionModels;
using Models.Inventory.ItemActionsController;
using Models.Models.BattleCharacterModel;
using Models.Models.BuilderSlotModel;
using Models.Models.HangarModel;
using Models.Models.HangarOwnerModel;
using Models.Models.InitStepsModel;
using Models.Models.OccupiedCharacterSlotsModel;
using Models.Models.PhysicsSceneModel;
using Models.Models.PlotModel;
using Models.Models.SettlementBonusModel;
using Models.Models.WorldEntitiesModel;
using Models.Models.WorldTickData;
using Models.References;
using Models.References.Builder;
using Models.References.Builder.Breach;
using Models.References.CargoWorkbench;
using Models.References.Collection;
using Models.References.CopFurniture;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.Inventory;
using Models.References.MoneyWorkbench;
using Models.References.Realm;
using Models.Utils;
using Models.Utils.InteractRules;
using Server.Battle.Controllers.BattleDespawnGarageVehiclesController;
using Server.Battle.Extensions;
using Server.Battle.Models.BattleChangeCoinsModel;
using Server.Battle.Models.BuilderConstructModel;
using Server.Battle.Models.BuildingLootSpawnModel;
using Server.Battle.Models.FurnitureLootSpawnModel;
using Server.Battle.Models.PlotBuilderSlotAdapter;
using Server.Battle.Models.PlotCollectionsSyncModel;
using Server.Battle.Models.PlotConstructModel;
using Server.Battle.Models.SettlementPlotsServerModel;
using Server.Battle.Models.SettlementTaxModel;
using Server.Battle.Models.States;
using Server.Battle.World;
using Server.Common.Controllers.SaveController;
using Server.Common.Models.AnalyticsModel;
using Server.Common.Utils;

namespace Server.Battle.Controllers.BattlePlotBuilderController
{
    public class BattlePlotBuilderController : IBattlePlotBuilderController
    {
        private readonly IAwaiter<IEither<IValue, IValue>> _validationAwaiter = ServerResponses.Validation.ToAwaiterError();
        private readonly IEither<IValue, IValue> _validationEither = ServerResponses.Validation.ToEitherError();

        private readonly BattleRoomState _state;
        private readonly IWorldEntitiesModel _worldEntitiesModel;
        private readonly ISaveController _saveController;
        private readonly ITick _tick;
        private readonly IHangarOwnerModel _hangarOwnerModel;
        private readonly IItemActionsController _itemActionsController;
        private readonly ItemActionModels _itemActionModels;
        private readonly ISettlementPlotsServerModel _plotsModel;
        private readonly BattleModeDescription _battleMode;
        private readonly IBattleAnalyticsModel _analyticsModel;

        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.MainBuilding, MainBuildingDescription> _mainBuildingSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CompoundWall, CompoundWallDescription> _compoundWallSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.Outbuilding, OutbuildingDescription> _outbuildingSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CopFurniture, CopFurnitureDescription> _copFurnitureSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CollectionTable, CollectionTableDescription> _collectionTableSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CargoWorkbench, CargoWorkbenchDescription> _cargoWorkbenchSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.MainBuildingStorage, StorageDescription> _mainBuildingStorageSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.MainBuildingShelving, ShelvingDescription> _mainBuildingShelvingSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.OutbuildingStorage, StorageDescription> _outbuildingStorageSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.OutbuildingShelving, ShelvingDescription> _outbuildingShelvingSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CargoWorkbenchUpgradeAdditionalSlot, bool> _cargoWorkbenchUpgradeAdditionalSlotAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CargoWorkbenchUpgradeSpeedup, CargoWorkbenchSpeedupUpgradeDescription> _cargoWorkbenchUpgradeSpeedupAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CollectionTableUpgrade, bool> _collectionTableUpgradeAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CopFurnitureUpgradeEquip, CopFurnitureEquipUpgradeDescription> _copFurnitureEquipUpgradeAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CopFurnitureUpgradeWeapon, CopFurnitureWeaponUpgradeDescription> _copFurnitureWeaponUpgradeAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CopFurnitureUpgradeReward, CopFurnitureRewardUpgradeDescription> _copFurnitureRewardUpgradeAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CopFurnitureUpgradeCar, CopFurnitureCarUpgradeDescription> _copFurnitureCarUpgradeAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CopFurnitureUpgradeHelicopter, CopFurnitureHelicopterUpgradeDescription> _copFurnitureHelicopterUpgradeAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.MoneyWorkbenchUpgradeSpeedup, MoneyWorkbenchSpeedupUpgradeDescription> _moneyWorkbenchUpgradeSpeedupAdapter;
        private readonly IPlotBuilderSlotAdapter<PlotBuilderSlotModel.MoneyWorkbenchUpgradeLaunderingRate, MoneyWorkbenchLaunderingRateUpgradeDescription> _moneyWorkbenchUpgradeLaunderingRateAdapter;

        public BattlePlotBuilderController(BattleRoomState state, IWorldEntitiesModel worldEntitiesModel, ISaveController saveController, ITick tick, IHangarOwnerModel hangarOwnerModel, IItemActionsController itemActionsController, ItemActionModels itemActionModels, ISettlementPlotsServerModel plotsModel,
            IPlotConstructModel plotConstructModel, IInitStepsModel plotConstructionStepsModel, IBuilderConstructModel builderConstructModel, IWorld world, IOccupiedCharacterSlotsModel occupiedCharacterSlotsModel, ISettlementBonusModel settlementBonusModel, ILoggers loggers,
            IBattleDespawnGarageVehiclesController despawnGarageVehiclesController, IFurnitureLootSpawnModel furnitureLootSpawnModel, IBuildingLootSpawnModel buildingLootSpawnModel, ISettlementTaxModel settlementTaxModel, BattleModeDescription battleMode,
            IBattleAnalyticsModel analyticsModel, IBattleChangeCoinsModel changeCoinsModel, SectorDescription sectorDescription, IPhysicsSceneModel physicsSceneModel, WorldTickData worldTickData, IPlotCollectionsSyncModel plotCollectionsSyncModel)
        {
            _state = state;
            _worldEntitiesModel = worldEntitiesModel;
            _saveController = saveController;
            _tick = tick;
            _hangarOwnerModel = hangarOwnerModel;
            _itemActionsController = itemActionsController;
            _itemActionModels = itemActionModels;
            _plotsModel = plotsModel;
            _battleMode = battleMode;
            _analyticsModel = analyticsModel;

            _mainBuildingSlotAdapter = new MainBuildingPlotBuilderSlotAdapter(plotConstructModel, plotConstructionStepsModel, buildingLootSpawnModel, settlementBonusModel, world, tick, despawnGarageVehiclesController, worldEntitiesModel, occupiedCharacterSlotsModel, settlementTaxModel, changeCoinsModel, hangarOwnerModel, sectorDescription);
            _compoundWallSlotAdapter = new CompoundWallPlotBuilderSlotAdapter(plotConstructModel, plotConstructionStepsModel, buildingLootSpawnModel, world, tick);
            _outbuildingSlotAdapter = new OutbuildingPlotBuilderSlotAdapter(plotConstructModel, plotConstructionStepsModel, buildingLootSpawnModel, world, worldEntitiesModel, plotsModel, tick, loggers, despawnGarageVehiclesController, physicsSceneModel, worldTickData);
            _copFurnitureSlotAdapter = new CopFurniturePlotBuilderSlotAdapter(builderConstructModel, tick, furnitureLootSpawnModel, world);
            _collectionTableSlotAdapter = new CollectionTablePlotBuilderSlotAdapter(builderConstructModel, plotsModel, tick, furnitureLootSpawnModel, world, plotCollectionsSyncModel);
            _cargoWorkbenchSlotAdapter = new CargoWorkbenchPlotBuilderSlotAdapter(builderConstructModel, world, tick, furnitureLootSpawnModel);
            _mainBuildingStorageSlotAdapter = new MainBuildingStoragePlotBuilderSlotAdapter(builderConstructModel, world, plotsModel, tick, furnitureLootSpawnModel, loggers);
            _mainBuildingShelvingSlotAdapter = new MainBuildingShelvingPlotBuilderSlotAdapter(builderConstructModel, plotsModel, tick, furnitureLootSpawnModel, world, loggers);
            _outbuildingStorageSlotAdapter = new OutbuildingStoragePlotBuilderSlotAdapter(builderConstructModel, world, plotsModel, tick, furnitureLootSpawnModel, loggers);
            _outbuildingShelvingSlotAdapter = new OutbuildingShelvingPloBuilderSlotAdapter(builderConstructModel, plotsModel, tick, furnitureLootSpawnModel, world, loggers);
            _cargoWorkbenchUpgradeAdditionalSlotAdapter = new CargoWorkbenchUpgradeAdditionalSlotPlotBuilderSlotAdapter(tick, worldEntitiesModel, furnitureLootSpawnModel, world);
            _cargoWorkbenchUpgradeSpeedupAdapter = new CargoWorkbenchUpgradeSpeedupPlotBuilderSlotAdapter(tick);
            _collectionTableUpgradeAdapter = new CollectionTableUpgradePlotBuilderSlotAdapter(tick, plotsModel, furnitureLootSpawnModel, world, plotCollectionsSyncModel);
            _copFurnitureEquipUpgradeAdapter = new CopFurnitureUpgradeEquipPlotBuilderSlotAdapter(tick);
            _copFurnitureRewardUpgradeAdapter = new CopFurnitureUpgradeRewardPlotBuilderSlotAdapter(tick);
            _copFurnitureWeaponUpgradeAdapter = new CopFurnitureUpgradeWeaponPlotBuilderSlotAdapter(tick);
            _copFurnitureCarUpgradeAdapter = new CopFurnitureUpgradeCarPlotBuilderSlotAdapter(tick);
            _copFurnitureHelicopterUpgradeAdapter = new CopFurnitureUpgradeHelicopterPlotBuilderSlotAdapter(tick);
            _moneyWorkbenchUpgradeSpeedupAdapter = new MoneyWorkbenchUpgradeSpeedupPlotBuilderSlotAdapter(settlementBonusModel, tick);
            _moneyWorkbenchUpgradeLaunderingRateAdapter = new MoneyWorkbenchUpgradeLaunderingRatePlotBuilderSlotAdapter(settlementBonusModel, tick);
        }
        
        public IAwaiter<IEither<IValue, IValue>> FromInventoryToBuilder(string peer, int builderSlotEntityId, InventorySlotDescription inventorySlot)
        {
            if (!_worldEntitiesModel.PlotBuilderSlots.TryGetModel(builderSlotEntityId, out var plotBuilderSlotModel))
            {
                return _validationAwaiter;
            }

            return plotBuilderSlotModel switch
            {
                PlotBuilderSlotModel.MainBuilding mainBuilding => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, mainBuilding, _mainBuildingSlotAdapter),
                PlotBuilderSlotModel.CompoundWall compoundWall => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, compoundWall, _compoundWallSlotAdapter),
                PlotBuilderSlotModel.Outbuilding outbuilding => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, outbuilding, _outbuildingSlotAdapter),
                PlotBuilderSlotModel.CopFurniture copFurniture => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, copFurniture, _copFurnitureSlotAdapter),
                PlotBuilderSlotModel.CollectionTable collectionTable => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, collectionTable, _collectionTableSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbench cargoWorkbench => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, cargoWorkbench, _cargoWorkbenchSlotAdapter),
                PlotBuilderSlotModel.MainBuildingStorage mainBuildingStorage => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, mainBuildingStorage, _mainBuildingStorageSlotAdapter),
                PlotBuilderSlotModel.MainBuildingShelving mainBuildingShelving => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, mainBuildingShelving, _mainBuildingShelvingSlotAdapter),
                PlotBuilderSlotModel.OutbuildingStorage outbuildingStorage => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, outbuildingStorage, _outbuildingStorageSlotAdapter),
                PlotBuilderSlotModel.OutbuildingShelving outbuildingShelving => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, outbuildingShelving, _outbuildingShelvingSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbenchUpgradeAdditionalSlot cargoWorkbenchUpgradeAdditionalSlot => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, cargoWorkbenchUpgradeAdditionalSlot, _cargoWorkbenchUpgradeAdditionalSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbenchUpgradeSpeedup cargoWorkbenchUpgradeSpeedup => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, cargoWorkbenchUpgradeSpeedup, _cargoWorkbenchUpgradeSpeedupAdapter),
                PlotBuilderSlotModel.CollectionTableUpgrade collectionTableUpgrade => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, collectionTableUpgrade, _collectionTableUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeEquip copFurnitureUpgradeEquip => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, copFurnitureUpgradeEquip, _copFurnitureEquipUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeWeapon copFurnitureUpgradeWeapon => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, copFurnitureUpgradeWeapon, _copFurnitureWeaponUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeReward copFurnitureUpgradeReward => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, copFurnitureUpgradeReward, _copFurnitureRewardUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeCar copFurnitureUpgradeCar => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, copFurnitureUpgradeCar, _copFurnitureCarUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeHelicopter copFurnitureUpgradeHelicopter => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, copFurnitureUpgradeHelicopter, _copFurnitureHelicopterUpgradeAdapter),
                PlotBuilderSlotModel.MoneyWorkbenchUpgradeSpeedup moneyWorkbenchUpgradeSpeedup => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, moneyWorkbenchUpgradeSpeedup, _moneyWorkbenchUpgradeSpeedupAdapter),
                PlotBuilderSlotModel.MoneyWorkbenchUpgradeLaunderingRate moneyWorkbenchUpgradeLaunderingRate => FromInventoryToBuilder(peer, builderSlotEntityId, inventorySlot, moneyWorkbenchUpgradeLaunderingRate, _moneyWorkbenchUpgradeLaunderingRateAdapter),
                _ => throw new ArgumentOutOfRangeException($"{plotBuilderSlotModel}")
            };
        }

        private async IAwaiter<IEither<IValue, IValue>> FromInventoryToBuilder<TPlotBuilderSlotModel, TValue>(string peer, int builderSlotEntityId, InventorySlotDescription inventorySlot, TPlotBuilderSlotModel slotModel, IPlotBuilderSlotAdapter<TPlotBuilderSlotModel, TValue> slotAdapter) where TPlotBuilderSlotModel : IPlotBuilderSlotModel
        {
            if (!_battleMode.CanUseInventory)
            {
                return _validationEither;
            }
            
            if (inventorySlot == null)
            {
                return _validationEither;
            }

            if (!TryGetBattleCharacter(peer, out var battleCharacterModel))
            {
                return _validationEither;
            }

            if (!battleCharacterModel.ValidationModel.CanPerformIdleActions())
            {
                return _validationEither;
            }

            if (!TryGetPlot(builderSlotEntityId, battleCharacterModel.Id, out var plotSlotDescription, out var plotModel))
            {
                return _validationEither;
            }

            if (!ValidatePlotState(plotSlotDescription))
            {
                return _validationEither;
            }
            
            
            if (!slotAdapter.Check(plotModel, slotModel))
            {
                return _validationEither;
            }

            var isSlotEmpty = slotAdapter.IsSlotEmpty(slotModel);
            if (!isSlotEmpty)
            {
                return _validationEither;
            }

            if (!CheckUnsetInventorySlot(battleCharacterModel, inventorySlot, out IItemActionModel itemActionModel, out var item))
            {
                return _validationEither;
            }

            if (!slotAdapter.TryFromItem(item, slotModel, out var newSlotValue))
            {
                return _validationEither;
            }

            if (!InteractRules.CheckDistance(battleCharacterModel.Transform, slotModel.Position, slotModel.InteractSqrDistance))
            {
                return _validationEither;
            }

            _itemActionsController.Unset(itemActionModel);

            slotAdapter.SetSlotValue(slotModel, newSlotValue);
            slotAdapter.ApplySlotEffect(plotModel, slotModel, newSlotValue);

            SendSetSlotAnalytics(battleCharacterModel, plotModel, builderSlotEntityId, slotModel, item);
            
            _saveController.DelayedSave();

            return new Right<IValue, IValue>(new LongValue(_tick.TickIndex));
        }

        public IAwaiter<IEither<IValue, IValue>> FromBuilderToInventory(string peer, int builderSlotEntityId, InventorySlotDescription inventorySlot)
        {
            if (!_worldEntitiesModel.PlotBuilderSlots.TryGetModel(builderSlotEntityId, out var plotBuilderSlotModel))
            {
                return _validationAwaiter;
            }

            return plotBuilderSlotModel switch
            {
                PlotBuilderSlotModel.MainBuilding mainBuilding => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, mainBuilding, _mainBuildingSlotAdapter),
                PlotBuilderSlotModel.CompoundWall compoundWall => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, compoundWall, _compoundWallSlotAdapter),
                PlotBuilderSlotModel.Outbuilding outbuilding => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, outbuilding, _outbuildingSlotAdapter),
                PlotBuilderSlotModel.CopFurniture copFurniture => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, copFurniture, _copFurnitureSlotAdapter),
                PlotBuilderSlotModel.CollectionTable collectionTable => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, collectionTable, _collectionTableSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbench cargoWorkbench => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, cargoWorkbench, _cargoWorkbenchSlotAdapter),
                PlotBuilderSlotModel.MainBuildingStorage mainBuildingStorage => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, mainBuildingStorage, _mainBuildingStorageSlotAdapter),
                PlotBuilderSlotModel.MainBuildingShelving mainBuildingShelving => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, mainBuildingShelving, _mainBuildingShelvingSlotAdapter),
                PlotBuilderSlotModel.OutbuildingStorage outbuildingStorage => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, outbuildingStorage, _outbuildingStorageSlotAdapter),
                PlotBuilderSlotModel.OutbuildingShelving outbuildingShelving => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, outbuildingShelving, _outbuildingShelvingSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbenchUpgradeAdditionalSlot cargoWorkbenchUpgradeAdditionalSlot => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, cargoWorkbenchUpgradeAdditionalSlot, _cargoWorkbenchUpgradeAdditionalSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbenchUpgradeSpeedup cargoWorkbenchUpgradeSpeedup => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, cargoWorkbenchUpgradeSpeedup, _cargoWorkbenchUpgradeSpeedupAdapter),
                PlotBuilderSlotModel.CollectionTableUpgrade collectionTableUpgrade => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, collectionTableUpgrade, _collectionTableUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeEquip copFurnitureUpgradeEquip => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, copFurnitureUpgradeEquip, _copFurnitureEquipUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeWeapon copFurnitureUpgradeWeapon => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, copFurnitureUpgradeWeapon, _copFurnitureWeaponUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeReward copFurnitureUpgradeReward => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, copFurnitureUpgradeReward, _copFurnitureRewardUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeCar copFurnitureUpgradeCar => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, copFurnitureUpgradeCar, _copFurnitureCarUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeHelicopter copFurnitureUpgradeHelicopter => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, copFurnitureUpgradeHelicopter, _copFurnitureHelicopterUpgradeAdapter),
                PlotBuilderSlotModel.MoneyWorkbenchUpgradeSpeedup moneyWorkbenchUpgradeSpeedup => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, moneyWorkbenchUpgradeSpeedup, _moneyWorkbenchUpgradeSpeedupAdapter),
                PlotBuilderSlotModel.MoneyWorkbenchUpgradeLaunderingRate moneyWorkbenchUpgradeLaunderingRate => FromBuilderToInventory(peer, builderSlotEntityId, inventorySlot, moneyWorkbenchUpgradeLaunderingRate, _moneyWorkbenchUpgradeLaunderingRateAdapter),
                _ => throw new ArgumentOutOfRangeException($"{plotBuilderSlotModel}")
            };
        }

        private async IAwaiter<IEither<IValue, IValue>> FromBuilderToInventory<TPlotBuilderSlotModel, TValue>(string peer, int builderSlotEntityId, InventorySlotDescription inventorySlot, TPlotBuilderSlotModel slotModel, IPlotBuilderSlotAdapter<TPlotBuilderSlotModel, TValue> slotAdapter) where TPlotBuilderSlotModel : IPlotBuilderSlotModel
        {
            if (!_battleMode.CanUseInventory)
            {
                return _validationEither;
            }
            
            if (inventorySlot == null)
            {
                return _validationEither;
            }

            if (!TryGetBattleCharacter(peer, out var battleCharacterModel))
            {
                return _validationEither;
            }

            if (!battleCharacterModel.ValidationModel.CanPerformIdleActions())
            {
                return _validationEither;
            }

            if (!TryGetPlot(builderSlotEntityId, battleCharacterModel.Id, out var plotSlotDescription, out var plotModel))
            {
                return _validationEither;
            }

            if (!ValidatePlotState(plotSlotDescription))
            {
                return _validationEither;
            }

            if (!slotAdapter.Check(plotModel, slotModel))
            {
                return _validationEither;
            }

            var isSlotEmpty = slotAdapter.IsSlotEmpty(slotModel);
            if (isSlotEmpty)
            {
                return _validationEither;
            }

            var slotValue = slotAdapter.GetSlotValue(slotModel);
            var item = slotAdapter.ToItem(slotModel, slotValue);

            if (!CheckSetItemInInventorySlot(battleCharacterModel, inventorySlot, item, out IItemActionModel itemActionModel))
            {
                return _validationEither;
            }

            if (!InteractRules.CheckDistance(battleCharacterModel.Transform, slotModel.Position, slotModel.InteractSqrDistance))
            {
                return _validationEither;
            }

            if (!slotAdapter.CheckCancelSlotEffect(plotModel, slotModel))
            {
                return _validationEither;
            }

            slotAdapter.CancelSlotEffect(plotModel, slotModel);
            slotAdapter.SetSlotValue(slotModel, default);
            _itemActionsController.Set(itemActionModel, item, 1, 0, 0, 0, true);

            SendUnsetSlotAnalytics(battleCharacterModel, plotModel, slotModel, item);

            _saveController.DelayedSave();

            return new Right<IValue, IValue>(new LongValue(_tick.TickIndex));
        }

        public IAwaiter<IEither<IValue, IValue>> Unset(string peer, int builderSlotEntityId)
        {
            if (!_worldEntitiesModel.PlotBuilderSlots.TryGetModel(builderSlotEntityId, out var plotBuilderSlotModel))
            {
                return _validationAwaiter;
            }

            return plotBuilderSlotModel switch
            {
                PlotBuilderSlotModel.MainBuilding mainBuilding => Unset(peer, builderSlotEntityId, mainBuilding, _mainBuildingSlotAdapter),
                PlotBuilderSlotModel.CompoundWall compoundWall => Unset(peer, builderSlotEntityId, compoundWall, _compoundWallSlotAdapter),
                PlotBuilderSlotModel.Outbuilding outbuilding => Unset(peer, builderSlotEntityId, outbuilding, _outbuildingSlotAdapter),
                PlotBuilderSlotModel.CopFurniture copFurniture => Unset(peer, builderSlotEntityId, copFurniture, _copFurnitureSlotAdapter),
                PlotBuilderSlotModel.CollectionTable collectionTable => Unset(peer, builderSlotEntityId, collectionTable, _collectionTableSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbench cargoWorkbench => Unset(peer, builderSlotEntityId, cargoWorkbench, _cargoWorkbenchSlotAdapter),
                PlotBuilderSlotModel.MainBuildingStorage mainBuildingStorage => Unset(peer, builderSlotEntityId, mainBuildingStorage, _mainBuildingStorageSlotAdapter),
                PlotBuilderSlotModel.MainBuildingShelving mainBuildingShelving => Unset(peer, builderSlotEntityId, mainBuildingShelving, _mainBuildingShelvingSlotAdapter),
                PlotBuilderSlotModel.OutbuildingStorage outbuildingStorage => Unset(peer, builderSlotEntityId, outbuildingStorage, _outbuildingStorageSlotAdapter),
                PlotBuilderSlotModel.OutbuildingShelving outbuildingShelving => Unset(peer, builderSlotEntityId, outbuildingShelving, _outbuildingShelvingSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbenchUpgradeAdditionalSlot cargoWorkbenchUpgradeAdditionalSlot => Unset(peer, builderSlotEntityId, cargoWorkbenchUpgradeAdditionalSlot, _cargoWorkbenchUpgradeAdditionalSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbenchUpgradeSpeedup cargoWorkbenchUpgradeSpeedup => Unset(peer, builderSlotEntityId, cargoWorkbenchUpgradeSpeedup, _cargoWorkbenchUpgradeSpeedupAdapter),
                PlotBuilderSlotModel.CollectionTableUpgrade collectionTableUpgrade => Unset(peer, builderSlotEntityId, collectionTableUpgrade, _collectionTableUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeEquip copFurnitureUpgradeEquip => Unset(peer, builderSlotEntityId, copFurnitureUpgradeEquip, _copFurnitureEquipUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeWeapon copFurnitureUpgradeWeapon => Unset(peer, builderSlotEntityId, copFurnitureUpgradeWeapon, _copFurnitureWeaponUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeReward copFurnitureUpgradeReward => Unset(peer, builderSlotEntityId, copFurnitureUpgradeReward, _copFurnitureRewardUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeCar copFurnitureUpgradeCar => Unset(peer, builderSlotEntityId, copFurnitureUpgradeCar, _copFurnitureCarUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeHelicopter copFurnitureUpgradeHelicopter => Unset(peer, builderSlotEntityId, copFurnitureUpgradeHelicopter, _copFurnitureHelicopterUpgradeAdapter),
                PlotBuilderSlotModel.MoneyWorkbenchUpgradeSpeedup moneyWorkbenchUpgradeSpeedup => Unset(peer, builderSlotEntityId, moneyWorkbenchUpgradeSpeedup, _moneyWorkbenchUpgradeSpeedupAdapter),
                PlotBuilderSlotModel.MoneyWorkbenchUpgradeLaunderingRate moneyWorkbenchUpgradeLaunderingRate => Unset(peer, builderSlotEntityId, moneyWorkbenchUpgradeLaunderingRate, _moneyWorkbenchUpgradeLaunderingRateAdapter),

                _ => throw new ArgumentOutOfRangeException($"{plotBuilderSlotModel}")
            };
        }

        private async IAwaiter<IEither<IValue, IValue>> Unset<TPlotBuilderSlotModel, TValue>(string peer, int builderSlotEntityId, TPlotBuilderSlotModel slotModel, IPlotBuilderSlotAdapter<TPlotBuilderSlotModel, TValue> slotAdapter) where TPlotBuilderSlotModel : IPlotBuilderSlotModel
        {
            if (!_battleMode.CanUseInventory)
            {
                return _validationEither;
            }
            
            if (!TryGetBattleCharacter(peer, out var battleCharacterModel))
            {
                return _validationEither;
            }

            if (!battleCharacterModel.ValidationModel.CanPerformIdleActions())
            {
                return _validationEither;
            }

            if (!TryGetPlot(builderSlotEntityId, battleCharacterModel.Id, out var plotSlotDescription, out var plotModel))
            {
                return _validationEither;
            }

            if (!ValidatePlotState(plotSlotDescription))
            {
                return _validationEither;
            }

            if (!slotAdapter.Check(plotModel, slotModel))
            {
                return _validationEither;
            }

            var isSlotEmpty = slotAdapter.IsSlotEmpty(slotModel);
            if (isSlotEmpty)
            {
                return _validationEither;
            }

            if (!InteractRules.CheckDistance(battleCharacterModel.Transform, slotModel.Position, slotModel.InteractSqrDistance))
            {
                return _validationEither;
            }

            if (!slotAdapter.CheckCancelSlotEffect(plotModel, slotModel))
            {
                return _validationEither;
            }
            
            var slotValue = slotAdapter.GetSlotValue(slotModel);
            var item = slotAdapter.ToItem(slotModel, slotValue);
            
            slotAdapter.CancelSlotEffect(plotModel, slotModel);
            slotAdapter.SetSlotValue(slotModel, default);

            _saveController.DelayedSave();

            SendUnsetSlotAnalytics(battleCharacterModel, plotModel, slotModel, item);

            return new Right<IValue, IValue>(new LongValue(_tick.TickIndex));
        }
        
        public IAwaiter<IEither<IValue, IValue>> SwapBuilderWithInventory(string peer, int builderSlotEntityId, InventorySlotDescription fromInventorySlot, InventorySlotDescription toInventorySlot)
        {
            if (!_worldEntitiesModel.PlotBuilderSlots.TryGetModel(builderSlotEntityId, out var plotBuilderSlotModel))
            {
                return _validationAwaiter;
            }

            return plotBuilderSlotModel switch
            {
                PlotBuilderSlotModel.MainBuilding mainBuilding => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, mainBuilding, _mainBuildingSlotAdapter),
                PlotBuilderSlotModel.CompoundWall compoundWall => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, compoundWall, _compoundWallSlotAdapter),
                PlotBuilderSlotModel.Outbuilding outbuilding => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, outbuilding, _outbuildingSlotAdapter),
                PlotBuilderSlotModel.CopFurniture copFurniture => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, copFurniture, _copFurnitureSlotAdapter),
                PlotBuilderSlotModel.CollectionTable collectionTable => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, collectionTable, _collectionTableSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbench cargoWorkbench => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, cargoWorkbench, _cargoWorkbenchSlotAdapter),
                PlotBuilderSlotModel.MainBuildingStorage mainBuildingStorage => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, mainBuildingStorage, _mainBuildingStorageSlotAdapter),
                PlotBuilderSlotModel.MainBuildingShelving mainBuildingShelving => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, mainBuildingShelving, _mainBuildingShelvingSlotAdapter),
                PlotBuilderSlotModel.OutbuildingStorage outbuildingStorage => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, outbuildingStorage, _outbuildingStorageSlotAdapter),
                PlotBuilderSlotModel.OutbuildingShelving outbuildingShelving => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, outbuildingShelving, _outbuildingShelvingSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbenchUpgradeAdditionalSlot cargoWorkbenchUpgradeAdditionalSlot => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, cargoWorkbenchUpgradeAdditionalSlot, _cargoWorkbenchUpgradeAdditionalSlotAdapter),
                PlotBuilderSlotModel.CargoWorkbenchUpgradeSpeedup cargoWorkbenchUpgradeSpeedup => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, cargoWorkbenchUpgradeSpeedup, _cargoWorkbenchUpgradeSpeedupAdapter),
                PlotBuilderSlotModel.CollectionTableUpgrade collectionTableUpgrade => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, collectionTableUpgrade, _collectionTableUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeEquip copFurnitureUpgradeEquip => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, copFurnitureUpgradeEquip, _copFurnitureEquipUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeWeapon copFurnitureUpgradeWeapon => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, copFurnitureUpgradeWeapon, _copFurnitureWeaponUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeReward copFurnitureUpgradeReward => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, copFurnitureUpgradeReward, _copFurnitureRewardUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeCar copFurnitureUpgradeCar => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, copFurnitureUpgradeCar, _copFurnitureCarUpgradeAdapter),
                PlotBuilderSlotModel.CopFurnitureUpgradeHelicopter copFurnitureUpgradeHelicopter => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, copFurnitureUpgradeHelicopter, _copFurnitureHelicopterUpgradeAdapter),
                PlotBuilderSlotModel.MoneyWorkbenchUpgradeSpeedup moneyWorkbenchUpgradeSpeedup => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, moneyWorkbenchUpgradeSpeedup, _moneyWorkbenchUpgradeSpeedupAdapter),
                PlotBuilderSlotModel.MoneyWorkbenchUpgradeLaunderingRate moneyWorkbenchUpgradeLaunderingRate => SwapBuilderWithInventory(peer, builderSlotEntityId, fromInventorySlot, toInventorySlot, moneyWorkbenchUpgradeLaunderingRate, _moneyWorkbenchUpgradeLaunderingRateAdapter),

                _ => throw new ArgumentOutOfRangeException($"{plotBuilderSlotModel}")
            };
        }
        
        private async IAwaiter<IEither<IValue, IValue>> SwapBuilderWithInventory<TPlotBuilderSlotModel, TValue>(string peer, int builderSlotEntityId, InventorySlotDescription fromInventorySlot, InventorySlotDescription toInventorySlot, TPlotBuilderSlotModel slotModel, IPlotBuilderSlotAdapter<TPlotBuilderSlotModel, TValue> slotAdapter) where TPlotBuilderSlotModel : IPlotBuilderSlotModel
        {
            if (!_battleMode.CanUseInventory)
            {
                return _validationEither;
            }
            
            if (fromInventorySlot == null || toInventorySlot == null)
            {
                return _validationEither;
            }

            if (!TryGetBattleCharacter(peer, out var battleCharacterModel))
            {
                return _validationEither;
            }

            if (!battleCharacterModel.ValidationModel.CanPerformIdleActions())
            {
                return _validationEither;
            }

            if (!TryGetPlot(builderSlotEntityId, battleCharacterModel.Id, out var plotSlotDescription, out var plotModel))
            {
                return _validationEither;
            }

            if (!ValidatePlotState(plotSlotDescription))
            {
                return _validationEither;
            }
            
            if (!slotAdapter.Check(plotModel, slotModel))
            {
                return _validationEither;
            }

            var isSlotEmpty = slotAdapter.IsSlotEmpty(slotModel);
            if (isSlotEmpty)
            {
                return _validationEither;
            }
            
            var slotValue = slotAdapter.GetSlotValue(slotModel);
            var builderSlotItem = slotAdapter.ToItem(slotModel, slotValue);

            if (!CheckUnsetInventorySlot(battleCharacterModel, fromInventorySlot, out IItemActionModel fromItemActionModel, out var inventorySlotItem) ||
                !CheckSwapItemInInventorySlot(battleCharacterModel, toInventorySlot, fromInventorySlot, builderSlotItem, out IItemActionModel toItemActionModel))
            {
                return _validationEither;
            }

            if (!slotAdapter.TryFromItem(inventorySlotItem, slotModel, out var newSlotValue))
            {
                return _validationEither;
            }

            if (!InteractRules.CheckDistance(battleCharacterModel.Transform, slotModel.Position, slotModel.InteractSqrDistance))
            {
                return _validationEither;
            }
            
            if (!slotAdapter.CheckCancelSlotEffect(plotModel, slotModel))
            {
                return _validationEither;
            }

            slotAdapter.SetSlotValueAndChangeEffect(plotModel, slotModel, newSlotValue);
            
            SendUnsetSlotAnalytics(battleCharacterModel, plotModel, slotModel, builderSlotItem);
            SendSetSlotAnalytics(battleCharacterModel, plotModel, builderSlotEntityId, slotModel, inventorySlotItem);
            
            _itemActionsController.Unset(fromItemActionModel);
            _itemActionsController.Set(toItemActionModel, builderSlotItem, 1, 0, 0, 0, true);

            _saveController.DelayedSave();

            return new Right<IValue, IValue>(new LongValue(_tick.TickIndex));
        }
        
        public IAwaiter<IEither<IValue, IValue>> SwapBuilder(string peer, int firstSlotEntityId, int secondSlotEntityId)
        {
            if (firstSlotEntityId == secondSlotEntityId)
            {
                return _validationAwaiter;
            }
            
            if (!_worldEntitiesModel.PlotBuilderSlots.TryGetModel(firstSlotEntityId, out var firstSlotModel) ||
                !_worldEntitiesModel.PlotBuilderSlots.TryGetModel(secondSlotEntityId, out var secondSlotModel))
            {
                return _validationAwaiter;
            }

            return (firstSlotModel, secondSlotModel) switch
            {
                (PlotBuilderSlotModel.Outbuilding firstOutbuilding, PlotBuilderSlotModel.Outbuilding secondOutbuilding) => SwapBuilder(peer, firstSlotEntityId, secondSlotEntityId, firstOutbuilding, secondOutbuilding, _outbuildingSlotAdapter),
                (PlotBuilderSlotModel.MainBuildingStorage firstMainBuildingStorage, PlotBuilderSlotModel.MainBuildingStorage secondMainBuildingStorage) => SwapBuilder(peer, firstSlotEntityId, secondSlotEntityId, firstMainBuildingStorage, secondMainBuildingStorage, _mainBuildingStorageSlotAdapter),
                (PlotBuilderSlotModel.MainBuildingShelving firstMainBuildingShelving, PlotBuilderSlotModel.MainBuildingShelving secondMainBuildingShelving) => SwapBuilder(peer, firstSlotEntityId, secondSlotEntityId, firstMainBuildingShelving, secondMainBuildingShelving, _mainBuildingShelvingSlotAdapter),
                (PlotBuilderSlotModel.OutbuildingStorage firstOutbuildingStorage, PlotBuilderSlotModel.OutbuildingStorage secondOutbuildingStorage) => SwapBuilder(peer, firstSlotEntityId, secondSlotEntityId, firstOutbuildingStorage, secondOutbuildingStorage, _outbuildingStorageSlotAdapter),
                (PlotBuilderSlotModel.OutbuildingShelving firstOutbuildingShelving, PlotBuilderSlotModel.OutbuildingShelving secondOutbuildingShelving) => SwapBuilder(peer, firstSlotEntityId, secondSlotEntityId, firstOutbuildingShelving, secondOutbuildingShelving, _outbuildingShelvingSlotAdapter),
                (PlotBuilderSlotModel.CargoWorkbenchUpgradeAdditionalSlot firstCargoWorkbenchUpgradeAdditionalSlot, PlotBuilderSlotModel.CargoWorkbenchUpgradeAdditionalSlot secondCargoWorkbenchUpgradeAdditionalSlot) => SwapBuilder(peer, firstSlotEntityId, secondSlotEntityId, firstCargoWorkbenchUpgradeAdditionalSlot, secondCargoWorkbenchUpgradeAdditionalSlot, _cargoWorkbenchUpgradeAdditionalSlotAdapter),
                (PlotBuilderSlotModel.MoneyWorkbenchUpgradeSpeedup firstMoneyWorkbenchUpgradeSpeedup, PlotBuilderSlotModel.MoneyWorkbenchUpgradeSpeedup secondMoneyWorkbenchUpgradeSpeedup) => SwapBuilder(peer, firstSlotEntityId, secondSlotEntityId, firstMoneyWorkbenchUpgradeSpeedup, secondMoneyWorkbenchUpgradeSpeedup, _moneyWorkbenchUpgradeSpeedupAdapter),
                (PlotBuilderSlotModel.MainBuilding, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CompoundWall, _) => _validationAwaiter,
                (PlotBuilderSlotModel.Outbuilding, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CopFurniture, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CollectionTable, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CargoWorkbench, _) => _validationAwaiter,
                (PlotBuilderSlotModel.MainBuildingStorage, _) => _validationAwaiter,
                (PlotBuilderSlotModel.MainBuildingShelving, _) => _validationAwaiter,
                (PlotBuilderSlotModel.OutbuildingStorage, _) => _validationAwaiter,
                (PlotBuilderSlotModel.OutbuildingShelving, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CargoWorkbenchUpgradeAdditionalSlot, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CargoWorkbenchUpgradeSpeedup, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CollectionTableUpgrade, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CopFurnitureUpgradeEquip, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CopFurnitureUpgradeWeapon, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CopFurnitureUpgradeReward, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CopFurnitureUpgradeCar, _) => _validationAwaiter,
                (PlotBuilderSlotModel.CopFurnitureUpgradeHelicopter, _) => _validationAwaiter,
                (PlotBuilderSlotModel.MoneyWorkbenchUpgradeSpeedup, _) => _validationAwaiter,
                (PlotBuilderSlotModel.MoneyWorkbenchUpgradeLaunderingRate, _) => _validationAwaiter,
                
                _ => throw new ArgumentOutOfRangeException($"({firstSlotModel}, {secondSlotModel})")
            };
        }

        private async IAwaiter<IEither<IValue, IValue>> SwapBuilder<TPlotBuilderSlotModel, TValue>(string peer, int firstSlotEntityId, int secondSlotEntityId, TPlotBuilderSlotModel firstSlotModel, TPlotBuilderSlotModel secondSlotModel, IPlotBuilderSlotAdapter<TPlotBuilderSlotModel, TValue> slotAdapter) where TPlotBuilderSlotModel : IPlotBuilderSlotModel
        {
            if (!_battleMode.CanUseInventory)
            {
                return _validationEither;
            }
            
            if (!TryGetBattleCharacter(peer, out var battleCharacterModel))
            {
                return _validationEither;
            }

            if (!battleCharacterModel.ValidationModel.CanPerformIdleActions())
            {
                return _validationEither;
            }

            if (!_worldEntitiesModel.PlotSlotByEntity.TryGetValue(firstSlotEntityId, out var firstPlotSlotDescription) ||
                !_worldEntitiesModel.PlotSlotByEntity.TryGetValue(secondSlotEntityId, out var secondPlotSlotDescription) ||
                firstPlotSlotDescription != secondPlotSlotDescription ||
                !_hangarOwnerModel.IsOwnerByEntityId(firstPlotSlotDescription, battleCharacterModel.Id) ||
                !_worldEntitiesModel.SlotPlotModels.TryGetValue(firstPlotSlotDescription, out var plotModel))
            {
                return _validationEither;
            }

            if (!ValidatePlotState(firstPlotSlotDescription))
            {
                return _validationEither;
            }
            
            if (!slotAdapter.Check(plotModel, firstSlotModel)) 
            {
                return _validationEither;
            }

            if (slotAdapter.IsSlotEmpty(firstSlotModel) && slotAdapter.IsSlotEmpty(secondSlotModel))
            {
                return _validationEither;
            }

            if (!slotAdapter.CheckSwap(plotModel, firstSlotModel, secondSlotModel))
            {
                return _validationEither;
            }
            
            if (!InteractRules.CheckDistance(battleCharacterModel.Transform, firstSlotModel.Position, firstSlotModel.InteractSqrDistance))
            {
                return _validationEither;
            }
            
            slotAdapter.Swap(plotModel, firstSlotModel, secondSlotModel);

            _saveController.DelayedSave();

            return new Right<IValue, IValue>(new LongValue(_tick.TickIndex));
        }
        
        private void SendSetSlotAnalytics<TPlotBuilderSlotModel>(IBattleCharacterModel battleCharacter, IPlotModel plotModel, int builderSlotEntityId, TPlotBuilderSlotModel slotModel, InventoryItemDescription item) where TPlotBuilderSlotModel : IPlotBuilderSlotModel
        {
            if (slotModel.SlotType.IsBuilding)
            {
                _analyticsModel.SetBuilding(battleCharacter.Id, plotModel.PlotDescription, slotModel.SlotType, slotModel.SlotId, item);
            }
            else if (slotModel.SlotType.IsFurniture)
            {
                BattlePlotBuilderExtensions.TryGetParentSlotData(_worldEntitiesModel, builderSlotEntityId, out PlotBuilderSlotTypeDescription parentSlotType, out IIdentified parentSlotId, out IIdentified parentId);
                _analyticsModel.SetFurniture(battleCharacter.Id, parentSlotType, parentSlotId, parentId, slotModel.SlotType, slotModel.SlotId, item);
            }
            else if (slotModel.SlotType.IsUpgrade)
            {
                BattlePlotBuilderExtensions.TryGetParentSlotData(_worldEntitiesModel, builderSlotEntityId, out PlotBuilderSlotTypeDescription parentSlotType, out IIdentified parentSlotId, out IIdentified parentId);
                _analyticsModel.SetFurnitureUpgrade(battleCharacter.Id, parentSlotType, parentSlotId, parentId, slotModel.SlotType, slotModel.SlotId, item);
            }
        }
        
        private void SendUnsetSlotAnalytics<TPlotBuilderSlotModel>(IBattleCharacterModel battleCharacter, IPlotModel plotModel, TPlotBuilderSlotModel slotModel, InventoryItemDescription item) where TPlotBuilderSlotModel : IPlotBuilderSlotModel
        {
            if (slotModel.SlotType.IsBuilding)
            {
                _analyticsModel.UnsetBuilding(battleCharacter.Id, plotModel.PlotDescription, slotModel.SlotType, slotModel.SlotId, item);
            }
        }
        
        private bool CheckUnsetInventorySlot(IBattleCharacterModel battleCharacterModel, InventorySlotDescription inventorySlot, out IItemActionModel itemActionModel, out InventoryItemDescription item)
        {
            item = null;
            return _itemActionModels.TryBuildModel(battleCharacterModel, inventorySlot, out itemActionModel) &&
                   itemActionModel.TryGetContent(out item, out int count, out _, out _, out _) &&
                   count == 1 &&
                   _itemActionsController.CheckUnset(itemActionModel);
        }

        private bool CheckSetItemInInventorySlot(IBattleCharacterModel battleCharacterModel, InventorySlotDescription inventorySlot, InventoryItemDescription itemDescription, out IItemActionModel itemActionModel)
        {
            return _itemActionModels.TryBuildModel(battleCharacterModel, inventorySlot, out itemActionModel) &&
                   _itemActionsController.CheckSet(itemActionModel, itemDescription, 1, 0, 0, 0, out _);
        }

        private bool CheckSwapItemInInventorySlot(IBattleCharacterModel battleCharacterModel, InventorySlotDescription inventorySlot, InventorySlotDescription ignoredInventorySlot, InventoryItemDescription itemDescription, out IItemActionModel itemActionModel)
        {
            var inventory = battleCharacterModel.PrivateModel.Inventory;
            var slotModel = inventory.Slots[inventorySlot];
            var ignoredSlotModel = inventory.Slots[ignoredInventorySlot];
            
            return _itemActionModels.TryBuildModel(battleCharacterModel, inventorySlot, out itemActionModel) &&
                   InventoryRules.CanSwapInventorySlot(inventory, slotModel, ignoredSlotModel, itemDescription, 1, 0, 0, 0, 0);
        }

        private bool TryGetPlot(int entityId, int characterId, out PlotSlotDescription plotSlotDescription, out IPlotModel plotModel)
        {
            plotModel = default;
            return _worldEntitiesModel.PlotSlotByEntity.TryGetValue(entityId, out plotSlotDescription) &&
                   _hangarOwnerModel.IsOwnerByEntityId(plotSlotDescription, characterId) &&
                   _worldEntitiesModel.SlotPlotModels.TryGetValue(plotSlotDescription, out plotModel);
        }

        private bool ValidatePlotState(PlotSlotDescription plotSlotDescription)
        {
            return _plotsModel.Slots.TryGetValue(plotSlotDescription, out var settlementPlotServerModel) &&
                   !settlementPlotServerModel.IsPlotUnderConstruction &&
                   !settlementPlotServerModel.IsLocked &&
                   !settlementPlotServerModel.IsReservedForMove &&
                   settlementPlotServerModel.IsBuilderUnlocked;
        }

        private bool TryGetBattleCharacter(string privateId, out IBattleCharacterModel model)
        {
            model = null;
            return _state.PlayerStates.TryGetValue(privateId, out var state) &&
                   _worldEntitiesModel.BattleCharacters.TryGetModel(state.EntityId, out model);
        }
    }
}