using System.Numerics;
using Framework.Ecs.Tick;
using Framework.Utility.Logger;
using Framework.Utility.Loggers;
using Models.Data;
using Models.Data.Party;
using Models.Models.BattleCharacterModel;
using Models.Models.ClanBattleSpawnPointsModel;
using Models.Models.HangarModel;
using Models.Models.Parties;
using Models.Models.PlotModel;
using Models.Models.VehicleModel;
using Models.Models.WorldModel;
using Models.Models.WorldModel.VehiclesWorldModel;
using Models.References;
using Models.References.Car;
using Models.References.ClanBattle;
using Models.References.Helicopter;
using Models.References.Location;
using Server.Battle.Analytics.Extensions;
using Server.Battle.Controllers.BattleSendUserEntitySpawnedController;
using Server.Battle.Models.BattleEnterPositionModel;
using Server.Battle.Models.CarUpgradesEmptyFlagModel;
using Server.Battle.Models.CharacterGlobalMapVehicleModel;
using Server.Battle.Models.GlobalMapVehicleModel;
using Server.Battle.Models.SettlementPlotsServerModel;
using Server.Battle.Models.SettlementSlotsModel;
using Server.Battle.Models.States;
using Server.Battle.Models.VehicleForSpawnModel;
using Server.Battle.Models.VehicleLootSpawnModel;
using Server.Battle.Models.VehicleSpawnModel;
using Server.Battle.World;
using Server.Common.Models.AnalyticsModel;

namespace Server.Battle.Controllers.BattleSpawnCharacterController
{
    public class BattleSpawnCharacterController : IBattleSpawnCharacterController
    {
        private readonly BattleModeDescription _battleMode;
        private readonly IWorld _world;
        private readonly IWorldModel _worldModel;
        private readonly IParties _parties;
        private readonly ITick _tick;
        private readonly IVehiclesWorldModel _vehiclesWorldModel;
        private readonly IGlobalMapVehicleModel _globalMapVehicleModel;
        private readonly IVehicleSpawnModel _vehicleSpawnModel;
        private readonly IBattleEnterPositionModel _enterPositionModel;
        private readonly IClanBattleSpawnPointsModel _clanBattleSpawnPointsModel;
        private readonly IBattleSendUserEntitySpawnedController _sendUserEntitySpawnedController;
        private readonly IVehicleLootSpawnModel _vehicleLootSpawnModel;
        private readonly ISettlementSlotsModel _settlementSlotsModel;
        private readonly ICarUpgradesEmptyFlagModel _carUpgradesEmptyFlagModel;
        private readonly IBattleAnalyticsModel _analyticsModel;
        private readonly ISettlementPlotsServerModel _plotsModel;
        private readonly ILogger _logger;

        public BattleSpawnCharacterController(BattleModeDescription battleMode, IWorld world, IWorldModel worldModel, IParties parties, ITick tick, IVehiclesWorldModel vehiclesWorldModel,
            IGlobalMapVehicleModel globalMapVehicleModel, IVehicleSpawnModel vehicleSpawnModel, IBattleEnterPositionModel enterPositionModel,
            IClanBattleSpawnPointsModel clanBattleSpawnPointsModel, IBattleSendUserEntitySpawnedController sendUserEntitySpawnedController, IVehicleLootSpawnModel vehicleLootSpawnModel,
            ISettlementSlotsModel settlementSlotsModel, ILoggers loggers, ICarUpgradesEmptyFlagModel carUpgradesEmptyFlagModel, IBattleAnalyticsModel analyticsModel, ISettlementPlotsServerModel plotsModel)
        {
            _battleMode = battleMode;
            _world = world;
            _worldModel = worldModel;
            _parties = parties;
            _tick = tick;
            _vehiclesWorldModel = vehiclesWorldModel;
            _globalMapVehicleModel = globalMapVehicleModel;
            _vehicleSpawnModel = vehicleSpawnModel;
            _enterPositionModel = enterPositionModel;
            _clanBattleSpawnPointsModel = clanBattleSpawnPointsModel;
            _sendUserEntitySpawnedController = sendUserEntitySpawnedController;
            _vehicleLootSpawnModel = vehicleLootSpawnModel;
            _settlementSlotsModel = settlementSlotsModel;
            _carUpgradesEmptyFlagModel = carUpgradesEmptyFlagModel;
            _analyticsModel = analyticsModel;
            _plotsModel = plotsModel;

            _logger = loggers.Build(nameof(BattleSpawnCharacterController));
        }

        public void SpawnCharacter(BattlePlayerState battlePlayerState, IBattleCharacterModel battleCharacterModel, bool useHangarEnterPoint, ClanBattleTeamDescription team, IVehicleModel partyVehicle, bool isPartyEnter)
        {
            string partyId = null;
            if (_parties.TryGetPartyByEntity(battleCharacterModel.Id, out var partyModel))
            {
                partyId = partyModel.Id;
            }

            _plotsModel.TryGetSlotByPrivateId(battlePlayerState.Uid, out var plotSlot);

            (Vector3 position, Vector3 lookDirection) = GetPlayerPositionAndLookDirection(partyId, useHangarEnterPoint, plotSlot, team, out var borderEnterPoint);
            _world.AddPlayerBattleCharacter(battleCharacterModel, position, lookDirection);

            var characterGlobalMapVehicleModel = battlePlayerState.GlobalMapVehicleModel;
            var isEnteredWithVehicle= characterGlobalMapVehicleModel.Car != null || characterGlobalMapVehicleModel.Helicopter != null;

            SpawnCharacterVehicle(battleCharacterModel, characterGlobalMapVehicleModel, borderEnterPoint);

            battlePlayerState.IsCharacterSpawned = true;

            if (_worldModel.Vehicles.TryGetCharacterVehicle(battleCharacterModel.Id, out var vehicleModel))
            {
                battleCharacterModel.PlayerBattleModel.LocationVehicleModel.Set(vehicleModel.VehicleType);
            }

            _carUpgradesEmptyFlagModel.UpdateCharacterCarUpgradesEmptyFlag(battleCharacterModel);

            _sendUserEntitySpawnedController.SendEntitySpawned(battleCharacterModel.Id);
            
            GetCharacterVehicle(battleCharacterModel, partyVehicle, out var car, out var helicopter, out var ownerId);
            var isOwner = ownerId == battlePlayerState.PublicId;
            _analyticsModel.EnterBattle(battleCharacterModel.Id, car, helicopter, isOwner, isEnteredWithVehicle, isPartyEnter, useHangarEnterPoint);
            BreachAnalyticsExtension.SendDefenseStateOnOwnerEnter(battleCharacterModel.Id, plotSlot, _analyticsModel, _worldModel.WorldEntitiesModel);
        }

        private (Vector3, Vector3) GetPlayerPositionAndLookDirection(string partyId, bool usePlotSpawnPoint, PlotSlotDescription plotSlotDescription, ClanBattleTeamDescription team, out LocationBorderEnterPoint borderEnterPoint)
        {
            OrientedPoint enter;
            if (_battleMode.IsClanBattle)
            {
                borderEnterPoint = team == ClanBattleTeamDescription.Attacker ? _clanBattleSpawnPointsModel.GetAttackerPoint() : _clanBattleSpawnPointsModel.GetDefenderPoint();
                enter = borderEnterPoint.Ground;
            }
            else if (usePlotSpawnPoint && plotSlotDescription != null)
            {
                IPlotModel plotModel = _worldModel.WorldEntitiesModel.SlotPlotModels[plotSlotDescription];
                enter = plotModel.MainBuildingSlot.Value != null ? plotModel.MainBuilding.DescriptionModel.Value.SpawnPoint : plotModel.PlotDescription.Enter;

                borderEnterPoint = null;
            }
            else
            {
                if (partyId != PartyIdData.EmptyValue && _parties.TryGetParty(partyId, out var partyModel))
                {
                    borderEnterPoint = _enterPositionModel.GetEnter(partyModel.EnterGroup);
                    enter = borderEnterPoint.Ground;
                }
                else
                {
                    var enterGroup = _enterPositionModel.GetEnterGroup();
                    borderEnterPoint = _enterPositionModel.GetEnter(enterGroup);
                    enter = borderEnterPoint.Ground;
                }
            }

            var lookDirection = Vector3.Transform(Vector3.UnitZ, enter.Orientation);
            return (enter.Position, lookDirection);
        }

        private void SpawnCharacterVehicle(IBattleCharacterModel battleCharacterModel, ICharacterGlobalMapVehicleModel characterGlobalMapVehicleModel, LocationBorderEnterPoint borderEnterPoint)
        {
            if (!_globalMapVehicleModel.TryGetCharacterGlobalMapVehicle(characterGlobalMapVehicleModel, out var car, out var helicopter)) return;

            OrientedPoint enterPoint;
            IVehicleForSpawnModel vehicleForSpawn;
            if (car != null)
            {
                vehicleForSpawn = new CarForSpawnModel(car);
                enterPoint = borderEnterPoint.Ground;
            }
            else
            {
                vehicleForSpawn = new HelicopterForSpawnModel(helicopter);
                enterPoint = borderEnterPoint.Helicopter;
            }

            var vehicleOwnerPublicId = characterGlobalMapVehicleModel.OwnerPublicId;
            if (!_globalMapVehicleModel.CanSpawnGlobalMapVehicle(vehicleOwnerPublicId, vehicleForSpawn))
            {
                _settlementSlotsModel.ReleaseSlotByVehicle(vehicleOwnerPublicId);
                
                _vehicleLootSpawnModel.SpawnGlobalVehicleLoot(_world, characterGlobalMapVehicleModel, enterPoint.Position);
                _globalMapVehicleModel.UnsetCharacterGlobalMapVehicle(characterGlobalMapVehicleModel);
                
                _logger.Warning("Can't spawn global map vehicle: duplicate or plot doesn't have such spawned vehicle.");
                
                return;
            }

            var vehicleOwnerPrivateId = characterGlobalMapVehicleModel.OwnerPrivateId;
            var vehicleOwnerNickname = characterGlobalMapVehicleModel.OwnerNickname;
            var vehicleOwnerClanTag = characterGlobalMapVehicleModel.OwnerClanTag;

            IVehicleModel vehicle = _vehicleSpawnModel.CreateCharacterVehicle(_world, battleCharacterModel.Id, vehicleOwnerPublicId, vehicleOwnerPrivateId, vehicleOwnerNickname, vehicleOwnerClanTag, vehicleForSpawn, enterPoint.Position, enterPoint.Orientation);

            _globalMapVehicleModel.SetVehicleFromCharacterGlobalMapVehicleModel(vehicle, characterGlobalMapVehicleModel);
            _globalMapVehicleModel.UnsetCharacterGlobalMapVehicle(characterGlobalMapVehicleModel);

            _carUpgradesEmptyFlagModel.UpdateCharacterCarUpgradesEmptyFlagByVehicle(vehicle);

            vehicle.CriminalStateModel.IsRobberOwner = battleCharacterModel.Role.Value.IsRobber;
            _vehiclesWorldModel.SetVehicleEngineEnabled(vehicle.Id, _tick.Ts);

            _vehiclesWorldModel.AddCharacterToVehicle(battleCharacterModel, vehicle.Id, vehicle.Seats, vehicle.Seats.GetDriverSeatSlot(), vehicle.VehicleType);
        }
        
        private void GetCharacterVehicle(IBattleCharacterModel battleCharacter, IVehicleModel partyVehicle, out CarDescription car, out HelicopterDescription helicopter, out long ownerPublicId)
        {
            car = null;
            helicopter = null;
            ownerPublicId = 0;

            var vehicleEntityId = BattleEntityIdData.Empty;

            var inVehicleModel = battleCharacter.InVehicleModel;
            if (inVehicleModel.IsInsideVehicle)
            {
                vehicleEntityId = inVehicleModel.EntityId;
            }
            else if (partyVehicle != null)
            {
                vehicleEntityId = partyVehicle.Id;
            }
            
            if (vehicleEntityId == BattleEntityIdData.Empty) return;
            
            if (_worldModel.WorldEntitiesModel.Cars.TryGetModel(vehicleEntityId, out var carModel))
            {
                ownerPublicId = carModel.CharacterOwner.OwnerPublicId;
                car = carModel.Description;
            }
            else if (_worldModel.WorldEntitiesModel.Helicopters.TryGetModel(vehicleEntityId, out var helicopterModel))
            {
                ownerPublicId = helicopterModel.CharacterOwner.OwnerPublicId;
                helicopter = helicopterModel.Description;
            }
        }
    }
}