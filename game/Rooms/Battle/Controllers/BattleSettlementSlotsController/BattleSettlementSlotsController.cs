using System.Collections.Generic;
using Framework.Core.Timer;
using Framework.Core.Timers;
using Framework.Utility.Logger;
using Framework.Utility.Loggers;
using Models.References;
using Server.Battle.Models.SettlementSlotsModel;
using Server.References;

namespace Server.Battle.Controllers.BattleSettlementSlotsController
{
    public class BattleSettlementSlotsController : IBattleSettlementSlotsController
    {
        private readonly ISettlementSlotsModel _settlementSlotsModel;
        private readonly ITimers _timers;
        private readonly ILogger _logger;

        private readonly Dictionary<BattleCharacterSlotDescription, ITimer> _characterTimers = new();
        private readonly Dictionary<BattleCharacterSlotDescription, ITimer> _vehicleTimers = new();

        public BattleSettlementSlotsController(ISettlementSlotsModel settlementSlotsModel, ITimers timers, ILoggers loggers)
        {
            _settlementSlotsModel = settlementSlotsModel;
            _timers = timers;
            
            _logger = loggers.Build(nameof(BattleSettlementSlotsController));
        }
        
        public async void ReserveSlotForCharacter(BattleCharacterSlotDescription slot)
        {
            var reserveEndTs = _timers.Now + BattleDescription.SlotReserveTime;
            var timer = _timers.StartTimer(reserveEndTs);
            
            _characterTimers.Add(slot, timer);

            await timer;

            _characterTimers.Remove(slot);

            _logger.Info($"slot: {slot.Id} remove reservation for character by timer");

            _settlementSlotsModel.ReleaseReservedForCharacterSlot(slot);
        }

        public void UseReservedForCharacterSlot(BattleCharacterSlotDescription slot)
        {
            if (_characterTimers.TryGetValue(slot, out var timer))
            {
                timer.Stop();
                _characterTimers.Remove(slot);
            }
        }
        
        public async void ReserveSlotForVehicle(BattleCharacterSlotDescription slot)
        {
            var reserveEndTs = _timers.Now + BattleDescription.SlotReserveTime;
            var timer = _timers.StartTimer(reserveEndTs);
            
            _vehicleTimers.Add(slot, timer);

            await timer;
            
            _vehicleTimers.Remove(slot);

            _logger.Info($"slot: {slot.Id} remove reservation for vehicle by timer");

            _settlementSlotsModel.ReleaseReservedForVehicleSlot(slot);
        }

        public void UseReservedForVehicleSlot(BattleCharacterSlotDescription slot)
        {
            if (_vehicleTimers.TryGetValue(slot, out var timer))
            {
                timer.Stop();
                _vehicleTimers.Remove(slot);
            }
        }
    }
}