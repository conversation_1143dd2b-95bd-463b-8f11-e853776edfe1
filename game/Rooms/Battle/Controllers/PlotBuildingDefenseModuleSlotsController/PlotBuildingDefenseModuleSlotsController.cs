using Framework.Async.Awaiter;
using Framework.Async.Extension;
using Framework.Core.Either;
using Framework.Ecs.Tick;
using Framework.Value.Values;
using Models.Inventory.ItemActionModels;
using Models.Models.BattleCharacterModel;
using Models.Models.PlotBuildingDefenseBlockPrivateModel;
using Models.Models.PlotBuildingDefenseModuleSlotModel;
using Models.Models.PlotBuildingDefenseModuleSlotsModel;
using Models.Models.WorldEntitiesModel;
using Models.References;
using Models.References.Inventory;
using Models.References.PlotBuildingDefenseBlock;
using Models.Utils;
using Models.Utils.Extensions;
using Server.Battle.Models.States;
using Server.Common.Controllers.SaveController;
using Server.Common.Utils;

namespace Server.Battle.Controllers.PlotBuildingDefenseModuleSlotsController
{
    public class PlotBuildingDefenseModuleSlotsController : IPlotBuildingDefenseModuleSlotsController
    {
        private readonly IAwaiter<IEither<IValue, IValue>> _validationAwaiter = ServerResponses.Validation.ToAwaiterError();
        
        private readonly BattleRoomState _state;
        private readonly IWorldEntitiesModel _worldEntitiesModel;
        private readonly IItemActionModels _itemActionModels;
        private readonly BattleModeDescription _battleMode;
        private readonly ISaveController _saveController;
        private readonly ITick _tick;

        public PlotBuildingDefenseModuleSlotsController(BattleRoomState state, IWorldEntitiesModel worldEntitiesModel, IItemActionModels itemActionModels, BattleModeDescription battleMode, ISaveController saveController, ITick tick)
        {
            _state = state;
            _worldEntitiesModel = worldEntitiesModel;
            _itemActionModels = itemActionModels;
            _battleMode = battleMode;
            _saveController = saveController;
            _tick = tick;
        }

        public IAwaiter<IEither<IValue, IValue>> FromInventoryToDefenseModuleSlot(string peer, InventorySlotDescription inventorySlot, int entityId, PlotBuildingDefenseModuleSlotDescription defenseModuleSlot)
        {
            if (inventorySlot == null || defenseModuleSlot == null)
            {
                return _validationAwaiter;
            }
            
            if (!_battleMode.CanUseInventory)
            {
                return _validationAwaiter;
            }

            if (!TryGetBattleCharacter(peer, out var battleCharacterModel))
            {
                return _validationAwaiter;
            }

            if (!_itemActionModels.TryBuildModel(battleCharacterModel, inventorySlot, out var itemActionModel))
            {
                return _validationAwaiter;
            }

            if (!TryGetDefenseModuleSlot(entityId, defenseModuleSlot, battleCharacterModel, out var defenseModuleSlotModel))
            {
                return _validationAwaiter;
            }

            if (!PlotBuildingDefenseModuleSlotsRules.TryGetDefenseBlockPublicModel(entityId, _worldEntitiesModel, out var defenseBlockPublicModel))
            {
                return _validationAwaiter;
            }

            if (!PlotBuildingDefenseModuleSlotsRules.CheckMoveFromInventoryToDefenseModuleSlot(defenseBlockPublicModel, itemActionModel, defenseModuleSlotModel, out var inventoryItem, out var isUnset))
            {
                return _validationAwaiter;
            }

            if (isUnset)
            {
                itemActionModel.Unset();
            }
            else
            {
                itemActionModel.ChangeStack(-1);
            }
            
            defenseModuleSlotModel.Set(inventoryItem.PlotBuildingDefenseModuleDescription);
            UpdateIsSlotsEmptyFlag(entityId);
   
            _saveController.DelayedSave();
            
            return new Right<IValue, IValue>(new LongValue(_tick.TickIndex)).ToAsync();
        }

        public IAwaiter<IEither<IValue, IValue>> FromDefenseModuleSlotToInventory(string peer, InventorySlotDescription inventorySlot, int entityId, PlotBuildingDefenseModuleSlotDescription defenseModuleSlot)
        {
            if (inventorySlot == null || defenseModuleSlot == null)
            {
                return _validationAwaiter;
            }
            
            if (!_battleMode.CanUseInventory)
            {
                return _validationAwaiter;
            }

            if (!TryGetBattleCharacter(peer, out var battleCharacterModel))
            {
                return _validationAwaiter;
            }
            
            if (!_itemActionModels.TryBuildModel(battleCharacterModel, inventorySlot, out var itemActionModel))
            {
                return _validationAwaiter;
            }
            
            if (!TryGetDefenseModuleSlot(entityId, defenseModuleSlot, battleCharacterModel, out var defenseModuleSlotModel))
            {
                return _validationAwaiter;
            }

            if (!PlotBuildingDefenseModuleSlotsRules.TryGetDefenseBlockPublicModel(entityId, _worldEntitiesModel, out var defenseBlockPublicModel))
            {
                return _validationAwaiter;
            }

            if (!PlotBuildingDefenseModuleSlotsRules.CheckMoveFromDefenseModuleSlotToInventory(defenseBlockPublicModel, defenseModuleSlotModel, itemActionModel, out var inventoryItem, out var isSet))
            {
                return _validationAwaiter;
            }

            if (isSet)
            {
                itemActionModel.Set(inventoryItem, 1, 0, 0, 0);
            }
            else
            {
                itemActionModel.ChangeStack(1);
            }
            
            defenseModuleSlotModel.Unset();
            UpdateIsSlotsEmptyFlag(entityId);

            _saveController.DelayedSave();
            
            return new Right<IValue, IValue>(new LongValue(_tick.TickIndex)).ToAsync();
        }

        public IAwaiter<IEither<IValue, IValue>> UnsetDefenseModuleSlot(string peer, int entityId, PlotBuildingDefenseModuleSlotDescription defenseModuleSlot)
        {
            if (defenseModuleSlot == null)
            {
                return _validationAwaiter;
            }
            
            if (!_battleMode.CanUseInventory)
            {
                return _validationAwaiter;
            }

            if (!TryGetBattleCharacter(peer, out var battleCharacterModel))
            {
                return _validationAwaiter;
            }
            
            if (!TryGetDefenseModuleSlot(entityId, defenseModuleSlot, battleCharacterModel, out var defenseModuleSlotModel))
            {
                return _validationAwaiter;
            }

            if (!PlotBuildingDefenseModuleSlotsRules.TryGetDefenseBlockPublicModel(entityId, _worldEntitiesModel, out var defenseBlockPublicModel))
            {
                return _validationAwaiter;
            }

            if (!PlotBuildingDefenseModuleSlotsRules.CheckUnsetDefenseModuleSlot(defenseBlockPublicModel, defenseModuleSlotModel))
            {
                return _validationAwaiter;
            }
            
            defenseModuleSlotModel.Unset();
            UpdateIsSlotsEmptyFlag(entityId);
    
            _saveController.DelayedSave();
            
            return new Right<IValue, IValue>(new LongValue(_tick.TickIndex)).ToAsync();
        }

        public IAwaiter<IEither<IValue, IValue>> SwapInventoryWithDefenseModuleSlot(string peer, InventorySlotDescription inventorySlot, int entityId, PlotBuildingDefenseModuleSlotDescription defenseModuleSlot)
        {
            if (inventorySlot == null || defenseModuleSlot == null)
            {
                return _validationAwaiter;
            }
            
            if (!_battleMode.CanUseInventory)
            {
                return _validationAwaiter;
            }

            if (!TryGetBattleCharacter(peer, out var battleCharacterModel))
            {
                return _validationAwaiter;
            }
            
            if (!_itemActionModels.TryBuildModel(battleCharacterModel, inventorySlot, out var itemActionModel))
            {
                return _validationAwaiter;
            }

            if (!TryGetDefenseModuleSlot(entityId, defenseModuleSlot, battleCharacterModel, out var defenseModuleSlotModel))
            {
                return _validationAwaiter;
            }

            if (!PlotBuildingDefenseModuleSlotsRules.TryGetDefenseBlockPublicModel(entityId, _worldEntitiesModel, out var defenseBlockPublicModel))
            {
                return _validationAwaiter;
            }

            if (!PlotBuildingDefenseModuleSlotsRules.CheckSwapInventoryWithDefenseModuleSlot(defenseBlockPublicModel, itemActionModel, defenseModuleSlotModel, out var inventoryItem))
            {
                return _validationAwaiter;
            }

            var defenseModuleInventoryItem = defenseModuleSlotModel.Item.GetItem();

            if (inventoryItem != defenseModuleInventoryItem)
            {
                itemActionModel.Set(defenseModuleInventoryItem, 1, 0, 0, 0);
                defenseModuleSlotModel.Set(inventoryItem.PlotBuildingDefenseModuleDescription);
            
                _saveController.DelayedSave();
            }
            
            return new Right<IValue, IValue>(new LongValue(_tick.TickIndex)).ToAsync();
        }

        public IAwaiter<IEither<IValue, IValue>> SwapDefenseModuleSlots(string peer, int entityId, PlotBuildingDefenseModuleSlotDescription firstSlot, PlotBuildingDefenseModuleSlotDescription secondSlot)
        {
            if (firstSlot == null || secondSlot == null || firstSlot == secondSlot)
            {
                return _validationAwaiter;
            }
            
            if (!_battleMode.CanUseInventory)
            {
                return _validationAwaiter;
            }

            if (!TryGetBattleCharacter(peer, out var battleCharacterModel))
            {
                return _validationAwaiter;
            }

            if (!TryGetDefenseBlockPrivateModel(entityId, battleCharacterModel, out var defenseBlockPrivateModel))
            {
                return _validationAwaiter;
            }

            if (!defenseBlockPrivateModel.SlotsModel.TryGet(firstSlot, out var firstSlotModel) ||
                !defenseBlockPrivateModel.SlotsModel.TryGet(secondSlot, out var secondSlotModel))
            {
                return _validationAwaiter;
            }

            if (!PlotBuildingDefenseModuleSlotsRules.TryGetDefenseBlockPublicModel(entityId, _worldEntitiesModel, out var defenseBlockPublicModel))
            {
                return _validationAwaiter;
            }

            if (!PlotBuildingDefenseModuleSlotsRules.CheckSwapDefenseModuleSlots(defenseBlockPublicModel, firstSlotModel, secondSlotModel))
            {
                return _validationAwaiter;
            }

            if (firstSlotModel.Item != secondSlotModel.Item)
            {
                SwapSlots(firstSlotModel, secondSlotModel);
                
                _saveController.DelayedSave();
            }
            
            return new Right<IValue, IValue>(new LongValue(_tick.TickIndex)).ToAsync();
        }

        private void SwapSlots(IPlotBuildingDefenseModuleSlotModel firstSlotModel, IPlotBuildingDefenseModuleSlotModel secondSlotModel)
        {
            var firstModule = firstSlotModel.Item;
            var secondModule = secondSlotModel.Item;

            if (secondModule == null)
            {
                firstSlotModel.Unset();
            }
            else
            {
                firstSlotModel.Set(secondModule);
            }

            if (firstModule == null)
            {
                secondSlotModel.Unset();
            }
            else
            {
                secondSlotModel.Set(firstModule);
            }
        }

        private bool TryGetBattleCharacter(string privateId, out IBattleCharacterModel model)
        {
            model = null;
            return _state.PlayerStates.TryGetValue(privateId, out var state) && _worldEntitiesModel.BattleCharacters.TryGetModel(state.EntityId, out model);
        }

        private bool TryGetDefenseModuleSlot(int entityId, PlotBuildingDefenseModuleSlotDescription slot, IBattleCharacterModel battleCharacterModel, out IPlotBuildingDefenseModuleSlotModel slotModel)
        {
            slotModel = null;
            return TryGetDefenseBlockPrivateModel(entityId, battleCharacterModel, out var defenseBlockPrivateModel) &&
                   defenseBlockPrivateModel.SlotsModel.TryGet(slot, out slotModel);
        }

        private bool TryGetDefenseBlockPrivateModel(int entityId, IBattleCharacterModel battleCharacterModel, out IPlotBuildingDefenseBlockPrivateModel defenseBlockPrivateModel)
        {
            return _worldEntitiesModel.PlotBuildingDefenseBlockPrivateModels.TryGetModel(entityId, out defenseBlockPrivateModel) &&
                   battleCharacterModel.ObservableInventories.Contains(entityId);
        }
        
        private void UpdateIsSlotsEmptyFlag(int entityId)
        {
            PlotBuildingDefenseModuleSlotsRules.TryGetDefenseBlockPublicModel(entityId, _worldEntitiesModel, out var defenseBlockPublicModel);
            var defenseBlockPrivateModel = _worldEntitiesModel.PlotBuildingDefenseBlockPrivateModels[entityId];
            defenseBlockPublicModel.UpdateDefenseBlockSlotsIsEmptyFlag(defenseBlockPrivateModel);
        }
    }
}