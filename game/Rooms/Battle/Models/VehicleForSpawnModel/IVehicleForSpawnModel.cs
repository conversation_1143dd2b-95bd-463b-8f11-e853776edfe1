using System.Numerics;
using Framework.Core.Identified;
using Models.Models.PlotModel;
using Models.Models.VehicleModel;
using Models.Models.WorldEntitiesModel;
using Models.References.Vehicle;
using Server.Battle.World;
using Server.Common.Models.AnalyticsModel;

namespace Server.Battle.Models.VehicleForSpawnModel
{
    public interface IVehicleForSpawnModel
    {
        Vector3 BoundingBoxLocalPositionRelativeToPivot { get; }
        Vector3 BoundingBoxSize { get; }
    
        bool HasTrunk { get; }

        VehicleTypeDescription VehicleTypeDescription { get; }

        IVehicleModel AddVehicleToWorld(IWorld world, IWorldEntitiesModel worldEntitiesModel, in Vector3 position, in Quaternion orientation);

        bool IsCanReuseOldVehicle(IWorldEntitiesModel worldEntitiesModel, IVehicleModel oldVehicle);
        bool HasSpawnedPlotVehicle(IPlotModel plotModel);

        void SendSpawnPlotVehicleAnalytics(IBattleAnalyticsModel analyticsModel, IWorldEntitiesModel worldEntitiesModel, int battleCharacterEntityId, IIdentified buildingId, bool isActiveVehicleTrunkDropped, bool isActiveVehicleNotOnLocation);
        void SendTeleportPlotVehicleAnalytics(IBattleAnalyticsModel analyticsModel, IWorldEntitiesModel worldEntitiesModel, int battleCharacterEntityId, IIdentified buildingId, bool isActiveVehicleTrunkDropped, bool isActiveVehicleNotOnLocation);
    }
}