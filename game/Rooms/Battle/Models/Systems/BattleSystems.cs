using System;
using System.Collections.Generic;
using Framework.Ecs.System;
using Framework.Utility.Loggers;
using Models.CharacterController;
using Models.HitBox.Interpolation.Collection;
using Models.Inventory.ItemActionsController;
using Models.Models.AmmoItemsModel;
using Models.Models.BattleCharacterModel;
using Models.Models.BattleCharacterSlotsModel;
using Models.Models.BattleClientNotificationsModel;
using Models.Models.BattleSystemsAnalytics;
using Models.Models.BreakEquipModel;
using Models.Models.CarModel;
using Models.Models.ChangeEquipModel;
using Models.Models.ChangeRobberQuestProgressModel;
using Models.Models.CharacterVehicleTrunkModel;
using Models.Models.CharacterZoneModel;
using Models.Models.CheckTakeLootObjectModel;
using Models.Models.ClanBattleCharacterTeamModel;
using Models.Models.ClanBattleModel;
using Models.Models.ClanBattleTeamZonesModel;
using Models.Models.CrouchStandUpModel;
using Models.Models.DropCargoModel;
using Models.Models.GrenadeTriggerQueueModels;
using Models.Models.HangarOwnerModel;
using Models.Models.HelicopterModel;
using Models.Models.HitBoxesModel;
using Models.Models.InitEquipModel;
using Models.Models.InitStepsModel;
using Models.Models.IPartyMemberAccessModel;
using Models.Models.KinematicObjectModel;
using Models.Models.KinematicObjectsControllerModel;
using Models.Models.LocationBoundsModel;
using Models.Models.LocationCraftWorkbenchModel;
using Models.Models.LocationEventDataModel;
using Models.Models.LocationSecurityAlarmsModel;
using Models.Models.MapVehiclesModel;
using Models.Models.MarksModel;
using Models.Models.OccupiedCharacterSlotsModel;
using Models.Models.Parties;
using Models.Models.PartiesVehiclesModel;
using Models.Models.PhysicsDebug.Models.PhysicsDebugEnabledModel;
using Models.Models.PhysicsDebug.Models.PhysicsDebugModel;
using Models.Models.PrimitiveModel;
using Models.Models.RefillCopEquipModel;
using Models.Models.SecurityAlarmLocationQueueModel;
using Models.Models.SecurityAlarmQueueModel;
using Models.Models.SettlementBonusModel;
using Models.Models.SnapshotsModel;
using Models.Models.SpecialCollisionsModel;
using Models.Models.Triggers.TriggersCollection;
using Models.Models.Triggers.TriggersEventsModel;
using Models.Models.UnlockingSafeDoorModel;
using Models.Models.VehicleMapTransformUpdateModel;
using Models.Models.WorldBuildingsModel;
using Models.Models.WorldModel;
using Models.Models.WorldTickData;
using Models.Physics.BroadPhase.BroadPhaseCallbacks;
using Models.Physics.Collections;
using Models.Physics.Collections.Data;
using Models.Physics.NarrowPhase.NarrowPhaseCallbacks;
using Models.Physics.Scene;
using Models.Physics.Snapshots;
using Models.References;
using Models.References.Character;
using Models.References.Location;
using Models.Replication.BattleEntityReplicationGridLayerDescription;
using Models.Systems;
using Models.Utils.Collections.EventsSet;
using Server.Battle.Controllers.BattlePlotVehicleController;
using Server.Battle.Models.BattleChangeCoinsModel;
using Server.Battle.Models.BattleCharacterRolesMetric;
using Server.Battle.Models.BattleStopByPlayersCountModel;
using Server.Battle.Models.BattleSystemsAnalytics;
using Server.Battle.Models.BuildingLootSpawnModel;
using Server.Battle.Models.CargoSpawnModel;
using Server.Battle.Models.CarUpgradesHolderModel;
using Server.Battle.Models.ChangeCharacterRole;
using Server.Battle.Models.ChangePoliceAttentionPointsModel;
using Server.Battle.Models.ChangeRoleTimeModel;
using Server.Battle.Models.ClanBattleChangePhaseModel;
using Server.Battle.Models.ClanBattleRuntimeParams;
using Server.Battle.Models.CopChangeCoinsModel;
using Server.Battle.Models.CopsAoeFindModel;
using Server.Battle.Models.DropCargoModel;
using Server.Battle.Models.ExitVehicleModel;
using Server.Battle.Models.FinishKnockoutModel;
using Server.Battle.Models.FurnitureLootSpawnModel;
using Server.Battle.Models.GlobalMapVehicleModel;
using Server.Battle.Models.GrenadesQueue;
using Server.Battle.Models.LeftPlayersModel;
using Server.Battle.Models.LocationBreakableCollidersReset;
using Server.Battle.Models.LocationEventsModel;
using Server.Battle.Models.LootModels;
using Server.Battle.Models.MapVisibleEntitiesModel;
using Server.Battle.Models.PlotConstructModel;
using Server.Battle.Models.PlotsToSpawnModel;
using Server.Battle.Models.PlotVehicleSpawnerModel;
using Server.Battle.Models.ResetPlotVehicleModel;
using Server.Battle.Models.RespawnModel;
using Server.Battle.Models.RolesCountModel;
using Server.Battle.Models.SettlementPlotsServerModel;
using Server.Battle.Models.SettlementRoomModel;
using Server.Battle.Models.VehicleCargoInventoryChangeModel;
using Server.Battle.Models.VehicleLootSpawnModel;
using Server.Battle.Models.VehicleSpawnModel;
using Server.Battle.Models.WorldSecurityAlarmEntitiesModel;
using Server.Battle.Models.WorldWaterZonesModel;
using Server.Battle.Replication.ReplicationModel;
using Server.Battle.World;
using Server.Common.Controllers.SaveController;
using Server.Common.Models.AnalyticsModel;
using Server.References;
using Server.Systems;
using Server.Systems.Bot;

namespace Server.Battle.Models.Systems
{
    public class BattleSystems
    {
        public readonly List<IUpdateSystem> Systems = new();

        public readonly CharacterControllerGroundCheckSystem CharacterControllerGroundCheckSystem;
        public readonly CharacterControllerStateByGroundSystem CharacterControllerStateByGroundSystem;
        public readonly FinishReloadSystem FinishReloadSystem;
        public readonly WeaponAutoReloadSystem WeaponAutoReloadSystem;
        public readonly SetInputSystem SetInputSystem;
        public readonly ApplyInputSystem ApplyInputSystem;
        public readonly LookDirectionSystem LookDirectionSystem;
        public readonly BotMovementSystem BotMovementSystem;
        public readonly BotSprintSystem BotSprintSystem;
        public readonly BotLookDirectionXSystem BotLookDirectionXSystem;
        public readonly BotLookDirectionYSystem BotLookDirectionYSystem;
        public readonly BotSelectWeaponSystem BotSelectWeaponSystem;
        public readonly BotFireSystem BotFireSystem;
        public readonly BotAimingSystem BotAimingSystem;
        public readonly BotUseHideObjectSystem BotUseHideObjectSystem;
        public readonly BotSwapWeaponSystem BotSwapWeaponSystem;
        public readonly BotEmotionSystem BotEmotionSystem;
        public readonly CharacterControllerInputSystem CharacterControllerInputSystem;
        public readonly MoveDirectionSystem MoveDirectionSystem;
        public readonly StopSprintSystem StopSprintSystem;
        public readonly SprintSystem SprintSystem;
        public readonly CharacterControllerInputVelocitySystem CharacterControllerInputVelocitySystem;
        public readonly CharacterControllerExtraVerticalVelocitySystem CharacterControllerExtraVerticalVelocitySystem;
        public readonly CharacterControllerExtraHorizontalVelocitySystem CharacterControllerExtraHorizontalVelocitySystem;
        public readonly CharacterControllerColliderSystem CharacterControllerColliderSystem;
        public readonly CharacterControllerApplyVelocitySystem CharacterControllerApplyVelocitySystem;
        public readonly CharacterControllerStickToGroundSystem CharacterControllerStickToGroundSystem;
        public readonly CharacterControllerForwardMovementSystem CharacterControllerForwardMovementSystem;
        public readonly PhysicsSceneTickPrepareSystem PhysicsSceneTickPrepareSystem;
        public readonly PhysicsSceneTickContactsUpdateSystem PhysicsSceneTickContactsUpdateSystem;
        public readonly PhysicsSceneTickContactsSolveSystem PhysicsSceneTickContactsSolveSystem;
        public readonly BattleCharactersPhysicsContactEventsSystem BattleCharactersPhysicsContactEventsSystem;
        public readonly UpdatePhysicsScenePositionSystem UpdatePhysicsScenePositionSystem;
        public readonly UpdatePhysicsSceneVelocitySystem UpdatePhysicsSceneVelocitySystem;
        public readonly ClearMoveInputSystem ClearMoveInputSystem;
        public readonly ClearLookDirectionInputSystem ClearLookDirectionInputSystem;
        public readonly ClearInputSystem ClearInputSystem;
        public readonly ClearEventsSystem ClearEventsSystem;
        public readonly FireSystem FireSystem;
        public readonly StopFireSystem StopFireSystem;
        public readonly ShotArgsSystem ShotArgsSystem;
        public readonly ShotSystem ShotSystem;
        public readonly WeaponRoundsSystem WeaponRoundsSystem;
        public readonly StartReloadSystem StartReloadSystem;
        public readonly TeleportSystem TeleportSystem;
        public readonly PhysicsSnapshotSystem PhysicsSnapshotSystem;
        public readonly ShotInterpolationSystem ShotInterpolationSystem;
        public readonly UpdateGridSystem CharacterUpdateGridSystem;
        public readonly UpdateGridSystem VehicleUpdateGridSystem;
        public readonly CharacterControllerStickToGroundClearSystem CharacterControllerStickToGroundClearSystem;
        public readonly CharacterControllerForwardMovementClearSystem CharacterControllerForwardMovementClearSystem;
        public readonly SkeletonAnimationSystem SkeletonAnimationSystem;
        public readonly ClearHitBoxesSystem ClearHitBoxesSystem;
        public readonly HitBoxSystem PlayerHitBoxSystem;
        public readonly VehicleHitBoxSystem VehicleHitBoxSystem;
        public readonly UpdateHitBoxesInfoSystem UpdateHitBoxesInfoSystem;
        public readonly FinishAimStateSystem FinishAimStateSystem;
        public readonly StartAimStateSystem StartAimStateSystem;
        public readonly ApplyHitsModelSystem ApplyHitsModelSystem;
        public readonly StopSprintTransitionSystem StopSprintTransitionSystem;
        public readonly StopCrouchTransitionSystem StopCrouchTransitionSystem;
        public readonly WeaponSpreadArgsSystem WeaponSpreadArgsSystem;
        public readonly WeaponSpreadSystem WeaponSpreadSystem;
        public readonly BotInputSystem BotInputSystem;
        public readonly CrouchSystem CrouchSystem;
        public readonly BotCrouchSystem BotCrouchSystem;
        public readonly BotJumpSystem BotJumpSystem;
        public readonly BotSelfHealingSystem BotSelfHealingSystem;
        public readonly BotArmorUpSystem BotArmorUpSystem;
        public readonly BotMeleeAttackSystem BotMeleeAttackSystem;
        public readonly BotSurrenderSystem BotSurrenderSystem;
        public readonly BotSurrenderCommandSystem BotSurrenderCommandSystem;
        public readonly BotKnockdownSystem BotKnockdownSystem;
        public readonly BotThrowingItemSystem BotThrowingItemSystem;
        public readonly SpawnLootSystem SpawnLootSystem;
        public readonly FinishQuickSlotSelectionSystem FinishQuickSlotSelectionSystem;
        public readonly SelectQuickSlotSystem SelectQuickSlotSystem;
        public readonly QuickSlotAutoSelectSystem QuickSlotAutoSelectSystem;
        public readonly CharacterControllerStartJumpSystem CharacterControllerStartJumpSystem;
        public readonly CharacterControllerStateInAirSystem CharacterControllerStateInAirSystem;
        public readonly CharacterControllerAirObstaclesSystem CharacterControllerAirObstaclesSystem;
        public readonly StartUseItemSystem StartUseItemSystem;
        public readonly FinishUseItemSystem FinishUseItemSystem;
        public readonly TurningSystem TurningSystem;
        public readonly RotationPitchSystem RotationPitchSystem;
        public readonly RotationYawSystem RotationYawSystem;
        public readonly CharacterControllerMovementTypeSystem CharacterControllerMovementTypeSystem;
        public readonly DoorOpenCloseSystem DoorOpenCloseSystem;
        public readonly WindowBlindsOpenCloseSystem WindowBlindsOpenCloseSystem;
        public readonly UnlockSystem UnlockSystem;
        public readonly FinishUnlockSystem FinishUnlockSystem;
        public readonly UseEntityRequestHandleSystem UseEntityRequestHandleSystem;
        public readonly DoorPhysicsStateSystem DoorPhysicsStateSystem;
        public readonly BotRestoreAmmoSystem BotRestoreAmmoSystem;
        public readonly PrepareMovedEntitiesSystem PrepareMovedEntitiesSystem;
        public readonly UpdateVehicleTransformFromPhysicsSceneSystem UpdateVehicleTransformFromPhysicsSceneSystem;
        public readonly VehicleEnterSystem VehicleEnterSystem;
        public readonly VehicleLeaveRequestSystem VehicleLeaveRequestSystem;
        public readonly CharacterInsideVehicleUpdateTransformSystem CharacterInsideVehicleUpdateTransformSystem;
        public readonly CharacterInsideVehicleUpdateVelocitySystem CharacterInsideVehicleUpdateVelocitySystem;
        public readonly UpdateRigidBodyEnabledSystem UpdateRigidBodyEnabledSystem;
        public readonly UpdateVehicleVelocitiesFromPhysicsSceneSystem UpdateVehicleVelocitiesFromPhysicsSceneSystem;
        public readonly ClearBattleCharacterVehiclesInputSystem ClearBattleCharacterVehiclesInputSystem;
        public readonly CarControllerClearInputSystem CarControllerClearInputSystem;
        public readonly CarSteeringSystem CarSteeringSystem;
        public readonly ContextMenuUseSystem ContextMenuUseSystem;
        public readonly TakeLootObjectSystem TakeLootObjectSystem;
        public readonly FinishTakeLootObjectSystem FinishTakeLootObjectSystem;
        public readonly StartBattleStanceSystem StartBattleStanceSystem;
        public readonly StartArmedSystem StartArmedSystem;
        public readonly FinishBattleStanceSystem FinishBattleStanceSystem;
        public readonly FinishArmedSystem FinishArmedSystem;
        public readonly StartInventoryUseItemSystem StartInventoryUseItemSystem;
        public readonly FinishInventoryUseItemSystem FinishInventoryUseItemSystem;
        public readonly BotDriveCarSystem BotDriveCarSystem;
        public readonly CarParkingSystem CarParkingSystem;
        public readonly FinishKnockoutSystem FinishKnockoutSystem;
        public readonly SuicideSystem SuicideSystem;
        public readonly CarControllerFlipSystem CarControllerFlipSystem;
        public readonly LeaveSystem LeaveSystem;
        public readonly RemoveOrRespawnDeadPlayersSystem RemoveOrRespawnDeadPlayersSystem;
        public readonly RemovePlayerSystem RemovePlayerSystem;
        public readonly RemoveLootSystem RemoveLootSystem;
        public readonly RemoveVehicleLootSystem RemoveVehicleLootSystem;
        public readonly RemoveFurnitureLootSystem RemoveFurnitureLootSystem;
        public readonly RemoveBuildingLootSystem RemoveBuildingLootSystem;
        public readonly SelfDamageSystem SelfDamageSystem;
        public readonly HealthAutoRestoreSystem HealthAutoRestoreSystem;
        public readonly ChangeRoleOnRemoveSystem ChangeRoleOnRemoveSystem;
        public readonly SecurityAlarmOnColliderBreakSystem SecurityAlarmOnColliderBreakSystem;
        public readonly ClearStartedSecurityAlarmSystem ClearStartedSecurityAlarmSystem;
        public readonly ClearSecurityAlarmLocationSystem ClearSecurityAlarmLocationSystem;
        public readonly RespawnSystem RespawnSystem;
        public readonly ChangeCharacterPoliceAttentionLevelSystem ChangeCharacterPoliceAttentionLevelSystem;
        public readonly ClearMapMarksSystem ClearVigilantNpcCallMapMarksSystem;
        public readonly KillUnderLocationSystem KillUnderLocationSystem;
        public readonly KillUnderWaterSystem KillUnderWaterSystem;
        public readonly CarControllerWheelGroundCheckSystem CarControllerWheelGroundCheckSystem;
        public readonly CarControllerWheelMovementDataSystem CarControllerWheelMovementDataSystem;
        public readonly CarControllerWheelSuspensionSystem CarControllerWheelSuspensionSystem;
        public readonly CarControllerWheelApplyInputSystem CarControllerWheelApplyInputSystem;
        public readonly CarControllerWheelFrictionSlipVelocitiesSystem CarControllerWheelFrictionSlipVelocitiesSystem;
        public readonly CarControllerWheelFrictionMaxPossibleForcesSystem CarControllerWheelFrictionMaxPossibleForcesSystem;
        public readonly BreakableColliderShotSystem BreakableColliderShotSystem;
        public readonly ClearShotEventsSystem ClearShotEventsSystem;
        public readonly ChangeAttentionsOnColliderBreakSystem ChangeAttentionsOnColliderBreakSystem;
        public readonly ClearBreakableColliderBreakEventsSystem ClearBreakableColliderBreakEventsSystem;
        public readonly DoorBreakableColliderShotSystem DoorBreakableColliderShotSystem;
        public readonly SecurityAlarmOnDoorColliderBreakSystem SecurityAlarmOnDoorColliderBreakSystem;
        public readonly ChangeAttentionsOnDoorColliderBreakSystem ChangeAttentionsOnDoorColliderBreakSystem;
        public readonly VehicleBreakCollidersSystem VehicleBreakCollidersSystem;
        public readonly ResetBuildingSystem ResetBuildingSystem;
        public readonly ResetLocationBreakableCollidersSystem ResetLocationBreakableCollidersSystem;
        public readonly UpdateEntityAoiSystem UpdateEntityAoiSystem;
        public readonly UpdateEntityGridModelSystem UpdateVehiclesEntityGridModelSystem;
        public readonly UpdateMapVisibleEntityPositionSystem UpdateAllRolesVisibleRobbersPositionSystem;
        public readonly UpdateVehicleMapPositionSystem UpdateVehicleMapPositionSystem;
        public readonly CarControllerControlOwnerSystem CarControllerControlOwnerSystem;
        public readonly CarControllerApplyCarInputByDriverSystem CarControllerApplyCarInputByDriverSystem;
        public readonly CarControllerApplyCarInputByParkingSystem CarControllerApplyCarInputByParkingSystem;
        public readonly CarControllerApplyCarInputByEmptySystem CarControllerApplyCarInputByEmptySystem;
        public readonly HelicopterControllerApplyHelicopterInputByDriverSystem HelicopterControllerApplyHelicopterInputByDriverSystem;
        public readonly HelicopterControllerApplyHelicopterInputByEmptySystem HelicopterControllerApplyHelicopterInputByEmptySystem;
        public readonly CarControllerMovementStateSystem CarControllerMovementStateSystem;
        public readonly CarControllerCombinedFrictionSystem CarControllerCombinedFrictionSystem;
        public readonly CarControllerBlockedFrictionSystem CarControllerBlockedFrictionSystem;
        public readonly CarControllerBurnoutFrictionSystem CarControllerBurnoutFrictionSystem;
        public readonly CarControllerApplyFrictionSystem CarControllerApplyFrictionSystem;
        public readonly CarControllerDirtLevelSystem CarControllerDirtLevelSystem;
        public readonly PrepareAlarmsToStartSystem PrepareAlarmsToStartSystem;
        public readonly StartSecurityAlarmSystem StartSecurityAlarmSystem;
        public readonly MetalDetectorCheckSystem MetalDetectorCheckSystem;
        public readonly VehicleLeaveSystem VehicleLeaveSystem;
        public readonly FinishUseHideObjectSystem FinishUseHideObjectSystem;
        public readonly FinishRevivalVigilantNpcSystem FinishRevivalVigilantNpcSystem;
        public readonly StartUseHideObjectSystem StartUseHideObjectSystem;
        public readonly StartRevivalVigilantNpcSystem StartRevivalVigilantNpcSystem;
        public readonly StartRevivalCharacterSystem StartRevivalCharacterSystem;
        public readonly FinishRevivalCharacterSystem FinishRevivalCharacterSystem;
        public readonly StartSecurityCommandSystem StartSecurityCommandSystem;
        public readonly FinishSecurityCommandSystem FinishSecurityCommandSystem;
        public readonly CharacterGridSystem CharacterGridSystem;
        public readonly StopHideSystem StopHideSystem;
        public readonly TeleportOutHideObjectSystem TeleportOutHideObjectSystem;
        public readonly TeleportInHideObjectSystem TeleportInHideObjectSystem;
        public readonly LeaveHideObjectSystem LeaveHideObjectSystem;
        public readonly CameraProgressSystem CameraProgressSystem;
        public readonly CameraTriggersAlarmSystem CameraTriggersAlarmSystem;
        public readonly CameraShotSystem CameraShotSystem;
        public readonly PhysicsGravitySystem PhysicsGravitySystem;
        public readonly CarControllerDragSystem CarControllerDragSystem;
        public readonly CarControllerDownForceSystem CarControllerDownForceSystem;
        public readonly TeleportVehicleSystem TeleportVehicleSystem;
        public readonly SurrenderSystem SurrenderSystem;
        public readonly SurrenderCommandSystem SurrenderCommandSystem;
        public readonly StartHandcuffSystem StartHandcuffSystem;
        public readonly FinishHandcuffSystem FinishHandcuffSystem;
        public readonly FinishArrestSystem FinishArrestSystem;
        public readonly RemoveArrestedPlayersSystem RemoveArrestedPlayersSystem;
        public readonly StartInterruptArrestingSystem StartInterruptArrestingSystem;
        public readonly FinishInterruptArrestingSystem FinishInterruptArrestingSystem;
        public readonly DeathUpdateInventorySystem DeathUpdateInventorySystem;
        public readonly FinishArrestGettingUpSystem FinishArrestGettingUpSystem;
        public readonly SubstepSystem PhysicsSubstepSystem;
        public readonly PhysicsSceneClearContactEventsSystem PhysicsSceneClearContactEventsSystem;
        public readonly StartBecomeRobberSystem StartBecomeRobberSystem;
        public readonly StopBecomeRobberSystem StopBecomeRobberSystem;
        public readonly StartBecomeRobberPartySystem StartBecomeRobberPartySystem;
        public readonly StartEmotionSystem StartEmotionSystem;
        public readonly FinishEmotionSystem FinishEmotionSystem;
        public readonly VigilantNpcCriminalZoneSystem VigilantNpcCriminalZoneSystem;
        public readonly VigilantNpcProgressSystem VigilantNpcProgressSystem;
        public readonly AimCooldownSystem AimCooldownSystem;
        public readonly VigilantNpcMeleeHitSystem VigilantNpcMeleeHitSystem;
        public readonly VigilantNpcShotSystem VigilantNpcShotSystem;
        public readonly VigilantNpcThreatenSystem VigilantNpcThreatenSystem;
        public readonly TakeLootNodeSystem TakeLootNodeSystem;
        public readonly FinishTakeLootNodeSystem FinishTakeLootNodeSystem;
        public readonly LootNotificationSystem LootNotificationSystem;
        public readonly ClearInterpolatedHitBoxesSystem ClearInterpolatedHitBoxesSystem;
        public readonly CollectHitBoxesInterpolateTimesSystem CollectHitBoxesInterpolateTimesSystem;
        public readonly InterpolateHitBoxesSystem InterpolateHitBoxesSystem;
        public readonly PrepareHitBoxIdSystem PrepareHitBoxIdSystem;
        public readonly PhysicsDebugStartNewTickSystem PhysicsDebugStartNewTickSystem;
        public readonly PhysicsDebugStartNewSubTickSystem PhysicsDebugStartNewSubTickSystem;
        public readonly PhysicsDebugCollectSubTickTransformSystem PhysicsDebugCollectSubTickTransformSystem;
        public readonly StartMeleeAttackSystem StartMeleeAttackSystem;
        public readonly StopMeleeAttackSystem StopMeleeAttackSystem;
        public readonly MeleeAttackHitSystem MeleeAttackHitSystem;
        public readonly InteractSystem InteractSystem;
        public readonly StopInteractSystem StopInteractSystem;
        public readonly CarControllerTransmissionSystem CarControllerTransmissionSystem;
        public readonly ObservableInventoryDistanceCheckSystem ObservableInventoryDistanceCheckSystem;
        public readonly StopObserveInventorySystem StopObserveInventorySystem;
        public readonly CarVsCarCollisionServerSystem CarVsCarCollisionSystem;
        public readonly VehicleTrunkPositionSystem VehicleTrunkPositionSystem;
        public readonly ClearAfterKnockoutDelaySystem ClearAfterKnockoutDelaySystem;
        public readonly ClearWarpedFlagSystem ClearWarpedFlagSystem;
        public readonly CarControllerPreparePhysicsForTickSystem CarControllerPreparePhysicsForTickSystem;
        public readonly CarControllerPreparePhysicsForSubTickSystem CarControllerPreparePhysicsForSubTickSystem;
        public readonly PhysicsDebugEntitiesTickInitializeSystem PhysicsDebugEntitiesTickInitializeSystem;
        public readonly CarControllerCalculateSlipParamsSystem CarControllerCalculateSlipParamsSystem;
        public readonly StartColliderBreakingSystem StartColliderBreakingSystem;
        public readonly FinishColliderBreakingSystem FinishColliderBreakingSystem;
        public readonly ColliderBreakingSystem ColliderBreakingSystem;
        public readonly CarSirenSystem CarSirenSystem;
        public readonly VehicleVsCharacterCollisionSystem VehicleVsCharacterCollisionSystem;
        public readonly RegisterSpecialCollisionsSystem RegisterSpecialCollisionsSystem;
        public readonly StartKnockdownSystem StartKnockdownSystem;
        public readonly KnockdownSystem KnockdownSystem;
        public readonly StartRefillEquipSystem StartRefillEquipSystem;
        public readonly FinishRefillEquipSystem FinishRefillEquipSystem;
        public readonly StopActionsOnDamageSystem StopActionsOnDamageSystem;
        public readonly StopActionsOnMoveSystem StopActionsOnMoveSystem;
        public readonly CloseOpenedObjectOnStopRefillEquipSystem CloseOpenedObjectOnStopRefillEquipSystem;
        public readonly StartArrestLootSystem StartArrestLootSystem;
        public readonly FinishArrestLootSystem FinishArrestLootSystem;
        public readonly TakeCargoSystem TakeCargoSystem;
        public readonly FinishTakeCargoSystem FinishTakeCargoSystem;
        public readonly CharacterControllerImpulseVelocitySystem CharacterControllerImpulseVelocitySystem;
        public readonly DropCargoSystem DropCargoSystem;
        public readonly RemoveCargoSystem RemoveCargoSystem;
        public readonly KinematicObjectsControllerStateSystem KinematicObjectsControllerStateSystem;
        public readonly CarControllerStuckSurfaceSystem CarControllerStuckSurfaceSystem;
        public readonly SetDrillSystem SetDrillSystem;
        public readonly FinishSetDrillSystem FinishSetDrillSystem;
        public readonly UnlockSafeDoorSystem UnlockSafeDoorSystem;
        public readonly CharacterControllerVerticalExtraVelocityChangedSystem CharacterControllerVerticalExtraVelocityChangedSystem;
        public readonly FallDamageSystem FallDamageSystem;
        public readonly FallDamageTrackSystem FallDamageTrackSystem;
        public readonly RemoveDrillSystem RemoveDrillSystem;
        public readonly FinishRemoveDrillSystem FinishRemoveDrillSystem;
        public readonly ClearCrouchStandUpCheckResultsSystem ClearCrouchStandUpCheckResultsSystem;
        public readonly RobberQuestSystem RobberQuestSystem;
        public readonly PartyRobberQuestSystem PartyRobberQuestSystem;
        public readonly HelicopterControllerControlOwnerSystem HelicopterControllerControlOwnerSystem;
        public readonly HelicopterEngineSystem HelicopterEngineSystem;
        public readonly HelicopterControllerPreparePhysicsForTickSystem HelicopterControllerPreparePhysicsForTickSystem;
        public readonly HelicopterControllerHandlingSystem HelicopterControllerHandlingSystem;
        public readonly CaptureBuildingSystem CaptureBuildingSystem;
        public readonly PartyCaptureBuildingSystem PartyCaptureBuildingSystem;
        public readonly RobberQuestAutoCompleteSystem RobberQuestAutoCompleteSystem;
        public readonly BotDriveHelicopterSystem BotDriveHelicopterSystem;
        public readonly ExitOnLocationBorderSystem ExitOnLocationBorderSystem;
        public readonly HelicopterControllerWheelGroundCheckSystem HelicopterControllerWheelGroundCheckSystem;
        public readonly HelicopterControllerPreparePhysicsForSubTickSystem HelicopterControllerPreparePhysicsForSubTickSystem;
        public readonly HelicopterControllerWheelSuspensionSystem HelicopterControllerWheelSuspensionSystem;
        public readonly VehicleControllerSuspensionLimitAddConstraintSystem VehicleControllerSuspensionLimitAddConstraintSystem;
        public readonly VehicleControllerSuspensionLimitClearConstraintSystem VehicleControllerSuspensionLimitClearConstraintSystem;
        public readonly HelicopterControllerChassisSystem HelicopterControllerChassisSystem;
        public readonly HelicopterControllerStateSystem HelicopterControllerStateSystem;
        public readonly TriggerSecurityAlarmSystem TriggerSecurityAlarmSystem;
        public readonly ClearTriggersEventsSystem ClearTriggersEventsSystem;
        public readonly TriggersHandleSystem TriggersHandleSystem;
        public readonly CharacterExitRequestsSystem CharacterExitRequestSystem;
        public readonly UseSwitchSystem UseSwitchSystem;
        public readonly CraftWorkbenchSystem CraftWorkbenchSystem;
        public readonly UpdateChangedRoleCharactersSystem UpdateChangedRoleCharactersSystem;
        public readonly TakeCashSystem TakeCashSystem;
        public readonly FinishTakeCashSystem FinishTakeCashSystem;
        public readonly TimeEffectsSystem TimeEffectsSystem;
        public readonly PlotSpawnSystem PlotSpawnSystem;
        public readonly ThrowGrenadeSystem ThrowGrenadeSystem;
        public readonly GrenadeFlySystem GrenadeFlySystem;
        public readonly GrenadeTriggerTimerRequestSystem GrenadeTriggerTimerRequestSystem;
        public readonly GrenadeTriggerEffectSystem GrenadeTriggerEffectSystem;
        public readonly GrenadeRemoveSystem GrenadeRemoveSystem;
        public readonly GrenadeBlindEffectOnCharacterSystem GrenadeBlindEffectOnCharacterSystem;
        public readonly PlotConstructionSystem PlotConstructionSystem;
        public readonly PutCargoInInventorySystem PutCargoInInventorySystem;
        public readonly FinishThrowItemSystem FinishThrowItemSystem;
        public readonly StartThrowItemSystem StartThrowItemSystem;
        public readonly ClanBattlePhaseSystem ClanBattlePhaseSystem;
        public readonly ClanBattleFlagCaptureSystem ClanBattleFlagCaptureSystem;
        public readonly UpdateClanBattleTeamMemberPositionsSystem UpdateClanBattleTeamMemberPositionSystem;
        public readonly UpdateVehicleSleepFromPhysicsSceneSystem UpdateVehicleSleepFromPhysicsSceneSystem;
        public readonly VehicleControllerPhysicsAwakeObserverSystem VehicleControllerPhysicsAwakeObserverSystem;
        public readonly RepairBreachSystem RepairBreachSystem;
        public readonly FinishRepairBreachSystem FinishRepairBreachSystem;
        public readonly ObserverPositionSystem ObserverPositionSystem;
        public readonly ExitLocationInPartyVehicleSystem ExitLocationInPartyVehicleSystem;
        public readonly ExitCharactersOnClanBattleEndSystem ExitCharactersOnClanBattleEndSystem;
        public readonly MainMenuExitSystem MainMenuExitSystem;
        public readonly AimModeSystem AimModeSystem;
        public readonly UpdateCarUpgradesHolderOpenedStateByDistanceSystem UpdateCarUpgradesHolderOpenedStateByDistanceSystem;
        public readonly BreakableColliderHitsClearSystem BreakableColliderHitsClearSystem;
        public readonly DoorBreakableColliderHitsClearSystem DoorBreakableColliderHitsClearSystem;
        public readonly FallingEntitySimpleDotPhysicsSystem FallingEntitySimpleDotPhysicsSystem;
        public readonly BotReviveCharacterSystem BotReviveCharacterSystem;
        public readonly BotRemovingHandcuffsSystem BotRemovingHandcuffsSystem;
        public readonly BotHandcuffingSystem BotHandcuffingSystem;
        public readonly BotSirenSwitchSystem BotSirenSwitchSystem;
        public readonly BotEnterLeaveVehicleSystem BotEnterLeaveVehicleSystem;
        public readonly CarControllerBurnoutSystem CarControllerBurnoutSystem;
        public readonly FinishChargeCumulativeItemSystem FinishChargeCumulativeItemSystem;
        public readonly ChargeCumulativeItemSystem ChargeCumulativeItemSystem;
        public readonly LocationEventsSystem RobberLocationEventsSystem;
        public readonly LocationEventStartSystem LocationEventStartSystem;
        public readonly CheckEventCargoUnderWaterSystem CheckEventCargoUnderWaterSystem;
        public readonly KinematicObjectObstacleSystem KinematicObjectObstacleSystem;
        public readonly KinematicObjectObstacleRevertMovementSystem KinematicObjectObstacleRevertMovementSystem;
        public readonly PlayerLifeTimerSystem PlayerLifeTimerSystem;
        public readonly CheckPlayersCountToCloseRoomSystem CheckPlayersCountToCloseRoomSystem;
        public readonly RaidDefenseBlockSystem RaidDefenseBlockSystem;
        public readonly FinishRaidDefenseBlockSystem FinishRaidDefenseBlockSystem;
        public readonly RepairDefenseBlockSystem RepairDefenseBlockSystem;
        public readonly FinishRepairDefenseBlockSystem FinishRepairDefenseBlockSystem;

        public BattleSystems(IWorld world, int tickPeriod, IOccupiedCharacterSlotsModel occupiedCharacterSlotsModel, IWorldModel worldModel, IHitBoxesModel hitBoxesModel, BattleModeDescription battleMode, LocationDescription location, int snapshotsHistorySize, ILeftPlayersModel leftPlayers,
            IItemActionsController itemActionsController, IReplicationModel replicationModel, Dictionary<int, int> selfDamageRequests, IChangeEquipModel changeEquipModel, ILootModels lootModels, IBattleClientNotificationsModel clientNotificationsModel,
            IChangePoliceAttentionPointsModel changePoliceAttentionPointsModel, IParties parties, IBattleCharacterRolesMetric characterRolesMetric, IWorldBuildingsModel worldBuildingsModel, IWorldSecurityAlarmEntitiesModel worldSecurityAlarmEntitiesModel,
            ILocationSecurityAlarmsModel locationSecurityAlarmsModel, IMapVehiclesModel mapCopVehiclesModel, IMapVisibleEntitiesModel allRolesVisibleRobbersModel, ILocationBoundsModel locationBoundsModel, WorldTickData worldTickData,
            IEntityMarksModel vigilantNpcCallMarksModel, ICopsAoeRewardModel copsAoeRewardModel, ICopsAoeFindModel copsAoeFindModel, IWorldWaterZonesModel worldWaterZonesModel, IPhysicsDebugEnabledModel physicsDebugEnabledModel, IPhysicsDebugModel physicsDebugModel,
            IVehicleSpawnModel vehicleSpawnModel, IInitEquipModel initEquipModel, ICargoSpawnModel cargoSpawnModel,
            IVehicleCargoInventoryChangeModel vehicleCargoInventoryChangeModel, IUnlockingSafeDoorModel unlockingSafeDoorModel, ISaveController saveController, IChangeRobberQuestProgressModel changeRobberQuestProgressModel, IRespawnModel respawnModel,
            IVehicleLootSpawnModel vehicleLootSpawnModel, ITriggersCollection triggeredCollection, ITriggersEventsModel triggersEventsModel, IHangarOwnerModel hangarOwnerModel, HashSet<int> characterExitRequests, IGlobalMapVehicleModel globalMapVehicleModel,
            ILocationCraftWorkbenchModel locationCraftWorkbenchModel, IBattleAnalyticsModel analyticsModel, IChangeRoleTimeModel changeRoleTimeModel, IBattleChangeCoinsModel changeCoinsModel, ISettlementRoomModel settlementRoomModel, IPlotConstructModel plotConstructModel,
            IInitStepsModel plotConstructionStepsModel, IClanBattleModel clanBattleModel, IClanBattleTeamZonesModel clanBattleTeamZonesModel, long clanBattleStartTs, IBattleCharacterSlotsModel battleCharacterSlotsModel,
            IClanBattleCharacterTeamModel clanBattleCharacterTeamModel, IGrenadesQueue grenadesQueue, IPartyMemberAccessModel partyMemberAccessModel, ISettlementBonusModel settlementBonusModel, IPlotVehicleSpawnerModel plotVehicleSpawnerModel,
            HashSet<int> hiddenCharacters, IExitVehicleModel exitVehicleModel, IResetPlotVehicleModel resetPlotVehicleModel, IFurnitureLootSpawnModel furnitureLootSpawnModel, IBuildingLootSpawnModel buildingLootSpawnModel, ILoggers loggers, BattleInitTickScore initTickScore,
            ChangeCharacterRoleModel changeCharacterRoleModel, ChangedRoleCharacters changedRoleCharacters, IChangeVehicleTrunkEmptyFlagModel changeVehicleTrunkEmptyFlagModel, ICarUpgradesHoldersModel carUpgradesHoldersModel, IBattlePlotVehicleController plotVehicleController,
            IPartiesVehiclesModel partiesVehiclesModel, bool isCheatsEnabled, ClanBattleRuntimeParamsModel clanBattleRuntimeParamsModel, LocationBreakableCollidersResetModels locationBreakableCollidersResetModels, ILocationEventsModel robberLocationEventsModel, IRolesCountModel rolesCountModel,
            IRobberLocationEventModels robberLocationEventModels, IBattleStopByPlayersCountModel stopByPlayersCountModel)
        {
            var physicsBodiesModel = worldModel.PhysicsBodies;

            var physicsSceneModel = worldModel.PhysicsScene;
            var grids = worldModel.Grids;

            ISettlementPlotsServerModel plotsModel = settlementRoomModel.PlotsModel;
            IPlotsToSpawnModel plotsToSpawnModel = settlementRoomModel.PlotsToSpawnModel;
            var worldEntitiesModel = worldModel.WorldEntitiesModel;

            var entitiesInfo = replicationModel.EntitiesInfo;

            var changeObservableInventoryModel = worldModel.ChangeObservableInventoryModel;

            var snapshotsCount = 1 + snapshotsHistorySize;
            var physicsSnapshots = new SnapshotsModel<PhysicsData>(snapshotsCount, () => PhysicsSnapshots.BuildQueriesSnapshotStorageForPhysicsData());

            var raycastModel = worldModel.RaycastModel;
            var sphereCastModel = worldModel.SphereCastModel;

            IDropCargoModel dropCargoModel = new ServerDropCargoModel(worldEntitiesModel.BattleCharacters, world, cargoSpawnModel);

            var interruptModel = worldModel.WorldInterruptModel;
            var finishArrestModel = new FinishArrestModel.FinishArrestModel(worldEntitiesModel.BattleCharacters, occupiedCharacterSlotsModel, clientNotificationsModel, copsAoeRewardModel, allRolesVisibleRobbersModel);
            var deathWorldModel = new DeathWorldModel.DeathWorldModel(world, worldEntitiesModel, worldModel.Vehicles, clientNotificationsModel, occupiedCharacterSlotsModel, parties, allRolesVisibleRobbersModel, dropCargoModel, vehicleSpawnModel, analyticsModel, vehicleLootSpawnModel, respawnModel, changeVehicleTrunkEmptyFlagModel, worldModel.WorldInterruptModel);
            var finishExitModel = new ExitCharacterModel.ExitCharacterModel(clientNotificationsModel, occupiedCharacterSlotsModel, dropCargoModel);

            float tickDeltaTime = tickPeriod / 1000f;

            var triggeredSecurityAlarmQueue = new SecurityAlarmQueueModel();
            var startedSecurityAlarmQueue = new SecurityAlarmQueueModel();
            var securityAlarmLocationQueue = new SecurityAlarmQueueLocationModel(SecurityAlarmDescription.AlarmDuration, locationSecurityAlarmsModel);
            ICharacterZoneModel characterZoneModel = new CharacterZoneModel(physicsSceneModel, physicsBodiesModel, worldEntitiesModel);
            var interpolatedHitBoxes = new InterpolatedHitBoxes(hitBoxesModel);
            var checkTakeLootObjectModel = new CheckTakeLootObjectModel();
            var breakEquipModel = new BreakEquipModel();
            var hitModel = new HitModel.HitModel(physicsBodiesModel, entitiesInfo, occupiedCharacterSlotsModel, deathWorldModel, changePoliceAttentionPointsModel, changeRobberQuestProgressModel, copsAoeRewardModel, parties, breakEquipModel, dropCargoModel, worldEntitiesModel, worldModel.Vehicles, analyticsModel, changeCoinsModel, interruptModel, copsAoeFindModel, clanBattleCharacterTeamModel, battleMode, location);
            var interactRaycastModel = worldModel.InteractRaycastModel;
            var vehicleMapTransformUpdateModel = new VehicleMapTransformUpdateModel(locationBoundsModel);
            var robberQuestTaskGenerateModel = new RobberQuestTaskGenerateModel.RobberQuestTaskGenerateModel();
            var robberQuestInitModel = new RobberQuestInitModel.RobberQuestInitModel(robberQuestTaskGenerateModel);

            IEventsSet<CharacterControllerVerticalExtraVelocityChangedEvent> characterControllerVerticalExtraVelocityChangeEvents = new EventsSet<CharacterControllerVerticalExtraVelocityChangedEvent>();
            var crouchStandUpModel = new CrouchStandUpModel(physicsSceneModel, sphereCastModel);
            ISpecialCollisionsModel specialCollisionsModel = new SpecialCollisionsModel();

            IBroadPhaseCallback physicsBroadPhaseCallback = new ShooterBroadPhaseCallback(physicsBodiesModel);
            INarrowPhaseCallback physicsNarrowPhaseCallback = new ShooterNarrowPhaseCallback(physicsBodiesModel);

            IAmmoItemsModel ammoItemsModel = new AmmoItemsModel();
            IRefillCopEquipModel refillCopEquipModel = new RefillCopEquipModel(ammoItemsModel);
            IBattleSystemsAnalyticsModel battleSystemsAnalytics = new BattleSystemsAnalyticsModel(analyticsModel, worldEntitiesModel);

            IClanBattleChangePhaseModel clanBattleChangePhaseModel = new ClanBattleChangePhaseModel.ClanBattleChangePhaseModel(clanBattleModel, worldEntitiesModel, clanBattleTeamZonesModel, battleCharacterSlotsModel, clanBattleRuntimeParamsModel);
            var isClanBattle = battleMode.IsClanBattle;
            if (isClanBattle)
            {
                clanBattleChangePhaseModel.Init(clanBattleStartTs);
            }

            IFinishKnockoutModel finishKnockoutModel = new FinishKnockoutModel.FinishKnockoutModel(deathWorldModel, copsAoeRewardModel, changeRobberQuestProgressModel, changePoliceAttentionPointsModel, worldEntitiesModel.BattleCharacters, changeCoinsModel, parties, battleMode, location);

            const int grenadesEffectsQueueStartCapacity = 32;
            PhysicsQueueData<GrenadeTriggerHeavyEffectItem> grenadesHeavyEffectsQueue = PhysicsQueue.Build<GrenadeTriggerHeavyEffectItem>(grenadesEffectsQueueStartCapacity);
            PhysicsQueueData<GrenadeTriggerLightEffectItem> grenadesLightEffectsQueue = PhysicsQueue.Build<GrenadeTriggerLightEffectItem>(grenadesEffectsQueueStartCapacity);

            IPrimitiveModel<int> grenadesExpectedHeavyEffectsCounter = new NoReplicationPrimitiveModel<int>();

            var battleCharacterLayer = BattleEntityReplicationGridLayerDescription.BattleCharacter.Layer;
            var vehicleLayer = BattleEntityReplicationGridLayerDescription.Vehicle.Layer;

            var canShoot = battleMode.CanShoot;
            var needSpawnBuildings = battleMode.NeedSpawnBuildings;
            var hasLocationBreakableColliders = location.LocationBreakableColliderSets.Count > 0;
            var needSpawnCraftWorkbench = battleMode.NeedSpawnCraftWorkbench;
            var hasVehicles = battleMode.HasVehicles;
            var hasRobbery = battleMode.HasRobbery;
            var hasKnockout = battleMode.HasKnockout;
            var canTakeItemInHands = battleMode.CanTakeItemInHands;
            var canUseInventory = battleMode.CanUseInventory;
            var isCharacterLootEnabled = !battleMode.IsCharacterLootDisabled;
            var isCargoEnabled = !battleMode.IsCargoDisabled;
            var hasRobberLocationEvents = battleMode.HasLocationEvents && location.HasLocationEvents && robberLocationEventsModel.HasEvents;
            var hasPlayerLifeTimer = battleMode.HasPlayerPersonalDisconnectTimer && location.HasPlayersPersonalTimer;
            var hasCheckPlayersCount = battleMode.HasPlayersCountCheck && location.HasPlayersCountCheck;
            var isRaidAvailable = battleMode.IsRaidAvailable;

            TriggersHandleSystem = AddSystem(new TriggersHandleSystem(triggeredCollection, worldEntitiesModel, occupiedCharacterSlotsModel));
            ClearTriggersEventsSystem = AddSystem(new ClearTriggersEventsSystem(triggersEventsModel));
            PhysicsDebugEntitiesTickInitializeSystem = AddSystem(new PhysicsDebugEntitiesTickInitializeSystem(worldEntitiesModel, physicsDebugEnabledModel, physicsDebugModel, new List<ICarModel>(), new List<IHelicopterModel>(), new List<IBattleCharacterModel>(), new List<IKinematicObjectsControllerModel>()));
            PhysicsDebugStartNewTickSystem = AddSystem(new PhysicsDebugStartNewTickSystem(physicsDebugEnabledModel, physicsDebugModel));
            PrepareMovedEntitiesSystem = AddSystem(new PrepareMovedEntitiesSystem(worldTickData));
            PrepareAlarmsToStartSystem = AddSystem(needSpawnBuildings, () => new PrepareAlarmsToStartSystem(worldTickData));
            BreakableColliderHitsClearSystem = AddSystem(new BreakableColliderHitsClearSystem());
            DoorBreakableColliderHitsClearSystem = AddSystem(new DoorBreakableColliderHitsClearSystem());
            ClearVigilantNpcCallMapMarksSystem = AddSystem(needSpawnBuildings, () => new ClearMapMarksSystem(vigilantNpcCallMarksModel));
            ClearWarpedFlagSystem = AddSystem(new ClearWarpedFlagSystem());
            CheckEventCargoUnderWaterSystem = AddSystem(hasRobberLocationEvents, () => new CheckEventCargoUnderWaterSystem(worldEntitiesModel.Cargos, robberLocationEventsModel, robberLocationEventModels, worldWaterZonesModel));
            RobberLocationEventsSystem = AddSystem(hasRobberLocationEvents, () => new LocationEventsSystem(robberLocationEventsModel, world));
            LocationEventStartSystem = AddSystem(hasRobberLocationEvents, () => new LocationEventStartSystem(robberLocationEventsModel));
            CraftWorkbenchSystem = AddSystem(needSpawnCraftWorkbench, () => new CraftWorkbenchSystem(worldEntitiesModel.CraftWorkbenches, locationCraftWorkbenchModel, grids[battleCharacterLayer], worldEntitiesModel));
            RemoveOrRespawnDeadPlayersSystem = AddSystem(new RemoveOrRespawnDeadPlayersSystem(respawnModel));
            UpdateChangedRoleCharactersSystem = AddSystem(hasRobbery, () => new UpdateChangedRoleCharactersSystem(changedRoleCharacters, worldEntitiesModel.BattleCharacters));
            RespawnSystem = AddSystem(new RespawnSystem(worldEntitiesModel, changeEquipModel, location, occupiedCharacterSlotsModel, plotsModel));
            TeleportSystem = AddSystem(new TeleportSystem(worldModel.Teleport, worldEntitiesModel.BattleCharacters));
            TeleportVehicleSystem = AddSystem(hasVehicles, () => new TeleportVehicleSystem(worldModel.Vehicles, worldModel.Teleport));
            ObserverPositionSystem = AddSystem(new ObserverPositionSystem(worldTickData));
            VehicleLeaveSystem = AddSystem(hasVehicles, () => new VehicleLeaveSystem(worldModel.Vehicles));
            CarParkingSystem = AddSystem(hasVehicles, () => new CarParkingSystem(physicsSceneModel));
            SuicideSystem = AddSystem(new SuicideSystem(world, deathWorldModel, globalMapVehicleModel, changeCharacterRoleModel));
            CheckPlayersCountToCloseRoomSystem = AddSystem(hasCheckPlayersCount, () => new CheckPlayersCountToCloseRoomSystem(stopByPlayersCountModel, analyticsModel));
            PlayerLifeTimerSystem = AddSystem(hasPlayerLifeTimer, () => new PlayerLifeTimerSystem(characterExitRequests, location.PersonalDisconnectTimer));
            MainMenuExitSystem = AddSystem(new MainMenuExitSystem(finishKnockoutModel, exitVehicleModel));
            LeaveSystem = AddSystem(new LeaveSystem(deathWorldModel, finishArrestModel, dropCargoModel, worldEntitiesModel, battleMode, location));
            FinishKnockoutSystem = AddSystem(hasKnockout, () => new FinishKnockoutSystem(finishKnockoutModel));
            BotUseHideObjectSystem = AddSystem(isCheatsEnabled, () => new BotUseHideObjectSystem(location));
            LeaveHideObjectSystem = AddSystem(new LeaveHideObjectSystem(worldEntitiesModel, hiddenCharacters));
            FinishUseHideObjectSystem = AddSystem(new FinishUseHideObjectSystem(worldEntitiesModel, analyticsModel, hiddenCharacters));
            FinishRevivalVigilantNpcSystem = AddSystem(needSpawnBuildings, () => new FinishRevivalVigilantNpcSystem(worldEntitiesModel.VigilantNpc, characterZoneModel, physicsSceneModel, worldEntitiesModel.BattleCharacters, copsAoeRewardModel, worldEntitiesModel.VigilantNpcBuildings, analyticsModel, changeCoinsModel));
            FinishRevivalCharacterSystem = AddSystem(hasKnockout, () => new FinishRevivalCharacterSystem(worldEntitiesModel.BattleCharacters, interruptModel, analyticsModel, parties));
            FinishSecurityCommandSystem = AddSystem(needSpawnBuildings, () => new FinishSecurityCommandSystem(worldBuildingsModel.BuildingDependEntities, changePoliceAttentionPointsModel, worldEntitiesModel.SecurityPanels, copsAoeRewardModel, analyticsModel, triggeredSecurityAlarmQueue, worldEntitiesModel.LocationSecurityAlarms));
            StopHideSystem = AddSystem(new StopHideSystem(worldEntitiesModel, hiddenCharacters));
            FinishColliderBreakingSystem = AddSystem(new FinishColliderBreakingSystem());
            ColliderBreakingSystem = AddSystem(new ColliderBreakingSystem(worldEntitiesModel.BreakableColliderModels, physicsBodiesModel, physicsSceneModel));
            TeleportOutHideObjectSystem = AddSystem(new TeleportOutHideObjectSystem(worldModel.Teleport, worldModel.SafeZone, worldEntitiesModel, grids[battleCharacterLayer], hiddenCharacters, interruptModel));
            UpdateRigidBodyEnabledSystem = AddSystem(new UpdateRigidBodyEnabledSystem(physicsSceneModel));
            TeleportInHideObjectSystem = AddSystem(new TeleportInHideObjectSystem(worldModel.Teleport, worldEntitiesModel, grids[battleCharacterLayer], hiddenCharacters));
            ClearStartedSecurityAlarmSystem = AddSystem(needSpawnBuildings, () => new ClearStartedSecurityAlarmSystem(startedSecurityAlarmQueue, unlockingSafeDoorModel, worldEntitiesModel.SecurityAlarmBuildings));
            ClearSecurityAlarmLocationSystem = AddSystem(needSpawnBuildings, () => new ClearSecurityAlarmLocationSystem(securityAlarmLocationQueue, locationSecurityAlarmsModel));
            StopFireSystem = AddSystem(canShoot, () => new StopFireSystem());
            FinishAimStateSystem = AddSystem(canShoot, () => new FinishAimStateSystem());
            FinishReloadSystem = AddSystem(canShoot, () => new FinishReloadSystem());
            StopInteractSystem = AddSystem(new StopInteractSystem(worldEntitiesModel));
            StopSprintTransitionSystem = AddSystem(new StopSprintTransitionSystem());
            StopCrouchTransitionSystem = AddSystem(new StopCrouchTransitionSystem());
            FinishUseItemSystem = AddSystem(canTakeItemInHands, () => new FinishUseItemSystem(battleSystemsAnalytics));
            FinishThrowItemSystem = AddSystem(canTakeItemInHands, () => new FinishThrowItemSystem());
            FinishInventoryUseItemSystem = AddSystem(canUseInventory, () => new FinishInventoryUseItemSystem(battleSystemsAnalytics));
            TimeEffectsSystem = AddSystem(new TimeEffectsSystem());
            FinishQuickSlotSelectionSystem = AddSystem(canTakeItemInHands, () => new FinishQuickSlotSelectionSystem());
            FinishUnlockSystem = AddSystem(new FinishUnlockSystem(worldEntitiesModel, worldBuildingsModel.BuildingDependEntities, changePoliceAttentionPointsModel, changeRobberQuestProgressModel, analyticsModel, worldSecurityAlarmEntitiesModel.SecurityAlarmsByDependEntities, worldTickData, worldEntitiesModel.LocationSecurityAlarms, saveController, vehicleCargoInventoryChangeModel, changeVehicleTrunkEmptyFlagModel, hangarOwnerModel, partyMemberAccessModel, occupiedCharacterSlotsModel, clanBattleCharacterTeamModel, interruptModel, plotsModel, robberLocationEventModels));
            FinishEmotionSystem = AddSystem(new FinishEmotionSystem());
            FinishTakeLootObjectSystem = AddSystem(needSpawnBuildings, () => new FinishTakeLootObjectSystem(worldEntitiesModel, itemActionsController, changePoliceAttentionPointsModel, checkTakeLootObjectModel, changeRobberQuestProgressModel, analyticsModel, battleMode, location, worldSecurityAlarmEntitiesModel.SecurityAlarmsByDependEntities, worldTickData));
            FinishTakeLootNodeSystem = AddSystem(needSpawnBuildings, () => new FinishTakeLootNodeSystem(worldEntitiesModel.LootNodes, worldEntitiesModel.LootNodeBuildings, itemActionsController, changePoliceAttentionPointsModel, changeRobberQuestProgressModel, analyticsModel, battleMode, location));
            FinishTakeCashSystem = AddSystem(new FinishTakeCashSystem(worldEntitiesModel, saveController, plotsModel, settlementBonusModel, analyticsModel));
            FinishHandcuffSystem = AddSystem(hasRobbery, () => new FinishHandcuffSystem(worldEntitiesModel.BattleCharacters, occupiedCharacterSlotsModel, interruptModel, analyticsModel));
            FinishArrestSystem = AddSystem(hasRobbery, () => new FinishArrestSystem(finishArrestModel, analyticsModel));
            FinishInterruptArrestingSystem = AddSystem(hasRobbery, () => new FinishInterruptArrestingSystem(worldEntitiesModel.BattleCharacters, changePoliceAttentionPointsModel, interruptModel, analyticsModel, parties));
            FinishArrestGettingUpSystem = AddSystem(hasRobbery, () => new FinishArrestGettingUpSystem());
            FinishRefillEquipSystem = AddSystem(hasRobbery, () => new FinishRefillEquipSystem(refillCopEquipModel, worldEntitiesModel.Vehicles, worldEntitiesModel.Buildings, worldEntitiesModel.BreachEntities, analyticsModel));
            CloseOpenedObjectOnStopRefillEquipSystem = AddSystem(hasRobbery, () => new CloseOpenedObjectOnStopRefillEquipSystem(worldEntitiesModel.Buildings, worldEntitiesModel.Vehicles));
            FinishArrestLootSystem = AddSystem(hasRobbery, () => new FinishArrestLootSystem(copsAoeRewardModel, world, lootModels, cargoSpawnModel, vehicleLootSpawnModel, worldEntitiesModel, analyticsModel));
            FinishTakeCargoSystem = AddSystem(isCargoEnabled, () => new FinishTakeCargoSystem(worldEntitiesModel, world, cargoSpawnModel, saveController, analyticsModel, vehicleCargoInventoryChangeModel, vehicleLootSpawnModel, furnitureLootSpawnModel, buildingLootSpawnModel, plotsModel, hangarOwnerModel, parties, battleMode, clanBattleCharacterTeamModel, occupiedCharacterSlotsModel, robberLocationEventsModel, robberLocationEventModels.SelectedCargoDeliveryPointsModel, robberLocationEventModels.CargoStateModel));
            FinishSetDrillSystem = AddSystem(hasRobbery, () => new FinishSetDrillSystem(worldEntitiesModel, analyticsModel));
            FinishRemoveDrillSystem = AddSystem(hasRobbery, () => new FinishRemoveDrillSystem(worldEntitiesModel.SafeDoors, copsAoeRewardModel, analyticsModel));
            FinishRepairBreachSystem = AddSystem(new FinishRepairBreachSystem(worldEntitiesModel, changeVehicleTrunkEmptyFlagModel, hangarOwnerModel, occupiedCharacterSlotsModel));
            FinishChargeCumulativeItemSystem = AddSystem(needSpawnBuildings, () => new FinishChargeCumulativeItemSystem(worldEntitiesModel.CumulativeChargers, worldEntitiesModel.CumulativeChargerBuildings, analyticsModel));
            FinishRaidDefenseBlockSystem = AddSystem(isRaidAvailable, () => new FinishRaidDefenseBlockSystem(worldEntitiesModel, hangarOwnerModel, partyMemberAccessModel, analyticsModel, plotsModel));
            FinishRepairDefenseBlockSystem = AddSystem(isRaidAvailable, () => new FinishRepairDefenseBlockSystem(worldEntitiesModel, hangarOwnerModel));
            StopBecomeRobberSystem = AddSystem(hasRobbery, () => new StopBecomeRobberSystem());
            KnockdownSystem = AddSystem(new KnockdownSystem(worldModel.Teleport, worldModel.SafeZone, physicsBodiesModel, physicsSceneModel));
            StopMeleeAttackSystem = AddSystem(canShoot, () => new StopMeleeAttackSystem());
            ClearAfterKnockoutDelaySystem = AddSystem(hasKnockout, () => new ClearAfterKnockoutDelaySystem());

            WeaponAutoReloadSystem = AddSystem(canShoot, () => new WeaponAutoReloadSystem());
            HealthAutoRestoreSystem = AddSystem(new HealthAutoRestoreSystem());
            RobberQuestAutoCompleteSystem = AddSystem(hasRobbery, () => new RobberQuestAutoCompleteSystem(parties, location, worldEntitiesModel.LocationBuildings));
            ContextMenuUseSystem = AddSystem(canUseInventory, () => new ContextMenuUseSystem());
            PlotSpawnSystem = AddSystem(new PlotSpawnSystem(world, plotConstructModel, plotConstructionStepsModel, worldModel, vehicleSpawnModel, vehicleLootSpawnModel, occupiedCharacterSlotsModel, loggers, plotVehicleController, globalMapVehicleModel, plotsToSpawnModel));
            PlotConstructionSystem = AddSystem(new PlotConstructionSystem(plotConstructionStepsModel, initTickScore));
            ResetBuildingSystem = AddSystem(needSpawnBuildings, () => new ResetBuildingSystem(worldModel, worldEntitiesModel, triggeredSecurityAlarmQueue, startedSecurityAlarmQueue, unlockingSafeDoorModel, analyticsModel, hiddenCharacters, worldTickData));
            ResetLocationBreakableCollidersSystem = AddSystem(hasLocationBreakableColliders, () => new ResetLocationBreakableCollidersSystem(worldEntitiesModel, locationBreakableCollidersResetModels, physicsSceneModel, physicsBodiesModel));
            UnlockSafeDoorSystem = AddSystem(needSpawnBuildings, () => new UnlockSafeDoorSystem(worldEntitiesModel.KinematicObjectsControllers, worldEntitiesModel.SafeDoorBuildings, clientNotificationsModel, unlockingSafeDoorModel, triggeredSecurityAlarmQueue, startedSecurityAlarmQueue, securityAlarmLocationQueue));
            BotMovementSystem = AddSystem(isCheatsEnabled, () => new BotMovementSystem());
            BotDriveCarSystem = AddSystem(isCheatsEnabled, () => new BotDriveCarSystem(worldEntitiesModel.Cars));
            BotDriveHelicopterSystem = AddSystem(isCheatsEnabled, () => new BotDriveHelicopterSystem(worldEntitiesModel.Helicopters));
            BotSprintSystem = AddSystem(isCheatsEnabled, () => new BotSprintSystem());
            BotLookDirectionXSystem = AddSystem(isCheatsEnabled, () => new BotLookDirectionXSystem());
            BotLookDirectionYSystem = AddSystem(isCheatsEnabled, () => new BotLookDirectionYSystem());
            BotSwapWeaponSystem = AddSystem(isCheatsEnabled, () => new BotSwapWeaponSystem());
            BotSelfHealingSystem = AddSystem(isCheatsEnabled, () => new BotSelfHealingSystem());
            BotArmorUpSystem = AddSystem(isCheatsEnabled, () => new BotArmorUpSystem());
            BotThrowingItemSystem = AddSystem(isCheatsEnabled, () => new BotThrowingItemSystem());
            BotSelectWeaponSystem = AddSystem(isCheatsEnabled, () => new BotSelectWeaponSystem());
            BotFireSystem = AddSystem(isCheatsEnabled, () => new BotFireSystem());
            BotAimingSystem = AddSystem(isCheatsEnabled, () => new BotAimingSystem());
            BotRestoreAmmoSystem = AddSystem(isCheatsEnabled, () => new BotRestoreAmmoSystem());
            BotCrouchSystem = AddSystem(isCheatsEnabled, () => new BotCrouchSystem());
            BotJumpSystem = AddSystem(isCheatsEnabled, () => new BotJumpSystem());
            BotMeleeAttackSystem = AddSystem(isCheatsEnabled, () => new BotMeleeAttackSystem());
            BotSurrenderSystem = AddSystem(isCheatsEnabled, () => new BotSurrenderSystem());
            BotSurrenderCommandSystem = AddSystem(isCheatsEnabled, () => new BotSurrenderCommandSystem());
            BotKnockdownSystem = AddSystem(isCheatsEnabled, () => new BotKnockdownSystem());
            BotEmotionSystem = AddSystem(isCheatsEnabled, () => new BotEmotionSystem());
            BotReviveCharacterSystem = AddSystem(isCheatsEnabled, () => new BotReviveCharacterSystem(worldEntitiesModel));
            BotRemovingHandcuffsSystem = AddSystem(isCheatsEnabled, () => new BotRemovingHandcuffsSystem(worldEntitiesModel));
            BotHandcuffingSystem = AddSystem(isCheatsEnabled, () => new BotHandcuffingSystem(worldEntitiesModel));
            BotInputSystem = AddSystem(isCheatsEnabled, () => new BotInputSystem());
            BotSirenSwitchSystem = AddSystem(isCheatsEnabled, () => new BotSirenSwitchSystem(worldEntitiesModel.Cars));
            BotEnterLeaveVehicleSystem = AddSystem(isCheatsEnabled, () => new BotEnterLeaveVehicleSystem(worldEntitiesModel.Cars));

            SetInputSystem = AddSystem(new SetInputSystem());
            CarControllerPreparePhysicsForTickSystem = AddSystem(hasVehicles, () => new CarControllerPreparePhysicsForTickSystem(physicsSceneModel));
            HelicopterControllerPreparePhysicsForTickSystem = AddSystem(hasVehicles, () => new HelicopterControllerPreparePhysicsForTickSystem(physicsSceneModel));
            CarControllerControlOwnerSystem = AddSystem(hasVehicles, () => new CarControllerControlOwnerSystem(worldEntitiesModel.BattleCharacters));
            HelicopterControllerControlOwnerSystem = AddSystem(hasVehicles, () => new HelicopterControllerControlOwnerSystem(worldEntitiesModel.BattleCharacters));
            CarControllerClearInputSystem = AddSystem(hasVehicles, () => new CarControllerClearInputSystem());
            CharacterControllerStateInAirSystem = AddSystem(new CharacterControllerStateInAirSystem());
            CharacterControllerGroundCheckSystem = AddSystem(new CharacterControllerGroundCheckSystem(physicsSceneModel, tickDeltaTime, sphereCastModel, physicsDebugEnabledModel, physicsDebugModel));
            CharacterControllerStateByGroundSystem = AddSystem(new CharacterControllerStateByGroundSystem());
            ApplyInputSystem = AddSystem(new ApplyInputSystem(snapshotsHistorySize, location, interruptModel, battleMode, dropCargoModel, crouchStandUpModel, worldModel.Vehicles));
            CarControllerApplyCarInputByDriverSystem = AddSystem(hasVehicles, () => new CarControllerApplyCarInputByDriverSystem(physicsSceneModel));
            CarControllerApplyCarInputByParkingSystem = AddSystem(hasVehicles, () => new CarControllerApplyCarInputByParkingSystem(physicsSceneModel));
            CarControllerApplyCarInputByEmptySystem = AddSystem(hasVehicles, () => new CarControllerApplyCarInputByEmptySystem());
            HelicopterControllerApplyHelicopterInputByDriverSystem = AddSystem(hasVehicles, () => new HelicopterControllerApplyHelicopterInputByDriverSystem(physicsSceneModel));
            HelicopterControllerApplyHelicopterInputByEmptySystem = AddSystem(hasVehicles, () => new HelicopterControllerApplyHelicopterInputByEmptySystem());
            DropCargoSystem = AddSystem(isCargoEnabled, () => new DropCargoSystem(dropCargoModel));
            StartBecomeRobberPartySystem = AddSystem(hasRobbery, () => new StartBecomeRobberPartySystem(parties, worldEntitiesModel, interruptModel, robberQuestInitModel));
            StartBecomeRobberSystem = AddSystem(hasRobbery, () => new StartBecomeRobberSystem(parties, characterRolesMetric, robberQuestInitModel, changeRoleTimeModel, worldModel.Vehicles, worldEntitiesModel.VehicleTrunks, analyticsModel, rolesCountModel));
            SurrenderSystem = AddSystem(hasRobbery, () => new SurrenderSystem(dropCargoModel, analyticsModel));
            SurrenderCommandSystem = AddSystem(hasRobbery, () => new SurrenderCommandSystem());
            InteractSystem = AddSystem(new InteractSystem(worldEntitiesModel, interactRaycastModel));
            UseEntityRequestHandleSystem = AddSystem(new UseEntityRequestHandleSystem(worldEntitiesModel, hangarOwnerModel, partyMemberAccessModel, battleMode));
            UseSwitchSystem = AddSystem(new UseSwitchSystem(triggersEventsModel, worldEntitiesModel.PlotSlotByEntity, hangarOwnerModel, worldEntitiesModel, partyMemberAccessModel));
            KinematicObjectsControllerStateSystem = AddSystem(new KinematicObjectsControllerStateSystem(triggersEventsModel, analyticsModel, saveController, physicsSceneModel, worldTickData, physicsDebugEnabledModel, physicsDebugModel, tickDeltaTime));
            VehicleControllerPhysicsAwakeObserverSystem = AddSystem(hasVehicles, () => new VehicleControllerPhysicsAwakeObserverSystem(physicsSceneModel));
            PhysicsGravitySystem = AddSystem(new PhysicsGravitySystem(physicsSceneModel));
            CarControllerDownForceSystem = AddSystem(hasVehicles, () => new CarControllerDownForceSystem(physicsSceneModel));
            CarControllerMovementStateSystem = AddSystem(hasVehicles, () => new CarControllerMovementStateSystem(physicsSceneModel));
            CarSteeringSystem = AddSystem(hasVehicles, () => new CarSteeringSystem(tickDeltaTime));
            VehicleEnterSystem = AddSystem(hasVehicles, () => new VehicleEnterSystem(parties, interruptModel, worldModel.Vehicles, battleMode, clanBattleCharacterTeamModel, occupiedCharacterSlotsModel));
            VehicleLeaveRequestSystem = AddSystem(hasVehicles, () => new VehicleLeaveRequestSystem());
            UnlockSystem = AddSystem(new UnlockSystem(worldEntitiesModel, interruptModel, hangarOwnerModel, partyMemberAccessModel, occupiedCharacterSlotsModel, clanBattleCharacterTeamModel, robberLocationEventModels));
            StartEmotionSystem = AddSystem(new StartEmotionSystem(worldModel.WorldInterruptModel, battleSystemsAnalytics));
            StartArrestLootSystem = AddSystem(hasRobbery, () => new StartArrestLootSystem(interruptModel, worldModel.WorldEntitiesModel));
            StartUseHideObjectSystem = AddSystem(new StartUseHideObjectSystem());
            StartSecurityCommandSystem = AddSystem(needSpawnBuildings, () => new StartSecurityCommandSystem(worldEntitiesModel.SecurityPanels, interruptModel));
            StartRevivalVigilantNpcSystem = AddSystem(needSpawnBuildings, () => new StartRevivalVigilantNpcSystem(worldEntitiesModel.VigilantNpc));
            StartRevivalCharacterSystem = AddSystem(hasKnockout, () => new StartRevivalCharacterSystem(worldEntitiesModel.BattleCharacters, interruptModel, clanBattleCharacterTeamModel, battleMode));
            StartColliderBreakingSystem = AddSystem(new StartColliderBreakingSystem(worldEntitiesModel.BreakableColliderModels));
            StartRefillEquipSystem = AddSystem(hasRobbery, () => new StartRefillEquipSystem(worldEntitiesModel.Buildings, worldEntitiesModel.Vehicles, worldEntitiesModel.BreachEntities));
            TakeLootObjectSystem = AddSystem(needSpawnBuildings, () => new TakeLootObjectSystem(worldEntitiesModel, interruptModel, checkTakeLootObjectModel));
            TakeLootNodeSystem = AddSystem(needSpawnBuildings, () => new TakeLootNodeSystem(worldEntitiesModel.LootNodes, itemActionsController));
            TakeCashSystem = AddSystem(new TakeCashSystem(worldEntitiesModel, settlementBonusModel));
            ChargeCumulativeItemSystem = AddSystem(needSpawnBuildings, () => new ChargeCumulativeItemSystem(worldEntitiesModel.CumulativeChargers));
            RaidDefenseBlockSystem = AddSystem(isRaidAvailable, () => new RaidDefenseBlockSystem(worldEntitiesModel, hangarOwnerModel, partyMemberAccessModel));
            RepairDefenseBlockSystem = AddSystem(isRaidAvailable, () => new RepairDefenseBlockSystem(worldEntitiesModel, hangarOwnerModel));
            StartHandcuffSystem = AddSystem(hasRobbery, () => new StartHandcuffSystem(worldEntitiesModel.BattleCharacters, interruptModel, copsAoeFindModel, crouchStandUpModel));
            StartInterruptArrestingSystem = AddSystem(hasRobbery, () => new StartInterruptArrestingSystem(worldEntitiesModel.BattleCharacters, interruptModel));
            TakeCargoSystem = AddSystem(isCargoEnabled, () => new TakeCargoSystem(worldEntitiesModel, interruptModel, crouchStandUpModel, robberLocationEventModels.EventModel));
            PutCargoInInventorySystem = AddSystem(isCargoEnabled, () => new PutCargoInInventorySystem(worldEntitiesModel, vehicleCargoInventoryChangeModel, hangarOwnerModel, locationCraftWorkbenchModel, saveController, analyticsModel, partyMemberAccessModel, occupiedCharacterSlotsModel, clanBattleCharacterTeamModel, robberLocationEventModels.SelectedCargoDeliveryPointsModel, robberLocationEventsModel, robberLocationEventModels.CargoStateModel));
            SetDrillSystem = AddSystem(hasRobbery, () => new SetDrillSystem(worldEntitiesModel.SafeDoors));
            RemoveDrillSystem = AddSystem(hasRobbery, () => new RemoveDrillSystem(worldEntitiesModel.SafeDoors));
            RepairBreachSystem = AddSystem(new RepairBreachSystem(interruptModel, worldEntitiesModel, hangarOwnerModel, occupiedCharacterSlotsModel));
            QuickSlotAutoSelectSystem = AddSystem(canTakeItemInHands, () => new QuickSlotAutoSelectSystem(battleMode, interruptModel));
            SelectQuickSlotSystem = AddSystem(canTakeItemInHands, () => new SelectQuickSlotSystem(dropCargoModel));
            CharacterControllerStartJumpSystem = AddSystem(new CharacterControllerStartJumpSystem(physicsSceneModel, sphereCastModel));
            CharacterControllerMovementTypeSystem = AddSystem(new CharacterControllerMovementTypeSystem(physicsDebugEnabledModel, physicsDebugModel));
            StartUseItemSystem = AddSystem(canTakeItemInHands, () => new StartUseItemSystem());
            StartThrowItemSystem = AddSystem(canTakeItemInHands, () => new StartThrowItemSystem());
            StartInventoryUseItemSystem = AddSystem(canUseInventory, () => new StartInventoryUseItemSystem());
            AimCooldownSystem = AddSystem(canShoot, () => new AimCooldownSystem());
            StartAimStateSystem = AddSystem(canShoot, () => new StartAimStateSystem());
            CrouchSystem = AddSystem(new CrouchSystem(crouchStandUpModel));
            AimModeSystem = AddSystem(canShoot, () => new AimModeSystem());
            ShotInterpolationSystem = AddSystem(canShoot, () => new ShotInterpolationSystem());
            LookDirectionSystem = AddSystem(new LookDirectionSystem());
            StartMeleeAttackSystem = AddSystem(canShoot, () => new StartMeleeAttackSystem());
            FireSystem = AddSystem(canShoot, () => new FireSystem());
            StartBattleStanceSystem = AddSystem(canShoot, () => new StartBattleStanceSystem());
            StartArmedSystem = AddSystem(canShoot, () => new StartArmedSystem());
            FinishBattleStanceSystem = AddSystem(canShoot, () => new FinishBattleStanceSystem());
            FinishArmedSystem = AddSystem(canShoot, () => new FinishArmedSystem());
            CharacterControllerInputSystem = AddSystem(new CharacterControllerInputSystem(physicsDebugEnabledModel, physicsDebugModel));
            MoveDirectionSystem = AddSystem(new MoveDirectionSystem());
            StopSprintSystem = AddSystem(new StopSprintSystem());
            SprintSystem = AddSystem(new SprintSystem(interruptModel));
            DoorOpenCloseSystem = AddSystem(new DoorOpenCloseSystem(worldEntitiesModel.BattleCharacters, saveController));
            WindowBlindsOpenCloseSystem = AddSystem(new WindowBlindsOpenCloseSystem(worldEntitiesModel.BattleCharacters, saveController));
            DoorPhysicsStateSystem = AddSystem(new DoorPhysicsStateSystem(physicsSceneModel));
            CharacterControllerInputVelocitySystem = AddSystem(new CharacterControllerInputVelocitySystem(physicsDebugEnabledModel, physicsDebugModel));
            CharacterControllerExtraVerticalVelocitySystem = AddSystem(new CharacterControllerExtraVerticalVelocitySystem(tickDeltaTime, characterControllerVerticalExtraVelocityChangeEvents, physicsDebugEnabledModel, physicsDebugModel));
            CharacterControllerExtraHorizontalVelocitySystem = AddSystem(new CharacterControllerExtraHorizontalVelocitySystem(tickDeltaTime));
            CharacterControllerImpulseVelocitySystem = AddSystem(new CharacterControllerImpulseVelocitySystem());
            CharacterControllerColliderSystem = AddSystem(new CharacterControllerColliderSystem(physicsSceneModel));
            CharacterControllerApplyVelocitySystem = AddSystem(new CharacterControllerApplyVelocitySystem(physicsSceneModel));
            CharacterControllerStickToGroundSystem = AddSystem(new CharacterControllerStickToGroundSystem(physicsSceneModel));
            CharacterControllerForwardMovementSystem = AddSystem(new CharacterControllerForwardMovementSystem(physicsSceneModel));
            HelicopterEngineSystem = AddSystem(hasVehicles, () => new HelicopterEngineSystem());
            HelicopterControllerWheelGroundCheckSystem = AddSystem(hasVehicles, () => new HelicopterControllerWheelGroundCheckSystem(physicsSceneModel, physicsBodiesModel, physicsDebugEnabledModel, physicsDebugModel));
            HelicopterControllerWheelSuspensionSystem = AddSystem(hasVehicles, () => new HelicopterControllerWheelSuspensionSystem(physicsSceneModel, tickDeltaTime, physicsDebugEnabledModel, physicsDebugModel, triggersEventsModel, physicsBodiesModel));
            HelicopterControllerStateSystem = AddSystem(hasVehicles, () => new HelicopterControllerStateSystem());
            HelicopterControllerChassisSystem = AddSystem(hasVehicles, () => new HelicopterControllerChassisSystem(physicsSceneModel, tickDeltaTime));
            HelicopterControllerHandlingSystem = AddSystem(hasVehicles, () => new HelicopterControllerHandlingSystem(physicsSceneModel));
            CarControllerWheelGroundCheckSystem = AddSystem(hasVehicles, () => new CarControllerWheelGroundCheckSystem(physicsSceneModel, physicsBodiesModel, physicsDebugEnabledModel, physicsDebugModel));
            CarControllerStuckSurfaceSystem = AddSystem(hasVehicles, () => new CarControllerStuckSurfaceSystem(physicsSceneModel));
            CarControllerWheelSuspensionSystem = AddSystem(hasVehicles, () => new CarControllerWheelSuspensionSystem(physicsSceneModel, tickDeltaTime, physicsDebugEnabledModel, physicsDebugModel, triggersEventsModel, physicsBodiesModel));
            CarControllerDragSystem = AddSystem(hasVehicles, () => new CarControllerDragSystem(physicsSceneModel));
            CarControllerFlipSystem = AddSystem(hasVehicles, () => new CarControllerFlipSystem(physicsSceneModel, tickDeltaTime));
            CarControllerBurnoutSystem = AddSystem(hasVehicles, () => new CarControllerBurnoutSystem(physicsSceneModel));
            CarControllerTransmissionSystem = AddSystem(hasVehicles, () => new CarControllerTransmissionSystem(physicsSceneModel));
            CarControllerWheelMovementDataSystem = AddSystem(hasVehicles, () => new CarControllerWheelMovementDataSystem(physicsSceneModel, physicsDebugEnabledModel, physicsDebugModel));
            CarControllerWheelApplyInputSystem = AddSystem(hasVehicles, () => new CarControllerWheelApplyInputSystem(physicsSceneModel, tickDeltaTime, physicsDebugEnabledModel, physicsDebugModel));
            CarControllerWheelFrictionSlipVelocitiesSystem = AddSystem(hasVehicles, () => new CarControllerWheelFrictionSlipVelocitiesSystem(physicsDebugEnabledModel, physicsDebugModel));
            CarControllerWheelFrictionMaxPossibleForcesSystem = AddSystem(hasVehicles, () => new CarControllerWheelFrictionMaxPossibleForcesSystem(tickDeltaTime, physicsDebugEnabledModel, physicsDebugModel));
            CarControllerCalculateSlipParamsSystem = AddSystem(hasVehicles, () => new CarControllerCalculateSlipParamsSystem(physicsDebugEnabledModel, physicsDebugModel));
            CarControllerCombinedFrictionSystem = AddSystem(hasVehicles, () => new CarControllerCombinedFrictionSystem());
            CarControllerBlockedFrictionSystem = AddSystem(hasVehicles, () => new CarControllerBlockedFrictionSystem());
            CarControllerBurnoutFrictionSystem = AddSystem(hasVehicles, () => new CarControllerBurnoutFrictionSystem());
            CarControllerApplyFrictionSystem = AddSystem(hasVehicles, () => new CarControllerApplyFrictionSystem(physicsSceneModel));
            CarControllerDirtLevelSystem = AddSystem(hasVehicles, () => new CarControllerDirtLevelSystem(location.DirtyingLevelSettings));
            PhysicsSceneClearContactEventsSystem = AddSystem(new PhysicsSceneClearContactEventsSystem(physicsSceneModel));
            {
                PhysicsSubstepSystem = AddSystem(new SubstepSystem(GameSettings.PhysicsTicksCountPerGameTick, tickPeriod));
            
                PhysicsDebugStartNewSubTickSystem = PhysicsSubstepSystem.AddSystem(new PhysicsDebugStartNewSubTickSystem(physicsDebugEnabledModel, physicsDebugModel));
                CarControllerPreparePhysicsForSubTickSystem = PhysicsSubstepSystem.AddSystem(new CarControllerPreparePhysicsForSubTickSystem(physicsSceneModel));
                HelicopterControllerPreparePhysicsForSubTickSystem = PhysicsSubstepSystem.AddSystem(new HelicopterControllerPreparePhysicsForSubTickSystem(physicsSceneModel));
                VehicleControllerSuspensionLimitAddConstraintSystem = PhysicsSubstepSystem.AddSystem(new VehicleControllerSuspensionLimitAddConstraintSystem(physicsSceneModel));
                PhysicsSceneTickPrepareSystem = PhysicsSubstepSystem.AddSystem(new PhysicsSceneTickPrepareSystem(physicsSceneModel));
                PhysicsSceneTickContactsUpdateSystem = PhysicsSubstepSystem.AddSystem(new PhysicsSceneTickContactsUpdateSystem(physicsSceneModel, physicsBroadPhaseCallback, physicsNarrowPhaseCallback));
                RegisterSpecialCollisionsSystem = PhysicsSubstepSystem.AddSystem(new RegisterSpecialCollisionsSystem(physicsSceneModel, physicsBodiesModel, specialCollisionsModel));
                CarVsCarCollisionSystem = PhysicsSubstepSystem.AddSystem(new CarVsCarCollisionServerSystem(worldEntitiesModel.BattleCharacters, physicsSceneModel, specialCollisionsModel.CarVsCarCollisionsData));
                VehicleVsCharacterCollisionSystem = PhysicsSubstepSystem.AddSystem(new VehicleVsCharacterCollisionSystem(physicsSceneModel, specialCollisionsModel.VehicleVsCharacterCollisionsData, true));
                VehicleBreakCollidersSystem = PhysicsSubstepSystem.AddSystem(new VehicleBreakCollidersSystem(physicsSceneModel, worldBuildingsModel.BuildingDependEntities, specialCollisionsModel.VehicleVsBreakableColliderCollisionsData, specialCollisionsModel.VehicleVsDoorCollisionsData, locationBreakableCollidersResetModels));
                KinematicObjectObstacleSystem = PhysicsSubstepSystem.AddSystem(new KinematicObjectObstacleSystem(physicsSceneModel, specialCollisionsModel.KinematicVsCharacterCollisionData, specialCollisionsModel.KinematicVsVehicleCollisionData));
                PhysicsSceneTickContactsSolveSystem = PhysicsSubstepSystem.AddSystem(new PhysicsSceneTickContactsSolveSystem(physicsSceneModel));
                VehicleControllerSuspensionLimitClearConstraintSystem = PhysicsSubstepSystem.AddSystem(new VehicleControllerSuspensionLimitClearConstraintSystem(physicsSceneModel));
                PhysicsDebugCollectSubTickTransformSystem = PhysicsSubstepSystem.AddSystem(new PhysicsDebugCollectSubTickTransformSystem(physicsSceneModel, physicsDebugEnabledModel, physicsDebugModel));
            }
            KinematicObjectObstacleRevertMovementSystem = AddSystem(new KinematicObjectObstacleRevertMovementSystem(physicsSceneModel, saveController));
            BattleCharactersPhysicsContactEventsSystem = AddSystem(new BattleCharactersPhysicsContactEventsSystem(physicsSceneModel, physicsBodiesModel));
            UpdatePhysicsScenePositionSystem = AddSystem(new UpdatePhysicsScenePositionSystem(physicsSceneModel, worldTickData));
            UpdatePhysicsSceneVelocitySystem = AddSystem(new UpdatePhysicsSceneVelocitySystem());
            UpdateVehicleTransformFromPhysicsSceneSystem = AddSystem(hasVehicles, () => new UpdateVehicleTransformFromPhysicsSceneSystem(physicsSceneModel, worldTickData, .00001f));
            VehicleTrunkPositionSystem = AddSystem(hasVehicles, () => new VehicleTrunkPositionSystem(worldTickData));
            UpdateVehicleVelocitiesFromPhysicsSceneSystem = AddSystem(hasVehicles, () => new UpdateVehicleVelocitiesFromPhysicsSceneSystem(physicsSceneModel, .00001f));
            UpdateVehicleSleepFromPhysicsSceneSystem = AddSystem(hasVehicles, () => new UpdateVehicleSleepFromPhysicsSceneSystem(physicsSceneModel));
            CharacterInsideVehicleUpdateTransformSystem = AddSystem(hasVehicles, () => new CharacterInsideVehicleUpdateTransformSystem(worldModel.Vehicles, worldTickData));
            CharacterInsideVehicleUpdateVelocitySystem = AddSystem(hasVehicles, () => new CharacterInsideVehicleUpdateVelocitySystem());
            CharacterControllerAirObstaclesSystem = AddSystem(new CharacterControllerAirObstaclesSystem(physicsSceneModel, tickDeltaTime, characterControllerVerticalExtraVelocityChangeEvents, physicsDebugEnabledModel, physicsDebugModel));
            CharacterControllerStickToGroundClearSystem = AddSystem(new CharacterControllerStickToGroundClearSystem(physicsSceneModel));
            CharacterControllerForwardMovementClearSystem = AddSystem(new CharacterControllerForwardMovementClearSystem(physicsSceneModel));
            StartKnockdownSystem = AddSystem(new StartKnockdownSystem(interruptModel));
            TurningSystem = AddSystem(new TurningSystem());
            RotationPitchSystem = AddSystem(new RotationPitchSystem());
            RotationYawSystem = AddSystem(new RotationYawSystem(worldEntitiesModel, location));
            SkeletonAnimationSystem = AddSystem(canShoot, () => new SkeletonAnimationSystem(worldEntitiesModel, SkeletonDescription.Player.Skeleton));
            UpdateHitBoxesInfoSystem = AddSystem(canShoot, () => new UpdateHitBoxesInfoSystem(hitBoxesModel));
            ClearHitBoxesSystem = AddSystem(canShoot, () => new ClearHitBoxesSystem(hitBoxesModel));
            PrepareHitBoxIdSystem = AddSystem(canShoot, () => new PrepareHitBoxIdSystem());
            PlayerHitBoxSystem = AddSystem(canShoot, () => new HitBoxSystem(hitBoxesModel, SkeletonHitBoxDescription.Player, SkeletonDescription.Player.Skeleton));
            VehicleHitBoxSystem = AddSystem(canShoot, () => new VehicleHitBoxSystem(hitBoxesModel));
            ClearInterpolatedHitBoxesSystem = AddSystem(canShoot, () => new ClearInterpolatedHitBoxesSystem(interpolatedHitBoxes, worldTickData));
            CollectHitBoxesInterpolateTimesSystem = AddSystem(canShoot, () => new CollectHitBoxesInterpolateTimesSystem(worldTickData, snapshotsHistorySize));
            InterpolateHitBoxesSystem = AddSystem(canShoot, () => new ServerInterpolateHitBoxesSystem(hitBoxesModel, interpolatedHitBoxes, worldTickData));
            PhysicsSnapshotSystem = AddSystem(canShoot, () => new PhysicsSnapshotSystem(physicsSceneModel, physicsSnapshots));
            ThrowGrenadeSystem = AddSystem(canTakeItemInHands, () => new ThrowGrenadeSystem(world, occupiedCharacterSlotsModel, grenadesQueue, analyticsModel));
            GrenadeFlySystem = AddSystem(canTakeItemInHands, () => new GrenadeFlySystem(physicsSceneModel, hitBoxesModel, raycastModel, worldTickData, worldEntitiesModel, physicsBodiesModel));
            GrenadeTriggerTimerRequestSystem = AddSystem(canTakeItemInHands, () => new GrenadeTriggerTimerRequestSystem(grenadesHeavyEffectsQueue, grenadesExpectedHeavyEffectsCounter));
            GrenadeTriggerEffectSystem = AddSystem(canTakeItemInHands, () => new GrenadeTriggerEffectSystem(physicsSceneModel, hitBoxesModel, raycastModel, physicsBodiesModel, worldEntitiesModel, hitModel, grenadesHeavyEffectsQueue, grenadesLightEffectsQueue, changePoliceAttentionPointsModel, analyticsModel));
            GrenadeRemoveSystem = AddSystem(canTakeItemInHands, () => new GrenadeRemoveSystem(world));
            GrenadeBlindEffectOnCharacterSystem = AddSystem(canTakeItemInHands, () => new GrenadeBlindEffectOnCharacterSystem());
            FallingEntitySimpleDotPhysicsSystem = AddSystem(isCharacterLootEnabled || isCargoEnabled || hasVehicles, () => new FallingEntitySimpleDotPhysicsSystem(physicsSceneModel, hitBoxesModel, raycastModel, worldEntitiesModel, physicsBodiesModel));FallingEntitySimpleDotPhysicsSystem = AddSystem(new FallingEntitySimpleDotPhysicsSystem(physicsSceneModel, hitBoxesModel, raycastModel, worldEntitiesModel, physicsBodiesModel));
            WeaponSpreadArgsSystem = AddSystem(canShoot, () => new WeaponSpreadArgsSystem());
            ShotArgsSystem = AddSystem(canShoot, () => new ShotArgsSystem(physicsSceneModel, hitBoxesModel, physicsSnapshots, raycastModel, interpolatedHitBoxes, sphereCastModel));
            ShotSystem = AddSystem(canShoot, () => new ShotSystem(physicsSceneModel, hitBoxesModel, physicsSnapshots, raycastModel, changePoliceAttentionPointsModel, interpolatedHitBoxes, hitModel));
            MeleeAttackHitSystem = AddSystem(canShoot, () => new MeleeAttackHitSystem(physicsSceneModel, physicsSnapshots, hitBoxesModel, raycastModel, hitModel));
            SelfDamageSystem = AddSystem(new SelfDamageSystem(selfDamageRequests, worldEntitiesModel, hitModel));
            KillUnderWaterSystem = AddSystem(new KillUnderWaterSystem(physicsSceneModel, deathWorldModel, worldWaterZonesModel, worldModel.Vehicles, worldEntitiesModel, battleMode, location));
            KillUnderLocationSystem = AddSystem(new KillUnderLocationSystem(battleMode, location, worldTickData, worldEntitiesModel, deathWorldModel, occupiedCharacterSlotsModel, loggers));
            CharacterControllerVerticalExtraVelocityChangedSystem = AddSystem(new CharacterControllerVerticalExtraVelocityChangedSystem(physicsBodiesModel, characterControllerVerticalExtraVelocityChangeEvents));
            FallDamageTrackSystem = AddSystem(new FallDamageTrackSystem(physicsDebugEnabledModel, physicsDebugModel));
            FallDamageSystem = AddSystem(new FallDamageSystem(hitModel));
            BreakableColliderShotSystem = AddSystem(new BreakableColliderShotSystem(physicsSceneModel, worldBuildingsModel.BuildingDependEntities, locationBreakableCollidersResetModels));
            SecurityAlarmOnColliderBreakSystem = AddSystem(needSpawnBuildings, () => new SecurityAlarmOnColliderBreakSystem(worldEntitiesModel, worldTickData));
            ChangeAttentionsOnColliderBreakSystem = AddSystem(needSpawnBuildings, () => new ChangeAttentionsOnColliderBreakSystem(changePoliceAttentionPointsModel, worldModel.Event, changeRobberQuestProgressModel, worldBuildingsModel.BuildingDependEntities, analyticsModel));
            DoorBreakableColliderShotSystem = AddSystem(new DoorBreakableColliderShotSystem(physicsSceneModel, worldBuildingsModel.BuildingDependEntities));
            SecurityAlarmOnDoorColliderBreakSystem = AddSystem(needSpawnBuildings, () => new SecurityAlarmOnDoorColliderBreakSystem(worldEntitiesModel, worldTickData));
            StopActionsOnDamageSystem = AddSystem(new StopActionsOnDamageSystem());
            StopActionsOnMoveSystem = AddSystem(new StopActionsOnMoveSystem(worldTickData));
            CameraProgressSystem = AddSystem(needSpawnBuildings, () => new CameraProgressSystem(worldEntitiesModel, characterZoneModel));
            VigilantNpcCriminalZoneSystem = AddSystem(needSpawnBuildings, () => new VigilantNpcCriminalZoneSystem(characterZoneModel));
            VigilantNpcProgressSystem = AddSystem(needSpawnBuildings, () => new VigilantNpcProgressSystem(vigilantNpcCallMarksModel, worldEntitiesModel.BattleCharacters, worldEntitiesModel.VigilantNpcBuildings, analyticsModel));
            VigilantNpcMeleeHitSystem = AddSystem(needSpawnBuildings, () => new VigilantNpcMeleeHitSystem(worldEntitiesModel, physicsBodiesModel, changePoliceAttentionPointsModel, changeRobberQuestProgressModel, analyticsModel));
            VigilantNpcShotSystem = AddSystem(needSpawnBuildings, () => new VigilantNpcShotSystem(worldEntitiesModel, physicsBodiesModel, changePoliceAttentionPointsModel, physicsSceneModel, changeRobberQuestProgressModel, analyticsModel));
            VigilantNpcThreatenSystem = AddSystem(needSpawnBuildings, () => new VigilantNpcThreatenSystem(worldEntitiesModel, changeRobberQuestProgressModel, analyticsModel));
            CameraTriggersAlarmSystem = AddSystem(needSpawnBuildings, () => new CameraTriggersAlarmSystem(worldEntitiesModel, worldTickData, analyticsModel));
            CameraShotSystem = AddSystem(needSpawnBuildings, () => new CameraShotSystem(worldEntitiesModel.BattleCharacters, physicsBodiesModel, changePoliceAttentionPointsModel, analyticsModel));
            TriggerSecurityAlarmSystem = AddSystem(needSpawnBuildings, () => new TriggerSecurityAlarmSystem(worldTickData, triggeredSecurityAlarmQueue, worldEntitiesModel.SecurityAlarms));
            StartSecurityAlarmSystem = AddSystem(needSpawnBuildings, () => new StartSecurityAlarmSystem(triggeredSecurityAlarmQueue, startedSecurityAlarmQueue, securityAlarmLocationQueue));
            ChangeAttentionsOnDoorColliderBreakSystem = AddSystem(needSpawnBuildings, () => new ChangeAttentionsOnDoorColliderBreakSystem(changePoliceAttentionPointsModel, worldModel.Event, changeRobberQuestProgressModel, worldBuildingsModel.BuildingDependEntities, analyticsModel));
            WeaponSpreadSystem = AddSystem(canShoot, () => new WeaponSpreadSystem());
            ApplyHitsModelSystem = AddSystem(new ApplyHitsModelSystem());
            WeaponRoundsSystem = AddSystem(canShoot, () => new WeaponRoundsSystem());
            StartReloadSystem = AddSystem(canShoot, () => new StartReloadSystem());
            ExitOnLocationBorderSystem = AddSystem(new ExitOnLocationBorderSystem(location.Exits.BorderExit, worldEntitiesModel, finishExitModel, analyticsModel, exitVehicleModel, worldModel.Vehicles, parties));
            ExitCharactersOnClanBattleEndSystem = AddSystem(isClanBattle, () => new ExitCharactersOnClanBattleEndSystem(clanBattleModel.Phase, worldEntitiesModel, characterExitRequests));
            CharacterExitRequestSystem = AddSystem(new CharacterExitRequestsSystem(characterExitRequests, finishExitModel, worldEntitiesModel.BattleCharacters, exitVehicleModel));
            CaptureBuildingSystem = AddSystem(needSpawnBuildings, () => new CaptureBuildingSystem(worldTickData, worldEntitiesModel.Buildings));
            PartyCaptureBuildingSystem = AddSystem(needSpawnBuildings, () => new PartyCaptureBuildingSystem(parties, worldEntitiesModel));
            RobberQuestSystem = AddSystem(hasRobbery, () => new RobberQuestSystem(robberQuestTaskGenerateModel, analyticsModel, changeCoinsModel, worldEntitiesModel));
            PartyRobberQuestSystem = AddSystem(hasRobbery, () => new PartyRobberQuestSystem(parties, robberQuestTaskGenerateModel, worldEntitiesModel.BattleCharacters, analyticsModel, changeCoinsModel));
            RemoveArrestedPlayersSystem = AddSystem(hasRobbery, () => new RemoveArrestedPlayersSystem());
            ClanBattleFlagCaptureSystem = AddSystem(isClanBattle, () => new ClanBattleFlagCaptureSystem(worldEntitiesModel, clanBattleModel, physicsSceneModel, physicsBodiesModel, location, occupiedCharacterSlotsModel));
            ClanBattlePhaseSystem = AddSystem(isClanBattle, () => new ClanBattlePhaseSystem(clanBattleChangePhaseModel));
            SpawnLootSystem = AddSystem(isCharacterLootEnabled, () => new SpawnLootSystem(world, lootModels, worldEntitiesModel, analyticsModel));
            DeathUpdateInventorySystem = AddSystem(new DeathUpdateInventorySystem(initEquipModel, plotsModel, occupiedCharacterSlotsModel, worldEntitiesModel));
            MetalDetectorCheckSystem = AddSystem(new MetalDetectorCheckSystem(physicsSceneModel, physicsBodiesModel, worldEntitiesModel));
            ChangeRoleOnRemoveSystem = AddSystem(hasRobbery, () => new ChangeRoleOnRemoveSystem(world, changeCharacterRoleModel));
            ChangeCharacterPoliceAttentionLevelSystem = AddSystem(hasRobbery, () => new ChangeCharacterPoliceAttentionLevelSystem(occupiedCharacterSlotsModel, parties, allRolesVisibleRobbersModel, clientNotificationsModel));
            CarSirenSystem = AddSystem(hasVehicles, () => new CarSirenSystem());
            ExitLocationInPartyVehicleSystem = AddSystem(hasVehicles, () => new ExitLocationInPartyVehicleSystem(parties, worldEntitiesModel, occupiedCharacterSlotsModel));
            RemovePlayerSystem = AddSystem(new RemovePlayerSystem(leftPlayers, world, worldModel, vehicleSpawnModel, changeRoleTimeModel, globalMapVehicleModel, analyticsModel, occupiedCharacterSlotsModel, clanBattleModel, battleMode, plotVehicleSpawnerModel, vehicleLootSpawnModel, plotsModel, hiddenCharacters, parties, plotVehicleController, robberLocationEventsModel, robberLocationEventModels.CargoStateModel));
            RemoveLootSystem = AddSystem(isCharacterLootEnabled, () => new RemoveLootSystem(world, lootModels, analyticsModel, worldEntitiesModel));
            RemoveVehicleLootSystem = AddSystem(hasVehicles, () => new RemoveVehicleLootSystem(world, vehicleLootSpawnModel, analyticsModel, worldEntitiesModel));
            RemoveFurnitureLootSystem = AddSystem(new RemoveFurnitureLootSystem(world, furnitureLootSpawnModel, worldEntitiesModel));
            RemoveBuildingLootSystem = AddSystem(new RemoveBuildingLootSystem(world, buildingLootSpawnModel));
            RemoveCargoSystem = AddSystem(isCargoEnabled, () => new RemoveCargoSystem(world, cargoSpawnModel));
            ObservableInventoryDistanceCheckSystem = AddSystem(new ObservableInventoryDistanceCheckSystem(worldEntitiesModel, worldTickData, changeObservableInventoryModel));
            StopObserveInventorySystem = AddSystem(new StopObserveInventorySystem(changeObservableInventoryModel));
            ClearMoveInputSystem = AddSystem(new ClearMoveInputSystem());
            ClearLookDirectionInputSystem = AddSystem(new ClearLookDirectionInputSystem());
            ClearEventsSystem = AddSystem(new ClearEventsSystem());
            ClearShotEventsSystem = AddSystem(canShoot, () => new ClearShotEventsSystem());
            ClearBreakableColliderBreakEventsSystem = AddSystem(new ClearBreakableColliderBreakEventsSystem());
            ClearInputSystem = AddSystem(new ClearInputSystem());
            ClearBattleCharacterVehiclesInputSystem = AddSystem(hasVehicles, () => new ClearBattleCharacterVehiclesInputSystem());
            ClearCrouchStandUpCheckResultsSystem = AddSystem(new ClearCrouchStandUpCheckResultsSystem(crouchStandUpModel));
            CharacterGridSystem = AddSystem(new CharacterGridSystem(grids[battleCharacterLayer]));
            CharacterUpdateGridSystem = AddSystem(new UpdateGridSystem(grids[battleCharacterLayer], worldTickData));
            VehicleUpdateGridSystem = AddSystem(hasVehicles, () => new UpdateGridSystem(grids[vehicleLayer], worldTickData));
            UpdateEntityAoiSystem = AddSystem(new UpdateEntityAoiSystem(grids, location.ReplicationGridDescriptions, worldTickData));
            UpdateVehiclesEntityGridModelSystem = AddSystem(hasVehicles, () => new UpdateEntityGridModelSystem(grids[vehicleLayer], worldTickData));
            UpdateAllRolesVisibleRobbersPositionSystem = AddSystem(hasRobbery, () => new UpdateMapVisibleEntityPositionSystem(allRolesVisibleRobbersModel, worldTickData));
            UpdateVehicleMapPositionSystem = AddSystem(hasVehicles, () => new UpdateVehicleMapPositionSystem(worldTickData, worldEntitiesModel, vehicleMapTransformUpdateModel, mapCopVehiclesModel, occupiedCharacterSlotsModel, partiesVehiclesModel));
            UpdateClanBattleTeamMemberPositionSystem = AddSystem(isClanBattle, () => new UpdateClanBattleTeamMemberPositionsSystem(worldTickData, occupiedCharacterSlotsModel, clanBattleModel, worldEntitiesModel.BattleCharacters, locationBoundsModel));
            UpdateCarUpgradesHolderOpenedStateByDistanceSystem = AddSystem(hasVehicles, () => new UpdateCarUpgradesHolderOpenedStateByDistanceSystem(worldEntitiesModel, carUpgradesHoldersModel));
            LootNotificationSystem = AddSystem(isCharacterLootEnabled, () => new LootNotificationSystem());
        }

        private T AddSystem<T>(T system) where T : IUpdateSystem
        {
            Systems.Add(system);
            return system;
        }

        private T AddSystem<T>(bool condition, Func<T> build) where T : IUpdateSystem
        {
            if (condition)
            {
                var system = build();
                Systems.Add(system);
                return system;
            }

            return default;
        }
    }
}