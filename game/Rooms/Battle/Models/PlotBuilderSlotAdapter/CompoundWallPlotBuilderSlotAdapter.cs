using Framework.Async.Awaiter;
using Framework.Core.Unit;
using Framework.Ecs.Tick;
using Models.Models.BuilderSlotModel;
using Models.Models.ExpiringResourceModel;
using Models.Models.InitStepsModel;
using Models.Models.PlotModel;
using Models.References;
using Models.References.Builder;
using Models.References.Inventory;
using Models.Utils.Extensions.Builder;
using Server.Battle.Models.BuildingLootSpawnModel;
using Server.Battle.Models.PlotConstructModel;
using Server.Battle.World;

namespace Server.Battle.Models.PlotBuilderSlotAdapter
{
    public class CompoundWallPlotBuilderSlotAdapter : IPlotBuilderSlotAdapter<PlotBuilderSlotModel.CompoundWall, CompoundWallDescription>
    {
        private readonly IPlotConstructModel _plotConstructModel;
        private readonly IInitStepsModel _plotConstructionStepsModel;
        private readonly IBuildingLootSpawnModel _buildingLootSpawnModel;
        private readonly IWorld _world;
        private readonly ITick _tick;

        public CompoundWallPlotBuilderSlotAdapter(IPlotConstructModel plotConstructModel, IInitStepsModel plotConstructionStepsModel, IBuildingLootSpawnModel buildingLootSpawnModel, IWorld world, ITick tick)
        {
            _plotConstructModel = plotConstructModel;
            _plotConstructionStepsModel = plotConstructionStepsModel;
            _buildingLootSpawnModel = buildingLootSpawnModel;
            _world = world;
            _tick = tick;
        }

        public bool IsSlotEmpty(PlotBuilderSlotModel.CompoundWall slotModel)
        {
            return GetSlotValue(slotModel) == null;
        }

        public CompoundWallDescription GetSlotValue(PlotBuilderSlotModel.CompoundWall slotModel)
        {
            return slotModel.SlotModel.Value;
        }

        public void SetSlotValue(PlotBuilderSlotModel.CompoundWall slotModel, CompoundWallDescription value)
        {
            slotModel.SlotModel.Value = value;
        }

        public bool TryFromItem(InventoryItemDescription item, PlotBuilderSlotModel.CompoundWall slotModel, out CompoundWallDescription value)
        {
            value = item.CompoundWallDescription;
            return value != null;
        }

        public InventoryItemDescription ToItem(PlotBuilderSlotModel.CompoundWall slotModel, CompoundWallDescription value)
        {
            return value.GetInventoryItem();
        }

        public bool CheckCancelSlotEffect(IPlotModel plotModel, PlotBuilderSlotModel.CompoundWall slotModel)
        {
            return !CompoundWallExtensions.HasLootEntity(plotModel) || !CompoundWallExtensions.HasLootInside(plotModel.CompoundWall);
        }

        public async void ApplySlotEffect(IPlotModel plotModel, PlotBuilderSlotModel.CompoundWall slotModel, CompoundWallDescription value)
        {
            GetExpiringResource(plotModel).IncAmount(_tick.Ts, BuilderDescription.ChangeCompoundWallExpiringResourceScore);
            
            await _plotConstructModel.SpawnCompoundWall(plotModel, value, _plotConstructionStepsModel, _world, BuilderCheckEntitiesInZoneFlag.Yes, true);
        }

        public async void CancelSlotEffect(IPlotModel plotModel, PlotBuilderSlotModel.CompoundWall slotModel)
        {
            GetExpiringResource(plotModel).IncAmount(_tick.Ts, BuilderDescription.ChangeCompoundWallExpiringResourceScore);
            
            await DespawnCompoundWall(plotModel);
        }

        public async void SetSlotValueAndChangeEffect(IPlotModel plotModel, PlotBuilderSlotModel.CompoundWall slotModel, CompoundWallDescription value)
        {
            GetExpiringResource(plotModel).IncAmount(_tick.Ts, BuilderDescription.ChangeCompoundWallExpiringResourceScore);
            
            await DespawnCompoundWall(plotModel);
            
            SetSlotValue(slotModel, value);

            await _plotConstructModel.SpawnCompoundWall(plotModel, value, _plotConstructionStepsModel, _world, BuilderCheckEntitiesInZoneFlag.Yes, true);
        }

        private IAwaiter<Unit> DespawnCompoundWall(IPlotModel plotModel)
        {
            _buildingLootSpawnModel.SpawnCompoundWallLoot(_world, plotModel);
            
            return _plotConstructModel.DespawnCompoundWall(plotModel, _plotConstructionStepsModel, _world, BuilderCheckEntitiesInZoneFlag.Yes, BuilderClearDataFlag.Yes);
        }

        public bool CheckSwap(IPlotModel plotModel, PlotBuilderSlotModel.CompoundWall firstSlotModel, PlotBuilderSlotModel.CompoundWall secondSlotModel)
        {
            return false;
        }

        public void Swap(IPlotModel plotModel, PlotBuilderSlotModel.CompoundWall firstSlotModel, PlotBuilderSlotModel.CompoundWall secondSlotModel)
        {
        }

        public bool Check(IPlotModel plotModel, PlotBuilderSlotModel.CompoundWall slotModel)
        {
            var now = _tick.Ts;
            return !PlotBuildingDefenseRules.IsAnyBuildingOnRaidLock(now, plotModel) && GetExpiringResource(plotModel).Check(now);
        }

        private IExpiringResourceModel GetExpiringResource(IPlotModel plotModel)
        {
            return plotModel.ChangeBuildingExpiringResource;
        }
    }
}