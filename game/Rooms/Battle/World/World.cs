using System;
using System.Collections.Generic;
using System.Numerics;
using Framework.Ecs.Tick;
using Framework.Replication.Data;
using Framework.Utility.Loggers;
using Models.CharacterController;
using Models.Data;
using Models.Data.Bollard;
using Models.Data.Car;
using Models.Data.FurnitureLoot;
using Models.Data.Hangar.Builder;
using Models.Data.Hangar.CargoWorkbench;
using Models.Data.Hangar.Loot;
using Models.Data.Hangar.PlotContainer;
using Models.Data.Hangar.Storage;
using Models.Data.Helicopter;
using Models.Data.PlotBuildingDefenseBlock;
using Models.Data.Role;
using Models.Data.Vehicle;
using Models.Data.VehicleLoot;
using Models.HitBox.Types;
using Models.Inventory.ItemActionsController;
using Models.MathUtils;
using Models.Messages;
using Models.Models.AngleSectorModel;
using Models.Models.BattleCharacterModel;
using Models.Models.BattleCharacterSlotModel;
using Models.Models.BattleCharacterSlotsModel;
using Models.Models.BattleClientNotificationsModel;
using Models.Models.BollardModel;
using Models.Models.BreakableColliderSetModel;
using Models.Models.BuilderDoorModel;
using Models.Models.BuildingLootEntityInventoryModel;
using Models.Models.BuildingLootModel;
using Models.Models.BuildingModel;
using Models.Models.BuildingRandomCargoModel;
using Models.Models.CargoExchangerModel;
using Models.Models.CargoModel;
using Models.Models.CargoWorkbenchSlotEntityInventoryModel;
using Models.Models.CargoWorkbenchSlotModel;
using Models.Models.CarModel;
using Models.Models.CarSettingsOverrideModel;
using Models.Models.CashLootNodeModel;
using Models.Models.ChangeEquipModel;
using Models.Models.ChangeObservableInventoryModel;
using Models.Models.ChangeRobberQuestProgressModel;
using Models.Models.CharacterVehicleTrunkModel;
using Models.Models.ClanBattleCharacterTeamModel;
using Models.Models.ClanBattleModel;
using Models.Models.ClanBattleTeamZonesModel;
using Models.Models.ContainerEntityInventoryModel;
using Models.Models.ContainerModel;
using Models.Models.CraftWorkbenchModel;
using Models.Models.CustomizationModel;
using Models.Models.DataCollection;
using Models.Models.DoorModel;
using Models.Models.EntityGridModel;
using Models.Models.EntityGridModels;
using Models.Models.EntityIdGenerator;
using Models.Models.FurnitureLootEntityInventoryModel;
using Models.Models.FurnitureLootModel;
using Models.Models.GrenadeModel;
using Models.Models.GridModel;
using Models.Models.HangarOwnerModel;
using Models.Models.HelicopterModel;
using Models.Models.HelicopterSettingsOverrideModel;
using Models.Models.HitBoxesModel;
using Models.Models.InitEquipModel;
using Models.Models.InitStepsModel;
using Models.Models.IPartyMemberAccessModel;
using Models.Models.KinematicObjectsControllerModel;
using Models.Models.LocationBoundsModel;
using Models.Models.LocationCraftWorkbenchModel;
using Models.Models.LocationDoorModel;
using Models.Models.LocationEventCargoSpawnPointModel;
using Models.Models.LocationEventDataModel;
using Models.Models.LocationPlotSpotsModel;
using Models.Models.LocationSecurityAlarmsModel;
using Models.Models.LootEntityInventoryModel;
using Models.Models.LootModel;
using Models.Models.MapVehiclesModel;
using Models.Models.MarksModel;
using Models.Models.MetalDetectorModel;
using Models.Models.ObservableInventoryEntityModel;
using Models.Models.OccupiedCharacterSlotsModel;
using Models.Models.Parties;
using Models.Models.PartiesVehiclesModel;
using Models.Models.PhysicsBodiesModel;
using Models.Models.PhysicsDebug.Models.PhysicsDebugEnabledModel;
using Models.Models.PhysicsDebug.Models.PhysicsDebugModel;
using Models.Models.PhysicsSceneModel;
using Models.Models.PlayerBattleModel;
using Models.Models.PlayerObserverModel;
using Models.Models.PlotBuildingDefenseBlockPrivateModel;
using Models.Models.PlotBuildingDefenseBlockPublicModel;
using Models.Models.PlotCarModel;
using Models.Models.PlotContainerInventoryModel;
using Models.Models.PlotContainerModel;
using Models.Models.PlotDescriptionBuilder;
using Models.Models.PlotHelicopterModel;
using Models.Models.PlotModel;
using Models.Models.PlotVehicleTrunkModel;
using Models.Models.PositionModel;
using Models.Models.RegularGrids;
using Models.Models.SettlementBonusModel;
using Models.Models.SettlementCashModel;
using Models.Models.StorageBoxInventoryModel;
using Models.Models.StorageBoxModel;
using Models.Models.SwitchModel;
using Models.Models.Triggers.TriggersCollection;
using Models.Models.Triggers.TriggersEventsModel;
using Models.Models.UnlockingSafeDoorModel;
using Models.Models.VehicleLootEntityInventoryModel;
using Models.Models.VehicleLootModel;
using Models.Models.VehicleMapTransformUpdateModel;
using Models.Models.VehicleTrunkInventoriesModel;
using Models.Models.VehicleTrunkModel;
using Models.Models.WindowBlindsModel;
using Models.Models.WorldBuildingsModel;
using Models.Models.WorldEntitiesModel;
using Models.Models.WorldModel;
using Models.Models.WorldSettingsOverrideModel;
using Models.Models.WorldTickData;
using Models.Physics.PhysicsBodies;
using Models.Physics.Types;
using Models.References;
using Models.References.Bollard;
using Models.References.BreakableCollider;
using Models.References.Builder;
using Models.References.Building;
using Models.References.BuildingLoot;
using Models.References.Car;
using Models.References.Cargo;
using Models.References.CargoInventory;
using Models.References.CargoSeller;
using Models.References.CashLootNode;
using Models.References.Character;
using Models.References.Character.Customizer;
using Models.References.Container;
using Models.References.CraftWorkbench;
using Models.References.Door;
using Models.References.FurnitureLoot;
using Models.References.Grenade;
using Models.References.HangarStorage;
using Models.References.Helicopter;
using Models.References.Inventory;
using Models.References.KinematicObject;
using Models.References.Location;
using Models.References.MetalDetector;
using Models.References.Physics;
using Models.References.Plot;
using Models.References.PlotBuildingDefenseBlock;
using Models.References.Replication;
using Models.References.Switch;
using Models.References.Vehicle;
using Models.References.Vehicle.Trunk;
using Models.RegularGrid;
using Models.Replication.BattleEntitiesDescriptions;
using Models.Replication.BattleEntityDescription;
using Models.Replication.BattleEntityReplicationGridLayerDescription;
using Models.Systems;
using Models.Utils.Extensions;
using Models.Utils.Extensions.Builder;
using Models.Utils.Extensions.Kinematic.KinematicObjectsController;
using Models.Utils.Extensions.SimpleDotPhysics.Types;
using Server.Battle.Controllers.BattlePlotVehicleController;
using Server.Battle.Data;
using Server.Battle.Models.BattleChangeCoinsModel;
using Server.Battle.Models.BattleCharacterRolesMetric;
using Server.Battle.Models.BattleEnterPositionModel;
using Server.Battle.Models.BattleStopByPlayersCountModel;
using Server.Battle.Models.BattleSystemsModel;
using Server.Battle.Models.BuildingLootSpawnModel;
using Server.Battle.Models.CargoSpawnModel;
using Server.Battle.Models.CarUpgradesHolderModel;
using Server.Battle.Models.ChangeCharacterRole;
using Server.Battle.Models.ChangePoliceAttentionPointsModel;
using Server.Battle.Models.ChangeRoleTimeModel;
using Server.Battle.Models.ClanBattleRuntimeParams;
using Server.Battle.Models.CopChangeCoinsModel;
using Server.Battle.Models.CopsAoeFindModel;
using Server.Battle.Models.DiffHistoryModels;
using Server.Battle.Models.ExitVehicleModel;
using Server.Battle.Models.FurnitureLootSpawnModel;
using Server.Battle.Models.GlobalMapVehicleModel;
using Server.Battle.Models.GrenadesQueue;
using Server.Battle.Models.LeftPlayersModel;
using Server.Battle.Models.LocationBreakableCollidersReset;
using Server.Battle.Models.LocationEventsModel;
using Server.Battle.Models.LootModels;
using Server.Battle.Models.MapVisibleEntitiesModel;
using Server.Battle.Models.ObservableInventoryFilterModel;
using Server.Battle.Models.PlotConstructModel;
using Server.Battle.Models.PlotVehicleSpawnerModel;
using Server.Battle.Models.ResetPlotVehicleModel;
using Server.Battle.Models.RespawnModel;
using Server.Battle.Models.RolesCountModel;
using Server.Battle.Models.SettlementPlotsServerModel;
using Server.Battle.Models.SettlementRoomModel;
using Server.Battle.Models.Systems;
using Server.Battle.Models.VehicleCargoInventoryChangeModel;
using Server.Battle.Models.VehicleLootSpawnModel;
using Server.Battle.Models.VehicleSpawnModel;
using Server.Battle.Models.WorldEntityGridModels;
using Server.Battle.Models.WorldSecurityAlarmEntitiesModel;
using Server.Battle.Models.WorldWaterZonesModel;
using Server.Battle.Replication.EntityInfo;
using Server.Battle.Replication.EntityReplicationDescriptions;
using Server.Battle.Replication.EntityStreamWriter;
using Server.Battle.Replication.EntityStreamWriters;
using Server.Battle.Replication.ReplicationIndexes;
using Server.Battle.Replication.ReplicationModel;
using Server.Battle.Replication.WorldEnteredLeftEntities;
using Server.Common.Controllers.SaveController;
using Server.Common.Models.AnalyticsModel;
using Server.References;

namespace Server.Battle.World
{
    public class World : IWorld
    {
        private readonly IDataCollection _entitiesDataCollection;
        private readonly IDataCollection _playerCharactersDataCollection;
        private readonly IDataCollection _playersDataCollection;
        private readonly IOccupiedCharacterSlotsModel _occupiedCharacterSlotsModel;
        private readonly IDiffHistoryModels _otherDiffHistoryModels;
        private readonly IDiffHistoryModels _selfDiffHistoryModels;
        private readonly IDiffHistoryModels _playersDiffHistoryModels;
        private readonly BattleModeDescription _battleMode;
        private readonly IPhysicsSceneModel _physicsSceneModel;
        private readonly LocationDescription _location;
        private readonly IRegularGrids _grids;
        private readonly IEntityStreamWriters _entityStreamWriters;
        private readonly IEntityReplicationDescriptions _entityReplicationDescriptions;
        private readonly IBattleEntitiesReplicationDescriptions _battleEntitiesReplicationDescriptions;
        private readonly ISettlementPlotsServerModel _plotsModel;
        private readonly IGrenadesQueue _grenadesQueue;
        private readonly ILocationPlotSpotsModel _locationPlotSpotsModel;
        private readonly IReplicationGridLayers _replicationGridLayers;

        private readonly IPhysicsBodiesModel _physicsBodiesModel;
        private readonly IWorldEntitiesModel _worldEntitiesModel;
        private readonly WorldEnteredLeftEntities _worldEnteredLeftEntities;
        private readonly EntitiesInfo _entitiesInfo;
        private readonly EntitiesInfo _playersEntitiesInfo;
        private readonly ITick _tick;
        private readonly Dictionary<int, int> _selfDamageRequests;
        private readonly IBattleCharacterRolesMetric _characterRolesMetric;
        private readonly IWorldEntityGridModels _worldEntityGridModels;
        private readonly IMapVehiclesModel _mapCopVehiclesModel;
        private readonly IMapVisibleEntitiesModel _allRolesVisibleRobbersModel;
        private readonly ILocationBoundsModel _locationBoundsModel;
        private readonly IWorldSettingsOverrideModel _worldSettingsOverrideModel;
        private readonly IRolesCountModel _rolesCountModel;
        private readonly IVehicleCargoInventoryChangeModel _vehicleCargoInventoryChangeModel;
        private readonly SettlementRoomData _settlementRoomData;
        private readonly IChangeVehicleTrunkEmptyFlagModel _changeVehicleTrunkEmptyFlagModel;
        private readonly CopsRoleData _copsRoleData;
        private readonly IVehicleMapTransformUpdateModel _vehicleMapTransformUpdateModel;
        private readonly IObservableInventoryFilterModel _observableInventoryFilterModel;
        private readonly IChangeObservableInventoryModel _changeObservableInventoryModel;
        
        private readonly Random _random = new Random();
    
        private readonly IEntityIdGenerator _entityIdGenerator;

        private readonly BattleSystems _systems;
        private readonly IBattleSystemsModel _systemsModel;

        private readonly ReplicationIndexesPools _replicationIndexesPools = new();

        public World(int tickPeriod, ISettlementRoomModel settlementRoomModel, IOccupiedCharacterSlotsModel occupiedCharacterSlotsModel, IWorldModel worldModel, IHitBoxesModel hitBoxesModel, BattleModeDescription battleMode, LocationDescription location, int snapshotsHistorySize, ILeftPlayersModel leftPlayers,
            IItemActionsController itemActionsController, IReplicationModel replicationModel, ITick tick, Dictionary<int, int> selfDamageRequests, IChangeEquipModel changeEquipModel, ILootModels lootModels,
            IBattleClientNotificationsModel clientNotificationsModel, IChangePoliceAttentionPointsModel changePoliceAttentionPointsModel, IParties parties,
            IBattleCharacterRolesMetric characterRolesMetric, IWorldBuildingsModel worldBuildingsModel, IWorldSecurityAlarmEntitiesModel worldSecurityAlarmEntitiesModel, IWorldEntityGridModels worldEntityGridModels, ILocationSecurityAlarmsModel locationSecurityAlarmsModel,
            IMapVehiclesModel mapCopVehiclesModel, IMapVisibleEntitiesModel allRolesVisibleRobbersModel, ILocationBoundsModel locationBoundsModel, WorldTickData worldTickData,
            IEntityMarksModel vigilantNpcCallMarksModel, ICopsAoeRewardModel copsAoeRewardModel, ICopsAoeFindModel copsAoeFindModel, IWorldWaterZonesModel worldWaterZonesModel, IPhysicsDebugEnabledModel physicsDebugEnabledModel, IPhysicsDebugModel physicsDebugModel,
            IWorldSettingsOverrideModel worldSettingsOverrideModel, IVehicleSpawnModel vehicleSpawnModel, IInitEquipModel initEquipModel, IRolesCountModel rolesCountModel, ICargoSpawnModel cargoSpawnModel, IVehicleCargoInventoryChangeModel vehicleCargoInventoryChangeModel,
            IUnlockingSafeDoorModel unlockingSafeDoorModel, ISaveController saveController,
            IChangeRobberQuestProgressModel changeRobberQuestProgressModel, IRespawnModel respawnModel, IVehicleLootSpawnModel vehicleLootSpawnModel,
            ITriggersCollection triggeredCollection, ITriggersEventsModel triggersEventsModel, IHangarOwnerModel hangarOwnerModel, HashSet<int> characterExitRequests, SettlementRoomData settlementRoomData, IBattleEnterPositionModel enterPositionModel, GlobalMapVehicleModel globalMapVehicleModel,
            ILocationCraftWorkbenchModel locationCraftWorkbenchModel, IChangeVehicleTrunkEmptyFlagModel changeVehicleTrunkEmptyFlagModel, IBattleAnalyticsModel analyticsModel, CopsRoleData copsRoleData, IChangeRoleTimeModel changeRoleTimeModel, IBattleChangeCoinsModel changeCoinsModel,
            IBattleEntitiesReplicationDescriptions battleEntitiesReplicationDescriptions, IPlotConstructModel plotConstructModel, IInitStepsModel plotConstructionStepsModel, IClanBattleModel clanBattleModel,
            IClanBattleTeamZonesModel clanBattleTeamZonesModel, long clanBattleStartTs, IBattleCharacterSlotsModel battleCharacterSlotsModel, IClanBattleCharacterTeamModel clanBattleCharacterTeamModel, IGrenadesQueue grenadesQueue, IPartyMemberAccessModel partyMemberAccessModel,
            ISettlementBonusModel settlementBonusModel, IPlotVehicleSpawnerModel plotVehicleSpawnerModel, HashSet<int> hiddenCharacters, IExitVehicleModel exitVehicleModel, IResetPlotVehicleModel resetPlotVehicleModel, ILoggers loggers, BattleInitTickScore initTickScore,
            ILocationPlotSpotsModel locationPlotSpotsModel, IFurnitureLootSpawnModel furnitureLootSpawnModel, IBuildingLootSpawnModel buildingLootSpawnModel, ChangeCharacterRoleModel changeCharacterRoleModel, ChangedRoleCharacters changedRoleCharacters,
            ICarUpgradesHoldersModel carUpgradesHoldersModel,
            IBattlePlotVehicleController plotVehicleController, IPartiesVehiclesModel partiesVehiclesModel, IReplicationGridLayers replicationGridLayers, bool isCheatsEnabled, ClanBattleRuntimeParamsModel clanBattleRuntimeParamsModel,
            LocationBreakableCollidersResetModels locationBreakableCollidersResetModels, ILocationEventsModel robberLocationEventsModel, IRobberLocationEventModels robberLocationEventModels, IBattleStopByPlayersCountModel stopByPlayersCountModel)
        {
            _entityIdGenerator = new EntityIdGenerator(location.StartEntityId);
            _entitiesDataCollection = worldModel.BattleDataCollections.Entities;
            _playerCharactersDataCollection = worldModel.BattleDataCollections.PlayerCharacters;
            _playersDataCollection = worldModel.BattleDataCollections.Players;
            _physicsBodiesModel = worldModel.PhysicsBodies;
            _occupiedCharacterSlotsModel = occupiedCharacterSlotsModel;
            _otherDiffHistoryModels = replicationModel.OtherDiffHistoryModels;
            _selfDiffHistoryModels = replicationModel.SelfDiffHistoryModels;
            _playersDiffHistoryModels = replicationModel.PlayersDiffHistoryModels;
            _battleMode = battleMode;
            _physicsSceneModel = worldModel.PhysicsScene;
            _grids = worldModel.Grids;
            _location = location;
            _worldEntitiesModel = worldModel.WorldEntitiesModel;
            _worldEnteredLeftEntities = replicationModel.WorldEnteredLeftEntities;
            _entitiesInfo = replicationModel.EntitiesInfo;
            _playersEntitiesInfo = replicationModel.PlayersEntitiesInfo;
            _tick = tick;
            _selfDamageRequests = selfDamageRequests;
            _characterRolesMetric = characterRolesMetric;
            _worldEntityGridModels = worldEntityGridModels;
            _mapCopVehiclesModel = mapCopVehiclesModel;
            _allRolesVisibleRobbersModel = allRolesVisibleRobbersModel;
            _locationBoundsModel = locationBoundsModel;
            _worldSettingsOverrideModel = worldSettingsOverrideModel;
            _rolesCountModel = rolesCountModel;
            _vehicleCargoInventoryChangeModel = vehicleCargoInventoryChangeModel;
            _settlementRoomData = settlementRoomData;
            _changeVehicleTrunkEmptyFlagModel = changeVehicleTrunkEmptyFlagModel;
            _copsRoleData = copsRoleData;
            _battleEntitiesReplicationDescriptions = battleEntitiesReplicationDescriptions;
            _plotsModel = settlementRoomModel.PlotsModel;
            _grenadesQueue = grenadesQueue;
            _locationPlotSpotsModel = locationPlotSpotsModel;
            _replicationGridLayers = replicationGridLayers;
            _entityStreamWriters = replicationModel.EntityStreamWriters;
            _entityReplicationDescriptions = replicationModel.EntityReplicationDescriptions;
         
            _changeObservableInventoryModel = worldModel.ChangeObservableInventoryModel;

            _vehicleMapTransformUpdateModel = new VehicleMapTransformUpdateModel(locationBoundsModel);

            _observableInventoryFilterModel = new ObservableInventoryFilterModel(_worldEntitiesModel, hangarOwnerModel, partyMemberAccessModel, occupiedCharacterSlotsModel, clanBattleCharacterTeamModel);

            _systems = new BattleSystems(this, tickPeriod, occupiedCharacterSlotsModel, worldModel, hitBoxesModel, battleMode, location, snapshotsHistorySize, leftPlayers, itemActionsController, 
                replicationModel, selfDamageRequests, changeEquipModel, lootModels, clientNotificationsModel, changePoliceAttentionPointsModel, parties, characterRolesMetric, worldBuildingsModel,
                worldSecurityAlarmEntitiesModel, locationSecurityAlarmsModel, mapCopVehiclesModel, allRolesVisibleRobbersModel, locationBoundsModel, worldTickData, vigilantNpcCallMarksModel,
                copsAoeRewardModel, copsAoeFindModel, worldWaterZonesModel, physicsDebugEnabledModel, physicsDebugModel, vehicleSpawnModel, initEquipModel, cargoSpawnModel, vehicleCargoInventoryChangeModel, unlockingSafeDoorModel, saveController, changeRobberQuestProgressModel, respawnModel, vehicleLootSpawnModel,
                triggeredCollection, triggersEventsModel, hangarOwnerModel, characterExitRequests, globalMapVehicleModel, locationCraftWorkbenchModel, analyticsModel, changeRoleTimeModel, changeCoinsModel,
                settlementRoomModel, plotConstructModel, plotConstructionStepsModel, clanBattleModel, clanBattleTeamZonesModel, clanBattleStartTs, battleCharacterSlotsModel, clanBattleCharacterTeamModel,
                grenadesQueue, partyMemberAccessModel, settlementBonusModel, plotVehicleSpawnerModel, hiddenCharacters, exitVehicleModel, resetPlotVehicleModel, furnitureLootSpawnModel, buildingLootSpawnModel, loggers, initTickScore,
                changeCharacterRoleModel, changedRoleCharacters, changeVehicleTrunkEmptyFlagModel, carUpgradesHoldersModel, plotVehicleController, partiesVehiclesModel, isCheatsEnabled, clanBattleRuntimeParamsModel,
                locationBreakableCollidersResetModels, robberLocationEventsModel, rolesCountModel, robberLocationEventModels, stopByPlayersCountModel);
            _systemsModel = new BattleSystemsModel();
        }

        public IBattleCharacterModel CreateBattleCharacter(PlayerRoleDescription role)
        {
            int id = _entityIdGenerator.Generate();

            var playerBattleData = new PlayerBattleData(_locationBoundsModel);
            var playerBattleModel = new PlayerBattleModel(playerBattleData);

            var battleCharacterData = BattleCharacterData.CreateBattleCharacter();
            var battleCharacterDescription = BattleCharacterDescription.Default;
            IBattleCharacterModel model = new BattleCharacterModel(id, battleCharacterDescription, battleCharacterData, playerBattleModel, _replicationGridLayers);

            {
                battleCharacterData.PrivateData.Seed.Value = RandomUtils.GenerateSeed();

                model.Role.Value = role;
                _characterRolesMetric.Inc(role);
                _rolesCountModel.Inc(role);

                model.ChangeRoleTickIndexModel.Value = _tick.TickIndex;
            }

            return model;
        }

        public void DestroyBattleCharacter(IBattleCharacterModel model)
        {
            var role = model.Role.Value;
            _characterRolesMetric.Dec(role);
            _rolesCountModel.Dec(role);
        }

        private void SpawnBattleCharacter(IBattleCharacterModel model, Vector3 spawnPosition, Vector3 lookDirection)
        {
            // physics, position & orientation
            {
                var physicsCharacter = model.Description.PhysicsCharacter;

                (float capsuleLength, float capsuleRadius, float capsuleDistanceToGround) = CharacterControllerColliderUtils.CalculateCapsuleColliderByState(physicsCharacter, false);
                int shapeId = _physicsSceneModel.AddPlayerShape(PhysicsRuntimeLayerType.Dynamic, capsuleLength, capsuleRadius, SurfaceType.Default, HitType.Default, PhysicsMaterialSettings.PlayerRestitution, PhysicsMaterialSettings.PlayerFriction);
                Vector3 scenePosition = spawnPosition + Vector3.UnitY * capsuleDistanceToGround;
                PhysicsBodyId bodyId = _physicsSceneModel.CreatePlayer(scenePosition, shapeId, physicsCharacter.InvMass);

                model.BodyIdModel.Value = bodyId;

                CharacterTransformExtensions.SetPosition(spawnPosition, model.Transform, model.ReplicatedTransform, model.StateModel);
                LookDirectionExtensions.Set(lookDirection, model.LookDirection, model.ReplicatedLookDirection, model.StateModel);
                model.DesiredYawModel.DesiredYaw = model.LookDirection.Yaw;

                CharacterTransformExtensions.SetYaw(model.LookDirection.Yaw, model.Transform, model.ReplicatedTransform, model.StateModel);

                model.PrivateModel.CharacterController.SetState(CharacterControllerStateDescription.Falling);
                model.PrivateModel.CharacterController.SetCapsuleColliderParams(capsuleRadius, capsuleLength, capsuleDistanceToGround);

                _physicsBodiesModel.AddBattleCharacter(bodyId, model);
            }

            model.PrivateModel.Health.SetHealth(model.PrivateModel.Health.MaxAmount);
            model.PrivateModel.Health.IsDead = false;
            
            model.BattleStanceModel.Stop();

            if (model.Role.Value.CanTakeItemInHands)
            {
                model.PrivateModel.SelectNotEmptyQuickSlotEvent.Invoke();
            }
        }

        private void DespawnBattleCharacter(IBattleCharacterModel model)
        {
            var id = model.Id;

            PhysicsBodyId bodyId = model.BodyId;
            _physicsSceneModel.Free(bodyId);
            _physicsBodiesModel.RemoveBattleCharacter(bodyId);

            _changeObservableInventoryModel.RemoveObserver(id);
            _allRolesVisibleRobbersModel.Remove(id);
        }

        private void AddBattleCharacterToWorld(IBattleCharacterModel battleCharacterModel, IEntityGridModels entityGridModels)
        {
            var id = battleCharacterModel.Id;
            _worldEntitiesModel.AddBattleCharacter(id, battleCharacterModel);
            _systemsModel.AddBattleCharacter(_systems, battleCharacterModel, entityGridModels);
        }

        private void RemoveBattleCharacterFromWorld(int entityId)
        {
            _worldEntitiesModel.RemoveBattleCharacter(entityId);
            _systemsModel.RemoveBattleCharacter(_systems, entityId);
        }

        private void AddBotBattleCharacterToWorld(IBattleCharacterModel battleCharacterModel, InventoryItemDescription firstWeapon, InventoryItemDescription secondWeapon, float carMaxSpeed, BotFlags botFlags, float helicopterTargetHeight, InventoryItemDescription throwingItemDescription, int drivePatternDurationInput1, int drivePatternDurationInput2)
        {
            var id = battleCharacterModel.Id;
            _worldEntitiesModel.AddBot(id);
            _systemsModel.AddBotBattleCharacter(_systems, battleCharacterModel, firstWeapon, secondWeapon, carMaxSpeed, botFlags, helicopterTargetHeight, throwingItemDescription, drivePatternDurationInput1, drivePatternDurationInput2, _tick.Ts);
        }

        private void RemoveBotBattleCharacterFromWorld(int entityId)
        {
            _worldEntitiesModel.RemoveBot(entityId);
            _systemsModel.RemoveBotBattleCharacter(_systems, entityId);
        }

        private void InitBotBattleCharacter(IBattleCharacterModel battleCharacterModel, InventoryItemDescription firstWeapon, InventoryItemDescription secondWeapon, CargoDescription cargoDescription, BotFlags botFlags, CustomizationInfo customizationInfo, int tattooGrade, int fTattooGrade)
        {
            var id = battleCharacterModel.Id;

            if (customizationInfo.Sex == CharacterSexDescription.Male)
            {
                battleCharacterModel.PlayerBattleModel.TattoosModel.SetGrade(customizationInfo.Tattoo, tattooGrade);
            }
            else
            {
                battleCharacterModel.PlayerBattleModel.TattoosModel.SetGrade(customizationInfo.FTattoo, fTattooGrade);
            }
            battleCharacterModel.CustomizationModel.Set(customizationInfo, battleCharacterModel.PlayerBattleModel.TattoosModel);

            battleCharacterModel.PrivateModel.EquipSlots[EquipSlotDescription.Backpack].ItemModel.Value = InventoryItemDescription.BackpackLegend;
            battleCharacterModel.PrivateModel.EquipSlots[EquipSlotDescription.Belt].ItemModel.Value = InventoryItemDescription.BeltLegend;

            if (firstWeapon != null)
            {
                battleCharacterModel.PrivateModel.EquipSlots[EquipSlotDescription.Weapon0].ItemModel.Value = firstWeapon;
                if (firstWeapon.IsWeapon)
                {
                    battleCharacterModel.PrivateModel.EquipAmmo[EquipSlotDescription.Weapon0.EquipAmmoOrArmorSlot].Value = firstWeapon.Weapon.Magazine;
                }
            }

            if (secondWeapon != null)
            {
                battleCharacterModel.PrivateModel.EquipSlots[EquipSlotDescription.Weapon1].ItemModel.Value = secondWeapon;
                if (secondWeapon.IsWeapon)
                {
                    battleCharacterModel.PrivateModel.EquipAmmo[EquipSlotDescription.Weapon1.EquipAmmoOrArmorSlot].Value = 0;
                }
            }

            if (botFlags.HasFlag(BotFlags.IsArmored))
            {
                battleCharacterModel.PrivateModel.EquipSlots[EquipSlotDescription.Vest].ItemModel.Value = InventoryItemDescription.Vest2Mk1;
                battleCharacterModel.PrivateModel.EquipAmmo[EquipAmmoOrArmorSlotDescription.VestArmor].Value = battleCharacterModel.PrivateModel.EquipSlots[EquipSlotDescription.Vest].ItemModel.Value.Armor.MaxArmorAmount;
                battleCharacterModel.PrivateModel.InitArmorModel.Init();
            }

            if (botFlags.HasFlag(BotFlags.IsHoldCargo))
            {
                battleCharacterModel.CargoModel.Cargo = cargoDescription;
            }

            if (botFlags.HasFlag(BotFlags.IsKnockedOut))
            {
                var knockoutDamage = 1000;
                _selfDamageRequests[id] = knockoutDamage;
            }

            battleCharacterModel.CopCustomizeModel.Value = BuilderSlotsExtensions.GetCopEquip(battleCharacterModel.PlayerBattleModel.CharacterBuilderSlotsModel).CopCustomize;
        }

        private void AddBattleCharacterToReplication(IBattleCharacterModel battleCharacterModel, IEntityGridModels entityGridModels)
        {
            var id = battleCharacterModel.Id;

            var transform = battleCharacterModel.Transform;
            AddToDynamicGrid(_systems.CharacterUpdateGridSystem, id, transform, entityGridModels[BattleEntityReplicationGridLayerDescription.BattleCharacter.Layer]);
            GridModel.InitEntity(transform.X, transform.Z, _grids, _location.ReplicationGridDescriptions, entityGridModels, battleCharacterModel.AoiModel);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);

            var battleCharacterData = battleCharacterModel.Data;
            var publicData = BattleCharacterData.CreatePublicBattleCharacter(battleCharacterData.CommonData, battleCharacterData.PublicData);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.Character;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            var entityStreamWriter = new NoParamRuntimeEntityStreamWriter(id, replicationIndex, battleEntityReplicationDescription.MaxCount);
            AddEntityReplicationSettings(id, entityStreamWriter, battleEntityReplicationDescription);

            MarkAdded(id);

            _entitiesDataCollection.Add(id, publicData);

            AddOtherHistoryModel(id, publicData, entityInfo);

            var playerBattleData = battleCharacterModel.PlayerBattleModel.Data;
            _playersDataCollection.Add(id, playerBattleData);

            var playerEntityInfo = _playersEntitiesInfo.Add(id, _tick.TickIndex);
            _playersDiffHistoryModels.AddEntity(id, playerBattleData, playerEntityInfo);
        }

        private void RemoveBattleCharacterFromReplication(int entityId)
        {
            RemoveFromDynamicGrid(_systems.CharacterUpdateGridSystem, entityId);

            _entitiesDataCollection.Remove(entityId);

            RemoveOtherHistoryModel(entityId);

            MarkRemoved(entityId);

            _entitiesInfo.Remove(entityId);

            RemoveEntityReplicationSettings(entityId);

            _playersDataCollection.Remove(entityId);

            _playersEntitiesInfo.Remove(entityId);
            _playersDiffHistoryModels.RemoveEntity(entityId);
        }

        private void AddPlayerToReplication(IBattleCharacterModel battleCharacterModel, EntityInfo entityInfo)
        {
            var id = battleCharacterModel.Id;

            var data = battleCharacterModel.Data;
            var privateData = BattleCharacterData.CreatePrivateBattleCharacter(data.CommonData, data.PrivateData);

            _playerCharactersDataCollection.Add(id, privateData);

            AddSelfHistoryModel(id, privateData, entityInfo);
        }

        private void RemovePlayerFromReplication(int entityId)
        {
            _playerCharactersDataCollection.Remove(entityId);

            _selfDiffHistoryModels.RemoveEntity(entityId);
        }

        public void AddPlayerBattleCharacter(IBattleCharacterModel model, Vector3 spawnPosition, Vector3 lookDirection)
        {
            IEntityGridModels entityGridModels = new EntityGridModels(_replicationGridLayers);

            SpawnBattleCharacter(model, spawnPosition, lookDirection);
            AddBattleCharacterToWorld(model, entityGridModels);
            AddBattleCharacterToReplication(model, entityGridModels);
            AddPlayerToReplication(model, _entitiesInfo.Items[model.Id]);
        }

        public void RemovePlayerBattleCharacter(int entityId)
        {
            var model = _worldEntitiesModel.BattleCharacters[entityId];

            DespawnBattleCharacter(model);

            RemovePlayerFromReplication(entityId);
            RemoveBattleCharacterFromReplication(entityId);
            RemoveBattleCharacterFromWorld(entityId);

            DestroyBattleCharacter(model);
        }

        public void AddBotBattleCharacter(IBattleCharacterModel model, InventoryItemDescription firstWeapon, InventoryItemDescription secondWeapon, CargoDescription cargoDescription, Vector3 position, Vector3 lookDirection, float carMaxSpeed, BotFlags botFlags, float helicopterTargetHeight, InventoryItemDescription throwingItemDescription, int drivePatternDurationInput1, int drivePatternDurationInput2, CustomizationInfo customizationInfo, int tattooGrade, int fTattooGrade)
        {
            IEntityGridModels entityGridModels = new EntityGridModels(_replicationGridLayers);
            InitBotBattleCharacter(model, firstWeapon, secondWeapon, cargoDescription, botFlags, customizationInfo, tattooGrade, fTattooGrade);

            SpawnBattleCharacter(model, position, lookDirection);
            AddBattleCharacterToWorld(model, entityGridModels);
            AddBattleCharacterToReplication(model, entityGridModels);
            AddBotBattleCharacterToWorld(model, firstWeapon, secondWeapon, carMaxSpeed, botFlags, helicopterTargetHeight, throwingItemDescription, drivePatternDurationInput1, drivePatternDurationInput2);
        }

        public void RemoveBotBattleCharacter(int entityId)
        {
            var model = _worldEntitiesModel.BattleCharacters[entityId];

            DespawnBattleCharacter(model);

            RemoveBotBattleCharacterFromWorld(entityId);
            RemoveBattleCharacterFromReplication(entityId);
            RemoveBattleCharacterFromWorld(entityId);

            DestroyBattleCharacter(model);
        }

        public IContainerModel AddContainer(LocationContainerDescription description)
        {
            ContainerData data = new ContainerData(description);
            
            int id = description.EntityId;
            
            Vector3 position = description.Position;

            _entitiesDataCollection.Add(id, data);

            IContainerModel model = new ContainerModel(id, data);

            _worldEntitiesModel.AddContainer(id, model);

            var defaultState = UnlockStateDescription.Locked;
            model.UnlockState.Value = defaultState;
            
            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            AddContainerInventory(description.InventoryEntityId, model);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.Container);
            AddToStaticGrid(id, position.X, position.Z, gridModel);

            AddEntityReplicationSettings(id, new LocationEntityStreamWriter<LocationContainerDescription>(_location.Containers, description), _battleEntitiesReplicationDescriptions.Container);
            
            return model;
        }
        
        public void RemoveContainer(int id)
        {
            if (_worldEntitiesModel.Containers.TryGetModel(id, out var model))
            {
                RemoveContainerInventory(model.Description.InventoryEntityId);
            
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);
                
                _worldEntitiesModel.RemoveContainer(id);
                
                _entitiesDataCollection.Remove(id);

                _entitiesInfo.Remove(id);

                RemoveFromStaticGrid(id);
                
                RemoveEntityReplicationSettings(id);
            }
        }

        private void AddContainerInventory(int id, IContainerModel containerModel)
        {
            ContainerEntityInventoryData data = new ContainerEntityInventoryData();
        
            _entitiesDataCollection.Add(id, data);

            IContainerEntityInventoryModel model = new ContainerEntityInventoryModel(id, data);
        
            _worldEntitiesModel.AddContainerInventory(id, model);
            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _observableInventoryFilterModel.ContainerInventory(containerModel), containerModel.InteractPositionModel, BattleDistancesDescription.ObservableInventoryStartInteractSqrDistance, BattleDistancesDescription.ObservableInventoryStopInteractSqrDistance));

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            AddEntityReplicationSettings(id, new LocationEntityStreamWriter<LocationContainerDescription>(_location.Containers, containerModel.Description), _battleEntitiesReplicationDescriptions.ContainerInventory);
        }

        private void RemoveContainerInventory(int id)
        {
            RemoveOtherHistoryModel(id);
            MarkRemoved(id);
        
            _changeObservableInventoryModel.RemoveObservableInventory(id);
        
            _worldEntitiesModel.RemoveContainerInventory(id);
            _worldEntitiesModel.RemoveObservableInventory(id);
                
            _entitiesDataCollection.Remove(id);
                
            _entitiesInfo.Remove(id);

            RemoveEntityReplicationSettings(id);
        }

        public ILootModel AddLoot(int sourceId, Vector3 position)
        {
            int id = _entityIdGenerator.Generate();
            
            LootData data = new LootData();
            
            _entitiesDataCollection.Add(id, data);

            ILootModel model = new LootModel(id, data);
            model.Transform.SetPosition(position.X, position.Y, position.Z);
            model.SpawnTickIndex.Value = (int)_tick.TickIndex;
            model.PhysicsSource.SourcePosition = model.Transform.ToVector3();
            model.PhysicsSource.SourceTs = _tick.Ts;
            model.PhysicsState.State = SimpleDotPhysicsState.FallDown;

            long removeTs = _tick.Ts + LootDescription.CharacterLootRemoveDelay;

            _systemsModel.AddLoot(_systems, model, removeTs);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            ILootEntityInventoryModel lootInventoryModel = AddLootInventory(model.InteractPositionModel);
        
            _occupiedCharacterSlotsModel.TryGetSlotByEntityId(sourceId, out IBattleCharacterSlotModel sourceCharacterSlotModel);
            lootInventoryModel.Name.Value = sourceCharacterSlotModel.Nickname;
            lootInventoryModel.ClanTag.Value = sourceCharacterSlotModel.ClanTag;
        
            _worldEntitiesModel.AddLoot(id, model);
            
            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.Loot);
            AddToStaticGrid(id, position.X, position.Z, gridModel);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.Loot;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);
            
            AddEntityReplicationSettings(id, new NoParamRuntimeEntityStreamWriter(id, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);
            
            return model;
        }

        public void RemoveLoot(int id)
        {
            if (_worldEntitiesModel.Loots.TryGetModel(id, out _))
            {
                int lootInventoryEntityId = ObservableInventoryDescription.GetInventoryEntityId(id);
                RemoveLootInventory(lootInventoryEntityId);
            
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _systemsModel.RemoveLoot(_systems, id);
                
                _worldEntitiesModel.RemoveLoot(id);
                
                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);
                
                RemoveFromStaticGrid(id);

                RemoveEntityReplicationSettings(id);
            }
        }

        private ILootEntityInventoryModel AddLootInventory(IPositionModel positionModel)
        {
            int id = _entityIdGenerator.Generate();

            LootEntityInventoryData data = new LootEntityInventoryData();
        
            _entitiesDataCollection.Add(id, data);

            ILootEntityInventoryModel model = new LootEntityInventoryModel(id, data);
        
            _worldEntitiesModel.AddLootInventory(id, model);
            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _observableInventoryFilterModel.CharacterLootInventory(), positionModel, BattleDistancesDescription.ObservableInventoryStartInteractSqrDistance, BattleDistancesDescription.ObservableInventoryStopInteractSqrDistance));

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.LootInventory;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new NoParamRuntimeEntityStreamWriter(id, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);

            return model;
        }

        private void RemoveLootInventory(int id)
        {
            RemoveOtherHistoryModel(id);
            MarkRemoved(id);
        
            _changeObservableInventoryModel.RemoveObservableInventory(id);
        
            _worldEntitiesModel.RemoveLootInventory(id);
            _worldEntitiesModel.RemoveObservableInventory(id);
                
            _entitiesDataCollection.Remove(id);
                
            _entitiesInfo.Remove(id);

            RemoveEntityReplicationSettings(id);
        }
    
        public IVehicleLootModel AddVehicleLoot(Vector3 position)
        {
            int id = _entityIdGenerator.Generate();
            
            VehicleLootData data = new VehicleLootData();
            
            _entitiesDataCollection.Add(id, data);

            IVehicleLootModel model = new VehicleLootModel(id, data);
            model.Transform.SetPosition(position.X, position.Y, position.Z);
            model.SpawnTickIndex.Value = (int)_tick.TickIndex;
            model.PhysicsSource.SourcePosition = model.Transform.ToVector3();
            model.PhysicsSource.SourceTs = _tick.Ts;
            model.PhysicsState.State = SimpleDotPhysicsState.FallDown;

            long removeTs = _tick.Ts + VehicleLootDescription.LootRemoveDelay;

            _systemsModel.AddVehicleLoot(_systems, model, removeTs);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);
            
            AddVehicleLootInventory(model.InteractPositionModel);
            
            _worldEntitiesModel.AddVehicleLoot(id, model);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.VehicleLoot);
            AddToStaticGrid(id, position.X, position.Z, gridModel);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.VehicleLoot;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new NoParamRuntimeEntityStreamWriter(id, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);
            
            return model;
        }

        public void RemoveVehicleLoot(int id)
        {
            if (_worldEntitiesModel.VehicleLoots.TryGetModel(id, out _))
            {
                int inventoryEntityId = ObservableInventoryDescription.GetInventoryEntityId(id);
                RemoveVehicleLootInventory(inventoryEntityId);
            
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _systemsModel.RemoveVehicleLoot(_systems, id);
                
                _worldEntitiesModel.RemoveVehicleLoot(id);
                
                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);
                
                RemoveFromStaticGrid(id);

                RemoveEntityReplicationSettings(id);
            }
        }

        private void AddVehicleLootInventory(IPositionModel positionModel)
        {
            int id = _entityIdGenerator.Generate();

            VehicleLootEntityInventoryData data = new VehicleLootEntityInventoryData();
        
            _entitiesDataCollection.Add(id, data);

            IVehicleLootEntityInventoryModel model = new VehicleLootEntityInventoryModel(id, data);
        
            _worldEntitiesModel.AddVehicleLootInventory(id, model);
            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _observableInventoryFilterModel.VehicleLootInventory(), positionModel, BattleDistancesDescription.ObservableInventoryStartInteractSqrDistance, BattleDistancesDescription.ObservableInventoryStopInteractSqrDistance));
        
            AddVehicleLootInventoryCargoSlots(model);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.VehicleLootInventory;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new NoParamRuntimeEntityStreamWriter(id, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);
        }

        private void RemoveVehicleLootInventory(int id)
        {
            RemoveOtherHistoryModel(id);
            MarkRemoved(id);
        
            RemoveVehicleLootInventoryCargoSlots(id);
        
            _changeObservableInventoryModel.RemoveObservableInventory(id);
        
            _worldEntitiesModel.RemoveVehicleLootInventory(id);
            _worldEntitiesModel.RemoveObservableInventory(id);
                
            _entitiesDataCollection.Remove(id);
                
            _entitiesInfo.Remove(id);

            RemoveEntityReplicationSettings(id);
        }

        private void AddVehicleLootInventoryCargoSlots(IVehicleLootEntityInventoryModel vehicleLootEntityInventoryModel)
        {
            foreach (var slotModel in vehicleLootEntityInventoryModel.CargoInventoryModel)
            {
                int entityId = _entityIdGenerator.Generate();
                _worldEntitiesModel.AddVehicleLootCargoInventorySlot(entityId, slotModel, vehicleLootEntityInventoryModel);
            }
        }

        private void RemoveVehicleLootInventoryCargoSlots(int vehicleLootInventoryEntityId)
        {
            if (_worldEntitiesModel.VehicleLootInventories.TryGetModel(vehicleLootInventoryEntityId, out var vehicleLootInventoryEntityModel))
            {
                int slotsCount = vehicleLootInventoryEntityModel.CargoInventoryModel.SlotsCount;
                int slotFirstId = vehicleLootInventoryEntityId + 1;
                int slotLastId = slotFirstId + slotsCount;
                for (int slotId = slotFirstId; slotId < slotLastId; slotId++)
                {
                    _worldEntitiesModel.RemoveVehicleLootCargoInventorySlot(slotId);
                }
            }
        }

        public ILocationDoorModel AddLocationDoor(LocationDoorDescription description)
        {
            var data = new LocationDoorData(description.DoorDescription);
            
            int id = description.EntityId;

            _entitiesDataCollection.Add(id, data);

            var model = new LocationDoorModel(id, description, data);

            BuilderDoorExtensions.SetLocationStartValues(model, _battleMode);

            model.Initialize.Initialize(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel, model.DoorModel.State.Value);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            _systemsModel.AddLocationDoor(_systems, model);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.Door);
            AddToStaticGrid(id, model.DoorModel.Position.X, model.DoorModel.Position.Z, gridModel);

            AddEntityReplicationSettings(id, new LocationEntityStreamWriter<LocationDoorDescription>(_location.Doors, description), _battleEntitiesReplicationDescriptions.Door);

            return model;
        }

        public void RemoveLocationDoor(int id)
        {
            if (_worldEntitiesModel.Doors.TryGetModel(id, out IDoorModel model))
            {
                model.Destroy.Destroy(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel);

                _systemsModel.RemoveLocationDoor(_systems, id);

                RemoveOtherHistoryModel(id);
                MarkRemoved(id);
                
                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);
                
                RemoveFromStaticGrid(id);
                
                RemoveEntityReplicationSettings(id);
            }
        }

        public IKinematicObjectsControllerModel AddLocationKinematicObjectsController(KinematicObjectsControllerEntityDescription locationKinematicObjectsControllerDescription)
        {
            int id = locationKinematicObjectsControllerDescription.EntityId;
            KinematicObjectsControllerData data = CreateKinematicObjectsControllerData(id, locationKinematicObjectsControllerDescription.Description);

            IKinematicObjectsControllerModel model = new KinematicObjectsControllerModel(id, locationKinematicObjectsControllerDescription.Index, locationKinematicObjectsControllerDescription.Description, data, false, new HashSet<PhysicsBodyId>());
            model.Initialize.InitializeLocation(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel, model.Description.StartState);
            
            AddKinematicObjectsController(data, model);

            AddEntityReplicationSettings(id, new LocationEntityStreamWriter<KinematicObjectsControllerEntityDescription>(_location.KinematicObjectsControllers, locationKinematicObjectsControllerDescription), _battleEntitiesReplicationDescriptions.LocationKinematicObjectsController);

            return model;
        }

        public IKinematicObjectsControllerModel AddPlotKinematicObjectsController(IPlotModel plotModel, int index)
        {
            int id = _entityIdGenerator.Generate();
        
            var plotSlotDescription = plotModel.PlotSlotDescription;
            var settlementPlotServerModel = _plotsModel.Slots[plotSlotDescription];
            
            var buildingPoint = BuilderKinematicObjectsControllerIndexDescription.GetBuildingPoint(_locationPlotSpotsModel, settlementPlotServerModel.SpotIndex, index);
            BuilderKinematicObjectsControllerIndexDescription.TryGetKinematicObjectsController(settlementPlotServerModel, index, out var plotKinematicObjectsControllerDescription);
            KinematicObjectsControllerDescription locationKinematicObjectsControllerDescription = KinematicObjectsControllerSpaceExtensions.RecalculateFromPlotSpaceToLocationSpace(plotKinematicObjectsControllerDescription, buildingPoint.Position, buildingPoint.Orientation);

            var plotKinematicObjectSlotDescription = PlotKinematicObjectsControllerSlotDescription.Enum[index];
            
            var stateData = settlementPlotServerModel.DatabasePartData.KinematicObjectsControllers[plotKinematicObjectSlotDescription].State;
            KinematicObjectsControllerData data = new KinematicObjectsControllerData(locationKinematicObjectsControllerDescription, stateData);
            HashSet<PhysicsBodyId> kinematicsInteractIgnoreRigidBodies = BreachKinematicObjectsControllerExtensions.GetKinematicObjectsControllerInteractIgnoreRigidBodies(settlementPlotServerModel.KinematicsInteractIgnoreRigidBodies, index);
            IKinematicObjectsControllerModel model = new KinematicObjectsControllerModel(id, index, locationKinematicObjectsControllerDescription, data, false, kinematicsInteractIgnoreRigidBodies);

            var initState = model.State.Value ?? model.Description.StartState;
            model.Initialize.InitializePlot(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel, plotSlotDescription, initState);
            AddKinematicObjectsController(data, model);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.PlotKinematicObjectsController;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new PlotKinematicObjectsControllerEntityStreamWriter(id, plotSlotDescription, index, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);

            return model;
        }

        private void AddKinematicObjectsController(KinematicObjectsControllerData data, IKinematicObjectsControllerModel model)
        {
            int id = model.Id;
            
            _entitiesDataCollection.Add(id, data);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
        
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var isPlotKinematicObject = !_location.TryGetKinematicObjectsController(id, out _);
            var isBollard = _location.TryGetBollardByKinematicObject(id, out _);
            var isLongRange = isPlotKinematicObject || isBollard;
            var entityReplicationGridLayerDescription = isLongRange ? BattleEntityReplicationGridLayerDescription.LongRangeKinematicObjects : BattleEntityReplicationGridLayerDescription.ShortRangeKinematicObjects;
            var entityGridModel = CreateEntityGridModel(entityReplicationGridLayerDescription);
            AddToStaticGrid(id, model.Position.X, model.Position.Z, entityGridModel);

            _systemsModel.AddKinematicObjectsController(_systems, model, entityGridModel);
        }

        private KinematicObjectsControllerData CreateKinematicObjectsControllerData(int entityId, KinematicObjectsControllerDescription description)
        {
            if (_location.TryGetBollardByKinematicObject(entityId, out var bollardDescription))
            {
                BollardData bollardData = _copsRoleData.Bollards[bollardDescription];
                return new KinematicObjectsControllerData(description, bollardData.State);
            }
            return new KinematicObjectsControllerData(description);
        }

        public void RemoveKinematicObjectsController(int id)
        {
            if (_worldEntitiesModel.KinematicObjectsControllers.TryGetModel(id, out IKinematicObjectsControllerModel model))
            {
                bool isPlotEntity = _worldEntitiesModel.PlotSlotByEntity.TryGetValue(id, out _);
                if (isPlotEntity)
                {
                    model.Destroy.DestroyPlot(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel);
                }
                else
                {
                    model.Destroy.DestroyLocation(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel);
                }

                _systemsModel.RemoveKinematicObjectsController(_systems, id);

                RemoveOtherHistoryModel(id);
                MarkRemoved(id);
            
                _entitiesDataCollection.Remove(id);
            
                _entitiesInfo.Remove(id);
            
                RemoveFromStaticGrid(id);
            
                RemoveEntityReplicationSettings(id);
            }
        }

        public ICarModel AddCar(CarDescription description, Vector3 position, Quaternion orientation)
        {
            int id = _entityIdGenerator.Generate();

            CarData data = new CarData(description);
            _entitiesDataCollection.Add(id, data);

            Vector3 pivotPosition = position + Vector3.UnitY * description.VerticalDistanceFromPivotToGroundInRest;
            ICarSettingsOverrideModel settingsOverride = _worldSettingsOverrideModel.GetCar(description);
            
            ICarModel model = new CarModel(id, data, settingsOverride, false);
            model.Initialize.Initialize(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel, pivotPosition, orientation);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            if (description.HasTrunk || description.CanRefillCopAmmo)
            {
                var trunkWorldPosition = VehicleTrunkSettingsDescription.GetVehicleTrunkWorldPosition(description.TrunkLocalPosition, model.Transform.PivotWorldPosition, model.Transform.PivotWorldOrientation);
                model.TrunkPosition.Set(trunkWorldPosition.X, trunkWorldPosition.Y, trunkWorldPosition.Z);

                int vehicleTrunkId = _entityIdGenerator.Generate();
                if (description.HasTrunk)
                {
                    var trunkDescription = description.PoliceCarTrunk?.CarTrunk ?? description.CarSlot.Trunk;
                    var vehicleTrunkData = new CarEntityTrunkData(trunkDescription);

                    _entitiesDataCollection.Add(vehicleTrunkId, vehicleTrunkData);

                    var vehicleTrunkEntityInfo = _entitiesInfo.Add(vehicleTrunkId, _tick.TickIndex);
                    AddOtherHistoryModel(vehicleTrunkId, vehicleTrunkData, vehicleTrunkEntityInfo);
                
                    var vehicleTrunkModel = new VehicleTrunkModel(vehicleTrunkId, vehicleTrunkData);
                    var vehicleTrunkInventoriesModel = new VehicleTrunkInventoriesModel(vehicleTrunkId, vehicleTrunkModel.Inventory, vehicleTrunkModel.CargoInventoryModel);

                    _worldEntitiesModel.AddVehicleTrunk(vehicleTrunkId, vehicleTrunkModel, vehicleTrunkInventoriesModel);
                    _worldEntitiesModel.AddObservableInventory(vehicleTrunkId, new ObservableInventoryEntityModel(vehicleTrunkId, _observableInventoryFilterModel.VehicleTrunk(id), model.TrunkPosition, BattleDistancesDescription.TrunkInteractSqrDistance, BattleDistancesDescription.TrunkInteractSqrDistance));

                    AddVehicleTrunkCargoInventorySlots(vehicleTrunkInventoriesModel);

                    _vehicleCargoInventoryChangeModel.UpdateCargoInventoryFlags(vehicleTrunkInventoriesModel);

                    var carTrunkReplicationDescription = _battleEntitiesReplicationDescriptions.CarTrunk;
                    var carTrunkReplicationIndex = OccupyEntityReplicationIndex(vehicleTrunkId, carTrunkReplicationDescription);

                    AddEntityReplicationSettings(vehicleTrunkId, new DescribedRuntimeEntityStreamWriter<CarTrunkDescription>(vehicleTrunkId, CarTrunkDescription.Enum, trunkDescription, carTrunkReplicationIndex, carTrunkReplicationDescription.MaxCount), carTrunkReplicationDescription);
                    MarkAdded(vehicleTrunkId);
                }
            }
            
            var entityGridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.Vehicle);

            _systemsModel.AddCar(_systems, model, entityGridModel);

            AddToDynamicGrid(_systems.VehicleUpdateGridSystem, id, model.Transform, entityGridModel);

            var carReplicationDescription = _battleEntitiesReplicationDescriptions.Car;
            var carReplicationIndex = OccupyEntityReplicationIndex(id, carReplicationDescription);

            AddEntityReplicationSettings(id, new DescribedRuntimeEntityStreamWriter<CarDescription>(id, CarDescription.Enum, description, carReplicationIndex, carReplicationDescription.MaxCount), carReplicationDescription);

            if (description.GroupType == VehicleGroupType.Police)
            {
                var mapCarModel = _mapCopVehiclesModel.Add(id, VehicleTypeDescription.Car);
                _vehicleMapTransformUpdateModel.UpdateVehicleMapTransform(model.Transform, mapCarModel.MapTransform);
            }
            
            return model;
        }

        public void RemoveCar(int id)
        {
            if (_worldEntitiesModel.Cars.TryGetModel(id, out ICarModel model))
            {
                _systemsModel.RemoveCar(_systems, id);

                RemoveOtherHistoryModel(id);
                MarkRemoved(id);
                
                model.Destroy.Destroy(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel);
                
                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);
                
                RemoveFromDynamicGrid(_systems.VehicleUpdateGridSystem, id);

                _mapCopVehiclesModel.Remove(id);

                RemoveEntityReplicationSettings(id);
            
                if (model.Description.HasTrunk)
                {
                    var vehicleTrunkEntityId = VehicleTrunkSettingsDescription.GetVehicleTrunkEntityId(id);
                
                    RemoveVehicleTrunkCargoInventorySlots(vehicleTrunkEntityId);
                
                    _entitiesDataCollection.Remove(vehicleTrunkEntityId);
                    _entitiesInfo.Remove(vehicleTrunkEntityId);
                    RemoveOtherHistoryModel(vehicleTrunkEntityId);
                    _changeObservableInventoryModel.RemoveObservableInventory(vehicleTrunkEntityId);
                    _worldEntitiesModel.RemoveVehicleTrunk(vehicleTrunkEntityId);
                    _worldEntitiesModel.RemoveObservableInventory(vehicleTrunkEntityId);

                    RemoveEntityReplicationSettings(vehicleTrunkEntityId);
                    MarkRemoved(vehicleTrunkEntityId);
                }
            }
        }

        public IHelicopterModel AddHelicopter(HelicopterDescription description, in Vector3 position, in Quaternion orientation)
        {
            int id = _entityIdGenerator.Generate();
        
            HelicopterData data = new HelicopterData(description);
            _entitiesDataCollection.Add(id, data);
        
            Vector3 pivotPosition = position + Vector3.UnitY * description.VerticalDistanceFromPivotToGroundInRest;
        
            IHelicopterSettingsOverrideModel settingsOverride = _worldSettingsOverrideModel.GetHelicopter(description);
        
            IHelicopterModel model = new HelicopterModel(id, data, settingsOverride, false);
            model.Initialize.Initialize(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel, pivotPosition, orientation);
        
            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            if (description.HasTrunk || description.CanRefillCopAmmo)
            {
                var trunkWorldPosition = VehicleTrunkSettingsDescription.GetVehicleTrunkWorldPosition(description.TrunkLocalPosition, model.Transform.PivotWorldPosition, model.Transform.PivotWorldOrientation);
                model.TrunkPosition.Set(trunkWorldPosition.X, trunkWorldPosition.Y, trunkWorldPosition.Z);

                int vehicleTrunkId = _entityIdGenerator.Generate();
                if (description.HasTrunk)
                {
                    var trunkDescription = description.HelicopterSlot.Trunk;
                    var vehicleTrunkData = new HelicopterEntityTrunkData(trunkDescription);

                    _entitiesDataCollection.Add(vehicleTrunkId, vehicleTrunkData);

                    var vehicleTrunkEntityInfo = _entitiesInfo.Add(vehicleTrunkId, _tick.TickIndex);
                    AddOtherHistoryModel(vehicleTrunkId, vehicleTrunkData, vehicleTrunkEntityInfo);

                    var vehicleTrunkModel = new VehicleTrunkModel(vehicleTrunkId, vehicleTrunkData);
                    var vehicleTrunkInventoriesModel = new VehicleTrunkInventoriesModel(vehicleTrunkId, vehicleTrunkModel.Inventory, vehicleTrunkModel.CargoInventoryModel);

                    _worldEntitiesModel.AddVehicleTrunk(vehicleTrunkId, vehicleTrunkModel, vehicleTrunkInventoriesModel);
                    _worldEntitiesModel.AddObservableInventory(vehicleTrunkId, new ObservableInventoryEntityModel(vehicleTrunkId, _observableInventoryFilterModel.VehicleTrunk(id), model.TrunkPosition, BattleDistancesDescription.TrunkInteractSqrDistance, BattleDistancesDescription.TrunkInteractSqrDistance));
                
                    AddVehicleTrunkCargoInventorySlots(vehicleTrunkInventoriesModel);

                    _vehicleCargoInventoryChangeModel.UpdateCargoInventoryFlags(vehicleTrunkInventoriesModel);

                    var helicopterTrunkReplicationDescription = _battleEntitiesReplicationDescriptions.HelicopterTrunk;
                    var helicopterTrunkReplicationIndex = OccupyEntityReplicationIndex(vehicleTrunkId, helicopterTrunkReplicationDescription);

                    AddEntityReplicationSettings(vehicleTrunkId, new DescribedRuntimeEntityStreamWriter<HelicopterTrunkDescription>(vehicleTrunkId, HelicopterTrunkDescription.Enum, description.HelicopterSlot.Trunk, helicopterTrunkReplicationIndex, helicopterTrunkReplicationDescription.MaxCount), helicopterTrunkReplicationDescription);
                    MarkAdded(vehicleTrunkId);
                }
            }

            var entityGridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.Vehicle);

            _systemsModel.AddHelicopter(_systems, model, entityGridModel);

            AddToDynamicGrid(_systems.VehicleUpdateGridSystem, id, model.Transform, entityGridModel);

            var helicopterReplicationDescription = _battleEntitiesReplicationDescriptions.Helicopter;
            var helicopterReplicationIndex = OccupyEntityReplicationIndex(id, helicopterReplicationDescription);

            AddEntityReplicationSettings(id, new DescribedRuntimeEntityStreamWriter<HelicopterDescription>(id, HelicopterDescription.Enum, description, helicopterReplicationIndex, helicopterReplicationDescription.MaxCount), helicopterReplicationDescription);
        
            if (description.GroupType == VehicleGroupType.Police)
            {
                var mapCarModel = _mapCopVehiclesModel.Add(id, VehicleTypeDescription.Helicopter);
                _vehicleMapTransformUpdateModel.UpdateVehicleMapTransform(model.Transform, mapCarModel.MapTransform);
            }
        
            return model;
        }

        public void RemoveHelicopter(int id)
        {
            if (_worldEntitiesModel.Helicopters.TryGetModel(id, out IHelicopterModel model))
            {
                _systemsModel.RemoveHelicopter(_systems, id);

                RemoveOtherHistoryModel(id);
                MarkRemoved(id);
            
                model.Destroy.Destroy(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel);
            
                _entitiesDataCollection.Remove(id);
            
                _entitiesInfo.Remove(id);
            
                RemoveFromDynamicGrid(_systems.VehicleUpdateGridSystem, id);
            
                _mapCopVehiclesModel.Remove(id);

                RemoveEntityReplicationSettings(id);
            
                if (model.Description.HasTrunk)
                {
                    var vehicleTrunkEntityId = VehicleTrunkSettingsDescription.GetVehicleTrunkEntityId(id);
                
                    RemoveVehicleTrunkCargoInventorySlots(vehicleTrunkEntityId);
                
                    _entitiesDataCollection.Remove(vehicleTrunkEntityId);
                    _entitiesInfo.Remove(vehicleTrunkEntityId);
                    RemoveOtherHistoryModel(vehicleTrunkEntityId);
                    _changeObservableInventoryModel.RemoveObservableInventory(vehicleTrunkEntityId);
                    _worldEntitiesModel.RemoveVehicleTrunk(vehicleTrunkEntityId);
                    _worldEntitiesModel.RemoveObservableInventory(vehicleTrunkEntityId);

                    RemoveEntityReplicationSettings(vehicleTrunkEntityId);
                    MarkRemoved(vehicleTrunkEntityId);
                }
            }
        }

        private void AddVehicleTrunkCargoInventorySlots(IVehicleTrunkInventoriesModel vehicleTrunkInventoriesModel)
        {
            for (int i = 0; i < vehicleTrunkInventoriesModel.CargoInventoryModel.MaxSlotsCount; i++)
            {
                var slotModel = vehicleTrunkInventoriesModel.CargoInventoryModel[i];
                int entityId = _entityIdGenerator.Generate();
                _worldEntitiesModel.AddVehicleTrunkCargoSlot(entityId, slotModel, vehicleTrunkInventoriesModel);
            }
        }

        private void RemoveVehicleTrunkCargoInventorySlots(int vehicleTrunkId)
        {
            if (_worldEntitiesModel.VehicleTrunks.TryGetModel(vehicleTrunkId, out var vehicleTrunkModel))
            {
                int slotStartId = vehicleTrunkId + 1;
                int slotLastId = slotStartId + vehicleTrunkModel.CargoInventoryModel.MaxSlotsCount;
                for (int slotId = slotStartId; slotId < slotLastId; slotId++)
                {
                    _worldEntitiesModel.RemoveVehicleTrunkCargoSlot(slotId);
                }
            }
        }

        public IBreakableColliderSetModel AddBreakableColliderSet(BreakableColliderSetDescription description)
        {
            BreakableColliderSetData data = new BreakableColliderSetData(description);
            
            int id = description.EntityId;

            _entitiesDataCollection.Add(id, data);

            IBreakableColliderSetModel model = new BreakableColliderSetModel(id, data);
            model.Initialize.InitializeServer(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel);
            
            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            _systemsModel.AddBreakableColliderSet(_systems, model);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.BreakableCollidersSet);
            AddToStaticGrid(id, description.Position.X, description.Position.Z, gridModel);

            AddEntityReplicationSettings(id, new LocationEntityStreamWriter<BreakableColliderSetDescription>(_location.BreakableCollidersSets, description), _battleEntitiesReplicationDescriptions.BreakableCollidersSet);
            
            return model;
        }

        public void RemoveBreakableColliderSet(int id)
        {
            if (_worldEntitiesModel.BreakableColliderSets.TryGetModel(id, out IBreakableColliderSetModel model))
            {
                _systemsModel.RemoveBreakableColliderSet(_systems, id);

                RemoveOtherHistoryModel(id);
                MarkRemoved(id);
                
                _entitiesInfo.Remove(id);
                
                model.Destroy.DestroyServer(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel);
                
                _entitiesDataCollection.Remove(id);
                
                RemoveFromStaticGrid(id);
                
                RemoveEntityReplicationSettings(id);
            }
        }

        public IBuildingModel AddBuilding(LocationBuildingDescription description)
        {
            BuildingData data = new BuildingData(description);

            int id = description.EntityId;
            
            _entitiesDataCollection.Add(id, data);

            IBuildingModel model = new BuildingModel(id, data);

            foreach (var randomLootObjectModel in model.RandomLootObjects)
            {
                randomLootObjectModel.GenerateItem(_random);
            }
            model.InitializeModel.InitializeRandomLootObjects(_physicsSceneModel, _physicsBodiesModel);

            foreach (var locationRandomCargoDescription in description.RandomCargos)
            {
                var buildingRandomCargoModel = model.RandomCargosModel[locationRandomCargoDescription];
                BuildingRandomCargoRules.Init(buildingRandomCargoModel);
            }

            model.InitializeModel.Initialize(_physicsSceneModel, _physicsBodiesModel);
        
            model.LootNodesModel.Reset(_random);
            model.VigilantNpcsModel.Reset(_physicsSceneModel);
            model.CumulativeChargersModel.Reset();
            
            _worldEntitiesModel.AddBuilding(id, model);

            _systemsModel.AddBuilding(_systems, model);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.Building);
            AddToStaticGrid(id, description.Position.X, description.Position.Z, gridModel);

            AddEntityReplicationSettings(id, new LocationEntityStreamWriter<LocationBuildingDescription>(_location.Buildings, description), _battleEntitiesReplicationDescriptions.Building);
            
            return model;
        }

        public void RemoveBuilding(int id)
        {
            if (_worldEntitiesModel.Buildings.TryGetModel(id, out var model))
            {
                _systemsModel.RemoveBuilding(_systems, id);

                model.DestroyModel.Destroy(_physicsSceneModel, _physicsBodiesModel);
                model.DestroyModel.DestroyRandomLootObjects(_physicsSceneModel, _physicsBodiesModel);
                
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _worldEntitiesModel.RemoveBuilding(id);
                
                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);
                
                RemoveFromStaticGrid(id);
                
                RemoveEntityReplicationSettings(id);
            }
        }

        public IMetalDetectorModel AddMetalDetector(LocationMetalDetectorDescription description)
        {
            MetalDetectorData data = new MetalDetectorData(description);
            
            int id = _entityIdGenerator.Generate();
            
            Vector3 position = description.Position;

            _entitiesDataCollection.Add(id, data);

            IMetalDetectorModel model = new MetalDetectorModel(id, data);

            _worldEntitiesModel.AddMetalDetector(id, model);
            
            model.MetalDetectorInitializeModel.Initialize(_physicsSceneModel, _physicsBodiesModel);
            
            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            _systemsModel.AddMetalDetector(_systems, model);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.MetalDetector);
            AddToStaticGrid(id, position.X, position.Z, gridModel);

            AddEntityReplicationSettings(id, new LocationEntityStreamWriter<LocationMetalDetectorDescription>(_location.MetalDetectors, description), _battleEntitiesReplicationDescriptions.MetalDetector);
            
            return model;
        }

        public void RemoveMetalDetector(int id)
        {
            if (_worldEntitiesModel.MetalDetectors.TryGetModel(id, out IMetalDetectorModel model))
            {
                model.MetalDetectorDestroyModel.Destroy(_physicsSceneModel, _physicsBodiesModel);
                
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);
                
                _worldEntitiesModel.RemoveMetalDetector(id);
                
                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);

                _systemsModel.RemoveMetalDetector(_systems, id);

                RemoveFromStaticGrid(id);
                
                RemoveEntityReplicationSettings(id);
            }
        }

        public ICargoModel AddCargo(CargoDescription description, Vector3 position, float rotationY)
        {
            int id = _entityIdGenerator.Generate();

            CargoData data = new CargoData(description);
            
            _entitiesDataCollection.Add(id, data);

            ICargoModel model = new CargoModel(id, data);
            model.Transform.SetPosition(position.X, position.Y, position.Z);
            model.RySector = AngleSectorModel.GetSector(CargoDescription.RotationSectorCount, rotationY);
            model.SpawnTickIndex.Value = (int) _tick.TickIndex;
            model.PhysicsSource.SourcePosition = model.Transform.ToVector3();
            model.PhysicsSource.SourceTs = _tick.Ts;
            model.PhysicsState.State = SimpleDotPhysicsState.FallDown;
            
            _worldEntitiesModel.AddCargo(id, model);
            
            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            long removeTs = _tick.Ts + _battleMode.CargoRemoveDelay;

            _systemsModel.AddCargo(_systems, model, removeTs);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.Cargo);
            AddToStaticGrid(id, position.X, position.Z, gridModel);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.Cargo;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new DescribedRuntimeEntityStreamWriter<CargoDescription>(id, CargoDescription.Enum, description, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);

            return model;
        }

        public void RemoveCargo(int id)
        {
            if (_worldEntitiesModel.Cargos.TryGetModel(id, out _))
            {
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);
                
                _worldEntitiesModel.RemoveCargo(id);

                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);

                _systemsModel.RemoveCargo(_systems, id);

                RemoveFromStaticGrid(id);
                
                RemoveEntityReplicationSettings(id);
            }
        }

        public void AddStorageBoxInventory(IStorageBoxModel storageBoxModel, HangarStorageData hangarStorageData)
        {
            LocationStorageDescription locationStorageDescription = storageBoxModel.DescriptionModel.Value;

            var id = locationStorageDescription.EntityIds.InventoryEntityId;
            var inventoryData = new StorageBoxInventoryData(hangarStorageData.Inventory);
            var storageDescription = locationStorageDescription.Description;
            var inventoryModel = new StorageBoxInventoryModel(id, inventoryData, storageDescription);
            _worldEntitiesModel.AddStorageBoxInventory(id, inventoryModel);
            _entitiesDataCollection.Add(id, inventoryData);
            EntityInfo entityInventoryInfo = _entitiesInfo.Add(id, _tick.TickIndex);

            MarkAdded(id);
            AddOtherHistoryModel(id, inventoryData, entityInventoryInfo);

            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _observableInventoryFilterModel.StorageInventory(storageBoxModel), storageBoxModel.InteractPositionModel, BattleDistancesDescription.ObservableInventoryStartInteractSqrDistance, BattleDistancesDescription.ObservableInventoryStopInteractSqrDistance));

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.StorageInventory;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new StorageInventoryEntityStreamWriter(id, storageDescription, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);
        }

        public void RemoveStorageBoxInventory(IStorageBoxModel storageBoxModel)
        {
            var locationStorageDescription = storageBoxModel.DescriptionModel.Value;
            var id = locationStorageDescription.EntityIds.InventoryEntityId;

            RemoveOtherHistoryModel(id);
            MarkRemoved(id);

            _worldEntitiesModel.RemoveStorageBoxInventory(id);
            _entitiesDataCollection.Remove(id);
            _entitiesInfo.Remove(id);
            _changeObservableInventoryModel.RemoveObservableInventory(id);
            _worldEntitiesModel.RemoveObservableInventory(id);

            RemoveEntityReplicationSettings(id);
        }

        public void AddPlotContainerInventory(IPlotContainerModel plotContainerModel, HangarPlotContainerData hangarPlotContainerData)
        {
            var locationPlotContainerDescription = plotContainerModel.DescriptionModel.Value;

            var id = locationPlotContainerDescription.EntityIds.InventoryEntityId;
            var inventoryData = new PlotContainerInventoryData(hangarPlotContainerData.Inventory);
            var plotContainerDescription = locationPlotContainerDescription.Description;
            var inventoryModel = new PlotContainerInventoryModel(id, inventoryData, plotContainerDescription);
            _worldEntitiesModel.AddPlotContainerInventory(id, inventoryModel);
            _entitiesDataCollection.Add(id, inventoryData);
            EntityInfo entityInventoryInfo = _entitiesInfo.Add(id, _tick.TickIndex);

            MarkAdded(id);
            AddOtherHistoryModel(id, inventoryData, entityInventoryInfo);

            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _observableInventoryFilterModel.PlotContainerInventory(plotContainerModel), plotContainerModel.InteractPositionModel, BattleDistancesDescription.ObservableInventoryStartInteractSqrDistance, BattleDistancesDescription.ObservableInventoryStopInteractSqrDistance));

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.PlotContainerInventory;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new PlotContainerInventoryEntityStreamWriter(id, plotContainerDescription, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);
        }

        public void RemovePlotContainerInventory(IPlotContainerModel plotContainerModel)
        {
            var locationPlotContainerDescription = plotContainerModel.DescriptionModel.Value;
            var id = locationPlotContainerDescription.EntityIds.InventoryEntityId;

            RemoveOtherHistoryModel(id);
            MarkRemoved(id);

            _worldEntitiesModel.RemovePlotContainerInventory(id);
            _entitiesDataCollection.Remove(id);
            _entitiesInfo.Remove(id);
            _changeObservableInventoryModel.RemoveObservableInventory(id);
            _worldEntitiesModel.RemoveObservableInventory(id);

            RemoveEntityReplicationSettings(id);
        }

        public void AddCargoWorkbenchSlotInventory(int id, PlotSlotDescription plotSlotDescription, ICargoWorkbenchSlotModel cargoWorkbenchSlotModel)
        {
            var hangarSlotData = _plotsModel.Slots[plotSlotDescription].DatabasePartData.MainBuilding.CargoWorkbench.Slots[cargoWorkbenchSlotModel.SlotDescription];
            var slotInventoryData = new CargoWorkbenchSlotEntityInventoryData(hangarSlotData.Inventory);
            var slotInventoryModel = new CargoWorkbenchSlotEntityInventoryModel(id, slotInventoryData);
            _worldEntitiesModel.AddCargoWorkbenchSlotInventory(id, slotInventoryModel);
            _entitiesDataCollection.Add(id, slotInventoryData);
            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
        
            MarkAdded(id);
            AddOtherHistoryModel(id, slotInventoryData, entityInfo);
        
            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _ => true, cargoWorkbenchSlotModel.InteractPositionModel, BattleDistancesDescription.ObservableInventoryStartInteractSqrDistance, BattleDistancesDescription.ObservableInventoryStopInteractSqrDistance));

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.CargoWorkbenchSlotInventory;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new NoParamRuntimeEntityStreamWriter(id, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);
        }

        public void RemoveCargoWorkbenchSlotInventory(int id)
        {
            RemoveOtherHistoryModel(id);
            MarkRemoved(id);

            _worldEntitiesModel.RemoveCargoWorkbenchSlotInventory(id);
            _entitiesDataCollection.Remove(id);
            _entitiesInfo.Remove(id);
            _changeObservableInventoryModel.RemoveObservableInventory(id);
            _worldEntitiesModel.RemoveObservableInventory(id);

            RemoveEntityReplicationSettings(id);
        }

        public IPlotModel AddPlot(PlotGradeDescription plotGradeDescription, PlotSlotDescription plotSlotDescription, Vector3 position, Quaternion orientation)
        {
            var plotSchemeDescription = plotGradeDescription.Scheme;
            var id = _entityIdGenerator.Generate();
            var settlementPlotServerModel = _plotsModel.Slots[plotSlotDescription];
            var data = new PlotData(plotSlotDescription, settlementPlotServerModel.CommonPartData, settlementPlotServerModel.DatabasePartData);
        
            var plotDescription = PlotDescriptionBuilder.Build(_entityIdGenerator, position, orientation, plotGradeDescription);
            var model = new PlotModel(id, plotDescription, plotSchemeDescription, data);

            model.InitializeModel.Initialize(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel, settlementPlotServerModel.KinematicsInteractIgnoreRigidBodies);

            _entitiesDataCollection.Add(id, data);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);

            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.Plot);
            AddToStaticGrid(id, position.X, position.Z, gridModel);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.Plot;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new DescribedRuntimeEntityStreamWriter<PlotSlotDescription>(id, PlotSlotDescription.Enum, plotSlotDescription, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);

            return model;
        }

        public void RemovePlot(int id)
        {
            if (_worldEntitiesModel.Plots.TryGetModel(id, out var model))
            {
                
                model.DestroyModel.Destroy(_physicsSceneModel, _worldEntitiesModel, _physicsBodiesModel);

                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _entitiesDataCollection.Remove(id);

                _entitiesInfo.Remove(id);

                RemoveFromStaticGrid(id);

                RemoveEntityReplicationSettings(id);
            }
        }

        public ISwitchModel AddSwitch(LocationSwitchDescription description)
        {
            int id = description.EntityId;

            ISwitchModel model = new SwitchModel(id, description.SwitchDescription);
            _worldEntitiesModel.AddSwitch(id, model);

            _systemsModel.AddSwitch(_systems, model);

            return model;
        }

        public void RemoveSwitch(int id)
        {
            if (_worldEntitiesModel.Switches.ContainsKey(id))
            {
                _worldEntitiesModel.RemoveSwitch(id);
                _systemsModel.RemoveSwitch(_systems, id);
            }
        }

        public ICraftWorkbenchModel AddCraftWorkbenchModel(LocationCraftWorkbenchDescription description)
        {
            CraftWorkbenchData data = new(description);

            int id = description.EntityId;

            Vector3 position = description.Position;

            _entitiesDataCollection.Add(id, data);

            ICraftWorkbenchModel model = new CraftWorkbenchModel(id, data);

            _worldEntitiesModel.AddCraftWorkbench(id, model);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);

            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.CraftWorkbench);
            AddToStaticGrid(id, position.X, position.Z, gridModel);

            AddEntityReplicationSettings(id, new NoParamEntityStreamWriter(), _battleEntitiesReplicationDescriptions.CraftWorkbench);

            return model;
        }

        public void RemoveCraftWorkbench(int id)
        {
            if (_worldEntitiesModel.CraftWorkbenches.TryGetModel(id, out ICraftWorkbenchModel model))
            {
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _worldEntitiesModel.RemoveCraftWorkbench(id);

                _entitiesDataCollection.Remove(id);

                _entitiesInfo.Remove(id);

                RemoveFromStaticGrid(id);

                RemoveEntityReplicationSettings(id);
            }
        }

        public IBollardModel AddBollard(LocationBollardDescription description)
        {
            int id = description.EntityId;
        
            BollardData data = _copsRoleData.Bollards[description];
        
            IBollardModel model = new BollardModel(data, description);
        
            _worldEntitiesModel.AddBollard(id, model);

            return model;
        }

        public void RemoveBollard(int id)
        {
            if (_worldEntitiesModel.Bollards.ContainsKey(id))
            {
                _worldEntitiesModel.RemoveBollard(id);
            }
        }

        public IGrenadeModel AddGrenade(GrenadeDescription description, in Vector3 position)
        {
            int id = _entityIdGenerator.Generate();

            GrenadeData data = new GrenadeData(description);
            _entitiesDataCollection.Add(id, data);
        
            IGrenadeModel model = new GrenadeModel(id, data, null, null);
            model.Initialize.Initialize(_worldEntitiesModel);

            model.Transform.Position = position;
            
            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
        
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);
        
            var entityGridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.Grenade);
            AddToDynamicGrid(_systems.CharacterUpdateGridSystem, id, model.Transform, entityGridModel);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.Grenade;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new DescribedRuntimeEntityStreamWriter<GrenadeDescription>(id, GrenadeDescription.Enum, description, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);
        
            _systemsModel.AddGrenade(_systems, model);
        
            _grenadesQueue.Enqueue(model);

            return model;
        }

        public void RemoveGrenade(int id)
        {
            if (_worldEntitiesModel.Grenades.TryGetModel(id, out IGrenadeModel model))
            {
                _grenadesQueue.Remove(model);
            
                _systemsModel.RemoveGrenade(_systems, id);

                RemoveOtherHistoryModel(id);
                MarkRemoved(id);
            
                model.Destroy.Destroy(_worldEntitiesModel);
            
                _entitiesDataCollection.Remove(id);
            
                _entitiesInfo.Remove(id);
                
                RemoveFromDynamicGrid(_systems.CharacterUpdateGridSystem, id);

                RemoveEntityReplicationSettings(id);
            }
        }
    
        public void AddCargoSeller(LocationCargoSellerDescription description)
        {
            int id = description.EntityId;
            _worldEntitiesModel.AddCargoSeller(id, description);
        }

        public void RemoveCargoSeller(int id)
        {
            if (_worldEntitiesModel.CargoSellers.ContainsKey(id))
            {
                _worldEntitiesModel.RemoveCargoSeller(id);
            }
        }
    
        public void AddCargoExchanger(LocationCargoExchangerDescription description)
        {
            CargoExchangerData data = new CargoExchangerData(description);

            int id = description.EntityId;
            
            _entitiesDataCollection.Add(id, data);

            ICargoExchangerModel model = new CargoExchangerModel(description, data);

            _worldEntitiesModel.AddCargoExchanger(id, model);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.CargoExchanger);
            AddToStaticGrid(id, description.CenterPoint.X, description.CenterPoint.Z, gridModel);

            AddEntityReplicationSettings(id, new LocationEntityStreamWriter<LocationCargoExchangerDescription>(_location.CargoExchangerDescriptions, description), _battleEntitiesReplicationDescriptions.CargoExchanger);
        }

        public void RemoveCargoExchanger(int id)
        {
            if (_worldEntitiesModel.CargoExchangers.TryGetModel(id, out var model))
            {
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _worldEntitiesModel.RemoveCargoExchanger(id);
                
                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);
                
                RemoveFromStaticGrid(id);
                
                RemoveEntityReplicationSettings(id);
            }
        }

        public IPlayerObserverModel AddPlayerObserver()
        {
            int id = _entityIdGenerator.Generate();
        
            IPlayerObserverModel model = new PlayerObserverModel(id, _replicationGridLayers);
            model.Position.Set(Vector3.Zero);
        
            var entityGridModels = new EntityGridModels(_replicationGridLayers);
        
            _worldEntitiesModel.AddPlayerObserver(id, model);
        
            _systemsModel.AddPlayerObserver(_systems, model, entityGridModels);
        
            GridModel.InitEntity(model.Position.X, model.Position.Z, _grids, _location.ReplicationGridDescriptions, entityGridModels, model.AoiModel);
        
            return model;
        }
    
        public void RemovePlayerObserver(int id)
        {
            if (_worldEntitiesModel.PlayerObservers.TryGetModel(id, out _))
            {
                _systemsModel.RemovePlayerObserver(_systems, id);
            
                _worldEntitiesModel.RemovePlayerObserver(id);
            }
        }

        public void AddSettlementCash(LocationSettlementCashDescription description)
        {
            var  data = _settlementRoomData.CashData;

            int id = description.EntityId;

            Vector3 position = description.CashPoint;

            _entitiesDataCollection.Add(id, data);

            var model = new SettlementCashModel(description, data);

            _worldEntitiesModel.AddSettlementCash(id, model);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);

            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.SettlementCash);
            AddToStaticGrid(id, position.X, position.Z, gridModel);

            AddEntityReplicationSettings(id, new NoParamEntityStreamWriter(), _battleEntitiesReplicationDescriptions.SettlementCash);
        }

        public void RemoveSettlementCash(int id)
        {
            RemoveOtherHistoryModel(id);
            MarkRemoved(id);

            _worldEntitiesModel.RemoveSettlementCash(id);

            _entitiesDataCollection.Remove(id);

            _entitiesInfo.Remove(id);

            RemoveFromStaticGrid(id);

            RemoveEntityReplicationSettings(id);
        }

        
        public void AddMainBuildingPlotCarTrunk(PlotSlotDescription plotSlotDescription, IPlotCarModel plotCarModel)
        {
            LocationVehicleSlotDescription<PlotCarSlotDescription> locationVehicleSlotDescription = plotCarModel.Description;
            PlotCarSlotDescription plotCarSlotDescription = locationVehicleSlotDescription.SlotDescription;

            var settlementPlotServerModel = _plotsModel.Slots[plotSlotDescription];
            VehicleTrunkData vehicleTrunkData = settlementPlotServerModel.DatabasePartData.MainBuilding.CarSlots[plotCarSlotDescription].TrunkData;
            
            AddPlotCarTrunk(plotCarModel, vehicleTrunkData);
        }

        public void AddOutbuildingPlotCarTrunk(PlotSlotDescription plotSlotDescription, OutbuildingSlotDescription outbuildingSlotDescription, IPlotCarModel plotCarModel)
        {
            LocationVehicleSlotDescription<PlotCarSlotDescription> locationVehicleSlotDescription = plotCarModel.Description;
            PlotCarSlotDescription plotCarSlotDescription = locationVehicleSlotDescription.SlotDescription;

            var settlementPlotServerModel = _plotsModel.Slots[plotSlotDescription];
            HangarOutbuildingData outbuildingData = settlementPlotServerModel.DatabasePartData.Outbuildings[outbuildingSlotDescription];
            VehicleTrunkData vehicleTrunkData = outbuildingData.CarSlots[plotCarSlotDescription].TrunkData;
            
            AddPlotCarTrunk(plotCarModel, vehicleTrunkData);
        }

        private void AddPlotCarTrunk(IPlotCarModel plotCarModel, VehicleTrunkData vehicleTrunkData)
        {
            var id = VehicleTrunkSettingsDescription.GetVehicleTrunkEntityId(plotCarModel.Id);

            var car = plotCarModel.VehicleDescription;
            var carTrunkDescription = car.PoliceCarTrunk?.CarTrunk ?? car.CarSlot.Trunk;

            var plotCarTrunkEntityData = new PlotCarTrunkEntityData(carTrunkDescription, vehicleTrunkData.Inventory, vehicleTrunkData.CargoInventory, vehicleTrunkData.InventoryAdditionalLines, vehicleTrunkData.CargoInventoryAdditionalSlots);

            var plotVehicleTrunkModel = new PlotVehicleTrunkModel(id, plotCarTrunkEntityData);
            var vehicleTrunkInventoriesModel = new VehicleTrunkInventoriesModel(id, plotVehicleTrunkModel.Inventory, plotVehicleTrunkModel.CargoInventoryModel);
            
            _entitiesDataCollection.Add(id, plotCarTrunkEntityData);
            
            var entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            AddOtherHistoryModel(id, plotCarTrunkEntityData, entityInfo);
            
            _worldEntitiesModel.AddPlotVehicleTrunk(plotVehicleTrunkModel, vehicleTrunkInventoriesModel);
            _worldEntitiesModel.AddPlotVehicleTrunkInteraction(plotCarModel);
            
            AddPlotVehicleTrunkCargoInventorySlots(vehicleTrunkInventoriesModel);
            
            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _observableInventoryFilterModel.PlotVehicleTrunk(plotCarModel), new PositionModel(plotCarModel.TransformModel.GetVehicleTrunkPosition()), BattleDistancesDescription.TrunkInteractSqrDistance, BattleDistancesDescription.TrunkInteractSqrDistance));
            
            _changeVehicleTrunkEmptyFlagModel.UpdateVehicleTrunkIsEmptyFlag(vehicleTrunkInventoriesModel);
            _vehicleCargoInventoryChangeModel.UpdateCargoInventoryFlags(vehicleTrunkInventoriesModel);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.PlotCarTrunk;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            var streamWriter = new DescribedRuntimeEntityStreamWriter<CarTrunkDescription>(id, CarTrunkDescription.Enum, carTrunkDescription, replicationIndex, battleEntityReplicationDescription.MaxCount);
            AddEntityReplicationSettings(id, streamWriter, battleEntityReplicationDescription);
            MarkAdded(id);
        }

        public void AddOutbuildingPlotHelicopterTrunk(PlotSlotDescription plotSlotDescription, OutbuildingSlotDescription outbuildingSlotDescription, IPlotHelicopterModel plotHelicopterModel)
        {
            LocationVehicleSlotDescription<PlotHelicopterSlotDescription> locationVehicleSlotDescription = plotHelicopterModel.Description;
            PlotHelicopterSlotDescription plotHelicopterSlotDescription = locationVehicleSlotDescription.SlotDescription;

            var settlementPlotServerModel = _plotsModel.Slots[plotSlotDescription];
            HangarOutbuildingData outbuildingData = settlementPlotServerModel.DatabasePartData.Outbuildings[outbuildingSlotDescription];
            VehicleTrunkData vehicleTrunkData = outbuildingData.HelicopterSlots[plotHelicopterSlotDescription].TrunkData;
            
            AddPlotHelicopterTrunk(plotHelicopterModel, vehicleTrunkData);
        }

        private void AddPlotHelicopterTrunk(IPlotHelicopterModel plotHelicopterModel, VehicleTrunkData vehicleTrunkData)
        {
            var id = VehicleTrunkSettingsDescription.GetVehicleTrunkEntityId(plotHelicopterModel.Id);

            var helicopter = plotHelicopterModel.VehicleDescription;
            var helicopterTrunkDescription = helicopter.HelicopterSlot.Trunk;

            var plotHelicopterTrunkEntityData = new PlotHelicopterTrunkEntityData(helicopterTrunkDescription, vehicleTrunkData.Inventory, vehicleTrunkData.CargoInventory, vehicleTrunkData.InventoryAdditionalLines, vehicleTrunkData.CargoInventoryAdditionalSlots);

            var plotVehicleTrunkModel = new PlotVehicleTrunkModel(id, plotHelicopterTrunkEntityData);
            var vehicleTrunkInventoriesModel = new VehicleTrunkInventoriesModel(id, plotVehicleTrunkModel.Inventory, plotVehicleTrunkModel.CargoInventoryModel);

            _entitiesDataCollection.Add(id, plotHelicopterTrunkEntityData);

            var entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            AddOtherHistoryModel(id, plotHelicopterTrunkEntityData, entityInfo);

            _worldEntitiesModel.AddPlotVehicleTrunk(plotVehicleTrunkModel, vehicleTrunkInventoriesModel);
            _worldEntitiesModel.AddPlotVehicleTrunkInteraction(plotHelicopterModel);

            AddPlotVehicleTrunkCargoInventorySlots(vehicleTrunkInventoriesModel);

            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _observableInventoryFilterModel.PlotVehicleTrunk(plotHelicopterModel), new PositionModel(plotHelicopterModel.TransformModel.GetVehicleTrunkPosition()), BattleDistancesDescription.TrunkInteractSqrDistance, BattleDistancesDescription.TrunkInteractSqrDistance));

            _changeVehicleTrunkEmptyFlagModel.UpdateVehicleTrunkIsEmptyFlag(vehicleTrunkInventoriesModel);
            _vehicleCargoInventoryChangeModel.UpdateCargoInventoryFlags(vehicleTrunkInventoriesModel);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.PlotHelicopterTrunk;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            var streamWriter = new DescribedRuntimeEntityStreamWriter<HelicopterTrunkDescription>(id, HelicopterTrunkDescription.Enum, helicopterTrunkDescription, replicationIndex, battleEntityReplicationDescription.MaxCount);
            AddEntityReplicationSettings(id, streamWriter, battleEntityReplicationDescription);
            MarkAdded(id);
        }

        public void RemovePlotVehicleTrunk(int id)
        {
            RemovePlotVehicleTrunkCargoInventorySlots(id);

            _entitiesDataCollection.Remove(id);
            _entitiesInfo.Remove(id);
            RemoveOtherHistoryModel(id);

            _changeObservableInventoryModel.RemoveObservableInventory(id);

            _worldEntitiesModel.RemovePlotVehicleTrunk(id);
            _worldEntitiesModel.RemovePlotVehicleTrunkInteraction(id);
            _worldEntitiesModel.RemoveObservableInventory(id);

            RemoveEntityReplicationSettings(id);
            MarkRemoved(id);
        }

        public void AddBuilderDoor(IBuilderDoorModel builderDoorModel)
        {
            _systemsModel.AddBuilderDoor(_systems, builderDoorModel);
        }

        public void RemoveBuilderDoor(IBuilderDoorModel builderDoorModel)
        {
            _systemsModel.RemoveBuilderDoor(_systems, builderDoorModel.DescriptionModel.Value.BaseDescription.EntityId);
        }

        private void AddPlotVehicleTrunkCargoInventorySlots(IVehicleTrunkInventoriesModel vehicleTrunkInventoriesModel)
        {
            int trunkEntityId = vehicleTrunkInventoriesModel.Id;
        
            for (int i = 0; i < vehicleTrunkInventoriesModel.CargoInventoryModel.MaxSlotsCount; i++)
            {
                var slotModel = vehicleTrunkInventoriesModel.CargoInventoryModel[i];
                var slot = CargoInventorySlotDescription.Enum[i];
                var slotEntityId = VehicleTrunkSettingsDescription.GetVehicleTrunkCargoInventorySlotEntityId(trunkEntityId, slot);
                _worldEntitiesModel.AddVehicleTrunkCargoSlot(slotEntityId, slotModel, vehicleTrunkInventoriesModel);
            }
        }

        private void RemovePlotVehicleTrunkCargoInventorySlots(int plotVehicleTrunkId)
        {
            if (_worldEntitiesModel.VehicleTrunkInventories.TryGetModel(plotVehicleTrunkId, out var vehicleTrunkInventoriesModel))
            {
                int slotsCount = vehicleTrunkInventoriesModel.CargoInventoryModel.MaxSlotsCount;
                for (int index = 0; index < slotsCount; index++)
                {
                    var slot = CargoInventorySlotDescription.Enum[index];
                    var slotEntityId = VehicleTrunkSettingsDescription.GetVehicleTrunkCargoInventorySlotEntityId(plotVehicleTrunkId, slot);
                    _worldEntitiesModel.RemoveVehicleTrunkCargoSlot(slotEntityId);
                }
            }
        }
        
        public IFurnitureLootModel AddFurnitureLootModel(Vector3 position)
        {
            int id = _entityIdGenerator.Generate();

            var data = new FurnitureLootData();
            
            _entitiesDataCollection.Add(id, data);

            var model = new FurnitureLootModel(id, data);
            model.PositionModel.Set(position.X, position.Y, position.Z);
            model.SpawnTickIndex.Value = (int)_tick.TickIndex;

            var removeTs = _tick.Ts + FurnitureLootDescription.Lifetime;
            
            _systemsModel.AddFurnitureLoot(_systems, model, removeTs);
            
            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            _worldEntitiesModel.AddFurnitureLoot(model);
            
            AddFurnitureLootInventoryModel(model.InteractPositionModel);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.FurnitureLoot);
            AddToStaticGrid(id, position.X, position.Z, gridModel);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.FurnitureLoot;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new NoParamRuntimeEntityStreamWriter(id, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);

            return model;
        }

        public void RemoveFurnitureLootModel(int id)
        {
            if (_worldEntitiesModel.FurnitureLoots.ContainsKey(id))
            {
                int furnitureLootInventoryEntityId = ObservableInventoryDescription.GetInventoryEntityId(id);
                RemoveFurnitureLootInventoryModel(furnitureLootInventoryEntityId);
            
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);
                
                _systemsModel.RemoveFurnitureLoot(_systems, id);
                
                _worldEntitiesModel.RemoveFurnitureLoot(id);
                
                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);
                
                RemoveFromStaticGrid(id);

                RemoveEntityReplicationSettings(id);
            }
        }

        private void AddFurnitureLootInventoryModel(IPositionModel positionModel)
        {
            int id = _entityIdGenerator.Generate();

            var data = new FurnitureLootEntityInventoryData();
            
            _entitiesDataCollection.Add(id, data);

            var model = new FurnitureLootEntityInventoryModel(id, data);
            
            _worldEntitiesModel.AddFurnitureLootInventory(model);
            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _ => true, positionModel, BattleDistancesDescription.ObservableInventoryStartInteractSqrDistance, BattleDistancesDescription.ObservableInventoryStopInteractSqrDistance));

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.FurnitureLootInventory;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new NoParamRuntimeEntityStreamWriter(id, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);
        }

        private void RemoveFurnitureLootInventoryModel(int id)
        {
            if (_worldEntitiesModel.FurnitureLootEntityInventories.TryGetModel(id, out var model))
            {
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _changeObservableInventoryModel.RemoveObservableInventory(id);

                RemoveFurnitureLootInventories(model);

                _worldEntitiesModel.RemoveFurnitureLootInventory(id);
                _worldEntitiesModel.RemoveObservableInventory(id);

                _entitiesDataCollection.Remove(id);

                _entitiesInfo.Remove(id);

                RemoveEntityReplicationSettings(id);
            }
        }

        public void AddFurnitureLootInventories(IFurnitureLootEntityInventoryModel furnitureLootEntityInventoryModel)
        {
            foreach (var inventoryModel in furnitureLootEntityInventoryModel.InventoryPagesModel)
            {
                var inventoryPageId = _entityIdGenerator.Generate();
                _worldEntitiesModel.AddFurnitureLootInventoryPage(inventoryPageId, inventoryModel, furnitureLootEntityInventoryModel);
            }

            foreach (var cargoInventoryModel in furnitureLootEntityInventoryModel.CargoInventoryPagesModel)
            {
                foreach (var slotModel in cargoInventoryModel)
                {
                    var id = _entityIdGenerator.Generate();
                    _worldEntitiesModel.AddFurnitureLootCargoInventorySlot(id, slotModel, furnitureLootEntityInventoryModel);
                }
            }
        }

        public void RemoveFurnitureLootInventories(IFurnitureLootEntityInventoryModel furnitureLootEntityInventoryModel)
        {
            foreach (var inventoryModel in furnitureLootEntityInventoryModel.InventoryPagesModel)
            {
                var inventoryPageId = FurnitureLootDescription.GetFurnitureLootInventoryPageEntityId(furnitureLootEntityInventoryModel, inventoryModel);
                _worldEntitiesModel.RemoveFurnitureLootInventoryPage(inventoryPageId);
            }

            foreach (var cargoInventoryModel in furnitureLootEntityInventoryModel.CargoInventoryPagesModel)
            {
                foreach (var slotDescription in FurnitureLootDescription.PageCargoInventoryDescription.Slots)
                {
                    var id = FurnitureLootDescription.GetFurnitureLootCargoInventorySlotEntityId(furnitureLootEntityInventoryModel, cargoInventoryModel, slotDescription);
                    _worldEntitiesModel.RemoveFurnitureLootCargoInventorySlot(id);
                }
            }
        }

        public void AddBuildingLoot(IBuildingLootModel buildingLootModel)
        {
            _systemsModel.AddBuildingLoot(_systems, buildingLootModel);
        }

        public void RemoveBuildingLoot(int id)
        {
            _systemsModel.RemoveBuildingLoot(_systems, id);
        }

        public IBuildingLootEntityInventoryModel AddMainBuildingLootInventory(PlotSlotDescription plotSlotDescription, IBuildingLootModel buildingLootModel)
        {
            var settlementPlotServerModel = _plotsModel.Slots[plotSlotDescription];
            HangarBuildingLootData hangarBuildingLootData = settlementPlotServerModel.DatabasePartData.MainBuildingLoot;
            
            return AddBuildingLootInventory(hangarBuildingLootData, buildingLootModel);
        }

        public IBuildingLootEntityInventoryModel AddOutbuildingLootInventory(PlotSlotDescription plotSlotDescription, OutbuildingSlotDescription outbuildingSlotDescription, IBuildingLootModel buildingLootModel)
        {
            var settlementPlotServerModel = _plotsModel.Slots[plotSlotDescription];
            HangarBuildingLootData hangarBuildingLootData = settlementPlotServerModel.DatabasePartData.OutbuildingLoots[outbuildingSlotDescription];
            
            return AddBuildingLootInventory(hangarBuildingLootData, buildingLootModel);
        }

        public IBuildingLootEntityInventoryModel AddCompoundWallBuildingLootInventory(PlotSlotDescription plotSlotDescription, IBuildingLootModel buildingLootModel)
        {
            var settlementPlotServerModel = _plotsModel.Slots[plotSlotDescription];
            HangarBuildingLootData hangarBuildingLootData = settlementPlotServerModel.DatabasePartData.CompoundWallLoot;
            
            return AddBuildingLootInventory(hangarBuildingLootData, buildingLootModel);
        }

        private IBuildingLootEntityInventoryModel AddBuildingLootInventory(HangarBuildingLootData hangarBuildingLootData, IBuildingLootModel buildingLootModel)
        {
            int id = buildingLootModel.Description.EntityIds.InventoryEntityId;
            
            var data = new BuildingLootEntityInventoryData(hangarBuildingLootData.InventoryPages, hangarBuildingLootData.CargoInventoryPages);
            
            _entitiesDataCollection.Add(id, data);

            var model = new BuildingLootEntityInventoryModel(id, data);
            
            _worldEntitiesModel.AddBuildingLootInventory(model);
            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _ => true, buildingLootModel.PositionModel, BattleDistancesDescription.ObservableInventoryStartInteractSqrDistance, BattleDistancesDescription.ObservableInventoryStopInteractSqrDistance));

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.BuildingLootInventory;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            AddEntityReplicationSettings(id, new NoParamRuntimeEntityStreamWriter(id, replicationIndex, battleEntityReplicationDescription.MaxCount), battleEntityReplicationDescription);

            return model;
        }

        public void RemoveBuildingLootInventory(int id)
        {
            if (_worldEntitiesModel.BuildingLootEntityInventories.ContainsKey(id))
            {
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _changeObservableInventoryModel.RemoveObservableInventory(id);

                _worldEntitiesModel.RemoveBuildingLootInventory(id);
                _worldEntitiesModel.RemoveObservableInventory(id);

                _entitiesDataCollection.Remove(id);

                _entitiesInfo.Remove(id);

                RemoveEntityReplicationSettings(id);
            }
        }
        
        public void AddBuildingLootInventories(IBuildingLootEntityInventoryModel buildingLootEntityInventoryModel)
        {
            var startEntityId = BattleEntityIdData.Empty;
            foreach (var inventoryModel in buildingLootEntityInventoryModel.InventoryPagesModel)
            {
                var inventoryPageId = _entityIdGenerator.Generate();
                _worldEntitiesModel.AddBuildingLootInventoryPage(inventoryPageId, inventoryModel, buildingLootEntityInventoryModel);

                if (startEntityId == BattleEntityIdData.Empty)
                {
                    startEntityId = inventoryPageId;
                }
            }

            foreach (var cargoInventoryModel in buildingLootEntityInventoryModel.CargoInventoryPagesModel)
            {
                foreach (var slotDescription in BuildingLootDescription.PageCargoInventoryDescription.Slots)
                {
                    var slotEntityId = _entityIdGenerator.Generate();
                    var slotModel = cargoInventoryModel[slotDescription];
                    _worldEntitiesModel.AddBuildingLootCargoInventorySlot(slotEntityId, slotModel, buildingLootEntityInventoryModel);
                    
                    if (startEntityId == BattleEntityIdData.Empty)
                    {
                        startEntityId = slotEntityId;
                    }
                }
            }

            buildingLootEntityInventoryModel.StartEntityId.Value = startEntityId;
        }

        public void RemoveBuildingLootInventories(IBuildingLootEntityInventoryModel buildingLootEntityInventoryModel)
        {
            foreach (var inventoryModel in buildingLootEntityInventoryModel.InventoryPagesModel)
            {
                var inventoryPageId = BuildingLootDescription.GetBuildingLootInventoryPageEntityId(buildingLootEntityInventoryModel, inventoryModel);
                _worldEntitiesModel.RemoveBuildingLootInventoryPage(inventoryPageId);
            }

            foreach (var cargoInventoryModel in buildingLootEntityInventoryModel.CargoInventoryPagesModel)
            {
                foreach (var slotDescription in BuildingLootDescription.PageCargoInventoryDescription.Slots)
                {
                    var slotEntityId = BuildingLootDescription.GetBuildingLootCargoInventorySlotEntityId(buildingLootEntityInventoryModel, cargoInventoryModel, slotDescription);
                    _worldEntitiesModel.RemoveBuildingLootCargoInventorySlot(slotEntityId);
                }
            }
        }
        
        public void AddWindowBlinds(IWindowBlindsModel windowBlindsModel)
        {
            _systemsModel.AddWindowBlinds(_systems, windowBlindsModel);
        }

        public void RemoveWindowBlinds(IWindowBlindsModel windowBlindsModel)
        {
            _systemsModel.RemoveWindowBlinds(_systems, windowBlindsModel.DescriptionModel.Value.EntityId);
        }

        public void AddLocationEventCargoSpawnPoint(LocationEventCargoSpawnPointDescription description)
        {
            LocationEventCargoData data = new LocationEventCargoData(description);

            int id = description.EntityId;
            
            _entitiesDataCollection.Add(id, data);

            ILocationEventCargoSpawnPointModel model = new LocationEventCargoSpawnPointModel(data);

            _worldEntitiesModel.AddLocationEventCargoSpawnPoint(id, model);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.LocationEventCargo);
            AddToStaticGrid(id, description.Position.X, description.Position.Z, gridModel);

            AddEntityReplicationSettings(id, new LocationEntityStreamWriter<LocationEventCargoSpawnPointDescription>(_location.LocationEventCargos, description), _battleEntitiesReplicationDescriptions.LocationEventCargo);
        }

        public void RemoveLocationEventCargoSpawnPoint(int id)
        {
            if (_worldEntitiesModel.LocationEventCargoSpawnPoints.ContainsKey(id))
            {
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _worldEntitiesModel.RemoveLocationEventCargoSpawnPoint(id);
                
                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);
                
                RemoveFromStaticGrid(id);
                
                RemoveEntityReplicationSettings(id);
            }
        }
        
        public void AddCashLootNode(LocationCashLootNodeDescription description)
        {
            CashLootNodeData data = new CashLootNodeData(description);

            int id = description.EntityId;
            
            _entitiesDataCollection.Add(id, data);

            ICashLootNodeModel model = new CashLootNodeModel(data);

            _worldEntitiesModel.AddCashLootNode(id, model);

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var gridModel = CreateEntityGridModel(BattleEntityReplicationGridLayerDescription.CashLootNode);
            AddToStaticGrid(id, description.Position.X, description.Position.Z, gridModel);

            AddEntityReplicationSettings(id, new LocationEntityStreamWriter<LocationCashLootNodeDescription>(_location.CashLootNodes, description), _battleEntitiesReplicationDescriptions.CashLootNode);
        }

        public void RemoveCashLootNode(int id)
        {
            if (_worldEntitiesModel.CashLootNodes.ContainsKey(id))
            {
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _worldEntitiesModel.RemoveCashLootNode(id);
                
                _entitiesDataCollection.Remove(id);
                
                _entitiesInfo.Remove(id);
                
                RemoveFromStaticGrid(id);
                
                RemoveEntityReplicationSettings(id);
            }
        }

        public void AddPlotBuildingDefenseBlock(IPlotBuildingDefenseBlockPublicModel defenseBlockPublicModel, PlotBuildingDefenseBlockData defenseBlockData)
        {
            var panelDescription = defenseBlockPublicModel.DescriptionModel.Value;

            var id = panelDescription.EntityIds.PrivateModelEntityId;
            
            var data = new PlotBuildingDefenseBlockPrivateData(panelDescription.Description, defenseBlockData.ModuleSlots, defenseBlockData.DecoderAttackPoints);

            _entitiesDataCollection.Add(id, data);

            var model = new PlotBuildingDefenseBlockPrivateModel(id, data);
            
            defenseBlockPublicModel.UpdateDefenseBlockSlotsIsEmptyFlag(model);
            
            _worldEntitiesModel.AddPlotBuildingDefenseBlockPrivateModel(id, model);
            _worldEntitiesModel.AddObservableInventory(id, new ObservableInventoryEntityModel(id, _observableInventoryFilterModel.PlotBuildingDefenseBlock(model), new PositionModel(panelDescription.Position), BattleDistancesDescription.ObservableInventoryStartInteractSqrDistance, BattleDistancesDescription.ObservableInventoryStopInteractSqrDistance));

            EntityInfo entityInfo = _entitiesInfo.Add(id, _tick.TickIndex);
            
            MarkAdded(id);
            AddOtherHistoryModel(id, data, entityInfo);

            var battleEntityReplicationDescription = _battleEntitiesReplicationDescriptions.PlotBuildingDefenseBlock;
            var replicationIndex = OccupyEntityReplicationIndex(id, battleEntityReplicationDescription);

            var streamWriter = new DescribedRuntimeEntityStreamWriter<PlotBuildingDefenseBlockDescription>(id, PlotBuildingDefenseBlockDescription.Enum, panelDescription.Description, replicationIndex, battleEntityReplicationDescription.MaxCount);
            AddEntityReplicationSettings(id, streamWriter, battleEntityReplicationDescription);
        }

        public void RemovePlotBuildingDefenseBlock(int id)
        {
            if (_worldEntitiesModel.PlotBuildingDefenseBlockPrivateModels.ContainsKey(id))
            {
                RemoveOtherHistoryModel(id);
                MarkRemoved(id);

                _changeObservableInventoryModel.RemoveObservableInventory(id);

                _worldEntitiesModel.RemovePlotBuildingDefenseBlockPrivateModel(id);
                _worldEntitiesModel.RemoveObservableInventory(id);

                _entitiesDataCollection.Remove(id);

                _entitiesInfo.Remove(id);

                RemoveEntityReplicationSettings(id);
            }
        }
        
        private void AddSelfHistoryModel(int id, IData data, EntityInfo entityInfo)
        {
            AddHistoryModel(id, data, entityInfo, _selfDiffHistoryModels);
        }
        
        private void AddOtherHistoryModel(int id, IData data, EntityInfo entityInfo)
        {
            AddHistoryModel(id, data, entityInfo, _otherDiffHistoryModels);
        }

        private void RemoveOtherHistoryModel(int id)
        {
            _otherDiffHistoryModels.RemoveEntity(id);
        }
        
        private void AddHistoryModel(int id, IData data, EntityInfo entityInfo, IDiffHistoryModels diffHistoryModels)
        {
            diffHistoryModels.AddEntity(id, data, entityInfo);
        }

        private void AddEntityReplicationSettings(int id, IEntityStreamWriter streamWriter, BattleEntityReplicationDescription battleEntityReplicationDescription)
        {
            _entityStreamWriters.Add(id, streamWriter);
            _entityReplicationDescriptions.Add(id, battleEntityReplicationDescription);
        }

        private void RemoveEntityReplicationSettings(int id)
        {
            var battleEntityReplicationDescription = _entityReplicationDescriptions[id];
            if (_replicationIndexesPools.TryGet(battleEntityReplicationDescription, out var replicationIndexesPool))
            {
                replicationIndexesPool.FreeIndex(id);
            }

            _entityStreamWriters.Remove(id);
            _entityReplicationDescriptions.Remove(id);
        }

        private int OccupyEntityReplicationIndex(int entityId, BattleEntityReplicationDescription battleEntityReplicationDescription)
        {
            var replicationIndexesPool = _replicationIndexesPools.Get(battleEntityReplicationDescription);
            var index = replicationIndexesPool.OccupyIndex(entityId);
            return index;
        }

        private void MarkAdded(int id)
        {
            _worldEnteredLeftEntities.Added.Add(id);
        }
        
        private void MarkRemoved(int id)
        {
            if (!_worldEnteredLeftEntities.Added.Remove(id))
            {
                _worldEnteredLeftEntities.Removed.Add(id);
            }
        }

        private void AddToStaticGrid(int id, float x, float y, IEntityGridModel entityGridModel)
        {
            IRegularGrid grid = _grids[entityGridModel.ReplicationGridLayer];

            GridModel.UpdateEntityGridModel(x, y, grid, entityGridModel);

            grid.Add(id, entityGridModel.X, entityGridModel.Y);
            entityGridModel.IsInGrid = true;
            _worldEntityGridModels.AddModel(id, entityGridModel);
        }

        private void RemoveFromStaticGrid(int id)
        {
            if (_worldEntityGridModels.GridModels.TryGetModel(id, out var entityGridModel))
            {
                IRegularGrid grid = _grids[entityGridModel.ReplicationGridLayer];
                grid.Remove(id);
                _worldEntityGridModels.RemoveModel(id);
            }
        }

        private void AddToDynamicGrid(UpdateGridSystem gridSystem, int id, IPositionModel positionModel, IEntityGridModel entityGridModel)
        {
            IRegularGrid grid = _grids[entityGridModel.ReplicationGridLayer];

            GridModel.UpdateEntityGridModel(positionModel.X, positionModel.Z, grid, entityGridModel);

            grid.Add(id, positionModel.X, positionModel.Z);
            entityGridModel.IsInGrid = true;
            gridSystem.Add(id, positionModel);
            _worldEntityGridModels.AddModel(id, entityGridModel);
        }

        private void RemoveFromDynamicGrid(UpdateGridSystem gridSystem, int id)
        {
            if (_worldEntityGridModels.GridModels.TryGetModel(id, out var entityGridModel))
            {
                var grid = _grids[entityGridModel.ReplicationGridLayer];
                grid.Remove(id);
                gridSystem.Remove(id);
                _worldEntityGridModels.RemoveModel(id);
            }
        }

        private IEntityGridModel CreateEntityGridModel(BattleEntityReplicationGridLayerDescription description)
        {
            return new EntityGridModel(description.Layer);
        }

        public void Update(ITick tick)
        {
            foreach (var system in _systems.Systems)
            {
                system.Update(tick);
            }
        }
    }
}