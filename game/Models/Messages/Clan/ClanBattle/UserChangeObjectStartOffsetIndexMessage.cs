using Framework.Ecs.Message;
using Framework.Replication.Data.Primitive;
using Models.Data.Clan;
using Models.References.ClanBattle;

namespace Models.Messages.Clan.ClanBattle
{
    public class UserChangeObjectStartOffsetIndexMessage : Message
    {
        private readonly IPrimitiveData<ClanConquestObjectTypeDescription> _objectType = new ReferenceData<ClanConquestObjectTypeDescription>(ClanConquestObjectTypeDescription.Enum);
        private readonly IPrimitiveData<int> _startOffsetIndex = new ClanBattleStartOffsetIndexData();

        public UserChangeObjectStartOffsetIndexMessage(ClanConquestObjectTypeDescription objectType, int startOffsetIndex) : this()
        {
            _objectType.Value = objectType;
            _startOffsetIndex.Value = startOffsetIndex;
        }

        public UserChangeObjectStartOffsetIndexMessage()
        {
            Add("object_type", _objectType);
            Add("start_offset_index", _startOffsetIndex);
        }

        public ClanConquestObjectTypeDescription ObjectType => _objectType.Value;
        public int StartOffsetIndex => _startOffsetIndex.Value;

        public override string Type => "user_change_object_start_offset_index";
    }
}