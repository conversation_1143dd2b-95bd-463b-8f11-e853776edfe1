using Models.Models.CarUpgradesHolderModel;

namespace Models.Messages.Vehicle.Car
{
    public class OpenCarUpgradesHolderMessage : CarUpgradesHolderMessage
    {
        public OpenCarUpgradesHolderMessage(int entityId, CarUpgradesHolderTypeDescription holderType) : base(entityId, holderType)
        {
        }

        public OpenCarUpgradesHolderMessage()
        {
        }

        public override string Type => "open_car_upgrades_holder";
    }
}