using Models.References.Inventory;

namespace Models.Messages.Inventory.Move
{
    public class FromEquipToInventoryMoveMessage : InventoryMoveMessage
    {
        public FromEquipToInventoryMoveMessage(EquipSlotDescription equipSlot, InventorySlotDescription inventorySlot, int count)
            : base(
                SlotSourceDescription.Equip,
                null,
                equipSlot,
                null,
                0,
                count,
                SlotSourceDescription.Inventory,
                inventorySlot,
                null,
                null,
                0)
        {
        }
    }
}