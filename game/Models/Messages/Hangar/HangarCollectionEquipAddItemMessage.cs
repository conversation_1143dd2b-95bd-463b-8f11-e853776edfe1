using Framework.Replication.Data.Primitive;
using Models.References.Collection;
using Models.References.Inventory;

namespace Models.Messages.Hangar
{
    public class HangarCollectionEquipAddItemMessage : HangarCollectionAddItemMessage
    {
        private readonly IPrimitiveData<EquipSlotDescription> _equipSlot = new ReferenceData<EquipSlotDescription>(EquipSlotDescription.Enum);

        public HangarCollectionEquipAddItemMessage(int entityId, CollectionTableSlotDescription collectionTableSlotDescription, CollectionItemSlotDescription collectionItemSlotDescription, EquipSlotDescription equipSlotDescription) : base(entityId, collectionTableSlotDescription, collectionItemSlotDescription)
        {
            Add("equip_slot", _equipSlot);
            _equipSlot.Value = equipSlotDescription;
        }

        public HangarCollectionEquipAddItemMessage()
        {
            Add("equip_slot", _equipSlot);
        }

        public override string Type => "hangar_collection_equip";
        public EquipSlotDescription EquipSlotDescription => _equipSlot.Value;
    }
}