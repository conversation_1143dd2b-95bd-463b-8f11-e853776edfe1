using Framework.Replication.Data.Primitive;
using Models.References.Collection;
using Models.References.Inventory;

namespace Models.Messages.Hangar
{
    public class HangarCollectionPocketAddItemMessage : HangarCollectionAddItemMessage
    {
        private readonly IPrimitiveData<PocketSlotDescription> _pocketSlot = new ReferenceData<PocketSlotDescription>(PocketSlotDescription.Enum);

        public HangarCollectionPocketAddItemMessage(int entityId, CollectionTableSlotDescription collectionTableSlotDescription, CollectionItemSlotDescription collectionItemSlotDescription, PocketSlotDescription pocketSlotDescription) : base(entityId, collectionTableSlotDescription, collectionItemSlotDescription)
        {
            Add("pocket_slot", _pocketSlot);
            _pocketSlot.Value = pocketSlotDescription;
        }

        public HangarCollectionPocketAddItemMessage()
        {
            Add("pocket_slot", _pocketSlot);
        }

        public override string Type => "hangar_collection_pocket";
        public PocketSlotDescription PocketSlotDescription => _pocketSlot.Value;
    }
}