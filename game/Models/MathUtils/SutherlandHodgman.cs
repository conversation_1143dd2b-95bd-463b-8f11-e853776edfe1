using System;
using Models.Utils.Assertion;

#if UNITY_MATH
using Unity.Mathematics;
#else
using System.Numerics;
#endif

namespace Models.MathUtils
{
    public static class SutherlandHodgman
    {
#if UNITY_MATH
        public static int Clip(in float3 clippingPlaneNormal, float clippingPlaneDistance, in Span<float3> clippingPolygon, int clippingPolygonVertexCount, in Span<float3> result)
#else
        public static int Clip(in Vector3 clippingPlaneNormal, float clippingPlaneDistance, in Span<Vector3> clippingPolygon, int clippingPolygonVertexCount, in Span<Vector3> result)
#endif
        {
            // reference: http://www.sunshine2k.de/coding/java/SutherlandHodgman/SutherlandHodgman.html
            // note: reference has wrong info about concave shapes; this algorithm cant cut concave shapes without problems

            // todo: assert with convexity check
            
            Assert.Condition(clippingPolygonVertexCount > 0, $"clipping polygon is empty or has invalid clippingPolygonVertexCount={clippingPolygonVertexCount}");
            Assert.Condition(clippingPolygon.Length >= clippingPolygonVertexCount, $"clipping polygon has invalid capacity clippingPolygon.Length={clippingPolygon.Length} clippingPolygonVertexCount={clippingPolygonVertexCount}");
            
            Assert.Condition(result.Length >= clippingPolygonVertexCount + 1, $"not enough memory for result result.Length={result.Length} clippingPolygonVertexCount={clippingPolygonVertexCount}");
            
            int clippingPolygonResultVertexCount = 0;
            
#if UNITY_MATH
            float3 vertex1 = clippingPolygon[clippingPolygonVertexCount - 1];
            float distanceToPlane1 = math.dot(vertex1, clippingPlaneNormal) - clippingPlaneDistance;
#else
            Vector3 vertex1 = clippingPolygon[clippingPolygonVertexCount - 1];
            float distanceToPlane1 = Vector3.Dot(vertex1, clippingPlaneNormal) - clippingPlaneDistance;
#endif

            for (int i = 0; i < clippingPolygonVertexCount; i++)
            {
#if UNITY_MATH
                float3 vertex2 = clippingPolygon[i];
                float distanceToPlane2 = math.dot(vertex2, clippingPlaneNormal) - clippingPlaneDistance;
#else
                Vector3 vertex2 = clippingPolygon[i];
                float distanceToPlane2 = Vector3.Dot(vertex2, clippingPlaneNormal) - clippingPlaneDistance;
#endif

                if (distanceToPlane1 <= 0 && distanceToPlane2 <= 0)
                {
                    // both vertices are behind or lying on the plane
                    // -> keep current vertex only (last iteration handle current vertex1 as vertex2)
                    result[clippingPolygonResultVertexCount++] = vertex2;
                }
                else if (distanceToPlane1 <= 0 && distanceToPlane2 > 0)
                {
                    // vertex1 is behind the plane
                    // vertex2 is in front of the plane
                    // -> save intersection point only (vertex2 is in front of the plane and we dont need it; vertex1 is behind the plane but its already handled or we handle it if its last vertex)

                    float timeFrom1to2 = distanceToPlane1 / (distanceToPlane1 - distanceToPlane2);
                    var intersectionVertex = vertex1 + (vertex2 - vertex1) * timeFrom1to2;
                    
                    result[clippingPolygonResultVertexCount++] = intersectionVertex;
                }
                else if (distanceToPlane1 > 0 && distanceToPlane2 <= 0)
                {
                    // vertex1 is in front of the plane
                    // vertex2 is behind the plane
                    // -> save intersection point AND vertex2 (vertex1 is in front of the plane and we dont need it; vertex2 is behind the plane and this is only place where we handle this points so we save it)

                    float timeFrom1to2 = distanceToPlane1 / (distanceToPlane1 - distanceToPlane2);
                    var intersectionVertex = vertex1 + (vertex2 - vertex1) * timeFrom1to2;
                    
                    result[clippingPolygonResultVertexCount++] = intersectionVertex;
                    result[clippingPolygonResultVertexCount++] = vertex2;
                }
                else
                {
                    // both vertices are in front of the plane
                    // -> skip
                }
                
                vertex1 = vertex2;
                distanceToPlane1 = distanceToPlane2;
            }

            return clippingPolygonResultVertexCount;
        }
    }
}