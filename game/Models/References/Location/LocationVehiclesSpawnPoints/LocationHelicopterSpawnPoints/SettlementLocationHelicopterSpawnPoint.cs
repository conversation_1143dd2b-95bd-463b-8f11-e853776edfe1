using System.Numerics;
using System.Collections.Generic;

namespace Models.References.Location.LocationVehiclesSpawnPoints.LocationHelicopterSpawnPoints
{
/// <auto-generated>This is autogenerated class from <see cref="LocationHelicoptersFile"/></auto-generated>
public class SettlementLocationHelicopterSpawnPoint : ILocationHelicopterSpawnPoint
{
    public SettlementLocationHelicopterSpawnPoint()
    {
        Points = new List<LocationVehicleSpawnPointDescription>()
        {
            new(320, VehicleSpawnPointDescription.ParkingMeter1, new Vector3(98.24f, 8.9554f, 17.11f), new Quaternion(0f, -0.2588186f, 0f, 0.965926f), new List<(Vector3, Quaternion)>() {(new Vector3(105.5734f, 9.1054f, 15.6282f), new Quaternion(0f, 0.2588212f, 0f, -0.9659253f))}),
        };
    }
    
    public IReadOnlyList<LocationVehicleSpawnPointDescription> Points { get; }
}
}