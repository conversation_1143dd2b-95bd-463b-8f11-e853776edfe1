using System.Numerics;
using System.Collections.Generic;

namespace Models.References.Location.LocationVehiclesSpawnPoints.LocationCarSpawnPoints
{
/// <auto-generated>This is autogenerated class from <see cref="LocationCarsFile"/></auto-generated>
public class HubLocationCarSpawnPoint : ILocationCarSpawnPoint
{
    public HubLocationCarSpawnPoint()
    {
        Points = new List<LocationVehicleSpawnPointDescription>()
        {
            new(187, VehicleSpawnPointDescription.ParkingMeter1, new Vector3(-38.0223f, 32.015f, 59.722f), new Quaternion(0f, 0.9914443f, 0f, 0.1305304f), new List<(Vector3, Quaternion)>() {(new Vector3(-34.9829f, 32.135f, 57.4202f), new Quaternion(0f, 0.9914443f, 0f, 0.1305304f)), (new Vector3(-31.3323f, 32.135f, 58.2846f), new Quaternion(0f, 0.9914443f, 0f, 0.1305304f))}),
            new(188, VehicleSpawnPointDescription.ParkingMeter1, new Vector3(-56.3503f, 32.015f, -16.9971f), new Quaternion(0f, 0.6087595f, 0f, 0.7933549f), new List<(Vector3, Quaternion)>() {(new Vector3(-65.6274f, 32.135f, -22.4024f), new Quaternion(0f, 0.6087595f, 0f, 0.7933549f)), (new Vector3(-67.4858f, 32.135f, -17.051f), new Quaternion(0f, 0.6087595f, 0f, 0.7933549f))}),
            new(189, VehicleSpawnPointDescription.ParkingMeter1, new Vector3(286.11f, 34.85f, -15.59f), new Quaternion(0f, 1f, 0f, 3.397465E-06f), new List<(Vector3, Quaternion)>() {(new Vector3(297.11f, 35.09f, -19.59f), new Quaternion(0f, 1f, 0f, 3.397465E-06f)), (new Vector3(291.11f, 35.09f, -19.59f), new Quaternion(0f, 1f, 0f, 3.397465E-06f))}),
            new(190, VehicleSpawnPointDescription.ParkingMeter1, new Vector3(301.21f, 34.8f, 105.6f), new Quaternion(0f, -1.877546E-06f, 0f, 1f), new List<(Vector3, Quaternion)>() {(new Vector3(291.32f, 34.98f, 109.22f), new Quaternion(0f, -1.877546E-06f, 0f, 1f)), (new Vector3(296.32f, 34.98f, 109.22f), new Quaternion(0f, -1.877546E-06f, 0f, 1f))}),
        };
    }
    
    public IReadOnlyList<LocationVehicleSpawnPointDescription> Points { get; }
}
}