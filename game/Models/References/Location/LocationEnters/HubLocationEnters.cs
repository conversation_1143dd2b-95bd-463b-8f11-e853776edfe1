using System.Collections.Generic;
using System.Numerics;

namespace Models.References.Location.LocationEnters
{
/// <auto-generated>This is autogenerated class from <see cref="LocationEntersFile"/></auto-generated>
public class HubLocationEnters : ILocationEnters
{
    public HubLocationEnters()
    {
        BorderEnterGroups = new LocationEnterGroupDescription[]
        {
            new (new LocationBorderEnterPoint(new OrientedPoint(new Vector3(541.8485f, 31.9981f, 24.6175f), new Quaternion(0f, 0.4718766f, 0f, -0.8816647f)), new OrientedPoint(new Vector3(547.3924f, 106.8881f, 32.6997f), new Quaternion(0f, 0.4718766f, 0f, -0.8816647f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(538.0157f, 31.9981f, 18.8679f), new Quaternion(0f, 0.4718766f, 0f, -0.8816647f)), new OrientedPoint(new Vector3(542.4915f, 106.8881f, 25.589f), new Quaternion(0f, 0.4718766f, 0f, -0.8816647f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(545.354f, 31.9981f, 29.8762f), new Quaternion(0f, 0.4718766f, 0f, -0.8816647f)), new OrientedPoint(new Vector3(537.8217f, 106.8881f, 18.5767f), new Quaternion(0f, 0.4718766f, 0f, -0.8816647f)))),
            new (new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-493.3846f, 34.9614f, -44.3019f), new Quaternion(0f, 0.4290712f, 0f, 0.9032708f)), new OrientedPoint(new Vector3(-491.8805f, 109.8513f, -54.0265f), new Quaternion(0f, 0.6402599f, 0f, 0.7681584f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-497.7504f, 34.9614f, -38.9457f), new Quaternion(0f, 0.4290712f, 0f, 0.9032708f)), new OrientedPoint(new Vector3(-493.3043f, 109.8513f, -45.5087f), new Quaternion(0f, 0.6402599f, 0f, 0.7681584f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-489.3918f, 34.9614f, -49.2008f), new Quaternion(0f, 0.4290712f, 0f, 0.9032708f)), new OrientedPoint(new Vector3(-494.8257f, 109.8513f, -37.2222f), new Quaternion(0f, 0.6402599f, 0f, 0.7681584f)))),
            new (new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-330.6733f, 53.0181f, 530.4665f), new Quaternion(0f, 0.6256344f, 0f, 0.7801164f)), new OrientedPoint(new Vector3(-329.1496f, 127.9081f, 529.2605f), new Quaternion(0f, 0.8324245f, 0f, 0.5541387f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-335.3646f, 53.0181f, 535.54f), new Quaternion(0f, 0.6256344f, 0f, 0.7801164f)), new OrientedPoint(new Vector3(-325.7254f, 127.9081f, 537.1936f), new Quaternion(0f, 0.8324245f, 0f, 0.5541387f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-326.3826f, 53.0181f, 525.8262f), new Quaternion(0f, 0.6256344f, 0f, 0.7801164f)), new OrientedPoint(new Vector3(-322.448f, 127.9081f, 544.991f), new Quaternion(0f, 0.8324245f, 0f, 0.5541387f)))),
            new (new LocationBorderEnterPoint(new OrientedPoint(new Vector3(133.9619f, 48.9081f, 543.9603f), new Quaternion(0f, 0.7623016f, 0f, -0.647222f)), new OrientedPoint(new Vector3(123.9326f, 123.7981f, 544.2825f), new Quaternion(0f, 0.9997131f, 0f, -0.02395607f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(135.0826f, 48.9081f, 537.1418f), new Quaternion(0f, 0.7623016f, 0f, -0.647222f)), new OrientedPoint(new Vector3(132.5513f, 123.7981f, 543.7353f), new Quaternion(0f, 0.9997131f, 0f, -0.02395607f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(132.9369f, 48.9081f, 550.1966f), new Quaternion(0f, 0.7623016f, 0f, -0.647222f)), new OrientedPoint(new Vector3(140.9668f, 123.7981f, 543.3356f), new Quaternion(0f, 0.9997131f, 0f, -0.02395607f)))),
            new (new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-330.3822f, 46.6781f, -519.2183f), new Quaternion(0f, 0.3563626f, 0f, 0.9343479f)), new OrientedPoint(new Vector3(-320.9433f, 121.5681f, -521.9596f), new Quaternion(0f, 0.1407201f, 0f, 0.9900495f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-335.5371f, 46.6781f, -514.6168f), new Quaternion(0f, 0.3563626f, 0f, 0.9343479f)), new OrientedPoint(new Vector3(-329.199f, 121.5681f, -519.425f), new Quaternion(0f, 0.1407201f, 0f, 0.9900495f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-325.6674f, 46.6781f, -523.4269f), new Quaternion(0f, 0.3563626f, 0f, 0.9343479f)), new OrientedPoint(new Vector3(-337.2914f, 121.5681f, -517.0811f), new Quaternion(0f, 0.1407201f, 0f, 0.9900495f)))),
            new (new LocationBorderEnterPoint(new OrientedPoint(new Vector3(535.1317f, 28.9859f, -252.8317f), new Quaternion(0f, 0.872849f, 0f, -0.4879905f)), new OrientedPoint(new Vector3(530.1099f, 103.8759f, -244.4151f), new Quaternion(0f, 0.872849f, 0f, -0.4879905f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(538.7506f, 28.9859f, -258.7182f), new Quaternion(0f, 0.872849f, 0f, -0.4879905f)), new OrientedPoint(new Vector3(534.5182f, 103.8759f, -251.8412f), new Quaternion(0f, 0.872849f, 0f, -0.4879905f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(531.8217f, 28.9859f, -247.4478f), new Quaternion(0f, 0.872849f, 0f, -0.4879905f)), new OrientedPoint(new Vector3(538.934f, 103.8759f, -259.0163f), new Quaternion(0f, 0.872849f, 0f, -0.4879905f)))),
            new (new LocationBorderEnterPoint(new OrientedPoint(new Vector3(536.3488f, 38.2281f, 389.0574f), new Quaternion(0f, 0.6402791f, 0f, -0.7681424f)), new OrientedPoint(new Vector3(538.2415f, 113.118f, 398.6738f), new Quaternion(0f, 0.6402791f, 0f, -0.7681424f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(535.1044f, 38.2281f, 382.2604f), new Quaternion(0f, 0.6402791f, 0f, -0.7681424f)), new OrientedPoint(new Vector3(536.5549f, 113.118f, 390.2041f), new Quaternion(0f, 0.6402791f, 0f, -0.7681424f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(537.4869f, 38.2281f, 395.2741f), new Quaternion(0f, 0.6402791f, 0f, -0.7681424f)), new OrientedPoint(new Vector3(535.0414f, 113.118f, 381.9161f), new Quaternion(0f, 0.6402791f, 0f, -0.7681424f)))),
        };
        RespawnEnters = new OrientedPoint[]
        {
            new (new Vector3(-320.69f, 52.795f, 522.17f), new Quaternion(0f, -0.6073862f, 0f, -0.7944068f)),
            new (new Vector3(-334.0482f, 46.465f, -525.1163f), new Quaternion(0f, 0.3479154f, 0f, 0.9375259f)),
            new (new Vector3(513.3138f, 28.8759f, -240.0752f), new Quaternion(0f, -0.8840928f, 0f, 0.4673114f)),
            new (new Vector3(527.4f, 38.515f, 400.29f), new Quaternion(0f, -0.5602592f, 0f, 0.8283174f)),
        };
    }
    
    public IReadOnlyList<LocationEnterGroupDescription> BorderEnterGroups { get; }
    public IReadOnlyList<OrientedPoint> RespawnEnters { get; }
    public int CharacterRespawnCarSpawnerEntityId { get; } = -1;
}
}