using System.Collections.Generic;
using System.Numerics;
using Models.Physics.BVHTree;

namespace Models.References.Location.LocationWaterZones
{
/// <auto-generated>This is autogenerated class from <see cref="LocationWaterZonesFile"/></auto-generated>
public class HubLocationWaterZones : ILocationWaterZones
{
    public IReadOnlyList<WaterZoneDescription> WaterZones { get; } = new WaterZoneDescription[]
    {
    };

    public BVHTreeData BuildWaterZonesTree(IBVHTree bvhTree)
    {
        const int treeNodesCount = 1;

        BVHTreeData data = bvhTree.BuildTree(treeNodesCount);

        return data;
    }
}
}
