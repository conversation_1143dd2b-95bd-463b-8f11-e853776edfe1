using System.Collections.Generic;
using System.Numerics;
using Framework.Replication.Enum;
using Models.MathUtils;
using Models.Models.Triggers;
using Models.Physics.Utils;
using Models.References.BreakableCollider;
using Models.References.Building;
using Models.References.Camera;
using Models.References.Cargo;
using Models.References.Collection;
using Models.References.Container;
using Models.References.Door;
using Models.References.KinematicObject;
using Models.References.HideObject;
using Models.References.Inventory;
using Models.References.Location.LocationTraders;
using Models.References.LootNode;
using Models.References.LootObject;
using Models.References.MetalDetector;
using Models.References.SecurityAlarm;
using Models.References.SecurityPanel;
using Models.References.Trader;
using Models.References.VigilantNpc;
using Models.References.WeightItems;
using Models.References.CargoWorkbench;
using Models.References.Physics;
using Models.References.Switch;
using Models.References.Trigger;
using Models.References.Vehicle;
using Models.References.Plot;
using Models.Physics.Types;
using Models.References.Bollard;
using Models.References.CargoSeller;
using Models.References.ClanBattle;
using Models.References.CraftWorkbench;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.MoneyWorkbench;
using Models.HitBox.Types;
using Models.References.CashLootNode;
using Models.References.CumulativeObjects;
using Models.References.Prison;
using Models.References.Tattoo;
using System;
using Models.References.Colliders;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.Physics.PhysicsBodies;
#if UNITY_MATH
using Unity.Collections;
using Unity.Mathematics;
#endif

namespace Models.References.Location.LocationContext
{
/// <auto-generated>This is autogenerated class from <see cref="LocationContextFile"/></auto-generated>
public class HubLocationContextBuilder : ILocationContextBuilder
{
    public (int entityId, IEnum<LocationContainerDescription> containers, IEnum<LocationDoorDescription> doors, IEnum<KinematicObjectsControllerEntityDescription> kinematicObjectsControllers, IEnum<BreakableColliderSetDescription> breakableCollidersSets, IEnum<LocationSecurityAlarmDescription> securityAlarms, IEnum<LocationPanelDescription> securityPanels, IEnum<LocationBuildingDescription> buildings, IEnum<LocationMetalDetectorDescription> metalDetectorDescriptions, IEnum<LocationHideObjectDescription> hideObjects, IReadOnlyList<LocationNpcDescription> npcDescriptions, IReadOnlyList<LocationSwitchDescription> switchDescriptions, IReadOnlyList<TriggerDescription> triggerDescriptions, LocationCraftWorkbenchDescription craftWorkbenchDescription, IReadOnlyCollection<LocationCargoSellerDescription> cargoSellerDescriptions, IEnum<LocationBollardDescription> bollards, IReadOnlyList<LocationClanBattleConquestObjectDescription> clanBattleConquestObjects, LocationClanBattleConquestObjectDescription sectorLocationClanBattleConquestObject, IEnum<LocationCargoExchangerDescription> cargoExchangerDescriptions, LocationSettlementCashDescription settlementCashDescription, IReadOnlyList<LocationTattooUnlockDescription> tattooUnlockDescriptions, IReadOnlyList<LocationPrisonEscapeDescription> prisonEscapeDescriptions, IReadOnlyList<LocationBreakableColliderSetDescription> locationBreakableColliderSets, IEnum<LocationCashLootNodeDescription> cashLootNodes, IEnum<LocationCargoDeliveryPointDescription> cargoDeliveryPoints, IEnum<LocationEventCargoSpawnPointDescription> locationEventCargos) Build()
    {
        var alarms = new Enum<LocationSecurityAlarmDescription>()
        {
        };
        var securityPanels = new Enum<LocationPanelDescription>()
        {
        };
        var containers = new Enum<LocationContainerDescription>
        {
        };
        var door0 = new LocationDoorDescription("0", 0, 35, DoorDescription.DoorSingleMetal_01, new Vector3(321.5487f, 33.7378f, 56.9831f), new Vector3(321.5487f, 35.0878f, 56.9831f), new Quaternion(0f, 0f, 0f, 1f), UnlockStateDescription.Unlocked, null, false, null, false, 3);
        var door1 = new LocationDoorDescription("1", 1, 36, DoorDescription.DoorSingleMetal_01, new Vector3(326.5488f, 33.7378f, 40.0331f), new Vector3(326.5488f, 35.0878f, 40.0331f), new Quaternion(0f, 0f, 0f, 1f), UnlockStateDescription.Unlocked, null, false, null, false, 3);
        var door2 = new LocationDoorDescription("2", 2, 37, DoorDescription.DoorSingleMetal_01, new Vector3(311.2488f, 28.7383f, 34.7329f), new Vector3(311.2488f, 30.0883f, 34.7329f), new Quaternion(0f, 0f, 0f, 1f), UnlockStateDescription.Unlocked, null, false, null, false, 3);
        var door3 = new LocationDoorDescription("3", 3, 38, DoorDescription.DoorSingleMetal_01, new Vector3(308.8631f, 28.7379f, 27.3772f), new Vector3(308.8631f, 30.0879f, 27.3772f), new Quaternion(0f, -0.7071066f, 0f, -0.7071071f), UnlockStateDescription.Unlocked, null, false, null, false, 3);
        var door4 = new LocationDoorDescription("4", 4, 39, DoorDescription.DoorSingleMetal_01, new Vector3(302.8988f, 28.7379f, 60.0115f), new Vector3(302.8988f, 30.0879f, 60.0115f), new Quaternion(0f, -1f, 0f, -3.576278E-07f), UnlockStateDescription.Unlocked, null, false, null, false, 3);
        var doors = new Enum<LocationDoorDescription>
        {
            door0,
            door1,
            door2,
            door3,
            door4,
        };
        var breakableColliderSet0 = new BreakableColliderSetDescription("0", 0, 45, new Vector3(-336.2077f, 50.2546f, 582.838f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 40, BreakableColliderDescription.DesertRoadsign01, new Vector3(-344.2222f, 48.816f, 600.8484f), new Quaternion(0f, -0.9990894f, 0f, -0.04266604f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("1", 1, 41, BreakableColliderDescription.DesertRoadsign01, new Vector3(-339.779f, 48.9745f, 594.48f), new Quaternion(0f, -0.9990894f, 0f, -0.04266604f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("2", 2, 42, BreakableColliderDescription.DesertRoadsign01, new Vector3(-336.4262f, 49.8296f, 584.9188f), new Quaternion(0f, -0.9990894f, 0f, -0.04266604f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("3", 3, 43, BreakableColliderDescription.DesertRoadsign01, new Vector3(-334.55f, 50.66f, 576.79f), new Quaternion(0f, -0.9990894f, 0f, -0.04266604f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("4", 4, 44, BreakableColliderDescription.DesertRoadsign06, new Vector3(-326.0612f, 52.9929f, 557.1528f), new Quaternion(0f, 0.9893882f, 0f, 0.1452966f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet1 = new BreakableColliderSetDescription("1", 1, 48, new Vector3(-306.635f, 52.846f, 529.43f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 46, BreakableColliderDescription.DesertRoadsign10, new Vector3(-309.8f, 53.052f, 540.88f), new Quaternion(0f, 0.9278374f, 0f, 0.3729849f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 47, BreakableColliderDescription.DesertRoadsign02, new Vector3(-303.47f, 52.64f, 517.98f), new Quaternion(0f, -0.5011889f, 0f, 0.8653379f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet2 = new BreakableColliderSetDescription("2", 2, 52, new Vector3(-225.0056f, 52.722f, 495.3355f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 49, BreakableColliderDescription.DesertRoadsign10, new Vector3(-244.2867f, 53.9249f, 517.9465f), new Quaternion(0f, 0.8370439f, 0f, 0.5471358f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 50, BreakableColliderDescription.DesertRoadsign06, new Vector3(-198.57f, 50.571f, 480.7f), new Quaternion(0f, -0.9438015f, 0f, -0.3305128f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("2", 2, 51, BreakableColliderDescription.DesertRoadsign02, new Vector3(-232.16f, 53.67f, 487.36f), new Quaternion(0f, -0.36689f, 0f, 0.9302644f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet3 = new BreakableColliderSetDescription("3", 3, 54, new Vector3(-165.89f, 46.547f, 436.55f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 53, BreakableColliderDescription.DesertRoadsign02, new Vector3(-165.89f, 46.547f, 436.55f), new Quaternion(0f, 0.9834348f, 0f, 0.1812625f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet4 = new BreakableColliderSetDescription("4", 4, 57, new Vector3(-157.8742f, 39.0135f, 374.5106f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 55, BreakableColliderDescription.DesertRoadsign08, new Vector3(-149.191f, 39.267f, 377.834f), new Quaternion(0f, 0.9940401f, 0f, 0.109015f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 56, BreakableColliderDescription.DesertRoadsign02, new Vector3(-166.5574f, 38.76f, 371.1871f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet5 = new BreakableColliderSetDescription("5", 5, 66, new Vector3(-142.8143f, 33.1044f, 296.71f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 58, BreakableColliderDescription.DesertRoadsign04, new Vector3(-119.76f, 32.73f, 270.89f), new Quaternion(0f, 0.3751721f, 0f, -0.9269552f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 59, BreakableColliderDescription.DesertRoadsign01, new Vector3(-132.1545f, 32.5082f, 282.6781f), new Quaternion(0f, -0.2809281f, 0f, 0.9597288f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("2", 2, 60, BreakableColliderDescription.DesertRoadsign01, new Vector3(-137.3645f, 32.7295f, 287.9767f), new Quaternion(0f, -0.302774f, 0f, 0.9530625f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("3", 3, 61, BreakableColliderDescription.DesertRoadsign01, new Vector3(-142.72f, 32.9303f, 294.23f), new Quaternion(0f, -0.2707154f, 0f, 0.9626594f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("4", 4, 62, BreakableColliderDescription.DesertRoadsign01, new Vector3(-147.2299f, 33.1594f, 299.5856f), new Quaternion(0f, -0.2741309f, 0f, 0.9616925f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("5", 5, 63, BreakableColliderDescription.DesertRoadsign01, new Vector3(-151.7692f, 33.3292f, 306.5034f), new Quaternion(0f, -0.200708f, 0f, 0.9796512f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("6", 6, 64, BreakableColliderDescription.DesertRoadsign01, new Vector3(-156.9484f, 33.85f, 318.8101f), new Quaternion(0f, -0.1189718f, 0f, 0.9928977f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("7", 7, 65, BreakableColliderDescription.DesertRoadsign01, new Vector3(-154.5676f, 33.5988f, 313.0064f), new Quaternion(0f, -0.2109891f, 0f, 0.9774885f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet6 = new BreakableColliderSetDescription("6", 6, 68, new Vector3(-98.82f, 32.51f, 276.61f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 67, BreakableColliderDescription.DesertRoadsign03, new Vector3(-98.82f, 32.51f, 276.61f), new Quaternion(0f, -0.8702143f, 0f, -0.4926734f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet7 = new BreakableColliderSetDescription("7", 7, 71, new Vector3(-11.1965f, 32.6145f, 220.8267f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 69, BreakableColliderDescription.DesertRoadsign04, new Vector3(-2.493f, 32.62f, 204.9533f), new Quaternion(0f, 0.4476299f, 0f, -0.8942189f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 70, BreakableColliderDescription.DesertRoadsign02, new Vector3(-19.9f, 32.609f, 236.7f), new Quaternion(0f, 0.8861886f, 0f, 0.4633246f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet8 = new BreakableColliderSetDescription("8", 8, 77, new Vector3(76.3516f, 34.3367f, 149.4102f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 72, BreakableColliderDescription.DesertRoadsign03, new Vector3(65.47f, 33.7f, 156.98f), new Quaternion(0f, -0.4601486f, 0f, 0.8878419f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 73, BreakableColliderDescription.DesertRoadsign04, new Vector3(64.99f, 33.098f, 181.65f), new Quaternion(0f, -0.9109312f, 0f, -0.4125582f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("2", 2, 74, BreakableColliderDescription.Roadpole01, new Vector3(85.6885f, 35.13f, 133.8938f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 75, BreakableColliderDescription.Roadpole01, new Vector3(82.8048f, 34.8779f, 137.2636f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 76, BreakableColliderDescription.Roadpole01, new Vector3(82.8048f, 34.8779f, 137.2636f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet9 = new BreakableColliderSetDescription("9", 9, 87, new Vector3(105.8485f, 36.2774f, 105.7667f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 78, BreakableColliderDescription.Roadpole01, new Vector3(119.11f, 36.6119f, 82.5821f), new Quaternion(0.1251514f, 0.04490416f, -0.0153212f, 0.9910026f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("1", 1, 79, BreakableColliderDescription.Roadpole01, new Vector3(116.4779f, 36.6025f, 87.5319f), new Quaternion(0f, 0f, -0.04166671f, 0.9991316f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 80, BreakableColliderDescription.Roadpole01, new Vector3(114.7365f, 36.5934f, 92.4721f), new Quaternion(0f, 0f, -0.04166671f, 0.9991316f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 81, BreakableColliderDescription.Roadpole01, new Vector3(110.9888f, 36.5827f, 99.3846f), new Quaternion(0f, 0f, 0.08009122f, 0.9967875f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 82, BreakableColliderDescription.Roadpole01, new Vector3(107.6019f, 36.5216f, 105.0913f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 83, BreakableColliderDescription.Roadpole01, new Vector3(104.1527f, 36.3955f, 110.4505f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 84, BreakableColliderDescription.Roadpole01, new Vector3(97.1283f, 36.0147f, 119.7921f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("7", 7, 85, BreakableColliderDescription.Roadpole01, new Vector3(93.4729f, 35.7445f, 125.0241f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("8", 8, 86, BreakableColliderDescription.Roadpole01, new Vector3(88.9677f, 35.4298f, 129.5719f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet10 = new BreakableColliderSetDescription("10", 10, 90, new Vector3(83.47f, 34.2629f, 38.275f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 88, BreakableColliderDescription.DesertRoadsign10, new Vector3(102.87f, 35.188f, 31.06f), new Quaternion(0f, -0.7563264f, 0f, 0.6541944f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 89, BreakableColliderDescription.DesertRoadsign10, new Vector3(64.07f, 33.3378f, 45.49f), new Quaternion(0f, 0.7611239f, 0f, 0.6486066f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet11 = new BreakableColliderSetDescription("11", 11, 92, new Vector3(51.2323f, 33.2685f, 25.7834f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 91, BreakableColliderDescription.DesertRoadsign10, new Vector3(51.2323f, 33.2685f, 25.7834f), new Quaternion(0f, -0.6864179f, 0f, 0.7272073f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet12 = new BreakableColliderSetDescription("12", 12, 97, new Vector3(121.6421f, 36.6494f, 68.748f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 93, BreakableColliderDescription.Roadpole01, new Vector3(126.5903f, 36.8032f, 67.3122f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("1", 1, 94, BreakableColliderDescription.Roadpole01, new Vector3(124.1887f, 36.7384f, 73.0573f), new Quaternion(0f, 0f, -0.1076054f, 0.9941937f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 95, BreakableColliderDescription.Roadpole01, new Vector3(121.1194f, 36.6747f, 78.3026f), new Quaternion(0f, 0f, -0.02763348f, 0.9996182f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 96, BreakableColliderDescription.DesertRoadsign10, new Vector3(114.67f, 36.3812f, 56.32f), new Quaternion(0f, 0.5781748f, 0f, 0.815913f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet13 = new BreakableColliderSetDescription("13", 13, 100, new Vector3(148.815f, 37.4139f, 12.795f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 98, BreakableColliderDescription.DesertRoadsign08, new Vector3(158.65f, 37.4274f, 15.13f), new Quaternion(0f, -0.9998644f, 0f, -0.01646749f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 99, BreakableColliderDescription.DesertRoadsign02, new Vector3(138.98f, 37.4004f, 10.46f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet14 = new BreakableColliderSetDescription("14", 14, 102, new Vector3(134.04f, 34.48f, -57.39f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 101, BreakableColliderDescription.DesertRoadsign04, new Vector3(134.04f, 34.48f, -57.39f), new Quaternion(0f, -0.1402614f, 0f, -0.9901145f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet15 = new BreakableColliderSetDescription("15", 15, 104, new Vector3(147.5052f, 33.245f, -127.4672f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 103, BreakableColliderDescription.DesertRoadsign03, new Vector3(147.5052f, 33.245f, -127.4672f), new Quaternion(0f, -0.9997837f, 0f, -0.0207968f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet16 = new BreakableColliderSetDescription("16", 16, 110, new Vector3(183.9047f, 34.2045f, -226.3741f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 105, BreakableColliderDescription.DesertRoadsign04, new Vector3(157.5387f, 35.03f, -200.0134f), new Quaternion(0f, 0.244688f, 0f, -0.9696019f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 106, BreakableColliderDescription.DesertRoadsign04, new Vector3(192.948f, 34.869f, -214.639f), new Quaternion(0f, -0.9321536f, 0f, -0.3620632f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("2", 2, 107, BreakableColliderDescription.Roadpole01, new Vector3(189.4f, 33.7285f, -238.8f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 108, BreakableColliderDescription.Roadpole01, new Vector3(183.9195f, 34.0552f, -232.7137f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 109, BreakableColliderDescription.Roadpole01, new Vector3(195.7172f, 33.3396f, -245.7042f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet17 = new BreakableColliderSetDescription("17", 17, 119, new Vector3(224.4297f, 31.8672f, -270.5598f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 111, BreakableColliderDescription.DesertRoadsign03, new Vector3(249.04f, 31.71f, -270.27f), new Quaternion(0f, -0.9062002f, 0f, -0.4228489f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 112, BreakableColliderDescription.Roadpole01, new Vector3(202.5302f, 32.9196f, -252.8996f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 113, BreakableColliderDescription.Roadpole01, new Vector3(210.691f, 32.4303f, -261.1521f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 114, BreakableColliderDescription.Roadpole01, new Vector3(215.9833f, 32.1178f, -266.5972f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 115, BreakableColliderDescription.Roadpole01, new Vector3(221.7121f, 31.8069f, -271.8976f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 116, BreakableColliderDescription.Roadpole01, new Vector3(226.9725f, 31.5458f, -276.2746f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 117, BreakableColliderDescription.Roadpole01, new Vector3(231.9078f, 31.3079f, -280.784f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("7", 7, 118, BreakableColliderDescription.Roadpole01, new Vector3(236.6009f, 31.0996f, -284.6028f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet18 = new BreakableColliderSetDescription("18", 18, 131, new Vector3(294.1309f, 30.0282f, -296.4386f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 120, BreakableColliderDescription.Roadpole01, new Vector3(293.9514f, 30.0622f, -296.3951f), new Quaternion(-0.01488312f, 0.9733371f, 0.05033853f, -0.2232922f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("1", 1, 121, BreakableColliderDescription.Roadpole01, new Vector3(288.8961f, 29.9178f, -295.1348f), new Quaternion(-0.001847265f, 0.9008735f, -0.04232538f, -0.4320095f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 122, BreakableColliderDescription.Roadpole01, new Vector3(317.889f, 29.6873f, -305.6602f), new Quaternion(-0.0009927406f, 0.9354808f, -0.03017358f, -0.3520855f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 123, BreakableColliderDescription.Roadpole01, new Vector3(284.317f, 30.2397f, -292.8441f), new Quaternion(0.002342655f, 0.9688664f, 0.01430972f, -0.2471591f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 124, BreakableColliderDescription.Roadpole01, new Vector3(313.1618f, 29.8946f, -304.0142f), new Quaternion(0.0006926974f, 0.9864335f, 0.02693655f, -0.1619351f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 125, BreakableColliderDescription.Roadpole01, new Vector3(275.713f, 30.2521f, -288.6248f), new Quaternion(0.05148241f, 0.3261245f, 0.03808889f, 0.9431552f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 126, BreakableColliderDescription.Roadpole01, new Vector3(304.1266f, 30.0133f, -300.6872f), new Quaternion(0.06034915f, 0.2422377f, 0.04384729f, 0.967345f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("7", 7, 127, BreakableColliderDescription.Roadpole01, new Vector3(279.395f, 30.0353f, -290.6347f), new Quaternion(-0.06043397f, 0.3177469f, -0.01591543f, 0.9461138f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("8", 8, 128, BreakableColliderDescription.Roadpole01, new Vector3(308.23f, 29.9569f, -302.4297f), new Quaternion(-0.04642444f, 0.2345959f, -0.01979117f, 0.970782f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("9", 9, 129, BreakableColliderDescription.Roadpole01, new Vector3(270.9377f, 30.2693f, -285.4493f), new Quaternion(-0.02378353f, 0.3189223f, -0.006002159f, 0.9474635f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("10", 10, 130, BreakableColliderDescription.Roadpole01, new Vector3(298.8218f, 29.9818f, -298.9504f), new Quaternion(-0.01075824f, 0.2354296f, -0.006718232f, 0.9718086f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet19 = new BreakableColliderSetDescription("19", 19, 139, new Vector3(350.0154f, 29.5871f, -327.1191f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 132, BreakableColliderDescription.Roadpole01, new Vector3(322.7253f, 29.7419f, -307.2393f), new Quaternion(-0.01929671f, 0.9884186f, 0.06131804f, -0.1374644f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("1", 1, 133, BreakableColliderDescription.DesertRoadsign01, new Vector3(339.506f, 29.781f, -328.678f), new Quaternion(0f, -0.6271865f, 0f, 0.7788692f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("2", 2, 134, BreakableColliderDescription.DesertRoadsign01, new Vector3(345.356f, 29.69f, -329.581f), new Quaternion(0f, -0.6271865f, 0f, 0.7788692f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("3", 3, 135, BreakableColliderDescription.DesertRoadsign01, new Vector3(352.044f, 29.556f, -330.471f), new Quaternion(0f, -0.6778898f, 0f, 0.7351636f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("4", 4, 136, BreakableColliderDescription.DesertRoadsign01, new Vector3(357.21f, 29.556f, -330.89f), new Quaternion(0f, -0.6778898f, 0f, 0.7351636f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("5", 5, 137, BreakableColliderDescription.DesertRoadsign01, new Vector3(363.235f, 29.47f, -331.376f), new Quaternion(0f, -0.6778898f, 0f, 0.7351636f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("6", 6, 138, BreakableColliderDescription.DesertRoadsign01, new Vector3(370.0313f, 29.315f, -331.5985f), new Quaternion(0f, -0.7071068f, 0f, 0.7071068f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet20 = new BreakableColliderSetDescription("20", 20, 153, new Vector3(463.1016f, 28.3555f, -302.6035f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 140, BreakableColliderDescription.Roadpole01, new Vector3(460.463f, 28.2176f, -289.9237f), new Quaternion(-0.0380684f, 0.8877843f, 0.05499569f, -0.4553739f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("1", 1, 141, BreakableColliderDescription.Roadpole01, new Vector3(454.6708f, 28.27f, -292.8359f), new Quaternion(0.04659879f, -0.9128829f, 0.06429336f, 0.4004246f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 142, BreakableColliderDescription.Roadpole01, new Vector3(448.9767f, 28.3714f, -295.3002f), new Quaternion(0.006316016f, -0.6233001f, -0.007923309f, 0.7819171f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 143, BreakableColliderDescription.Roadpole01, new Vector3(443.648f, 28.46f, -297.7464f), new Quaternion(0.0710496f, -0.8589795f, 0.09256473f, 0.4985358f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 144, BreakableColliderDescription.Roadpole01, new Vector3(490.3128f, 28.4046f, -293.9384f), new Quaternion(-0.002930037f, 0.9935428f, -0.08358458f, -0.07666685f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 145, BreakableColliderDescription.Roadpole01, new Vector3(457.2108f, 28.4898f, -312.1069f), new Quaternion(-0.02466855f, -0.2111287f, -0.03545841f, 0.9765034f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 146, BreakableColliderDescription.Roadpole01, new Vector3(485.263f, 28.1995f, -297.8347f), new Quaternion(0.003683147f, 0.9831023f, -0.05419135f, -0.1748133f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("7", 7, 147, BreakableColliderDescription.Roadpole01, new Vector3(451.0643f, 28.46f, -314.5877f), new Quaternion(0.001434442f, -0.2427672f, -0.005731848f, 0.9700666f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("8", 8, 148, BreakableColliderDescription.Roadpole01, new Vector3(479.2957f, 28.2176f, -301.1712f), new Quaternion(-0.03938812f, 0.8985441f, 0.05405835f, -0.4337566f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("9", 9, 149, BreakableColliderDescription.Roadpole01, new Vector3(444.803f, 28.4299f, -317.1454f), new Quaternion(0.1388822f, 0.488619f, -0.001553221f, 0.8613715f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("10", 10, 150, BreakableColliderDescription.Roadpole01, new Vector3(473.6512f, 28.27f, -304.3603f), new Quaternion(0.04502928f, -0.9223055f, 0.06540219f, 0.3782161f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("11", 11, 151, BreakableColliderDescription.Roadpole01, new Vector3(468.0829f, 28.3714f, -307.0972f), new Quaternion(0.006505907f, -0.6420395f, -0.007768146f, 0.7666045f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("12", 12, 152, BreakableColliderDescription.Roadpole01, new Vector3(462.8789f, 28.46f, -309.7982f), new Quaternion(0.06878879f, -0.8707923f, 0.09425698f, 0.477603f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet21 = new BreakableColliderSetDescription("21", 21, 170, new Vector3(401.8497f, 28.6222f, -318.8961f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 154, BreakableColliderDescription.DesertRoadsign08, new Vector3(409.94f, 29.2143f, -307.94f), new Quaternion(0f, 0.6128654f, 0f, 0.7901874f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 155, BreakableColliderDescription.Roadpole01, new Vector3(418.8806f, 28.4139f, -306.4326f), new Quaternion(0.05234467f, 0.9973505f, 0.0387559f, 0.03240234f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 156, BreakableColliderDescription.Roadpole01, new Vector3(412.801f, 28.4735f, -308.3241f), new Quaternion(0.01488786f, 0.9850159f, 0.07192899f, 0.1560395f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 157, BreakableColliderDescription.Roadpole01, new Vector3(407.3167f, 28.4228f, -310.1522f), new Quaternion(-0.03694009f, 0.9884164f, 0.02652978f, 0.1447917f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 158, BreakableColliderDescription.Roadpole01, new Vector3(402.3918f, 28.3266f, -311.4252f), new Quaternion(0.01225659f, 0.9955446f, -0.0557601f, -0.07504363f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 159, BreakableColliderDescription.Roadpole01, new Vector3(397.5239f, 28.4979f, -312.5974f), new Quaternion(-0.007451358f, 0.9925684f, -0.0009117897f, 0.1214562f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 160, BreakableColliderDescription.Roadpole01, new Vector3(387.8499f, 28.5556f, -314.3343f), new Quaternion(0.01875179f, -0.03770954f, 0.05734058f, 0.997466f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("7", 7, 161, BreakableColliderDescription.Roadpole01, new Vector3(392.516f, 28.5164f, -313.9237f), new Quaternion(-0.065875f, -0.04821787f, -0.03347054f, 0.9961f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("8", 8, 162, BreakableColliderDescription.Roadpole01, new Vector3(382.2151f, 28.6274f, -314.8417f), new Quaternion(-0.03535812f, -0.04718439f, -0.01093245f, 0.9982004f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("9", 9, 163, BreakableColliderDescription.Roadpole01, new Vector3(422.3329f, 28.3266f, -325.4572f), new Quaternion(0.01360236f, 0.9970691f, -0.05544715f, -0.05093005f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("10", 10, 164, BreakableColliderDescription.Roadpole01, new Vector3(417.5274f, 28.4979f, -326.8636f), new Quaternion(-0.007427113f, 0.9893386f, -0.001091841f, 0.1454402f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("11", 11, 165, BreakableColliderDescription.Roadpole01, new Vector3(407.9486f, 28.5556f, -329.0665f), new Quaternion(0.01735869f, -0.06183657f, 0.05777757f, 0.9962614f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("12", 12, 166, BreakableColliderDescription.Roadpole01, new Vector3(412.5895f, 28.5164f, -328.4306f), new Quaternion(-0.06504571f, -0.07230878f, -0.03505486f, 0.9946415f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("13", 13, 167, BreakableColliderDescription.Roadpole01, new Vector3(402.3451f, 28.6274f, -329.8459f), new Quaternion(-0.03508321f, -0.0713264f, -0.01178489f, 0.9967662f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("14", 14, 168, BreakableColliderDescription.DesertRoadsign01, new Vector3(380.737f, 29.144f, -331.142f), new Quaternion(0f, -0.7309699f, 0f, 0.6824097f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("15", 15, 169, BreakableColliderDescription.DesertRoadsign01, new Vector3(374.68f, 29.239f, -331.56f), new Quaternion(0f, -0.7309699f, 0f, 0.6824097f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet22 = new BreakableColliderSetDescription("22", 22, 177, new Vector3(432.1765f, 28.4483f, -311.9038f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 171, BreakableColliderDescription.Roadpole01, new Vector3(437.8749f, 28.4898f, -299.7781f), new Quaternion(-0.0255194f, -0.187436f, -0.03485106f, 0.9813266f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("1", 1, 172, BreakableColliderDescription.Roadpole01, new Vector3(431.6155f, 28.46f, -301.9586f), new Quaternion(0.001295315f, -0.219221f, -0.005764882f, 0.9756573f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 173, BreakableColliderDescription.Roadpole01, new Vector3(425.2379f, 28.4299f, -304.2104f), new Quaternion(0.138804f, 0.5093206f, -0.004913632f, 0.849295f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 174, BreakableColliderDescription.Roadpole01, new Vector3(438.5607f, 28.4139f, -319.6727f), new Quaternion(0.05139147f, 0.9962744f, 0.04001125f, 0.05652812f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 175, BreakableColliderDescription.Roadpole01, new Vector3(432.5798f, 28.4735f, -321.8561f), new Quaternion(0.01314287f, 0.9809514f, 0.0722682f, 0.1798306f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 176, BreakableColliderDescription.Roadpole01, new Vector3(427.1904f, 28.4228f, -323.9474f), new Quaternion(-0.03757127f, 0.9846231f, 0.02562808f, 0.1686684f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var locationBreakableCollidersSets = new Enum<BreakableColliderSetDescription>()
        {
            breakableColliderSet0,
            breakableColliderSet1,
            breakableColliderSet2,
            breakableColliderSet3,
            breakableColliderSet4,
            breakableColliderSet5,
            breakableColliderSet6,
            breakableColliderSet7,
            breakableColliderSet8,
            breakableColliderSet9,
            breakableColliderSet10,
            breakableColliderSet11,
            breakableColliderSet12,
            breakableColliderSet13,
            breakableColliderSet14,
            breakableColliderSet15,
            breakableColliderSet16,
            breakableColliderSet17,
            breakableColliderSet18,
            breakableColliderSet19,
            breakableColliderSet20,
            breakableColliderSet21,
            breakableColliderSet22,
        };
        var hideObject0 = new LocationHideObjectDescription("0", 0, 0, HideObjectDescription.Box0, new Vector3(-38.58f, 32.0583f, 5.31f), new Quaternion(0f, -0.6174967f, 0f, -0.7865735f), new Vector3(-38.601f, 32.8534f, 5.4078f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(1523, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(1536, PhysicsBodyIdType.BakedStatic)});
        var hideObject1 = new LocationHideObjectDescription("1", 1, 1, HideObjectDescription.Box0, new Vector3(-2.375f, 32.0583f, 17.029f), new Quaternion(0f, 0.570032f, 0f, 0.8216226f), new Vector3(-2.4073f, 32.8534f, 17.1237f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(2577, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(2608, PhysicsBodyIdType.BakedStatic)});
        var hideObject2 = new LocationHideObjectDescription("2", 2, 2, HideObjectDescription.Box0, new Vector3(17.863f, 32.0338f, 47.864f), new Quaternion(0f, 0.3552749f, 0f, 0.9347619f), new Vector3(17.7901f, 32.8289f, 47.9325f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(5927, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(5929, PhysicsBodyIdType.BakedStatic)});
        var hideObject3 = new LocationHideObjectDescription("3", 3, 3, HideObjectDescription.Toilet_01, new Vector3(33.439f, 32.0583f, -12.832f), new Quaternion(0f, 0.9224265f, 0f, -0.3861728f), new Vector3(33.408f, 33.4156f, -12.8078f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(5665, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(5664, PhysicsBodyIdType.BakedStatic)});
        var hideObject4 = new LocationHideObjectDescription("4", 4, 4, HideObjectDescription.Toilet_01, new Vector3(-37.365f, 32.0583f, 79.149f), new Quaternion(0f, -0.9909273f, 0f, -0.1343991f), new Vector3(-37.4013f, 33.4156f, 79.1337f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(5328, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(5324, PhysicsBodyIdType.BakedStatic)});
        var hideObject5 = new LocationHideObjectDescription("5", 5, 5, HideObjectDescription.Toilet_01, new Vector3(-45.1557f, 32.0426f, 12.793f), new Quaternion(0f, 0.8033435f, 0f, -0.595516f), new Vector3(-45.172f, 33.3999f, 12.8289f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(2208, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(2207, PhysicsBodyIdType.BakedStatic)});
        var hideObject6 = new LocationHideObjectDescription("6", 6, 6, HideObjectDescription.Toilet_01, new Vector3(-45.49f, 32.0426f, 14.19f), new Quaternion(0f, 0.7845016f, 0f, -0.620127f), new Vector3(-45.504f, 33.3999f, 14.2268f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(2322, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(2323, PhysicsBodyIdType.BakedStatic)});
        var hideObject7 = new LocationHideObjectDescription("7", 7, 7, HideObjectDescription.Toilet_01, new Vector3(-21.698f, 32.0426f, -42.408f), new Quaternion(0f, -0.7082204f, 0f, -0.7059914f), new Vector3(-21.693f, 33.3999f, -42.4471f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(257, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(256, PhysicsBodyIdType.BakedStatic)});
        var hideObject8 = new LocationHideObjectDescription("8", 8, 8, HideObjectDescription.Toilet_01, new Vector3(-71.09f, 32.0583f, -45.36f), new Quaternion(0f, -0.5518026f, 0f, -0.8339748f), new Vector3(-71.07f, 33.4156f, -45.394f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(220, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(221, PhysicsBodyIdType.BakedStatic)});
        var hideObject9 = new LocationHideObjectDescription("9", 9, 9, HideObjectDescription.Toilet_01, new Vector3(-70.229f, 32.0583f, -46.982f), new Quaternion(0f, -0.4356294f, 0f, -0.9001262f), new Vector3(-70.2008f, 33.4156f, -47.0095f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(212, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(213, PhysicsBodyIdType.BakedStatic)});
        var hideObject10 = new LocationHideObjectDescription("10", 10, 10, HideObjectDescription.Box0, new Vector3(-10.566f, 32.0583f, -30.535f), new Quaternion(0f, 0.8824648f, 0f, -0.4703784f), new Vector3(-10.5126f, 32.8534f, -30.6196f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(338, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(334, PhysicsBodyIdType.BakedStatic)});
        var hideObject11 = new LocationHideObjectDescription("11", 11, 11, HideObjectDescription.Box0, new Vector3(24.547f, 32.0583f, -9.368f), new Quaternion(0f, 0.3785634f, 0f, -0.9255754f), new Vector3(24.4737f, 32.8534f, -9.436f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(5645, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(5643, PhysicsBodyIdType.BakedStatic)});
        var hideObject12 = new LocationHideObjectDescription("12", 12, 12, HideObjectDescription.CupboardHide_02, new Vector3(-11.962f, 32.587f, 7.902f), new Quaternion(0f, 0.7933521f, 0f, -0.608763f), new Vector3(-11.9667f, 33.9103f, 7.8858f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(1747, PhysicsBodyIdType.BakedStatic)});
        var hideObject13 = new LocationHideObjectDescription("13", 13, 13, HideObjectDescription.CupboardHide_02, new Vector3(-12.2855f, 32.587f, 9.1094f), new Quaternion(0f, 0.7933521f, 0f, -0.608763f), new Vector3(-12.2902f, 33.9103f, 9.0932f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(1890, PhysicsBodyIdType.BakedStatic)});
        var hideObject14 = new LocationHideObjectDescription("14", 14, 14, HideObjectDescription.CupboardHide_02, new Vector3(-46.374f, 37.4301f, 24.838f), new Quaternion(0f, 0.6087632f, 0f, 0.793352f), new Vector3(-46.3693f, 38.7534f, 24.8542f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(3400, PhysicsBodyIdType.BakedStatic)});
        var hideObject15 = new LocationHideObjectDescription("15", 15, 15, HideObjectDescription.CupboardHide_02, new Vector3(-46.693f, 37.4301f, 26.027f), new Quaternion(0f, 0.6087632f, 0f, 0.793352f), new Vector3(-46.6883f, 38.7534f, 26.0432f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(3490, PhysicsBodyIdType.BakedStatic)});
        var hideObject16 = new LocationHideObjectDescription("16", 16, 16, HideObjectDescription.CupboardHide_02, new Vector3(-36.199f, 37.087f, 7.425f), new Quaternion(0f, 0.1305243f, 0f, -0.9914452f), new Vector3(-36.2152f, 38.4103f, 7.4297f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(1694, PhysicsBodyIdType.BakedStatic)});
        var hideObject17 = new LocationHideObjectDescription("17", 17, 17, HideObjectDescription.CupboardHide_02, new Vector3(-34.961f, 37.087f, 7.757f), new Quaternion(0f, 0.1305243f, 0f, -0.9914452f), new Vector3(-34.9772f, 38.4103f, 7.7617f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(1722, PhysicsBodyIdType.BakedStatic)});
        var hideObject18 = new LocationHideObjectDescription("18", 18, 18, HideObjectDescription.CupboardHide_02, new Vector3(-8.815f, 32.087f, 34.147f), new Quaternion(0f, 0.9914451f, 0f, 0.1305242f), new Vector3(-8.7988f, 33.4103f, 34.1423f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(4227, PhysicsBodyIdType.BakedStatic)});
        var hideObject19 = new LocationHideObjectDescription("19", 19, 19, HideObjectDescription.CupboardHide_02, new Vector3(-7.6076f, 32.087f, 34.4705f), new Quaternion(0f, 0.9914451f, 0f, 0.1305242f), new Vector3(-7.5914f, 33.4103f, 34.4658f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(4278, PhysicsBodyIdType.BakedStatic)});
        var hideObject20 = new LocationHideObjectDescription("20", 20, 20, HideObjectDescription.CupboardHide_02, new Vector3(-26.053f, 33.0869f, 41.237f), new Quaternion(0f, -0.793355f, 0f, 0.6087593f), new Vector3(-26.0577f, 34.4102f, 41.2208f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(4855, PhysicsBodyIdType.BakedStatic)});
        var hideObject21 = new LocationHideObjectDescription("21", 21, 21, HideObjectDescription.Toilet_01, new Vector3(510.027f, 27.9407f, -223.515f), new Quaternion(0f, 0.4999996f, 0f, 0.8660257f), new Vector3(510.051f, 29.298f, -223.5463f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(1038, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(1037, PhysicsBodyIdType.BakedStatic)});
        var hideObject22 = new LocationHideObjectDescription("22", 22, 22, HideObjectDescription.Box0, new Vector3(521.35f, 27.9568f, -217.59f), new Quaternion(0f, -0.8433928f, 0f, -0.5372975f), new Vector3(521.3948f, 28.7519f, -217.5006f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(1052, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(1054, PhysicsBodyIdType.BakedStatic)});
        var hideObject23 = new LocationHideObjectDescription("23", 23, 23, HideObjectDescription.Box0, new Vector3(496.092f, 27.9568f, -227.812f), new Quaternion(0f, -0.8660256f, 0f, 0.4999997f), new Vector3(496.1396f, 28.7519f, -227.9f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(997, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(996, PhysicsBodyIdType.BakedStatic)});
        var hideObject24 = new LocationHideObjectDescription("24", 24, 24, HideObjectDescription.CupboardHide_02, new Vector3(323.51f, 32.7382f, 25.176f), new Quaternion(0f, 0f, 0f, -1f), new Vector3(323.4955f, 34.0615f, 25.1847f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(7009, PhysicsBodyIdType.BakedStatic)});
        var hideObject25 = new LocationHideObjectDescription("25", 25, 25, HideObjectDescription.CupboardHide_02, new Vector3(322.2f, 32.7382f, 25.176f), new Quaternion(0f, 0f, 0f, -1f), new Vector3(322.1855f, 34.0615f, 25.1847f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(7006, PhysicsBodyIdType.BakedStatic)});
        var hideObject26 = new LocationHideObjectDescription("26", 26, 26, HideObjectDescription.CupboardHide_02, new Vector3(322.919f, 33.6979f, 40.496f), new Quaternion(0f, 0f, 0f, -1f), new Vector3(322.9045f, 35.0212f, 40.5047f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(7378, PhysicsBodyIdType.BakedStatic)});
        var hideObject27 = new LocationHideObjectDescription("27", 27, 27, HideObjectDescription.CupboardHide_02, new Vector3(325.2525f, 33.7378f, 56.4607f), new Quaternion(0f, -1f, 0f, -1.177192E-06f), new Vector3(325.267f, 35.0611f, 56.452f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(7901, PhysicsBodyIdType.BakedStatic)});
        var hideObject28 = new LocationHideObjectDescription("28", 28, 28, HideObjectDescription.CupboardHide_02, new Vector3(323.9315f, 33.7378f, 56.4607f), new Quaternion(0f, -1f, 0f, -1.177192E-06f), new Vector3(323.946f, 35.0611f, 56.452f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(7903, PhysicsBodyIdType.BakedStatic)});
        var hideObject29 = new LocationHideObjectDescription("29", 29, 29, HideObjectDescription.CupboardHide_02, new Vector3(321.251f, 32.7379f, 66.58f), new Quaternion(0f, -1f, 0f, -1.177192E-06f), new Vector3(321.2655f, 34.0612f, 66.5713f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(8257, PhysicsBodyIdType.BakedStatic)});
        var hideObject30 = new LocationHideObjectDescription("30", 30, 30, HideObjectDescription.CupboardHide_02, new Vector3(322.47f, 32.7379f, 66.58f), new Quaternion(0f, -1f, 0f, -1.177192E-06f), new Vector3(322.4845f, 34.0612f, 66.5713f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(8256, PhysicsBodyIdType.BakedStatic)});
        var hideObject31 = new LocationHideObjectDescription("31", 31, 31, HideObjectDescription.CupboardHide_02, new Vector3(284.885f, 28.7379f, 59.599f), new Quaternion(0f, -1f, 0f, -1.177192E-06f), new Vector3(284.8995f, 30.0612f, 59.5903f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(8029, PhysicsBodyIdType.BakedStatic)});
        var hideObject32 = new LocationHideObjectDescription("32", 32, 32, HideObjectDescription.CupboardHide_02, new Vector3(284.221f, 28.7379f, 35.153f), new Quaternion(0f, -5.252659E-07f, 0f, 1f), new Vector3(284.2065f, 30.0612f, 35.1617f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(7258, PhysicsBodyIdType.BakedStatic)});
        var hideObject33 = new LocationHideObjectDescription("33", 33, 33, HideObjectDescription.CupboardHide_02, new Vector3(310.01f, 28.7379f, 59.57f), new Quaternion(0f, -1f, 0f, -2.052635E-06f), new Vector3(310.0245f, 30.0612f, 59.5613f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(8025, PhysicsBodyIdType.BakedStatic)});
        var hideObject34 = new LocationHideObjectDescription("34", 34, 34, HideObjectDescription.CupboardHide_02, new Vector3(308.773f, 28.7379f, 59.57f), new Quaternion(0f, -1f, 0f, -2.052635E-06f), new Vector3(308.7875f, 30.0612f, 59.5613f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(8024, PhysicsBodyIdType.BakedStatic)});
        var hideObjects = new Enum<LocationHideObjectDescription>
        {
            hideObject0,
            hideObject1,
            hideObject2,
            hideObject3,
            hideObject4,
            hideObject5,
            hideObject6,
            hideObject7,
            hideObject8,
            hideObject9,
            hideObject10,
            hideObject11,
            hideObject12,
            hideObject13,
            hideObject14,
            hideObject15,
            hideObject16,
            hideObject17,
            hideObject18,
            hideObject19,
            hideObject20,
            hideObject21,
            hideObject22,
            hideObject23,
            hideObject24,
            hideObject25,
            hideObject26,
            hideObject27,
            hideObject28,
            hideObject29,
            hideObject30,
            hideObject31,
            hideObject32,
            hideObject33,
            hideObject34,
        };
        var npcDescriptions = new List<LocationNpcDescription>
        {
            
        };
        var kinematicObjectsControllers = new Enum<KinematicObjectsControllerEntityDescription>()
        {
        };
        var buildings = new Enum<LocationBuildingDescription>()
        {
        };
        var metalDetectors = new Enum<LocationMetalDetectorDescription>
        {
        };
        var switches = new List<LocationSwitchDescription>()
        {
        };
        var triggers = new List<TriggerDescription>()
        {
        };
        LocationCraftWorkbenchDescription craftWorkbenchDescription = new(CraftWorkbenchDescription.CraftWorkbench0, 178, new Vector3(-21.431f, 32.1722f, 24.3546f), new Quaternion(0f, 0.9895712f, 0f, 0.1440446f), new Vector3(-21.4001f, 32.6594f, 24.4315f), new HashSet<PhysicsBodyId>() {new PhysicsBodyId(3355, PhysicsBodyIdType.BakedStatic)}, new int[] {179,180,181,182,183,184});
        IReadOnlyCollection<LocationCargoSellerDescription> cargoSellerDescriptions = new List<LocationCargoSellerDescription>()
        {
            new("0", 185, CargoSellerDescription.BunkerWeaponSeller, new Vector3(281.751f, 28.78f, 47.209f), new Vector3(281.6737f, 29.4328f, 47.062f), new HashSet<PhysicsBodyId>() {new PhysicsBodyId(7635, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7591, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7587, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7629, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7690, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7671, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7618, PhysicsBodyIdType.BakedStatic)}),
            new("1", 186, CargoSellerDescription.Smuggling, new Vector3(303.136f, 28.737f, 46.911f), new Vector3(303.0429f, 29.3899f, 46.7734f), new HashSet<PhysicsBodyId>() {new PhysicsBodyId(7623, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7576, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7575, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7620, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7665, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7633, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(7583, PhysicsBodyIdType.BakedStatic)}),
        };
        var locationCargoExchangerDescriptions = new Enum<LocationCargoExchangerDescription>()
        {
        };
        var bollards = new Enum<LocationBollardDescription>()
        {
        };
        var clanBattleZone0 = new LocationClanBattleTeamZoneDescription(new Vector3(473.5f, 3.3f, -252.4f), new OBB(new Vector3(0f, 0f, 0f), new Vector3(100f, 125f, 100f), new Quaternion(0f, 0f, 0f, 1f)), new[] { new BoxColliderDescription(new Vector3(102.5f, 0f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(5f, 260f, 210f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(-102.5f, 0f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(5f, 260f, 210f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 127.5f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(210f, 5f, 210f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, -127.5f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(210f, 5f, 210f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 0f, 102.5f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(210f, 260f, 5f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 0f, -102.5f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(210f, 260f, 5f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f) }, new[] { new LocationBorderEnterPoint(new OrientedPoint(new Vector3(544.1999f, 29.045f, -248.7298f), new Quaternion(0f, -0.835619f, 0f, 0.5493096f)), new OrientedPoint(new Vector3(409.7001f, 108.675f, -301.6003f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(542.8899f, 29.045f, -245.7798f), new Quaternion(0f, -0.835619f, 0f, 0.5493096f)), new OrientedPoint(new Vector3(436.9001f, 108.675f, -301.6002f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(545.5199f, 29.045f, -251.7798f), new Quaternion(0f, -0.835619f, 0f, 0.5493096f)), new OrientedPoint(new Vector3(422.3001f, 108.675f, -301.6002f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(540.1398f, 28.985f, -219.6098f), new Quaternion(0f, 0.9678373f, 0f, -0.2515773f)), new OrientedPoint(new Vector3(459.2101f, 108.675f, -301.6001f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(537.1198f, 28.985f, -218.0699f), new Quaternion(0f, 0.9678373f, 0f, -0.2515773f)), new OrientedPoint(new Vector3(486.4101f, 108.675f, -301.6f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(543.3499f, 28.985f, -221.5198f), new Quaternion(0f, 0.9678373f, 0f, -0.2515773f)), new OrientedPoint(new Vector3(471.8101f, 108.675f, -301.6001f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(520.4598f, 29.265f, -225.5899f), new Quaternion(0f, 0.9318627f, 0f, 0.3628112f)), new OrientedPoint(new Vector3(502.1701f, 108.675f, -301.6f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(518.0798f, 29.265f, -227.8f), new Quaternion(0f, 0.9318627f, 0f, 0.3628112f)), new OrientedPoint(new Vector3(529.3701f, 108.675f, -301.5999f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(522.8398f, 29.265f, -223.3699f), new Quaternion(0f, 0.9318627f, 0f, 0.3628112f)), new OrientedPoint(new Vector3(514.7701f, 108.675f, -301.6f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(497.8799f, 28.935f, -252.11f), new Quaternion(0f, -0.8999451f, 0f, 0.4360032f)), new OrientedPoint(new Vector3(552.8201f, 108.675f, -288.0999f), new Quaternion(0f, 0.7071072f, 0f, -0.7071064f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(495.8599f, 28.935f, -249.5301f), new Quaternion(0f, -0.8999451f, 0f, 0.4360032f)), new OrientedPoint(new Vector3(552.8199f, 108.675f, -260.8999f), new Quaternion(0f, 0.7071072f, 0f, -0.7071064f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(499.8999f, 28.935f, -254.69f), new Quaternion(0f, -0.8999451f, 0f, 0.4360032f)), new OrientedPoint(new Vector3(552.82f, 108.675f, -275.4999f), new Quaternion(0f, 0.7071072f, 0f, -0.7071064f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(518.3699f, 28.975f, -264.01f), new Quaternion(0f, -0.8433942f, 0f, 0.5372953f)), new OrientedPoint(new Vector3(553.5099f, 108.675f, -238.8999f), new Quaternion(0f, 0.7071079f, 0f, -0.7071056f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(516.9999f, 28.975f, -261.06f), new Quaternion(0f, -0.8433942f, 0f, 0.5372953f)), new OrientedPoint(new Vector3(553.5098f, 108.675f, -211.6999f), new Quaternion(0f, 0.7071079f, 0f, -0.7071056f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(519.7599f, 28.975f, -266.98f), new Quaternion(0f, -0.8433942f, 0f, 0.5372953f)), new OrientedPoint(new Vector3(553.5098f, 108.675f, -226.2999f), new Quaternion(0f, 0.7071079f, 0f, -0.7071056f))) });
        var clanBattleZone1 = new LocationClanBattleTeamZoneDescription(new Vector3(-4.8f, 0f, 21.6f), new OBB(new Vector3(0f, 0f, 0f), new Vector3(125f, 125f, 125f), new Quaternion(0f, 0f, 0f, 1f)), new[] { new BoxColliderDescription(new Vector3(127.5f, 0f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(5f, 260f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(-127.5f, 0f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(5f, 260f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 127.5f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 5f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, -127.5f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 5f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 0f, 127.5f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 260f, 5f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 0f, -127.5f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 260f, 5f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f) }, new[] { new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-43.0001f, 33.0035f, 51.2499f), new Quaternion(0f, 0.7071043f, 0f, 0.7071093f)), new OrientedPoint(new Vector3(-32.0999f, 116.575f, -32.7f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-18f, 33.0035f, 51.2499f), new Quaternion(0f, 0.7071043f, 0f, 0.7071093f)), new OrientedPoint(new Vector3(-10.9999f, 116.575f, -32.7f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-30.5f, 33.0035f, 51.2499f), new Quaternion(0f, 0.7071043f, 0f, 0.7071093f)), new OrientedPoint(new Vector3(-23f, 116.575f, -32.7f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-15.4435f, 33.0035f, -8.8777f), new Quaternion(0f, 0.3608045f, 0f, 0.9326414f)), new OrientedPoint(new Vector3(1.0001f, 116.575f, -32.9f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-22.9436f, 33.0035f, -16.3776f), new Quaternion(0f, 0.3608045f, 0f, 0.9326414f)), new OrientedPoint(new Vector3(22.1001f, 116.575f, -32.8999f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-7.9435f, 33.0035f, -1.3777f), new Quaternion(0f, 0.3608045f, 0f, 0.9326414f)), new OrientedPoint(new Vector3(10.1001f, 116.575f, -32.9f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(13.75f, 33.0035f, 38f), new Quaternion(0f, -1f, 0f, -2.354383E-06f)), new OrientedPoint(new Vector3(-60.8787f, 116.575f, 60.6566f), new Quaternion(0f, -0.7071046f, 0f, -0.7071092f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(13.75f, 33.0035f, 45.5f), new Quaternion(0f, -1f, 0f, -4.351139E-06f)), new OrientedPoint(new Vector3(-60.8415f, 116.575f, 39.5796f), new Quaternion(0f, -0.7071046f, 0f, -0.7071092f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(8.7499f, 33.0035f, 40.5001f), new Quaternion(0f, -1f, 0f, -4.351139E-06f)), new OrientedPoint(new Vector3(-60.8912f, 116.575f, 51.6211f), new Quaternion(0f, -0.7071046f, 0f, -0.7071092f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(10.4835f, 33.0135f, 3.6118f), new Quaternion(0f, -0.3716307f, 0f, 0.9283807f)), new OrientedPoint(new Vector3(-60.8123f, 116.575f, 17.4303f), new Quaternion(0f, 0.7071066f, 0f, 0.7071071f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(13.2121f, 33.0035f, 7.0058f), new Quaternion(0f, -0.3716313f, 0f, 0.9283805f)), new OrientedPoint(new Vector3(-60.7751f, 116.575f, -3.6467f), new Quaternion(0f, 0.7071066f, 0f, 0.7071071f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(7.3314f, 33.0035f, 0.5153f), new Quaternion(0f, -0.3716322f, 0f, 0.9283801f)), new OrientedPoint(new Vector3(-60.8161f, 116.575f, 8.2952f), new Quaternion(0f, 0.7071066f, 0f, 0.7071071f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-40.4536f, 33.0035f, -13.644f), new Quaternion(0f, -0.7195815f, 0f, -0.6944079f)), new OrientedPoint(new Vector3(-57.8f, 116.575f, -16.0001f), new Quaternion(0f, 0.3420191f, 0f, 0.939693f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-47.9536f, 33.0035f, -6.144f), new Quaternion(0f, -0.7195815f, 0f, -0.6944079f)), new OrientedPoint(new Vector3(-41.6f, 116.575f, -29.6001f), new Quaternion(0f, 0.3420191f, 0f, 0.939693f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-52.9537f, 33.0035f, 1.3559f), new Quaternion(0f, -0.7195815f, 0f, -0.6944079f)), new OrientedPoint(new Vector3(-50.8f, 116.575f, -21.9001f), new Quaternion(0f, 0.3420191f, 0f, 0.939693f))) });
        var clanBattleZone2 = new LocationClanBattleTeamZoneDescription(new Vector3(291.45f, 0f, 43.6f), new OBB(new Vector3(0f, 0f, 0f), new Vector3(125f, 125f, 125f), new Quaternion(0f, 0f, 0f, 1f)), new[] { new BoxColliderDescription(new Vector3(127.5f, 0f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(5f, 260f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(-127.5f, 0f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(5f, 260f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 127.5f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 5f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, -127.5f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 5f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 0f, 127.5f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 260f, 5f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 0f, -127.5f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 260f, 5f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f) }, new[] { new LocationBorderEnterPoint(new OrientedPoint(new Vector3(210.41f, 35.965f, 9.4998f), new Quaternion(0f, -3.010034E-06f, 0f, 1f)), new OrientedPoint(new Vector3(264.1501f, 116.575f, -10.7f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(210.31f, 35.965f, -0.5002f), new Quaternion(0f, -3.010034E-06f, 0f, 1f)), new OrientedPoint(new Vector3(285.2501f, 116.575f, -10.7f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(210.2101f, 35.965f, -12.0002f), new Quaternion(0f, -3.010034E-06f, 0f, 1f)), new OrientedPoint(new Vector3(273.2501f, 116.575f, -10.7f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(252.0826f, 36.855f, -71.6651f), new Quaternion(0f, 0.2816016f, 0f, 0.9595314f)), new OrientedPoint(new Vector3(297.2501f, 116.575f, -10.9f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(261.9596f, 36.855f, -70.0981f), new Quaternion(0f, 0.2816016f, 0f, 0.9595314f)), new OrientedPoint(new Vector3(318.3501f, 116.575f, -10.8999f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(273.3156f, 36.855f, -68.2813f), new Quaternion(0f, 0.2816016f, 0f, 0.9595314f)), new OrientedPoint(new Vector3(306.3501f, 116.575f, -10.9f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(221.2553f, 35.855f, 55.262f), new Quaternion(0f, -0.7071083f, 0f, 0.7071053f)), new OrientedPoint(new Vector3(235.3713f, 116.575f, 82.6566f), new Quaternion(0f, -0.7071046f, 0f, -0.7071092f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(221.3052f, 35.855f, 51.8232f), new Quaternion(0f, -0.7071102f, 0f, 0.7071034f)), new OrientedPoint(new Vector3(235.4085f, 116.575f, 61.5796f), new Quaternion(0f, -0.7071046f, 0f, -0.7071092f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(221.3459f, 35.855f, 48.6045f), new Quaternion(0f, -0.7071102f, 0f, 0.7071034f)), new OrientedPoint(new Vector3(235.3588f, 116.575f, 73.6211f), new Quaternion(0f, -0.7071046f, 0f, -0.7071092f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(221.1795f, 36.695f, -72.388f), new Quaternion(0f, 0.2703293f, 0f, 0.962768f)), new OrientedPoint(new Vector3(235.4377f, 116.575f, 39.4303f), new Quaternion(0f, 0.7071066f, 0f, 0.7071071f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(218.3853f, 36.695f, -70.8558f), new Quaternion(0f, 0.2703285f, 0f, 0.9627683f)), new OrientedPoint(new Vector3(235.4749f, 116.575f, 18.3533f), new Quaternion(0f, 0.7071066f, 0f, 0.7071071f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(215.8345f, 36.695f, -69.1215f), new Quaternion(0f, 0.2703283f, 0f, 0.9627683f)), new OrientedPoint(new Vector3(235.4339f, 116.575f, 30.2952f), new Quaternion(0f, 0.7071066f, 0f, 0.7071071f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(244.9153f, 37.735f, 157.0266f), new Quaternion(0f, 0.9475626f, 0f, -0.3195704f)), new OrientedPoint(new Vector3(238.45f, 116.575f, 5.9999f), new Quaternion(0f, 0.3420191f, 0f, 0.939693f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(260.5056f, 37.735f, 158.9926f), new Quaternion(0f, 0.9475626f, 0f, -0.3195704f)), new OrientedPoint(new Vector3(254.65f, 116.575f, -7.6001f), new Quaternion(0f, 0.3420191f, 0f, 0.939693f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(271.8666f, 37.735f, 160.5175f), new Quaternion(0f, 0.9475626f, 0f, -0.3195704f)), new OrientedPoint(new Vector3(245.45f, 116.575f, 0.0999f), new Quaternion(0f, 0.3420191f, 0f, 0.939693f))) });

        var clanBattleConquestObjects = new List<LocationClanBattleConquestObjectDescription>()
        {
            new LocationClanBattleConquestObjectDescription(ClanConquestObjectTypeDescription.HubLaboratory, new Vector3(-24.06f, 34.19f, 21.42f), new OBB(new Vector3(-26.6667f, 33.5264f, 22.1366f), new Vector3(5.8539f, 1.4784f, 6.5143f), new Quaternion(0f, -0.1305267f, 0f, 0.9914448f)), clanBattleZone1, clanBattleZone0, new Vector3(32.558f, 32.1954f, 24.055f)),
            new LocationClanBattleConquestObjectDescription(ClanConquestObjectTypeDescription.HubBunker, new Vector3(291.7f, 31.19f, 45.4304f), new OBB(new Vector3(293.7488f, 31.6903f, 47.4981f), new Vector3(10f, 3.4289f, 6.4902f), new Quaternion(0f, -7.264316E-07f, 0f, 1f)), clanBattleZone2, clanBattleZone0, new Vector3(301.207f, 34.8679f, -15.486f)),
        };
        LocationClanBattleConquestObjectDescription sectorLocationClanBattleConquestObject = null;
        LocationSettlementCashDescription settlementCashDescription = null;
        var tattooUnlockDescriptions = new List<LocationTattooUnlockDescription>()
        {
        };
        var prisonEscapes = new List<LocationPrisonEscapeDescription>()
        {
        };
        var locationBreakableColliderSets = new List<LocationBreakableColliderSetDescription>()
        {
            new LocationBreakableColliderSetDescription(breakableColliderSet0, new AABB(new Vector3(-394.2222f, 38.816f, 507.1528f), new Vector3(-276.0612f, 62.9929f, 650.8484f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet1, new AABB(new Vector3(-359.8f, 42.64f, 467.98f), new Vector3(-253.47f, 63.052f, 590.88f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet2, new AABB(new Vector3(-294.2867f, 40.571f, 430.7f), new Vector3(-148.57f, 63.9249f, 567.9465f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet3, new AABB(new Vector3(-215.89f, 36.547f, 386.55f), new Vector3(-115.89f, 56.547f, 486.55f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet4, new AABB(new Vector3(-216.5574f, 28.76f, 321.1871f), new Vector3(-99.191f, 49.267f, 427.834f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet5, new AABB(new Vector3(-206.9484f, 22.5082f, 220.89f), new Vector3(-69.76f, 43.85f, 368.8101f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet6, new AABB(new Vector3(-148.82f, 22.51f, 226.61f), new Vector3(-48.82f, 42.51f, 326.61f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet7, new AABB(new Vector3(-69.9f, 22.609f, 154.9533f), new Vector3(47.507f, 42.62f, 286.7f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet8, new AABB(new Vector3(14.99f, 23.098f, 83.8938f), new Vector3(135.6885f, 45.13f, 231.65f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet9, new AABB(new Vector3(38.9677f, 25.4298f, 32.5821f), new Vector3(169.11f, 46.6119f, 179.5719f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet10, new AABB(new Vector3(14.07f, 23.3378f, -18.94f), new Vector3(152.87f, 45.188f, 95.49f)), new List<LocationHideObjectDescription>() { hideObject2, hideObject3, hideObject11 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet11, new AABB(new Vector3(1.2323f, 23.2685f, -24.2166f), new Vector3(101.2323f, 43.2685f, 75.7834f)), new List<LocationHideObjectDescription>() { hideObject2, hideObject3, hideObject11 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet12, new AABB(new Vector3(64.67f, 26.3812f, 6.32f), new Vector3(176.5903f, 46.8032f, 128.3026f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet13, new AABB(new Vector3(88.98f, 27.4004f, -39.54f), new Vector3(208.65f, 47.4274f, 65.13f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet14, new AABB(new Vector3(84.04f, 24.48f, -107.39f), new Vector3(184.04f, 44.48f, -7.39f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet15, new AABB(new Vector3(97.5052f, 23.245f, -177.4672f), new Vector3(197.5052f, 43.245f, -77.4672f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet16, new AABB(new Vector3(107.5387f, 23.3396f, -295.7042f), new Vector3(245.7172f, 45.03f, -150.0134f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet17, new AABB(new Vector3(152.5302f, 21.0996f, -334.6028f), new Vector3(299.04f, 42.9196f, -202.8996f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet18, new AABB(new Vector3(220.9377f, 19.6873f, -355.6602f), new Vector3(367.889f, 40.2693f, -235.4493f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet19, new AABB(new Vector3(272.7253f, 19.315f, -381.5985f), new Vector3(420.0313f, 39.781f, -257.2393f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet20, new AABB(new Vector3(393.648f, 18.1995f, -367.1454f), new Vector3(540.3129f, 38.4898f, -239.9237f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet21, new AABB(new Vector3(324.68f, 18.3266f, -381.56f), new Vector3(472.3329f, 39.239f, -256.4326f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet22, new AABB(new Vector3(375.2379f, 18.4139f, -373.9474f), new Vector3(488.5607f, 38.4898f, -249.7781f)), new List<LocationHideObjectDescription>() {  }),
        };
        var cashLootNodes = new Enum<LocationCashLootNodeDescription>()
        {
        };
        var cargoDeliveryPoints = new Enum<LocationCargoDeliveryPointDescription>()
        {
        };
        var locationEventCargos = new Enum<LocationEventCargoSpawnPointDescription>()
        {
        };
        var entityId = 191;
        return (entityId, containers,  doors, kinematicObjectsControllers, locationBreakableCollidersSets, alarms, securityPanels, buildings, metalDetectors, hideObjects, npcDescriptions, switches, triggers, craftWorkbenchDescription, cargoSellerDescriptions, bollards, clanBattleConquestObjects, sectorLocationClanBattleConquestObject, locationCargoExchangerDescriptions, settlementCashDescription, tattooUnlockDescriptions, prisonEscapes, locationBreakableColliderSets, cashLootNodes, cargoDeliveryPoints, locationEventCargos);
    }
}
}
