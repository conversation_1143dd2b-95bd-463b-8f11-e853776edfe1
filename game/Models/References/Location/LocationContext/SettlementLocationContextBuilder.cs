using System.Collections.Generic;
using System.Numerics;
using Framework.Replication.Enum;
using Models.MathUtils;
using Models.Models.Triggers;
using Models.Physics.Utils;
using Models.References.BreakableCollider;
using Models.References.Building;
using Models.References.Camera;
using Models.References.Cargo;
using Models.References.Collection;
using Models.References.Container;
using Models.References.Door;
using Models.References.KinematicObject;
using Models.References.HideObject;
using Models.References.Inventory;
using Models.References.Location.LocationTraders;
using Models.References.LootNode;
using Models.References.LootObject;
using Models.References.MetalDetector;
using Models.References.SecurityAlarm;
using Models.References.SecurityPanel;
using Models.References.Trader;
using Models.References.VigilantNpc;
using Models.References.WeightItems;
using Models.References.CargoWorkbench;
using Models.References.Physics;
using Models.References.Switch;
using Models.References.Trigger;
using Models.References.Vehicle;
using Models.References.Plot;
using Models.Physics.Types;
using Models.References.Bollard;
using Models.References.CargoSeller;
using Models.References.ClanBattle;
using Models.References.CraftWorkbench;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.MoneyWorkbench;
using Models.HitBox.Types;
using Models.References.CashLootNode;
using Models.References.CumulativeObjects;
using Models.References.Prison;
using Models.References.Tattoo;
using System;
using Models.References.Colliders;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.Physics.PhysicsBodies;
#if UNITY_MATH
using Unity.Collections;
using Unity.Mathematics;
#endif

namespace Models.References.Location.LocationContext
{
/// <auto-generated>This is autogenerated class from <see cref="LocationContextFile"/></auto-generated>
public class SettlementLocationContextBuilder : ILocationContextBuilder
{
    public (int entityId, IEnum<LocationContainerDescription> containers, IEnum<LocationDoorDescription> doors, IEnum<KinematicObjectsControllerEntityDescription> kinematicObjectsControllers, IEnum<BreakableColliderSetDescription> breakableCollidersSets, IEnum<LocationSecurityAlarmDescription> securityAlarms, IEnum<LocationPanelDescription> securityPanels, IEnum<LocationBuildingDescription> buildings, IEnum<LocationMetalDetectorDescription> metalDetectorDescriptions, IEnum<LocationHideObjectDescription> hideObjects, IReadOnlyList<LocationNpcDescription> npcDescriptions, IReadOnlyList<LocationSwitchDescription> switchDescriptions, IReadOnlyList<TriggerDescription> triggerDescriptions, LocationCraftWorkbenchDescription craftWorkbenchDescription, IReadOnlyCollection<LocationCargoSellerDescription> cargoSellerDescriptions, IEnum<LocationBollardDescription> bollards, IReadOnlyList<LocationClanBattleConquestObjectDescription> clanBattleConquestObjects, LocationClanBattleConquestObjectDescription sectorLocationClanBattleConquestObject, IEnum<LocationCargoExchangerDescription> cargoExchangerDescriptions, LocationSettlementCashDescription settlementCashDescription, IReadOnlyList<LocationTattooUnlockDescription> tattooUnlockDescriptions, IReadOnlyList<LocationPrisonEscapeDescription> prisonEscapeDescriptions, IReadOnlyList<LocationBreakableColliderSetDescription> locationBreakableColliderSets, IEnum<LocationCashLootNodeDescription> cashLootNodes, IEnum<LocationCargoDeliveryPointDescription> cargoDeliveryPoints, IEnum<LocationEventCargoSpawnPointDescription> locationEventCargos) Build()
    {
        var alarm0 = new LocationSecurityAlarmDescription("0", 0, 45);
        var alarm1 = new LocationSecurityAlarmDescription("1", 1, 46);
        var alarm2 = new LocationSecurityAlarmDescription("2", 2, 47);
        var alarm3 = new LocationSecurityAlarmDescription("3", 3, 48);
        var alarm4 = new LocationSecurityAlarmDescription("4", 4, 49);
        var alarm5 = new LocationSecurityAlarmDescription("5", 5, 50);
        var alarms = new Enum<LocationSecurityAlarmDescription>()
        {
            alarm0,
            alarm1,
            alarm2,
            alarm3,
            alarm4,
            alarm5,
        };
        var securityPanels = new Enum<LocationPanelDescription>()
        {
        };
        var containers = new Enum<LocationContainerDescription>
        {
        };
        var door0 = new LocationDoorDescription("0", 0, 33, DoorDescription.DoorSingleMetal_02, new Vector3(46.6161f, 8.9966f, -40.5626f), new Vector3(46.6161f, 10.3666f, -40.5626f), new Quaternion(0f, 0.9659263f, 0f, 0.258817f), UnlockStateDescription.Unlocked, null, false, alarm3, false, 3);
        var door1 = new LocationDoorDescription("1", 1, 34, DoorDescription.DoorSingleMetal_02, new Vector3(102.5668f, 8.9967f, -16.1461f), new Vector3(102.5668f, 10.3667f, -16.1461f), new Quaternion(0f, 0.2588199f, 0f, -0.9659256f), UnlockStateDescription.Unlocked, null, false, null, false, 3);
        var door2 = new LocationDoorDescription("2", 2, 35, DoorDescription.DoorSingleMetal_02, new Vector3(50.4491f, 8.9966f, -26.9516f), new Vector3(50.4491f, 10.3666f, -26.9516f), new Quaternion(0f, 0.5000018f, 0f, 0.8660244f), UnlockStateDescription.Unlocked, null, false, alarm3, false, 3);
        var door3 = new LocationDoorDescription("3", 3, 36, DoorDescription.DoorSingleMetal_02, new Vector3(90.8757f, 8.9966f, -17.4665f), new Vector3(90.8757f, 10.3666f, -17.4665f), new Quaternion(0f, 0.5000018f, 0f, 0.8660244f), UnlockStateDescription.Unlocked, null, false, alarm3, false, 3);
        var door4 = new LocationDoorDescription("4", 4, 37, DoorDescription.DoorSingleMetal_02, new Vector3(60.4804f, 8.9899f, 48.2918f), new Vector3(60.4804f, 10.3599f, 48.2918f), new Quaternion(0f, -0.5000017f, 0f, -0.8660245f), UnlockStateDescription.Unlocked, null, false, alarm5, false, 3);
        var door5 = new LocationDoorDescription("5", 5, 38, DoorDescription.DoorSingleMetal_02, new Vector3(37.9105f, 8.99f, 39.8821f), new Vector3(37.9105f, 10.36f, 39.8821f), new Quaternion(0f, 0.8660244f, 0f, -0.5000017f), UnlockStateDescription.Unlocked, null, false, alarm5, false, 3);
        var doors = new Enum<LocationDoorDescription>
        {
            door0,
            door1,
            door2,
            door3,
            door4,
            door5,
        };
        var breakableColliderSet0 = new BreakableColliderSetDescription("0", 0, 52, new Vector3(-296.648f, 5.961f, -45.735f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 51, BreakableColliderDescription.ShippingBox, new Vector3(-296.648f, 5.961f, -45.735f), new Quaternion(-0.009518092f, 0.01885932f, 0.01813269f, 0.9996124f), new Vector3(0.85f, 0.319f, 0.6f), true, -1, null, false),
            }, null
        );
        var breakableColliderSet1 = new BreakableColliderSetDescription("1", 1, 54, new Vector3(511.1037f, 22.7527f, 168.961f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 53, BreakableColliderDescription.ShippingBox, new Vector3(511.1037f, 22.7527f, 168.961f), new Quaternion(-0.008352616f, 0.08202188f, 0.0186982f, 0.9964201f), new Vector3(0.85f, 0.319f, 0.6f), true, -1, null, false),
            }, null
        );
        var breakableColliderSet2 = new BreakableColliderSetDescription("2", 2, 59, new Vector3(40.9531f, 12.433f, -34.462f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 55, BreakableColliderDescription.DefaultGlassSmall, new Vector3(43.9515f, 12.433f, -39.6554f), new Quaternion(0f, 0.5f, 0f, 0.8660254f), new Vector3(3.0297f, 0.7089f, 0.0571f), false, -1, null, true),
                new("1", 1, 56, BreakableColliderDescription.DefaultGlassSmall, new Vector3(41.9445f, 12.433f, -36.1791f), new Quaternion(0f, 0.5f, 0f, 0.8660254f), new Vector3(3.0297f, 0.7089f, 0.0571f), false, -1, null, true),
                new("2", 2, 57, BreakableColliderDescription.DefaultGlassSmall, new Vector3(39.939f, 12.433f, -32.7055f), new Quaternion(0f, 0.5f, 0f, 0.8660254f), new Vector3(3.0297f, 0.7089f, 0.0571f), false, -1, null, true),
                new("3", 3, 58, BreakableColliderDescription.DefaultGlassSmall, new Vector3(37.9775f, 12.433f, -29.3081f), new Quaternion(0f, 0.5f, 0f, 0.8660254f), new Vector3(3.0297f, 0.7089f, 0.0571f), false, -1, null, true),
            }, alarm3
        );
        var breakableColliderSet3 = new BreakableColliderSetDescription("3", 3, 62, new Vector3(302.09f, 26.3685f, 475.085f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 60, BreakableColliderDescription.DesertRoadsign10, new Vector3(295.38f, 27.2422f, 486.67f), new Quaternion(-0.03443821f, 0.07469033f, -0.005149262f, 0.9965987f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 61, BreakableColliderDescription.DesertRoadsign10, new Vector3(308.8f, 25.4949f, 463.5f), new Quaternion(-0.003930241f, -0.9985875f, 0.03494994f, 0.03982574f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet4 = new BreakableColliderSetDescription("4", 4, 64, new Vector3(310.34f, 23.8387f, 435.98f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 63, BreakableColliderDescription.DesertRoadsign08, new Vector3(310.34f, 23.8387f, 435.98f), new Quaternion(0.01015256f, -0.9992387f, 0.02212665f, 0.03048557f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet5 = new BreakableColliderSetDescription("5", 5, 67, new Vector3(427.495f, 30.9086f, -307.705f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 65, BreakableColliderDescription.DesertRoadsign08, new Vector3(440.31f, 30.9109f, -311.82f), new Quaternion(0.001263619f, -0.7048137f, 0.00161821f, 0.7093896f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 66, BreakableColliderDescription.DesertRoadsign08, new Vector3(414.68f, 30.9063f, -303.59f), new Quaternion(-0.002130779f, -0.6816278f, 0.001535135f, -0.7316944f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet6 = new BreakableColliderSetDescription("6", 6, 75, new Vector3(-342.4467f, 16.4496f, -400.4344f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 68, BreakableColliderDescription.DesertRoadsign02, new Vector3(-332.5341f, 16.3475f, -379.9055f), new Quaternion(-0.03691285f, 0.9589114f, 0.02030018f, -0.2805606f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 69, BreakableColliderDescription.Roadpole01, new Vector3(-338.44f, 15.97f, -391.63f), new Quaternion(-0.03825992f, 0.7567004f, -0.007638568f, 0.6525967f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 70, BreakableColliderDescription.Roadpole01, new Vector3(-340.83f, 16.16f, -396.14f), new Quaternion(-0.0197905f, 0.742132f, -0.01637086f, 0.6697615f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 71, BreakableColliderDescription.Roadpole01, new Vector3(-342.9f, 16.34f, -400.4f), new Quaternion(0.005657529f, 0.795902f, -0.02466371f, 0.6048964f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 72, BreakableColliderDescription.Roadpole01, new Vector3(-345.34f, 16.53f, -406.63f), new Quaternion(0.005962821f, 0.8376492f, 0.01235114f, 0.5460365f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 73, BreakableColliderDescription.Roadpole01, new Vector3(-347.387f, 16.76f, -411.248f), new Quaternion(-0.08507539f, 0.7087687f, 0.02964804f, 0.6996644f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 74, BreakableColliderDescription.Roadpole01, new Vector3(-349.696f, 17.04f, -417.087f), new Quaternion(0.009953901f, 0.706302f, -0.06610248f, 0.7047474f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet7 = new BreakableColliderSetDescription("7", 7, 79, new Vector3(501.001f, 25.511f, 35.781f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 76, BreakableColliderDescription.DesertRoadsign06, new Vector3(520.02f, 26.2847f, 42.646f), new Quaternion(8.857896E-05f, 0.6652306f, 7.892102E-05f, 0.746638f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 77, BreakableColliderDescription.DesertRoadsign06, new Vector3(511.973f, 26.1911f, 26.807f), new Quaternion(0.00943229f, -0.6594437f, 0.01057263f, 0.7516205f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("2", 2, 78, BreakableColliderDescription.DesertRoadsign02, new Vector3(471.01f, 24.0572f, 37.89f), new Quaternion(0.02084438f, -0.707799f, -0.01474492f, -0.7059523f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet8 = new BreakableColliderSetDescription("8", 8, 82, new Vector3(111.77f, 22.8141f, -443.61f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 80, BreakableColliderDescription.DesertRoadsign08, new Vector3(110.33f, 22.5606f, -418.9f), new Quaternion(0.03658626f, -0.7157155f, 0.04857074f, 0.6957397f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 81, BreakableColliderDescription.DesertRoadsign10, new Vector3(113.21f, 23.0676f, -468.32f), new Quaternion(-0.007041381f, -0.9863047f, -0.01736953f, -0.1638654f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet9 = new BreakableColliderSetDescription("9", 9, 84, new Vector3(284.7831f, 24.013f, 379.9735f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 83, BreakableColliderDescription.DesertRoadsign08, new Vector3(284.7831f, 24.013f, 379.9735f), new Quaternion(0.004030389f, -0.1861943f, 0.00243792f, -0.9825017f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet10 = new BreakableColliderSetDescription("10", 10, 91, new Vector3(451.7272f, 22.9467f, 32.3237f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 85, BreakableColliderDescription.Roadsign01, new Vector3(435.0233f, 23.348f, 15.0319f), new Quaternion(0f, -0.9999939f, 0f, -0.003518216f), new Vector3(0.31f, 2.75f, 0.1f), false, -1, null, false),
                new("1", 1, 86, BreakableColliderDescription.Roadpole01, new Vector3(444.38f, 22.6119f, 34.88f), new Quaternion(0.09226037f, 0.9887094f, 0.1170306f, 0.01567001f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 87, BreakableColliderDescription.Roadpole01, new Vector3(449.61f, 22.676f, 35.18f), new Quaternion(-0.03790359f, 0.9933292f, -0.05758677f, 0.09243526f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 88, BreakableColliderDescription.Roadpole01, new Vector3(454.57f, 22.7988f, 35.6f), new Quaternion(0.07491723f, 0.987344f, 0.1213266f, 0.06942099f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 89, BreakableColliderDescription.Roadpole01, new Vector3(460.37f, 22.9947f, 36.23f), new Quaternion(-0.01970799f, 0.9978623f, -0.001230386f, 0.06229738f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 90, BreakableColliderDescription.Roadpole01, new Vector3(466.41f, 23.2511f, 37.02f), new Quaternion(-0.01909439f, 0.9956946f, -0.05892862f, 0.06895711f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet11 = new BreakableColliderSetDescription("11", 11, 94, new Vector3(342.645f, 36.3933f, -298.935f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 92, BreakableColliderDescription.DesertRoadsign10, new Vector3(328.38f, 35.1035f, -321.22f), new Quaternion(-0.01704918f, 0.8788559f, -0.05989359f, -0.4730057f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 93, BreakableColliderDescription.DesertRoadsign10, new Vector3(356.91f, 37.683f, -276.65f), new Quaternion(-0.002074418f, -0.3544588f, 0.0003755021f, -0.9350693f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet12 = new BreakableColliderSetDescription("12", 12, 97, new Vector3(124.9113f, 23.9738f, -409.9799f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 95, BreakableColliderDescription.DesertRoadsign08, new Vector3(100.0625f, 21.7887f, -407.1598f), new Quaternion(-0.03656156f, 0.676575f, 0.01167911f, 0.7353728f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 96, BreakableColliderDescription.DesertRoadsign10, new Vector3(149.76f, 26.1588f, -412.8f), new Quaternion(-0.01921325f, 0.6744007f, -0.02727366f, -0.7376115f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet13 = new BreakableColliderSetDescription("13", 13, 107, new Vector3(-166.1144f, 13.0991f, 484.1967f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 98, BreakableColliderDescription.DesertRoadsign04, new Vector3(-151.31f, 13.5884f, 480.42f), new Quaternion(-9.711588E-05f, 0.9998212f, 0.003404485f, -0.0186047f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 99, BreakableColliderDescription.DesertRoadsign04, new Vector3(-168.31f, 13.43f, 500.91f), new Quaternion(-0.004345336f, -0.008238661f, -4.762705E-05f, -0.9999566f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("2", 2, 100, BreakableColliderDescription.Roadpole01, new Vector3(-167.847f, 12.994f, 471.783f), new Quaternion(-0.1477001f, -0.7084974f, -0.01748221f, 0.6898627f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 101, BreakableColliderDescription.Roadpole01, new Vector3(-167.94f, 12.9687f, 485.87f), new Quaternion(-0.08464634f, -0.7126025f, -0.004605711f, 0.6964277f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 102, BreakableColliderDescription.Roadpole01, new Vector3(-167.86f, 13.0399f, 475.46f), new Quaternion(0.06537199f, -0.7095936f, 0.01105782f, 0.701485f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 103, BreakableColliderDescription.Roadpole01, new Vector3(-167.97f, 12.9416f, 489.56f), new Quaternion(0.06571186f, -0.7078142f, 0.01058395f, 0.7032561f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 104, BreakableColliderDescription.Roadpole01, new Vector3(-167.89f, 13.0187f, 478.67f), new Quaternion(0.06546186f, -0.7094053f, 0.01095482f, 0.7016687f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("7", 7, 105, BreakableColliderDescription.Roadpole01, new Vector3(-167.983f, 12.916f, 492.907f), new Quaternion(-0.01540109f, -0.7059522f, -0.009171062f, 0.7080327f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("8", 8, 106, BreakableColliderDescription.Roadpole01, new Vector3(-167.92f, 12.9948f, 482.19f), new Quaternion(-0.06663615f, -0.7179157f, 0.001404314f, 0.692932f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet14 = new BreakableColliderSetDescription("14", 14, 110, new Vector3(412.65f, 33.9422f, -42.19f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 108, BreakableColliderDescription.DesertRoadsign08, new Vector3(415.34f, 32.3207f, -34.23f), new Quaternion(-0.1026383f, -0.1926099f, 0.00711438f, -0.9758669f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 109, BreakableColliderDescription.DesertRoadsign10, new Vector3(409.96f, 35.5637f, -50.15f), new Quaternion(-0.08094353f, -0.1753484f, -0.0005191297f, -0.9811732f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet15 = new BreakableColliderSetDescription("15", 15, 113, new Vector3(5.94f, 20.6555f, 442.345f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 111, BreakableColliderDescription.DesertRoadsign08, new Vector3(14f, 22.127f, 449.2f), new Quaternion(0.07122082f, -0.7382645f, -0.05009837f, -0.6688672f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 112, BreakableColliderDescription.DesertRoadsign10, new Vector3(-2.12f, 19.1839f, 435.49f), new Quaternion(0.04164654f, -0.6966478f, 0.05525178f, 0.7140691f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet16 = new BreakableColliderSetDescription("16", 16, 127, new Vector3(-154.221f, 13.1389f, 411.8542f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 114, BreakableColliderDescription.Roadsign01, new Vector3(-147.602f, 13.84f, 444.928f), new Quaternion(0.04031373f, -0.667929f, 0.009685195f, -0.7430692f), new Vector3(0.31f, 2.75f, 0.1f), false, -1, null, false),
                new("1", 1, 115, BreakableColliderDescription.Roadpole01, new Vector3(-150.22f, 13.0265f, 401.03f), new Quaternion(0.009953901f, 0.706302f, -0.06610248f, 0.7047474f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 116, BreakableColliderDescription.Roadpole01, new Vector3(-150.3269f, 13.1313f, 415.1299f), new Quaternion(0.03831568f, 0.709212f, -0.0430002f, 0.7026387f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 117, BreakableColliderDescription.Roadpole01, new Vector3(-150.25f, 13.0568f, 404.72f), new Quaternion(0.01824839f, 0.706818f, 0.03784369f, 0.7061468f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 118, BreakableColliderDescription.Roadpole01, new Vector3(-168.0452f, 13.0066f, 398.5399f), new Quaternion(-0.02273743f, 0.7051265f, 0.07821308f, 0.704388f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 119, BreakableColliderDescription.Roadpole01, new Vector3(-150.3552f, 13.152f, 418.82f), new Quaternion(0.04287108f, 0.7002847f, 0.02599663f, 0.7121009f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 120, BreakableColliderDescription.Roadpole01, new Vector3(-150.495f, 12.972f, 396.956f), new Quaternion(-0.01818578f, 0.691228f, 0.1467806f, 0.7073391f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("7", 7, 121, BreakableColliderDescription.Roadpole01, new Vector3(-150.28f, 13.0815f, 407.93f), new Quaternion(-0.02208296f, 0.7079607f, 0.07864159f, 0.7015123f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("8", 8, 122, BreakableColliderDescription.Roadpole01, new Vector3(-168.0746f, 13.0331f, 401.7499f), new Quaternion(0.04144216f, 0.6943752f, 0.02516739f, 0.7179779f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("9", 9, 123, BreakableColliderDescription.Roadpole01, new Vector3(-150.3846f, 13.1653f, 422.0299f), new Quaternion(-0.001339433f, 0.7124298f, -0.001319337f, 0.7017409f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("10", 10, 124, BreakableColliderDescription.Roadpole01, new Vector3(-150.31f, 13.107f, 411.45f), new Quaternion(0.01868666f, 0.707411f, 0.03823309f, 0.7055202f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("11", 11, 125, BreakableColliderDescription.Roadpole01, new Vector3(-168.11f, 13.0613f, 405.27f), new Quaternion(0.01844508f, 0.7069402f, 0.03803014f, 0.7060093f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("12", 12, 126, BreakableColliderDescription.Roadpole01, new Vector3(-150.42f, 13.173f, 425.55f), new Quaternion(-0.0001898968f, 0.7071068f, -0.0001898968f, 0.7071068f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet17 = new BreakableColliderSetDescription("17", 17, 129, new Vector3(-37.67f, 17.3073f, 436.73f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 128, BreakableColliderDescription.DesertRoadsign08, new Vector3(-37.67f, 17.3073f, 436.73f), new Quaternion(-0.0120393f, 0.6496737f, -0.01066022f, -0.7600431f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet18 = new BreakableColliderSetDescription("18", 18, 142, new Vector3(-163.2031f, 12.9156f, 381.5575f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 130, BreakableColliderDescription.DesertRoadsign03, new Vector3(-151.1f, 13.4539f, 384.49f), new Quaternion(-0.0001013994f, -0.999902f, 0.005058706f, -0.01306009f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 131, BreakableColliderDescription.DesertRoadsign02, new Vector3(-165.12f, 12.8481f, 352.99f), new Quaternion(-0.0145346f, -0.01873348f, 0.005280527f, 0.999705f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("2", 2, 132, BreakableColliderDescription.Roadpole01, new Vector3(-166.64f, 12.6888f, 369.02f), new Quaternion(-0.00510698f, 0.6692037f, 0.06207579f, 0.740464f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 133, BreakableColliderDescription.Roadpole01, new Vector3(-150.44f, 12.9282f, 390.06f), new Quaternion(0.0003288784f, 0.6910883f, 0.06574433f, 0.7197739f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 134, BreakableColliderDescription.Roadpole01, new Vector3(-167.91f, 12.8436f, 380.75f), new Quaternion(0.0382652f, 0.6887314f, -0.04321251f, 0.7227154f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 135, BreakableColliderDescription.Roadpole01, new Vector3(-168.0169f, 12.9747f, 394.8499f), new Quaternion(-0.003068555f, 0.7041229f, -0.003094449f, 0.7100648f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 136, BreakableColliderDescription.Roadpole01, new Vector3(-167.15f, 12.7593f, 373f), new Quaternion(-0.001986399f, 0.6753414f, 0.06481352f, 0.7346491f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("7", 7, 137, BreakableColliderDescription.Roadpole01, new Vector3(-150.47f, 12.9625f, 393.75f), new Quaternion(-0.003202562f, 0.7014053f, -0.003254352f, 0.7127481f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("8", 8, 138, BreakableColliderDescription.Roadpole01, new Vector3(-167.94f, 12.8793f, 384.44f), new Quaternion(-0.0002079835f, 0.6863443f, 0.06563986f, 0.7243087f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("9", 9, 139, BreakableColliderDescription.Roadpole01, new Vector3(-167.6799f, 12.7973f, 376.5199f), new Quaternion(0.007084339f, 0.9554157f, -0.05794072f, 0.2894368f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("10", 10, 140, BreakableColliderDescription.Roadpole01, new Vector3(-167.97f, 12.9096f, 387.65f), new Quaternion(-0.003233148f, 0.6986555f, -0.003310836f, 0.7154433f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("11", 11, 141, BreakableColliderDescription.Roadpole01, new Vector3(-168f, 12.942f, 391.17f), new Quaternion(0.000452899f, 0.6913982f, 0.06583883f, 0.7194676f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet19 = new BreakableColliderSetDescription("19", 19, 144, new Vector3(-143.26f, 12.0427f, 328.94f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 143, BreakableColliderDescription.DesertRoadsign02, new Vector3(-143.26f, 12.0427f, 328.94f), new Quaternion(-0.002383032f, -0.9998755f, 0.01244672f, -0.009399693f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet20 = new BreakableColliderSetDescription("20", 20, 148, new Vector3(377.8167f, 22.0536f, 25.0333f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 145, BreakableColliderDescription.DesertRoadsign06, new Vector3(353.81f, 21.4177f, 20.22f), new Quaternion(-2.589419E-06f, -0.7343225f, 2.801218E-06f, 0.6788008f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 146, BreakableColliderDescription.DesertRoadsign03, new Vector3(391.28f, 22.4066f, 35.67f), new Quaternion(0.009966698f, -0.6778755f, -0.01166293f, -0.7350168f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("2", 2, 147, BreakableColliderDescription.DesertRoadsign03, new Vector3(388.36f, 22.3365f, 19.21f), new Quaternion(-0.01064265f, 0.6649254f, -0.01105264f, -0.7467521f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet21 = new BreakableColliderSetDescription("21", 21, 151, new Vector3(339.67f, 21.502f, 96.8326f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 149, BreakableColliderDescription.DesertRoadsign10, new Vector3(329.31f, 22.3123f, 117.4351f), new Quaternion(0.02404727f, 0.116028f, -0.0002665765f, -0.9929548f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 150, BreakableColliderDescription.DesertRoadsign10, new Vector3(350.03f, 20.6916f, 76.23f), new Quaternion(0.006305626f, -0.9999741f, 0.001204473f, 0.00324431f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet22 = new BreakableColliderSetDescription("22", 22, 156, new Vector3(320.1149f, 21.1133f, 32.4456f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 152, BreakableColliderDescription.Roadsign01, new Vector3(337.5006f, 21.535f, 39.9573f), new Quaternion(0f, 0.009172888f, 0f, -0.9999579f), new Vector3(0.31f, 2.75f, 0.1f), false, -1, null, false),
                new("1", 1, 153, BreakableColliderDescription.DesertRoadsign06, new Vector3(302.33f, 20.5311f, 33.75f), new Quaternion(-0.02260657f, 0.6762425f, 0.0162871f, 0.7361521f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("2", 2, 154, BreakableColliderDescription.DesertRoadsign04, new Vector3(325.466f, 21.3592f, 36.74f), new Quaternion(0.004919814f, -0.7374806f, -0.004116305f, -0.6753379f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("3", 3, 155, BreakableColliderDescription.DesertRoadsign04, new Vector3(315.163f, 21.028f, 19.335f), new Quaternion(0.01363899f, -0.7395272f, 0.01558225f, 0.6728082f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet23 = new BreakableColliderSetDescription("23", 23, 172, new Vector3(-139.9309f, 11.2203f, 244.8075f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 157, BreakableColliderDescription.DesertRoadsign01, new Vector3(-126.792f, 11.0467f, 260.255f), new Quaternion(0.0005050757f, -0.9984764f, -0.003529722f, 0.05506668f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("1", 1, 158, BreakableColliderDescription.DesertRoadsign01, new Vector3(-127.022f, 11.1615f, 250.715f), new Quaternion(0.0004808838f, -0.9914927f, -0.007178809f, 0.1299636f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("2", 2, 159, BreakableColliderDescription.DesertRoadsign01, new Vector3(-128.072f, 11.259f, 244.155f), new Quaternion(-0.0002032419f, -0.9746567f, -0.007184818f, 0.2235904f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("3", 3, 160, BreakableColliderDescription.DesertRoadsign01, new Vector3(-129.822f, 11.337f, 238.835f), new Quaternion(-0.0003528071f, -0.9685065f, -0.006487239f, 0.2489036f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("4", 4, 161, BreakableColliderDescription.DesertRoadsign01, new Vector3(-131.802f, 11.4018f, 233.945f), new Quaternion(-0.0005792728f, -0.9544834f, -0.005464983f, 0.2982135f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("5", 5, 162, BreakableColliderDescription.DesertRoadsign01, new Vector3(-134.68f, 11.46f, 228.66f), new Quaternion(-0.0006698163f, -0.9369164f, -0.004154561f, 0.3495285f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("6", 6, 163, BreakableColliderDescription.DesertRoadsign04, new Vector3(-143.26f, 11.37f, 266.75f), new Quaternion(0.001231734f, -0.04361943f, 5.377875E-05f, -0.9990475f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("7", 7, 164, BreakableColliderDescription.DesertRoadsign02, new Vector3(-158.563f, 11.878f, 225.507f), new Quaternion(-0.0003689087f, -0.5172802f, -0.0002229792f, -0.855816f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("8", 8, 165, BreakableColliderDescription.Roadpole01, new Vector3(-142.69f, 10.8045f, 261.06f), new Quaternion(-0.004180459f, -0.7658905f, 0.01365144f, 0.6428126f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("9", 9, 166, BreakableColliderDescription.Roadpole01, new Vector3(-142.57f, 10.8609f, 256.52f), new Quaternion(0.005768244f, -0.6811267f, -0.006200063f, 0.7321166f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("10", 10, 167, BreakableColliderDescription.Roadpole01, new Vector3(-143.05f, 10.9623f, 251.18f), new Quaternion(0.006552577f, -0.6545662f, -0.007567369f, 0.7559384f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("11", 11, 168, BreakableColliderDescription.Roadpole01, new Vector3(-144.2008f, 11.074f, 245.8208f), new Quaternion(0.006436432f, -0.6277208f, -0.007981149f, 0.7783712f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("12", 12, 169, BreakableColliderDescription.Roadpole01, new Vector3(-145.84f, 11.1619f, 241.31f), new Quaternion(-0.03127468f, -0.5600054f, 0.02146854f, 0.8276201f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("13", 13, 170, BreakableColliderDescription.Roadpole01, new Vector3(-148.44f, 11.2446f, 235.9f), new Quaternion(-0.06349579f, -0.5386034f, 0.01508118f, 0.840028f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("14", 14, 171, BreakableColliderDescription.Roadpole01, new Vector3(-152.16f, 11.2819f, 231.5f), new Quaternion(0.0006445599f, -0.3625101f, -0.0016571f, 0.9319781f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet24 = new BreakableColliderSetDescription("24", 24, 175, new Vector3(-207.939f, 13.0739f, 174.821f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 173, BreakableColliderDescription.Roadsign01, new Vector3(-224.718f, 13.887f, 154.002f), new Quaternion(0.02121646f, 0.511283f, -0.03562109f, -0.8584117f), new Vector3(0.31f, 2.75f, 0.1f), false, -1, null, false),
                new("1", 1, 174, BreakableColliderDescription.DesertRoadsign03, new Vector3(-191.16f, 12.2608f, 195.64f), new Quaternion(0.01423753f, 0.3600062f, -0.00580246f, 0.9328232f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet25 = new BreakableColliderSetDescription("25", 25, 177, new Vector3(-172.63f, 12.0605f, 191.09f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 176, BreakableColliderDescription.DesertRoadsign04, new Vector3(-172.63f, 12.0605f, 191.09f), new Quaternion(-0.002226636f, -0.8427481f, -0.00763146f, 0.5382494f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet26 = new BreakableColliderSetDescription("26", 26, 183, new Vector3(268.764f, 17.608f, 12.6558f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 178, BreakableColliderDescription.DesertRoadsign01, new Vector3(263.648f, 17.2422f, 15.405f), new Quaternion(-0.04912971f, 0.5323882f, 0.01877743f, 0.8448647f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("1", 1, 179, BreakableColliderDescription.DesertRoadsign01, new Vector3(269.418f, 17.9692f, 19.805f), new Quaternion(-0.04322061f, 0.5494872f, 0.01752626f, 0.8341994f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("2", 2, 180, BreakableColliderDescription.DesertRoadsign01, new Vector3(274.468f, 18.4427f, 22.855f), new Quaternion(-0.03128933f, 0.6327153f, 0.01662293f, 0.7735735f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("3", 3, 181, BreakableColliderDescription.DesertRoadsign01, new Vector3(278.486f, 18.7062f, 24.989f), new Quaternion(-0.02366081f, 0.5842175f, 0.01076374f, 0.8111808f), new Vector3(0.4f, 1.62f, 0.1f), false, -1, null, false),
                new("4", 4, 182, BreakableColliderDescription.DesertRoadsign06, new Vector3(257.8f, 15.6797f, -19.775f), new Quaternion(0.0004749298f, 0.9556214f, 0.005089638f, -0.2945533f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet27 = new BreakableColliderSetDescription("27", 27, 185, new Vector3(-244.91f, 12.7608f, 56.41f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 184, BreakableColliderDescription.DesertRoadsign02, new Vector3(-244.91f, 12.7608f, 56.41f), new Quaternion(-0.001790507f, -0.9261034f, 0.01177624f, 0.3770819f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet28 = new BreakableColliderSetDescription("28", 28, 194, new Vector3(-269.515f, 15.2968f, -277.0588f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 186, BreakableColliderDescription.DesertRoadsign04, new Vector3(-297.2f, 16.4081f, -284.3f), new Quaternion(-0.01728755f, -0.3860289f, 0.008661525f, -0.9222841f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 187, BreakableColliderDescription.DesertRoadsign04, new Vector3(-286.23f, 16.4642f, -299.06f), new Quaternion(-0.007590496f, -0.9014863f, -0.0176047f, 0.4323828f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("2", 2, 188, BreakableColliderDescription.DesertRoadsign02, new Vector3(-277.46f, 15.4412f, -266.32f), new Quaternion(0.007306522f, 0.2606343f, -0.003274212f, 0.9654045f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("3", 3, 189, BreakableColliderDescription.Roadpole01, new Vector3(-266.7f, 14.8954f, -280.46f), new Quaternion(-0.0132193f, -0.4876603f, -0.01179568f, 0.8728537f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 190, BreakableColliderDescription.Roadpole01, new Vector3(-263.22f, 14.8219f, -277.18f), new Quaternion(-0.0004555508f, -0.2628507f, 0.04837438f, 0.963623f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 191, BreakableColliderDescription.Roadpole01, new Vector3(-259.24f, 14.7952f, -273.42f), new Quaternion(0.01343397f, 0.7070411f, -0.06257625f, 0.7042704f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 192, BreakableColliderDescription.Roadpole01, new Vector3(-255.31f, 14.7866f, -269.75f), new Quaternion(0.04101165f, 0.7059557f, -0.04032629f, 0.7059167f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("7", 7, 193, BreakableColliderDescription.Roadpole01, new Vector3(-250.76f, 14.7616f, -265.98f), new Quaternion(-0.02057973f, 0.370908f, -0.05143522f, 0.9270158f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet29 = new BreakableColliderSetDescription("29", 29, 196, new Vector3(64.5f, 20.9462f, -397.6f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 195, BreakableColliderDescription.DesertRoadsign10, new Vector3(64.5f, 20.9462f, -397.6f), new Quaternion(0.02893001f, -0.2409815f, 0.0001769455f, 0.9700984f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet30 = new BreakableColliderSetDescription("30", 30, 199, new Vector3(-242.4f, 14.6813f, -257.555f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 197, BreakableColliderDescription.Roadpole01, new Vector3(-245.08f, 14.7163f, -260.23f), new Quaternion(-0.01495499f, 0.3342009f, -0.04212809f, 0.9414412f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("1", 1, 198, BreakableColliderDescription.Roadpole01, new Vector3(-239.72f, 14.6464f, -254.88f), new Quaternion(-0.05642302f, 0.3926812f, 0.04938519f, 0.9166127f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet31 = new BreakableColliderSetDescription("31", 31, 202, new Vector3(236.5404f, 16.026f, -42.6544f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 200, BreakableColliderDescription.DesertRoadsign03, new Vector3(235.3997f, 16.1516f, -52.0897f), new Quaternion(-0.006406451f, -0.03431852f, 0.0001752264f, -0.9993904f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 201, BreakableColliderDescription.DesertRoadsign02, new Vector3(237.681f, 15.9003f, -33.219f), new Quaternion(-0.006473564f, -0.2252312f, -0.0002920666f, -0.9742838f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet32 = new BreakableColliderSetDescription("32", 32, 206, new Vector3(-229.5507f, 12.9106f, -118.8587f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 203, BreakableColliderDescription.DesertRoadsign04, new Vector3(-241.78f, 12.2301f, -98.3f), new Quaternion(0.02224315f, -0.06481411f, 0.004653768f, 0.9976386f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("1", 1, 204, BreakableColliderDescription.DesertRoadsign04, new Vector3(-219.88f, 13.107f, -114.32f), new Quaternion(0.001888472f, -0.9813502f, -0.01727525f, -0.1914416f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
                new("2", 2, 205, BreakableColliderDescription.DesertRoadsign03, new Vector3(-226.992f, 13.3948f, -143.956f), new Quaternion(0.004644015f, 0.3205079f, 0.002156282f, 0.947232f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet33 = new BreakableColliderSetDescription("33", 33, 210, new Vector3(227.6084f, 16.2582f, -115.1247f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 207, BreakableColliderDescription.Roadsign01, new Vector3(227.7954f, 16.438f, -91.5441f), new Quaternion(0f, 0.7246829f, 0f, -0.6890825f), new Vector3(0.31f, 2.75f, 0.1f), false, -1, null, false),
                new("1", 1, 208, BreakableColliderDescription.DesertRoadsign06, new Vector3(219.56f, 16.1394f, -123.94f), new Quaternion(-0.007399702f, 0.3966323f, 0.002201531f, 0.9179451f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("2", 2, 209, BreakableColliderDescription.DesertRoadsign06, new Vector3(235.47f, 16.1973f, -129.89f), new Quaternion(-0.0009364333f, 0.9361533f, -0.004693642f, -0.3515597f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet34 = new BreakableColliderSetDescription("34", 34, 214, new Vector3(56.6611f, 9.3073f, 76.4468f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 211, BreakableColliderDescription.CactusBig05Breakable, new Vector3(77.4632f, 9.01f, 83.0605f), new Quaternion(0.01085383f, -0.7544307f, -0.009440927f, 0.6562222f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("1", 1, 212, BreakableColliderDescription.CactusBig05Breakable, new Vector3(53.51f, 9.478f, 84.64f), new Quaternion(-0.00320368f, 0.9861554f, -0.01240299f, 0.1653285f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("2", 2, 213, BreakableColliderDescription.CactusBig07Breakable, new Vector3(39.01f, 9.434f, 61.64f), new Quaternion(-3.328777E-09f, 0.9998478f, -1.907058E-07f, 0.01745238f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet35 = new BreakableColliderSetDescription("35", 35, 216, new Vector3(110.1265f, 9.39f, 27.1557f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 215, BreakableColliderDescription.CactusBig07Breakable, new Vector3(110.1265f, 9.39f, 27.1557f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet36 = new BreakableColliderSetDescription("36", 36, 225, new Vector3(175.5103f, 13.6081f, -175.034f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 217, BreakableColliderDescription.DesertRoadsign06, new Vector3(166.03f, 13.437f, -200.8f), new Quaternion(-0.002830744f, 0.8665123f, -0.007514757f, -0.4990912f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 218, BreakableColliderDescription.DesertRoadsign02, new Vector3(192.97f, 14.3922f, -175.52f), new Quaternion(-0.005628197f, 0.8888714f, -0.01657902f, -0.4578221f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("2", 2, 219, BreakableColliderDescription.Roadpole01, new Vector3(163.65f, 13.0895f, -180.61f), new Quaternion(0.04010475f, 0.9291355f, 0.1379771f, 0.3406775f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 220, BreakableColliderDescription.Roadpole01, new Vector3(167.69f, 13.2295f, -176.67f), new Quaternion(-0.02625328f, 0.9308072f, -0.06778008f, 0.3582102f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 221, BreakableColliderDescription.Roadpole01, new Vector3(172.12f, 13.397f, -172.57f), new Quaternion(0.007904715f, 0.9279578f, -0.05871766f, 0.3679459f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 222, BreakableColliderDescription.Roadpole01, new Vector3(176.96f, 13.601f, -168.12f), new Quaternion(-0.00131825f, 0.9252582f, 0.07634822f, 0.3715731f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 223, BreakableColliderDescription.Roadpole01, new Vector3(180.9f, 13.7895f, -164.26f), new Quaternion(0.01344859f, 0.9160269f, 0.05551793f, 0.3970285f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("7", 7, 224, BreakableColliderDescription.Roadpole01, new Vector3(183.762f, 13.9289f, -161.722f), new Quaternion(0.005384069f, 0.9167512f, -0.06027005f, 0.3948491f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet37 = new BreakableColliderSetDescription("37", 37, 233, new Vector3(119.5351f, 13.5681f, -230.9122f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 226, BreakableColliderDescription.DesertRoadsign06, new Vector3(123.34f, 13.5296f, -213.77f), new Quaternion(0.01485312f, 0.5196335f, -0.004262382f, 0.8542495f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 227, BreakableColliderDescription.DesertRoadsign06, new Vector3(89.95f, 14.9546f, -245.04f), new Quaternion(0.00533839f, 0.7542511f, 0.007727718f, -0.656519f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("2", 2, 228, BreakableColliderDescription.Roadpole01, new Vector3(138.48f, 12.7829f, -223.37f), new Quaternion(0.06544174f, -0.3354341f, -0.0232086f, 0.9395013f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 229, BreakableColliderDescription.Roadpole01, new Vector3(130.66f, 13.0099f, -228.49f), new Quaternion(0.004843877f, -0.2951496f, -0.01567817f, 0.9553102f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 230, BreakableColliderDescription.Roadpole01, new Vector3(123.7f, 13.3363f, -232.82f), new Quaternion(-0.01514662f, -0.2577768f, 0.03672175f, 0.9653876f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 231, BreakableColliderDescription.Roadpole01, new Vector3(118.19f, 13.5762f, -235.37f), new Quaternion(0.003903597f, -0.2113163f, -0.01805238f, 0.9772432f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("6", 6, 232, BreakableColliderDescription.Roadpole01, new Vector3(112.426f, 13.7877f, -237.5254f), new Quaternion(0.05934194f, -0.1898963f, -0.004014309f, 0.980001f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet38 = new BreakableColliderSetDescription("38", 38, 239, new Vector3(127.3304f, 9.6714f, -87.4339f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 234, BreakableColliderDescription.CactusBig06Breakable, new Vector3(102.08f, 10.304f, -112.74f), new Quaternion(0.1056419f, 0.002427463f, -0.003947316f, 0.9943935f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("1", 1, 235, BreakableColliderDescription.CactusBig06Breakable, new Vector3(140.53f, 10.305f, -103.77f), new Quaternion(-0.01153292f, 0.6991044f, -0.010517f, -0.7148492f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("2", 2, 236, BreakableColliderDescription.CactusBig05Breakable, new Vector3(145.87f, 9.181f, -78.17f), new Quaternion(0.01725575f, -0.5935867f, -0.02337966f, 0.8042452f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("3", 3, 237, BreakableColliderDescription.CactusBig07Breakable, new Vector3(118.16f, 9.459f, -77.42f), new Quaternion(0.002753264f, 0.2480057f, 0.0003205316f, 0.9687546f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("4", 4, 238, BreakableColliderDescription.CactusBig05Breakable, new Vector3(130.0122f, 9.108f, -65.0694f), new Quaternion(0.009108341f, -0.4849748f, -0.01642078f, 0.8743266f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet39 = new BreakableColliderSetDescription("39", 39, 242, new Vector3(51.8338f, 15.414f, -254.655f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 240, BreakableColliderDescription.Roadsign01, new Vector3(31.4606f, 15.581f, -259.8329f), new Quaternion(0f, 0.9963249f, 0f, 0.08565468f), new Vector3(0.31f, 2.75f, 0.1f), false, -1, null, false),
                new("1", 1, 241, BreakableColliderDescription.DesertRoadsign04, new Vector3(72.207f, 15.2469f, -249.477f), new Quaternion(-0.003829797f, -0.783609f, -0.00495404f, 0.6212228f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet40 = new BreakableColliderSetDescription("40", 40, 244, new Vector3(87.92f, 14.9042f, -228.58f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 243, BreakableColliderDescription.DesertRoadsign04, new Vector3(87.92f, 14.9042f, -228.58f), new Quaternion(-0.008831926f, -0.6122687f, 0.006790814f, -0.7905713f), new Vector3(0.66f, 2.48f, 0.1f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet41 = new BreakableColliderSetDescription("41", 41, 246, new Vector3(116.09f, 9.451f, -56.83f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 245, BreakableColliderDescription.CactusBig06Breakable, new Vector3(116.09f, 9.451f, -56.83f), new Quaternion(0.0009849751f, -0.07708375f, -0.01273892f, 0.9969428f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet42 = new BreakableColliderSetDescription("42", 42, 251, new Vector3(-15.8137f, 9.486f, 22.7736f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 247, BreakableColliderDescription.CactusBig07Breakable, new Vector3(-26.3923f, 9.41f, 44.2721f), new Quaternion(3.475867E-08f, 0.983255f, -1.87541E-07f, -0.1822355f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("1", 1, 248, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-34.6423f, 9.539f, 16.5221f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("2", 2, 249, BreakableColliderDescription.CactusBig05Breakable, new Vector3(9.12f, 9.488f, 34.95f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("3", 3, 250, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-11.34f, 9.507f, -4.65f), new Quaternion(-3.9656E-08f, 0.9781476f, -1.865668E-07f, 0.2079117f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet43 = new BreakableColliderSetDescription("43", 43, 256, new Vector3(65.0225f, 10.6182f, -131.3675f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 252, BreakableColliderDescription.CactusBig07Breakable, new Vector3(41.48f, 10.741f, -155.56f), new Quaternion(0.002570091f, 0.8279995f, -0.0144326f, 0.5605373f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("1", 1, 253, BreakableColliderDescription.CactusBig07Breakable, new Vector3(50.8f, 9.599f, -115.37f), new Quaternion(0.01121158f, 0.3257088f, -0.004305955f, 0.9453939f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("2", 2, 254, BreakableColliderDescription.CactusBig06Breakable, new Vector3(82.13f, 10.315f, -136.76f), new Quaternion(0.01014833f, 0.9884155f, -0.04510757f, -0.1445584f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("3", 3, 255, BreakableColliderDescription.CactusBig07Breakable, new Vector3(85.68f, 11.818f, -117.78f), new Quaternion(-0.04946341f, 0.8683685f, 0.02806188f, -0.492648f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet44 = new BreakableColliderSetDescription("44", 44, 264, new Vector3(29.9798f, 9.4823f, -55.3988f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 257, BreakableColliderDescription.CactusBig07Breakable, new Vector3(10.5361f, 9.437f, -43.2606f), new Quaternion(-1.348699E-07f, 0.7071068f, -1.348699E-07f, 0.7071068f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("1", 1, 258, BreakableColliderDescription.CactusBig05Breakable, new Vector3(44.2861f, 9.498f, -61.0106f), new Quaternion(0f, 0.9781476f, 0f, 0.2079117f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("2", 2, 259, BreakableColliderDescription.CactusBig05Breakable, new Vector3(55.2861f, 9.466f, -60.5106f), new Quaternion(3.614901E-06f, 0.983255f, -1.950426E-05f, -0.1822355f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("3", 3, 260, BreakableColliderDescription.CactusBig05Breakable, new Vector3(29.9f, 9.468f, -32.11f), new Quaternion(0f, 0.9998478f, 0f, 0.01745238f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("4", 4, 261, BreakableColliderDescription.CactusBig07Breakable, new Vector3(22.42f, 9.415f, -42.12f), new Quaternion(0f, 0.983255f, 0f, -0.1822355f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("5", 5, 262, BreakableColliderDescription.CactusBig06Breakable, new Vector3(11.13f, 9.575f, -71.1796f), new Quaternion(0.0001043349f, 0.9781475f, 0.0004908572f, 0.2079117f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("6", 6, 263, BreakableColliderDescription.CactusBig05Breakable, new Vector3(36.3f, 9.517f, -77.6f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet45 = new BreakableColliderSetDescription("45", 45, 272, new Vector3(-68.9151f, 9.9353f, -15.3006f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 265, BreakableColliderDescription.CactusBig07Breakable, new Vector3(-57.6963f, 9.546f, 9.4902f), new Quaternion(0.005109866f, -0.2962331f, -0.01647256f, 0.95496f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("1", 1, 266, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-88.7871f, 10.917f, -1.8488f), new Quaternion(0.01786256f, 0.619969f, -0.01516265f, 0.7842764f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("2", 2, 267, BreakableColliderDescription.CactusBig07Breakable, new Vector3(-90.5452f, 10.404f, -19.2145f), new Quaternion(-0.02380881f, 0.6007676f, -0.03164279f, 0.7984424f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("3", 3, 268, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-74.2383f, 9.522f, -32.7828f), new Quaternion(-0.004245029f, 0.7254441f, -0.004027419f, 0.6882563f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("4", 4, 269, BreakableColliderDescription.CactusBig07Breakable, new Vector3(-72.9608f, 10.107f, -13.9782f), new Quaternion(-0.005009074f, 0.3305971f, -0.01429781f, 0.9436504f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("5", 5, 270, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-57.4639f, 9.537f, -27.5095f), new Quaternion(0.002221252f, 0.9205075f, -0.005787459f, 0.3906757f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("6", 6, 271, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-40.7139f, 9.514f, -21.2606f), new Quaternion(0f, 0.983255f, 0f, -0.1822355f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet46 = new BreakableColliderSetDescription("46", 46, 277, new Vector3(-1.576f, 10.5385f, -79.8777f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 273, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-19.7139f, 9.562f, -55.0106f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("1", 1, 274, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-18.98f, 9.533f, -60.45f), new Quaternion(0f, 0.9998478f, 0f, 0.01745238f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("2", 2, 275, BreakableColliderDescription.CactusBig05Breakable, new Vector3(7.59f, 12.414f, -104.35f), new Quaternion(0.1423444f, 0.7005842f, -0.06389939f, 0.6963021f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("3", 3, 276, BreakableColliderDescription.CactusBig06Breakable, new Vector3(24.8f, 10.645f, -99.7f), new Quaternion(0.03834053f, 0.5417805f, -0.008033015f, 0.8396067f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet47 = new BreakableColliderSetDescription("47", 47, 281, new Vector3(11.84f, 11.6273f, -150.581f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 278, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-0.79f, 10.858f, -159.11f), new Quaternion(-0.002875146f, 0.7587441f, -0.008054786f, -0.6513328f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("1", 1, 279, BreakableColliderDescription.CactusBig05Breakable, new Vector3(24.4f, 11.025f, -164.8f), new Quaternion(0.00830462f, -0.184863f, 0.001263892f, 0.9827284f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("2", 2, 280, BreakableColliderDescription.CactusBig07Breakable, new Vector3(11.91f, 12.999f, -127.8331f), new Quaternion(0.003672453f, -0.2083094f, -0.01724026f, 0.9779041f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet48 = new BreakableColliderSetDescription("48", 48, 284, new Vector3(-106.8438f, 9.4925f, -69.8462f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 282, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-123.3923f, 9.508f, -87.2279f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("1", 1, 283, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-90.2952f, 9.477f, -52.4645f), new Quaternion(-3.328777E-09f, 0.9998478f, -1.907058E-07f, 0.01745238f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet49 = new BreakableColliderSetDescription("49", 49, 286, new Vector3(-40.49f, 9.617f, -83.77f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 285, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-40.49f, 9.617f, -83.77f), new Quaternion(-0.02124827f, 0.893763f, 0.01064857f, -0.4479095f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet50 = new BreakableColliderSetDescription("50", 50, 291, new Vector3(-54.3175f, 12.3985f, -162.7888f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 287, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-59.52f, 11.09f, -139.56f), new Quaternion(0.01468625f, -0.04830815f, 0.006468002f, 0.9987036f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("1", 1, 288, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-46.52f, 14.136f, -184.19f), new Quaternion(0.06543599f, 0.2323498f, -0.01810656f, 0.9702597f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("2", 2, 289, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-76.9f, 12.647f, -164.9954f), new Quaternion(0.03599061f, -0.8545501f, -0.02180206f, 0.5176616f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("3", 3, 290, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-34.33f, 11.721f, -162.41f), new Quaternion(0.02016746f, -0.4238149f, -0.04304114f, 0.9045008f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet51 = new BreakableColliderSetDescription("51", 51, 293, new Vector3(-90.56f, 9.427f, -117.4797f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 292, BreakableColliderDescription.CactusBig07Breakable, new Vector3(-90.56f, 9.427f, -117.4797f), new Quaternion(0.0001012021f, 0.9781475f, 0.0004761184f, 0.2079117f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet52 = new BreakableColliderSetDescription("52", 52, 298, new Vector3(-109.5776f, 16.0244f, -234.4268f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 294, BreakableColliderDescription.DesertRoadsign03, new Vector3(-85.5f, 16.1672f, -251.61f), new Quaternion(-0.01367272f, -0.4996948f, -0.01512483f, 0.8659616f), new Vector3(0.38f, 2.87f, 0.1f), false, -1, null, false),
                new("1", 1, 295, BreakableColliderDescription.Roadpole01, new Vector3(-132.99f, 15.9351f, -236.74f), new Quaternion(0.05701962f, 0.983797f, 0.036252f, -0.1660665f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 296, BreakableColliderDescription.Roadpole01, new Vector3(-128.34f, 15.9534f, -238.74f), new Quaternion(-0.01919857f, 0.7067941f, 0.08161198f, 0.7024336f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 297, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-91.4805f, 16.042f, -210.6171f), new Quaternion(0.0196893f, -0.0686572f, 0.000666108f, 0.9974458f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet53 = new BreakableColliderSetDescription("53", 53, 305, new Vector3(-162.174f, 9.6527f, -130.045f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 299, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-176.0179f, 9.492f, -127.872f), new Quaternion(-0.007416261f, 0.9880207f, 0.001156996f, -0.1541391f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("1", 1, 300, BreakableColliderDescription.CactusBig07Breakable, new Vector3(-179.2065f, 10.13f, -144.9863f), new Quaternion(0.08034896f, 0.2323871f, -0.02226082f, 0.9690433f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("2", 2, 301, BreakableColliderDescription.CactusBig07Breakable, new Vector3(-155.8924f, 9.704f, -144.7279f), new Quaternion(0.02040864f, -0.6838494f, -0.02175649f, 0.7290132f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("3", 3, 302, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-144.3924f, 9.546f, -129.2279f), new Quaternion(-1.997266E-08f, 0.9998478f, -1.144235E-06f, 0.01745238f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
                new("4", 4, 303, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-157.3923f, 9.505f, -123.2279f), new Quaternion(-1.348699E-07f, 0.7071068f, -1.348699E-07f, 0.7071068f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("5", 5, 304, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-160.1424f, 9.539f, -110.2279f), new Quaternion(0f, 0.983255f, 0f, -0.1822355f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet54 = new BreakableColliderSetDescription("54", 54, 309, new Vector3(-165.5932f, 13.4417f, -182.2301f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 306, BreakableColliderDescription.Roadsign01, new Vector3(-185.9192f, 14.97f, -202.6906f), new Quaternion(0f, 0.932777f, 0f, -0.3604541f), new Vector3(0.31f, 2.75f, 0.1f), false, -1, null, false),
                new("1", 1, 307, BreakableColliderDescription.CactusBig05Breakable, new Vector3(-144.4f, 12.929f, -177.9f), new Quaternion(0.02932486f, -0.007585855f, -0.0008012763f, 0.9995409f), new Vector3(0.5156f, 1.1463f, 0.5854f), false, -1, null, false),
                new("2", 2, 308, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-166.4603f, 12.426f, -166.0999f), new Quaternion(0.07756298f, 0.338565f, 0.00155456f, 0.9377395f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
            }, null
        );
        var breakableColliderSet55 = new BreakableColliderSetDescription("55", 55, 317, new Vector3(-137.1853f, 15.4122f, -220.2155f), new Enum<LocationBreakableColliderDescription>
            {
                new("0", 0, 310, BreakableColliderDescription.Roadpole01, new Vector3(-155.31f, 15.6494f, -224.95f), new Quaternion(0.06255507f, 0.9526722f, 0.0462048f, -0.293884f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("1", 1, 311, BreakableColliderDescription.Roadpole01, new Vector3(-141.28f, 15.8841f, -233.01f), new Quaternion(-0.003843588f, 0.9723669f, 0.0009226854f, -0.2334248f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("2", 2, 312, BreakableColliderDescription.Roadpole01, new Vector3(-136.91f, 15.9147f, -235.04f), new Quaternion(-0.0609801f, 0.9689916f, -0.100286f, -0.2174383f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("3", 3, 313, BreakableColliderDescription.Roadpole01, new Vector3(-149.98f, 15.7827f, -228.42f), new Quaternion(-0.007025061f, 0.9610838f, 0.002018599f, -0.2761603f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("4", 4, 314, BreakableColliderDescription.Roadpole01, new Vector3(-145.33f, 15.8458f, -231.04f), new Quaternion(-0.06519534f, 0.9647549f, -0.09857738f, -0.235117f), new Vector3(0.15f, 1.26f, 0.18f), false, -1, null, false),
                new("5", 5, 315, BreakableColliderDescription.CactusBig07Breakable, new Vector3(-105.4152f, 14.271f, -189.5693f), new Quaternion(0.03285551f, -0.8398713f, -0.02117848f, 0.5413762f), new Vector3(0.7528f, 1.0082f, 0.597f), false, -1, null, false),
                new("6", 6, 316, BreakableColliderDescription.CactusBig06Breakable, new Vector3(-126.0721f, 14.538f, -199.4792f), new Quaternion(0.03914927f, -0.6836265f, -0.04166688f, 0.7275892f), new Vector3(0.4861f, 1.2738f, 0.5202f), false, -1, null, false),
            }, null
        );
        var locationBreakableCollidersSets = new Enum<BreakableColliderSetDescription>()
        {
            breakableColliderSet0,
            breakableColliderSet1,
            breakableColliderSet2,
            breakableColliderSet3,
            breakableColliderSet4,
            breakableColliderSet5,
            breakableColliderSet6,
            breakableColliderSet7,
            breakableColliderSet8,
            breakableColliderSet9,
            breakableColliderSet10,
            breakableColliderSet11,
            breakableColliderSet12,
            breakableColliderSet13,
            breakableColliderSet14,
            breakableColliderSet15,
            breakableColliderSet16,
            breakableColliderSet17,
            breakableColliderSet18,
            breakableColliderSet19,
            breakableColliderSet20,
            breakableColliderSet21,
            breakableColliderSet22,
            breakableColliderSet23,
            breakableColliderSet24,
            breakableColliderSet25,
            breakableColliderSet26,
            breakableColliderSet27,
            breakableColliderSet28,
            breakableColliderSet29,
            breakableColliderSet30,
            breakableColliderSet31,
            breakableColliderSet32,
            breakableColliderSet33,
            breakableColliderSet34,
            breakableColliderSet35,
            breakableColliderSet36,
            breakableColliderSet37,
            breakableColliderSet38,
            breakableColliderSet39,
            breakableColliderSet40,
            breakableColliderSet41,
            breakableColliderSet42,
            breakableColliderSet43,
            breakableColliderSet44,
            breakableColliderSet45,
            breakableColliderSet46,
            breakableColliderSet47,
            breakableColliderSet48,
            breakableColliderSet49,
            breakableColliderSet50,
            breakableColliderSet51,
            breakableColliderSet52,
            breakableColliderSet53,
            breakableColliderSet54,
            breakableColliderSet55,
        };
        var hideObject0 = new LocationHideObjectDescription("0", 0, 0, HideObjectDescription.Toilet_01, new Vector3(-31.1549f, 14.3407f, -284.721f), new Quaternion(0f, -0.7352757f, 0f, 0.6777682f), new Vector3(-31.1631f, 15.698f, -284.6825f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(291, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(292, PhysicsBodyIdType.BakedStatic)});
        var hideObject1 = new LocationHideObjectDescription("1", 1, 1, HideObjectDescription.Box0, new Vector3(-43.4782f, 14.3568f, -284.5829f), new Quaternion(0f, 0.2915839f, 0f, -0.9565453f), new Vector3(-43.5628f, 15.1519f, -284.6363f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(249, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(289, PhysicsBodyIdType.BakedStatic)});
        var hideObject2 = new LocationHideObjectDescription("2", 2, 2, HideObjectDescription.Box0, new Vector3(-16.729f, 14.3568f, -288.3647f), new Quaternion(0f, -0.7138256f, 0f, -0.7003236f), new Vector3(-16.7243f, 15.1519f, -288.2648f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(238, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(242, PhysicsBodyIdType.BakedStatic)});
        var hideObject3 = new LocationHideObjectDescription("3", 3, 3, HideObjectDescription.CupboardHide_01, new Vector3(52.106f, 9.0001f, -36.845f), new Quaternion(0f, -0.2588184f, 0f, 0.965926f), new Vector3(52.1074f, 10.3107f, -36.8443f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(2509, PhysicsBodyIdType.BakedStatic)});
        var hideObject4 = new LocationHideObjectDescription("4", 4, 4, HideObjectDescription.CupboardHide_01, new Vector3(53.1885f, 9.0001f, -36.22f), new Quaternion(0f, -0.2588184f, 0f, 0.965926f), new Vector3(53.1899f, 10.3107f, -36.2193f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(2542, PhysicsBodyIdType.BakedStatic)});
        var hideObject5 = new LocationHideObjectDescription("5", 5, 5, HideObjectDescription.CupboardHide_01, new Vector3(-7.524f, 8.9884f, 11.945f), new Quaternion(0f, 0.9803728f, 0f, 0.1971529f), new Vector3(-7.5255f, 10.299f, 11.9445f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(691, PhysicsBodyIdType.BakedStatic)});
        var hideObject6 = new LocationHideObjectDescription("6", 6, 6, HideObjectDescription.CupboardHide_01, new Vector3(-0.6927f, 8.9884f, 18.4351f), new Quaternion(0f, 0.5849108f, 0f, 0.8110977f), new Vector3(-0.6922f, 10.299f, 18.4336f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(730, PhysicsBodyIdType.BakedStatic)});
        var hideObject7 = new LocationHideObjectDescription("7", 7, 7, HideObjectDescription.CupboardHide_01, new Vector3(43.785f, 8.9967f, -14.3631f), new Quaternion(0f, 0.5000007f, 0f, 0.8660251f), new Vector3(43.7858f, 10.3072f, -14.3644f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(3832, PhysicsBodyIdType.BakedStatic)});
        var hideObject8 = new LocationHideObjectDescription("8", 8, 8, HideObjectDescription.CupboardHide_01, new Vector3(44.41f, 8.9967f, -15.4456f), new Quaternion(0f, 0.5000007f, 0f, 0.8660251f), new Vector3(44.4108f, 10.3072f, -15.447f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(3757, PhysicsBodyIdType.BakedStatic)});
        var hideObject9 = new LocationHideObjectDescription("9", 9, 9, HideObjectDescription.CupboardHide_01, new Vector3(72.6471f, 8.9951f, -29.5416f), new Quaternion(0f, -0.2588209f, 0f, 0.9659253f), new Vector3(72.6485f, 10.3057f, -29.5409f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(2869, PhysicsBodyIdType.BakedStatic)});
        var hideObject10 = new LocationHideObjectDescription("10", 10, 10, HideObjectDescription.CupboardHide_01, new Vector3(62.0776f, 12.23f, -35.7028f), new Quaternion(0f, -0.2588209f, 0f, 0.9659253f), new Vector3(62.079f, 13.5406f, -35.702f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(3264, PhysicsBodyIdType.BakedStatic)});
        var hideObject11 = new LocationHideObjectDescription("11", 11, 11, HideObjectDescription.CupboardHide_01, new Vector3(73.7556f, 8.9951f, -28.9016f), new Quaternion(0f, -0.2588209f, 0f, 0.9659253f), new Vector3(73.757f, 10.3057f, -28.9009f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(2879, PhysicsBodyIdType.BakedStatic)});
        var hideObject12 = new LocationHideObjectDescription("12", 12, 12, HideObjectDescription.CupboardHide_01, new Vector3(88.5516f, 8.9951f, -20.3591f), new Quaternion(0f, -0.2588209f, 0f, 0.9659253f), new Vector3(88.553f, 10.3057f, -20.3584f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(3558, PhysicsBodyIdType.BakedStatic)});
        var hideObject13 = new LocationHideObjectDescription("13", 13, 13, HideObjectDescription.CupboardHide_01, new Vector3(87.4431f, 8.9951f, -20.9991f), new Quaternion(0f, -0.2588209f, 0f, 0.9659253f), new Vector3(87.4445f, 10.3057f, -20.9984f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(3527, PhysicsBodyIdType.BakedStatic)});
        var hideObject14 = new LocationHideObjectDescription("14", 14, 14, HideObjectDescription.CupboardHide_01, new Vector3(74.1304f, 8.9967f, 6.8127f), new Quaternion(0f, 0.9659259f, 0f, 0.258819f), new Vector3(74.129f, 10.3072f, 6.8119f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(4241, PhysicsBodyIdType.BakedStatic)});
        var hideObject15 = new LocationHideObjectDescription("15", 15, 15, HideObjectDescription.CupboardHide_01, new Vector3(72.976f, 8.9967f, 6.1462f), new Quaternion(0f, 0.9659259f, 0f, 0.258819f), new Vector3(72.9746f, 10.3072f, 6.1454f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(4239, PhysicsBodyIdType.BakedStatic)});
        var hideObject16 = new LocationHideObjectDescription("16", 16, 16, HideObjectDescription.CupboardHide_01, new Vector3(42.3022f, 8.9884f, 33.2494f), new Quaternion(0f, 0.5000011f, 0f, 0.8660249f), new Vector3(42.3029f, 10.299f, 33.248f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(4877, PhysicsBodyIdType.BakedStatic)});
        var hideObject17 = new LocationHideObjectDescription("17", 17, 17, HideObjectDescription.CupboardHide_01, new Vector3(42.9687f, 8.9884f, 32.0949f), new Quaternion(0f, 0.5000011f, 0f, 0.8660249f), new Vector3(42.9694f, 10.299f, 32.0936f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(4855, PhysicsBodyIdType.BakedStatic)});
        var hideObject18 = new LocationHideObjectDescription("18", 18, 18, HideObjectDescription.CupboardHide_01, new Vector3(57.5409f, 8.9884f, 52.4634f), new Quaternion(0f, 0.8660263f, 0f, -0.4999986f), new Vector3(57.5401f, 10.299f, 52.4648f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(5206, PhysicsBodyIdType.BakedStatic)});
        var hideObject19 = new LocationHideObjectDescription("19", 19, 19, HideObjectDescription.CupboardHide_01, new Vector3(56.8744f, 8.9884f, 53.6178f), new Quaternion(0f, 0.8660263f, 0f, -0.4999986f), new Vector3(56.8736f, 10.299f, 53.6192f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(5218, PhysicsBodyIdType.BakedStatic)});
        var hideObject20 = new LocationHideObjectDescription("20", 20, 20, HideObjectDescription.Toilet_01, new Vector3(93.0475f, 8.9967f, 1.8573f), new Quaternion(0f, 0.9392034f, 0f, 0.3433612f), new Vector3(93.021f, 10.3539f, 1.8282f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(4155, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4154, PhysicsBodyIdType.BakedStatic)});
        var hideObject21 = new LocationHideObjectDescription("21", 21, 21, HideObjectDescription.Toilet_01, new Vector3(91.6506f, 8.9967f, 0.8994f), new Quaternion(0f, 0.9664112f, 0f, 0.2570007f), new Vector3(91.6192f, 10.3539f, 0.8756f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(4141, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4142, PhysicsBodyIdType.BakedStatic)});
        var hideObject22 = new LocationHideObjectDescription("22", 22, 22, HideObjectDescription.Toilet_01, new Vector3(94.5341f, 8.9967f, 2.7745f), new Quaternion(0f, 0.9659259f, 0f, 0.258819f), new Vector3(94.5028f, 10.3539f, 2.7505f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(4171, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4173, PhysicsBodyIdType.BakedStatic)});
        var hideObject23 = new LocationHideObjectDescription("23", 23, 23, HideObjectDescription.Toilet_01, new Vector3(62.1482f, 8.9595f, 59.4911f), new Quaternion(0f, -0.9645191f, 0f, -0.2640134f), new Vector3(62.1172f, 10.3168f, 59.4668f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(5289, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(5290, PhysicsBodyIdType.BakedStatic)});
        var hideObject24 = new LocationHideObjectDescription("24", 24, 24, HideObjectDescription.Toilet_01, new Vector3(60.5894f, 8.9595f, 58.5911f), new Quaternion(0f, -0.9645191f, 0f, -0.2640134f), new Vector3(60.5584f, 10.3168f, 58.5668f), new HashSet<PhysicsBodyId>(){new PhysicsBodyId(5278, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(5277, PhysicsBodyIdType.BakedStatic)});
        var hideObjects = new Enum<LocationHideObjectDescription>
        {
            hideObject0,
            hideObject1,
            hideObject2,
            hideObject3,
            hideObject4,
            hideObject5,
            hideObject6,
            hideObject7,
            hideObject8,
            hideObject9,
            hideObject10,
            hideObject11,
            hideObject12,
            hideObject13,
            hideObject14,
            hideObject15,
            hideObject16,
            hideObject17,
            hideObject18,
            hideObject19,
            hideObject20,
            hideObject21,
            hideObject22,
            hideObject23,
            hideObject24,
        };
        var locationNpc0 = new LocationNpcDescription("0", 0, 27, NPCDescription.TraderTruck, new Vector3(54.273f, 10.702f, -11.945f), new Quaternion(0f, 0.3665088f, 0f, 0.9304146f), TraderInventoryDescription.SettlerTrader, null);
        var npcDescriptions = new List<LocationNpcDescription>
        {
            locationNpc0
        };
        var lootNode0 = new LocationLootNodeDescription("0", 0, 25, LootNodeDescription.CrateAmmo, new Vector3(511.092f, 22.5897f, 168.94f), new Quaternion(-0.01560218f, 0.9935161f, -0.03387532f, -0.1074f), new Vector3(511.0901f, 22.6691f, 168.9347f), null, null, 1f, false);
        var lootNode1 = new LocationLootNodeDescription("0", 0, 26, LootNodeDescription.CrateAlcohol, new Vector3(-296.6576f, 5.7967f, -45.7178f), new Quaternion(-0.01855875f, 0.9991342f, -0.03235022f, -0.01844017f), new Vector3(-296.6634f, 5.9463f, -45.7274f), null, null, 1f, false);
        var random_cargo0 = new LocationRandomCargoDescription("0", 0, 28, new Vector3(-21.411f, 14.945f, -288.099f), new Quaternion(0f, -0.7038802f, 0f, 0.7103187f), RandomCargoDescription.RandomCargoBox);
        var random_cargo1 = new LocationRandomCargoDescription("0", 0, 29, new Vector3(66.115f, 9.888f, -32.901f), new Quaternion(0f, 0.05397254f, 0f, -0.9985424f), RandomCargoDescription.RandomCargoBox);
        var random_cargo2 = new LocationRandomCargoDescription("0", 0, 30, new Vector3(-4.664f, 8.993f, 15.246f), new Quaternion(0f, 0.9284664f, 0f, 0.3714162f), RandomCargoDescription.RandomCargoBox);
        var random_cargo3 = new LocationRandomCargoDescription("0", 0, 31, new Vector3(62.289f, 8.99f, 41.034f), new Quaternion(0f, 0.434202f, 0f, -0.9008156f), RandomCargoDescription.RandomCargoBox);
        var kinematicObjectsController0 = new KinematicObjectsControllerEntityDescription("0", 0, 32,
			new KinematicObjectsControllerDescription(
				new KinematicObjectDescription[] {
						new KinematicObjectDescription(
						new CollidersDescription(
							boxes: new BoxColliderDescription[] {
								new BoxColliderDescription(new Vector3(-1.9501f, 0.2f, -1.5455f), new Quaternion(0f, 7.018131E-09f, 0f, 1f), new Vector3(9.4001f, 3f, 0.1f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f)
							}, 
							triangles: Array.Empty<TriangleColliderDescription>(), 
							capsules: Array.Empty<CapsuleColliderDescription>(), 
							hulls: Array.Empty<HullColliderDescription>(), 
							hullData: Array.Empty<HullShapeData>()))
					}, 
				new KinematicObjectMovementDescription[] {
						new KinematicObjectMovementDescription(new Vector3(106.3241f, 10.4061f, -23.455f), new Quaternion(0f, -0.2588193f, 0f, 0.9659258f), new Vector3(106.3241f, 10.8061f, -23.455f), new Quaternion(0.5880184f, -0.2053352f, 0.1575592f, 0.7663205f))
					}, 
				3000, 500, KinematicObjectMovementStateDescription.Point1, false, 
				new OBB[] {  }, 
				new OBB[] {  }, 
				new HashSet<KinematicObjectMovementStateDescription> { KinematicObjectMovementStateDescription.Point1 }));
        var kinematicObjectsControllers = new Enum<KinematicObjectsControllerEntityDescription>()
        {
            kinematicObjectsController0,
        };
        var buildings = new Enum<LocationBuildingDescription>()
        {
            new LocationBuildingDescription("0", 0, 324, null, new Vector3(9.19f, -16.33f, -262.99f), 
                new List<OBB>() { new OBB(new Vector3(-23.653f, 15.9404f, -280.7213f), new Vector3(8.1968f, 2.9209f, 8.3006f), new Quaternion(0f, -0.7071071f, 0f, 0.7071065f)) }, 
                new Vector3(-2.18f, 14.8f, -285.06f), new Vector3(-0.6386f, 0f, 0.7695f), 
                new (Vector3 Point, Quaternion Orientation)[] {(new Vector3(-2.18f, 14.8f, -285.06f), new Quaternion(0f, 0.3394799f, 0f, -0.9406134f))},
                900000, 
                alarm0, 
                null, 
                new List<LocationContainerDescription>() {  }, 
                new List<LocationDoorDescription>() {  }, 
                new List<KinematicObjectsControllerEntityDescription>() {  }, 
                new List<BreakableColliderSetDescription>() {  }, 
                new Enum<RandomLootObjectDescription> { new("0", 0, 39, new Vector3(-31.161f, 15.343f, -281.55f), new Quaternion(0f, 0f, 0f, 1f), new WeightItems<LootObjectItemsListDescription>(0, new WeightItem<LootObjectItemsListDescription>[] {new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.ElectronicSmallUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.AlcoholUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.LaptopsUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.Medicine),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.ElectronicSmallRare),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.AlcoholRare),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.GraphicsCardRare),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.LaptopsRare),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.ElectronicSmallEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.AlcoholEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.LaptopsEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.GraphicsCardEpic),new WeightItem<LootObjectItemsListDescription>(1, LootObjectItemsListDescription.AlcoholLegendary),}), new Vector3(-31.161f, 15.523f, -281.55f), null, false, null), }, 
                new Enum<StaticLootObjectDescription> {  }, 
                new Enum<LocationCameraDescription>() {  },
                new List<LocationHideObjectDescription>() { hideObject0, hideObject2 },
                new Enum<LocationVigilantNpcDescription>() {  },
                new Enum<LocationLootNodeDescription>() {  },
                new List<LocationBuildingGroupLootDescription>() {  },
                null,
                new Enum<LocationStaticCargoDescription>() {  },
                new Enum<LocationRandomCargoDescription>() { random_cargo0 },
                null,
                new Enum<LocationCumulativeChargerDescription>() {  },
                new Vector3(9.19f, -16.33f, -262.99f)
            ),
            new LocationBuildingDescription("1", 1, 325, null, new Vector3(527.07f, -11.1188f, 148.55f), 
                new List<OBB>() { new OBB(new Vector3(511.2502f, 22.1244f, 168.75f), new Vector3(1.2498f, 0.8913f, 1.25f), new Quaternion(0f, 0f, 0f, 1f)) }, 
                new Vector3(515.858f, 22.9682f, 169.946f), new Vector3(0.9994f, 0f, -0.0349f), 
                new (Vector3 Point, Quaternion Orientation)[] {(new Vector3(515.858f, 22.9682f, 169.946f), new Quaternion(0f, -0.7193435f, 0f, -0.6946546f))},
                900000, 
                alarm1, 
                null, 
                new List<LocationContainerDescription>() {  }, 
                new List<LocationDoorDescription>() {  }, 
                new List<KinematicObjectsControllerEntityDescription>() {  }, 
                new List<BreakableColliderSetDescription>() { breakableColliderSet1 }, 
                new Enum<RandomLootObjectDescription> {  }, 
                new Enum<StaticLootObjectDescription> {  }, 
                new Enum<LocationCameraDescription>() {  },
                new List<LocationHideObjectDescription>() {  },
                new Enum<LocationVigilantNpcDescription>() {  },
                new Enum<LocationLootNodeDescription>() { lootNode0 },
                new List<LocationBuildingGroupLootDescription>() {  },
                null,
                new Enum<LocationStaticCargoDescription>() {  },
                new Enum<LocationRandomCargoDescription>() {  },
                null,
                new Enum<LocationCumulativeChargerDescription>() {  },
                new Vector3(527.07f, -11.1188f, 148.55f)
            ),
            new LocationBuildingDescription("2", 2, 326, null, new Vector3(-280.02f, -27.62f, -66.42f), 
                new List<OBB>() { new OBB(new Vector3(-296.25f, 5.3782f, -46.2431f), new Vector3(1.25f, 0.9182f, 1.2431f), new Quaternion(0f, 0f, 0f, 1f)) }, 
                new Vector3(-292.46f, 5.875f, -45.23f), new Vector3(0.9994f, 0f, -0.0349f), 
                new (Vector3 Point, Quaternion Orientation)[] {(new Vector3(-292.46f, 5.875f, -45.23f), new Quaternion(0f, -0.7193435f, 0f, -0.6946546f))},
                900000, 
                alarm2, 
                null, 
                new List<LocationContainerDescription>() {  }, 
                new List<LocationDoorDescription>() {  }, 
                new List<KinematicObjectsControllerEntityDescription>() {  }, 
                new List<BreakableColliderSetDescription>() { breakableColliderSet0 }, 
                new Enum<RandomLootObjectDescription> {  }, 
                new Enum<StaticLootObjectDescription> {  }, 
                new Enum<LocationCameraDescription>() {  },
                new List<LocationHideObjectDescription>() {  },
                new Enum<LocationVigilantNpcDescription>() {  },
                new Enum<LocationLootNodeDescription>() { lootNode1 },
                new List<LocationBuildingGroupLootDescription>() {  },
                null,
                new Enum<LocationStaticCargoDescription>() {  },
                new Enum<LocationRandomCargoDescription>() {  },
                null,
                new Enum<LocationCumulativeChargerDescription>() {  },
                new Vector3(-280.02f, -27.62f, -66.42f)
            ),
            new LocationBuildingDescription("3", 3, 327, null, new Vector3(21.4807f, -23.834f, -34.6533f), 
                new List<OBB>() { new OBB(new Vector3(66.662f, 10.5216f, -15.2816f), new Vector3(20f, 3.2489f, 16f), new Quaternion(0f, -0.9659258f, 0f, -0.2588192f)), new OBB(new Vector3(63.4033f, 10.1263f, -21.6374f), new Vector3(26f, 2.8537f, 8.125f), new Quaternion(0f, -0.9659258f, 0f, -0.2588192f)) }, 
                new Vector3(42.7548f, 9.196f, -3.3412f), new Vector3(-0.883f, 0f, -0.4695f), 
                new (Vector3 Point, Quaternion Orientation)[] {(new Vector3(42.7548f, 9.196f, -3.3412f), new Quaternion(0f, 0.8571646f, 0f, -0.5150427f))},
                900000, 
                alarm3, 
                null, 
                new List<LocationContainerDescription>() {  }, 
                new List<LocationDoorDescription>() { door0, door2, door3 }, 
                new List<KinematicObjectsControllerEntityDescription>() {  }, 
                new List<BreakableColliderSetDescription>() { breakableColliderSet2 }, 
                new Enum<RandomLootObjectDescription> { new("0", 0, 40, new Vector3(71.977f, 12.8299f, -23.648f), new Quaternion(0f, 0f, 0f, 1f), new WeightItems<LootObjectItemsListDescription>(0, new WeightItem<LootObjectItemsListDescription>[] {new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.ElectronicSmallUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.AlcoholUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.LaptopsUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.Medicine),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.ElectronicSmallRare),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.AlcoholRare),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.GraphicsCardRare),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.LaptopsRare),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.ElectronicSmallEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.AlcoholEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.LaptopsEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.GraphicsCardEpic),new WeightItem<LootObjectItemsListDescription>(1, LootObjectItemsListDescription.AlcoholLegendary),}), new Vector3(71.977f, 13.0099f, -23.648f), null, false, null), }, 
                new Enum<StaticLootObjectDescription> {  }, 
                new Enum<LocationCameraDescription>() {  },
                new List<LocationHideObjectDescription>() { hideObject3, hideObject4, hideObject7, hideObject8, hideObject9, hideObject10, hideObject11, hideObject12, hideObject13, hideObject14, hideObject15 },
                new Enum<LocationVigilantNpcDescription>() {  },
                new Enum<LocationLootNodeDescription>() {  },
                new List<LocationBuildingGroupLootDescription>() {  },
                null,
                new Enum<LocationStaticCargoDescription>() {  },
                new Enum<LocationRandomCargoDescription>() { random_cargo1 },
                null,
                new Enum<LocationCumulativeChargerDescription>() {  },
                new Vector3(21.4807f, -23.834f, -34.6533f)
            ),
            new LocationBuildingDescription("4", 4, 328, null, new Vector3(-40.3f, -23.834f, -20.87f), 
                new List<OBB>() { new OBB(new Vector3(0.4259f, 10.1313f, 10.6514f), new Vector3(12.5001f, 2.8587f, 7.5f), new Quaternion(0f, -0.9238796f, 0f, -0.3826833f)) }, 
                new Vector3(7.2725f, 9.196f, -4.2128f), new Vector3(0.682f, 0f, -0.7314f), 
                new (Vector3 Point, Quaternion Orientation)[] {(new Vector3(7.2725f, 9.196f, -4.2128f), new Quaternion(0f, 0.9304196f, 0f, 0.3664961f))},
                900000, 
                alarm4, 
                null, 
                new List<LocationContainerDescription>() {  }, 
                new List<LocationDoorDescription>() {  }, 
                new List<KinematicObjectsControllerEntityDescription>() {  }, 
                new List<BreakableColliderSetDescription>() {  }, 
                new Enum<RandomLootObjectDescription> { new("0", 0, 41, new Vector3(-5.74f, 10.1294f, 0.543f), new Quaternion(0f, 0f, 0f, 1f), new WeightItems<LootObjectItemsListDescription>(0, new WeightItem<LootObjectItemsListDescription>[] {new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.ElectronicSmallUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.AlcoholUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.LaptopsUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.Medicine),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.ElectronicSmallRare),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.AlcoholRare),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.GraphicsCardRare),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.LaptopsRare),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.ElectronicSmallEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.AlcoholEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.LaptopsEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.GraphicsCardEpic),new WeightItem<LootObjectItemsListDescription>(1, LootObjectItemsListDescription.AlcoholLegendary),}), new Vector3(-5.74f, 10.3094f, 0.543f), null, false, null), }, 
                new Enum<StaticLootObjectDescription> {  }, 
                new Enum<LocationCameraDescription>() {  },
                new List<LocationHideObjectDescription>() { hideObject5, hideObject6 },
                new Enum<LocationVigilantNpcDescription>() {  },
                new Enum<LocationLootNodeDescription>() {  },
                new List<LocationBuildingGroupLootDescription>() {  },
                null,
                new Enum<LocationStaticCargoDescription>() {  },
                new Enum<LocationRandomCargoDescription>() { random_cargo2 },
                null,
                new Enum<LocationCumulativeChargerDescription>() {  },
                new Vector3(-40.3f, -23.834f, -20.87f)
            ),
            new LocationBuildingDescription("5", 5, 329, null, new Vector3(0.71f, -23.834f, 23.31f), 
                new List<OBB>() { new OBB(new Vector3(49.1909f, 10.5216f, 44.0958f), new Vector3(12f, 3.2489f, 10f), new Quaternion(0f, -0.9659258f, 0f, -0.2588192f)) }, 
                new Vector3(50.9727f, 9.196f, 27.087f), new Vector3(0.4695f, 0f, -0.883f), 
                new (Vector3 Point, Quaternion Orientation)[] {(new Vector3(50.9727f, 9.196f, 27.087f), new Quaternion(0f, 0.970297f, 0f, 0.2419168f))},
                900000, 
                alarm5, 
                null, 
                new List<LocationContainerDescription>() {  }, 
                new List<LocationDoorDescription>() { door4, door5 }, 
                new List<KinematicObjectsControllerEntityDescription>() {  }, 
                new List<BreakableColliderSetDescription>() {  }, 
                new Enum<RandomLootObjectDescription> { new("0", 0, 42, new Vector3(44.683f, 8.99f, 52.118f), new Quaternion(0f, 0.974719f, 0f, 0.2234342f), new WeightItems<LootObjectItemsListDescription>(0, new WeightItem<LootObjectItemsListDescription>[] {new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.ElectronicSmallUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.AlcoholUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.LaptopsUncommon),new WeightItem<LootObjectItemsListDescription>(25, LootObjectItemsListDescription.Medicine),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.ElectronicSmallRare),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.AlcoholRare),new WeightItem<LootObjectItemsListDescription>(15, LootObjectItemsListDescription.GraphicsCardRare),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.LaptopsRare),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.ElectronicSmallEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.AlcoholEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.LaptopsEpic),new WeightItem<LootObjectItemsListDescription>(5, LootObjectItemsListDescription.GraphicsCardEpic),new WeightItem<LootObjectItemsListDescription>(1, LootObjectItemsListDescription.AlcoholLegendary),}), new Vector3(44.683f, 9.17f, 52.118f), null, false, null), }, 
                new Enum<StaticLootObjectDescription> {  }, 
                new Enum<LocationCameraDescription>() {  },
                new List<LocationHideObjectDescription>() { hideObject16, hideObject17, hideObject18, hideObject19 },
                new Enum<LocationVigilantNpcDescription>() {  },
                new Enum<LocationLootNodeDescription>() {  },
                new List<LocationBuildingGroupLootDescription>() {  },
                null,
                new Enum<LocationStaticCargoDescription>() {  },
                new Enum<LocationRandomCargoDescription>() { random_cargo3 },
                null,
                new Enum<LocationCumulativeChargerDescription>() {  },
                new Vector3(0.71f, -23.834f, 23.31f)
            ),
        };
        var metalDetectors = new Enum<LocationMetalDetectorDescription>
        {
        };
        var switch0 = new LocationSwitchDescription(43, new SwitchDescription(4.5f, new Vector3(106.8989f, 10.4012f, -24.7005f), null));
        var switch1 = new LocationSwitchDescription(44, new SwitchDescription(4.5f, new Vector3(107.273f, 10.4012f, -24.8506f), null));
        var switches = new List<LocationSwitchDescription>()
        {
            switch0,
            switch1,
        };
        var trigger0 = new TriggerDescription(new TriggerEventDescription.SwitchTriggered(0), new TriggerConditionDescription.NoCondition(), new TriggerActionDescription.SetKinematicObjectsControllerChangeStateTimer(0, 500, false));
        var trigger1 = new TriggerDescription(new TriggerEventDescription.SwitchTriggered(1), new TriggerConditionDescription.NoCondition(), new TriggerActionDescription.SetKinematicObjectsControllerChangeStateTimer(0, 500, false));
        var triggers = new List<TriggerDescription>()
        {
            trigger0,
            trigger1,
        };
        LocationCraftWorkbenchDescription craftWorkbenchDescription = null;
        IReadOnlyCollection<LocationCargoSellerDescription> cargoSellerDescriptions = new List<LocationCargoSellerDescription>()
        {
            new("0", 318, CargoSellerDescription.SettlementSeller, new Vector3(67.6334f, 9.0181f, -18.7877f), new Vector3(67.7928f, 9.6709f, -18.8346f), new HashSet<PhysicsBodyId>() {new PhysicsBodyId(3628, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3641, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3627, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3639, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3663, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3630, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3646, PhysicsBodyIdType.BakedStatic)}),
        };
        var locationCargoExchangerDescriptions = new Enum<LocationCargoExchangerDescription>()
        {
        };
        var bollards = new Enum<LocationBollardDescription>()
        {
        };

        var clanBattleConquestObjects = new List<LocationClanBattleConquestObjectDescription>()
        {
        };
        LocationClanBattleConquestObjectDescription sectorLocationClanBattleConquestObject = new LocationClanBattleConquestObjectDescription(null, new Vector3(65.09f, 12.33f, -14.27f), new OBB(new Vector3(64.9816f, 11.7466f, -13.9424f), new Vector3(10f, 2.9554f, 5.8276f), new Quaternion(0f, 0.4999993f, 0f, 0.8660258f)), new LocationClanBattleTeamZoneDescription(new Vector3(-4.8f, 0f, 21.6f), new OBB(new Vector3(0f, 0f, 0f), new Vector3(125f, 125f, 125f), new Quaternion(0f, 0f, 0f, 1f)), new[] { new BoxColliderDescription(new Vector3(127.5f, 0f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(5f, 260f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(-127.5f, 0f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(5f, 260f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 127.5f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 5f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, -127.5f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 5f, 260f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 0f, 127.5f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 260f, 5f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 0f, -127.5f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(260f, 260f, 5f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f) }, new[] { new LocationBorderEnterPoint(new OrientedPoint(new Vector3(13.14f, 10.095f, -19.7099f), new Quaternion(0f, 0.4721464f, 0f, 0.8815202f)), new OrientedPoint(new Vector3(-32.0999f, 116.575f, -32.7f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(4.77f, 10.095f, -25.1499f), new Quaternion(0f, 0.4721464f, 0f, 0.8815202f)), new OrientedPoint(new Vector3(-10.9999f, 116.575f, -32.7f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-4.85f, 10.095f, -31.4799f), new Quaternion(0f, 0.4721464f, 0f, 0.8815202f)), new OrientedPoint(new Vector3(-23f, 116.575f, -32.7f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(95.4998f, 10.115f, 51.8003f), new Quaternion(0f, 0.9979882f, 0f, -0.06340016f)), new OrientedPoint(new Vector3(1.0001f, 116.575f, -32.9f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(87.2998f, 10.115f, 46.2003f), new Quaternion(0f, 0.9979882f, 0f, -0.06340016f)), new OrientedPoint(new Vector3(22.1001f, 116.575f, -32.8999f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(77.7998f, 10.115f, 39.7002f), new Quaternion(0f, 0.9979882f, 0f, -0.06340016f)), new OrientedPoint(new Vector3(10.1001f, 116.575f, -32.9f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(8.4435f, 10.045f, -0.9358f), new Quaternion(0f, -0.9307795f, 0f, -0.3655812f)), new OrientedPoint(new Vector3(-60.8787f, 116.575f, 60.6566f), new Quaternion(0f, -0.7071046f, 0f, -0.7071092f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(10.9358f, 10.045f, 1.4405f), new Quaternion(0f, -0.9307787f, 0f, -0.365583f)), new OrientedPoint(new Vector3(-60.8415f, 116.575f, 39.5796f), new Quaternion(0f, -0.7071046f, 0f, -0.7071092f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(13.2611f, 10.045f, 3.6616f), new Quaternion(0f, -0.9307787f, 0f, -0.365583f)), new OrientedPoint(new Vector3(-60.8912f, 116.575f, 51.6211f), new Quaternion(0f, -0.7071046f, 0f, -0.7071092f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(39.1854f, 10.035f, -13.1704f), new Quaternion(0f, -0.2862211f, 0f, 0.9581636f)), new OrientedPoint(new Vector3(-60.8123f, 116.575f, 17.4303f), new Quaternion(0f, 0.7071066f, 0f, 0.7071071f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(36.5897f, 10.035f, -15.0538f), new Quaternion(0f, -0.2862223f, 0f, 0.9581633f)), new OrientedPoint(new Vector3(-60.7751f, 116.575f, -3.6467f), new Quaternion(0f, 0.7071066f, 0f, 0.7071071f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(33.9458f, 10.035f, -16.7306f), new Quaternion(0f, -0.286223f, 0f, 0.9581631f)), new OrientedPoint(new Vector3(-60.8161f, 116.575f, 8.2952f), new Quaternion(0f, 0.7071066f, 0f, 0.7071071f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(74.7198f, 10.075f, 23.1403f), new Quaternion(0f, 0.8959582f, 0f, -0.4441384f)), new OrientedPoint(new Vector3(-57.8f, 116.575f, -16.0001f), new Quaternion(0f, 0.3420191f, 0f, 0.939693f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(67.2606f, 10.075f, 17.5425f), new Quaternion(0f, 0.8959582f, 0f, -0.4441384f)), new OrientedPoint(new Vector3(-41.6f, 116.575f, -29.6001f), new Quaternion(0f, 0.3420191f, 0f, 0.939693f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(60.8906f, 10.075f, 12.4325f), new Quaternion(0f, 0.8959582f, 0f, -0.4441384f)), new OrientedPoint(new Vector3(-50.8f, 116.575f, -21.9001f), new Quaternion(0f, 0.3420191f, 0f, 0.939693f))) }), new LocationClanBattleTeamZoneDescription(new Vector3(-393.5f, 3.3f, -442f), new OBB(new Vector3(0f, 0f, 0f), new Vector3(100f, 125f, 100f), new Quaternion(0f, 0f, 0f, 1f)), new[] { new BoxColliderDescription(new Vector3(102.5f, 0f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(5f, 260f, 210f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(-102.5f, 0f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(5f, 260f, 210f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 127.5f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(210f, 5f, 210f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, -127.5f, 0f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(210f, 5f, 210f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 0f, 102.5f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(210f, 260f, 5f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f), new BoxColliderDescription(new Vector3(0f, 0f, -102.5f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(210f, 260f, 5f), 0, SurfaceType.Hole, HitType.Default, 1f, 1f) }, new[] { new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-385.17f, 19.965f, -454.68f), new Quaternion(0f, 0.1736425f, 0f, 0.9848088f)), new OrientedPoint(new Vector3(-326.3901f, 108.675f, -428.4998f), new Quaternion(0f, 0.7071079f, 0f, -0.7071056f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-382.1201f, 19.965f, -455.7999f), new Quaternion(0f, 0.1736425f, 0f, 0.9848088f)), new OrientedPoint(new Vector3(-326.3902f, 108.675f, -401.2997f), new Quaternion(0f, 0.7071079f, 0f, -0.7071056f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-388.2501f, 19.965f, -453.56f), new Quaternion(0f, 0.1736425f, 0f, 0.9848088f)), new OrientedPoint(new Vector3(-326.3902f, 108.675f, -415.8997f), new Quaternion(0f, 0.7071079f, 0f, -0.7071056f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-382.649f, 18.905f, -428.2985f), new Quaternion(0f, 0.3148066f, 0f, 0.9491559f)), new OrientedPoint(new Vector3(-327.08f, 108.675f, -477.6997f), new Quaternion(0f, 0.7071072f, 0f, -0.7071064f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-380.0301f, 18.905f, -430.2677f), new Quaternion(0f, 0.3148066f, 0f, 0.9491559f)), new OrientedPoint(new Vector3(-327.08f, 108.675f, -450.4997f), new Quaternion(0f, 0.7071072f, 0f, -0.7071064f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-385.268f, 18.905f, -426.3292f), new Quaternion(0f, 0.3148066f, 0f, 0.9491559f)), new OrientedPoint(new Vector3(-327.08f, 108.675f, -465.0997f), new Quaternion(0f, 0.7071072f, 0f, -0.7071064f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-363.5601f, 18.425f, -432.8298f), new Quaternion(0f, -0.1686384f, 0f, -0.985678f)), new OrientedPoint(new Vector3(-377.7299f, 108.675f, -491.1999f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-360.4901f, 18.425f, -433.8898f), new Quaternion(0f, -0.1686384f, 0f, -0.985678f)), new OrientedPoint(new Vector3(-350.5299f, 108.675f, -491.1998f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-366.6301f, 18.425f, -431.7698f), new Quaternion(0f, -0.1686384f, 0f, -0.985678f)), new OrientedPoint(new Vector3(-365.1299f, 108.675f, -491.1998f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-396.2101f, 19.915f, -439.4299f), new Quaternion(0f, 0.290782f, 0f, 0.9567894f)), new OrientedPoint(new Vector3(-420.6899f, 108.675f, -491.2f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-393.5101f, 19.915f, -441.2399f), new Quaternion(0f, 0.290782f, 0f, 0.9567894f)), new OrientedPoint(new Vector3(-393.4899f, 108.675f, -491.1999f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-398.9301f, 19.915f, -437.5999f), new Quaternion(0f, 0.290782f, 0f, 0.9567894f)), new OrientedPoint(new Vector3(-408.0899f, 108.675f, -491.1999f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-367.5801f, 18.975f, -447.5497f), new Quaternion(0f, 0.1623465f, 0f, 0.9867339f)), new OrientedPoint(new Vector3(-470.1999f, 108.675f, -491.2001f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-364.5101f, 18.975f, -448.5497f), new Quaternion(0f, 0.1623465f, 0f, 0.9867339f)), new OrientedPoint(new Vector3(-442.9999f, 108.675f, -491.2f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))), new LocationBorderEnterPoint(new OrientedPoint(new Vector3(-370.7301f, 18.975f, -446.4898f), new Quaternion(0f, 0.1623465f, 0f, 0.9867339f)), new OrientedPoint(new Vector3(-457.5999f, 108.675f, -491.2001f), new Quaternion(0f, 1.66893E-06f, 0f, -1f))) }), new Vector3(0f, 0f, 0f));
        var settlementCashDescription = new LocationSettlementCashDescription(321, 322, 323, new Vector3(68.3153f, 10.168f, -5.095f), new Vector3(69.7025f, 10.419f, -7.4565f), new Vector3(69.7025f, 10.419f, -7.4565f), null, new HashSet<PhysicsBodyId>() {new PhysicsBodyId(3978, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3879, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4042, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4001, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3883, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3881, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3889, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3944, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3893, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3953, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3928, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3925, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3948, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3943, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3952, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3951, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3942, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3847, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3854, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3887, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3823, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4006, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4004, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4003, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3990, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4027, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3971, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3968, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3919, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3979, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3975, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4033, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4034, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3945, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3935, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3929, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3921, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3930, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3924, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3965, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3954, PhysicsBodyIdType.BakedStatic)}, new HashSet<PhysicsBodyId>() {new PhysicsBodyId(3978, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3879, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4042, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4001, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3883, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3881, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3889, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3944, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3893, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3953, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3928, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3925, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3948, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3943, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3952, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3951, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3942, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3847, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3854, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3887, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3823, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4006, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4004, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4003, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3990, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4027, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3971, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3968, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3919, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3979, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3975, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4033, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(4034, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3945, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3935, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3929, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3921, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3930, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3924, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3965, PhysicsBodyIdType.BakedStatic),new PhysicsBodyId(3954, PhysicsBodyIdType.BakedStatic)});
        var tattooUnlockDescriptions = new List<LocationTattooUnlockDescription>()
        {
        };
        var prisonEscapes = new List<LocationPrisonEscapeDescription>()
        {
        };
        var locationBreakableColliderSets = new List<LocationBreakableColliderSetDescription>()
        {
            new LocationBreakableColliderSetDescription(breakableColliderSet3, new AABB(new Vector3(245.38f, 15.4949f, 413.5f), new Vector3(358.8f, 37.2422f, 536.67f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet4, new AABB(new Vector3(260.34f, 13.8387f, 385.98f), new Vector3(360.34f, 33.8387f, 485.98f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet5, new AABB(new Vector3(364.68f, 20.9063f, -361.82f), new Vector3(490.31f, 40.9109f, -253.59f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet6, new AABB(new Vector3(-399.696f, 5.97f, -467.087f), new Vector3(-282.5341f, 27.04f, -329.9055f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet7, new AABB(new Vector3(421.01f, 14.0572f, -23.193f), new Vector3(570.02f, 36.2847f, 92.646f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet8, new AABB(new Vector3(60.33f, 12.5606f, -518.32f), new Vector3(163.21f, 33.0676f, -368.9f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet9, new AABB(new Vector3(234.7831f, 14.013f, 329.9735f), new Vector3(334.7831f, 34.013f, 429.9735f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet10, new AABB(new Vector3(385.0233f, 12.6119f, -34.9681f), new Vector3(516.41f, 33.348f, 87.02f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet11, new AABB(new Vector3(278.38f, 25.1035f, -371.22f), new Vector3(406.91f, 47.683f, -226.65f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet12, new AABB(new Vector3(50.0625f, 11.7887f, -462.8f), new Vector3(199.76f, 36.1588f, -357.1598f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet13, new AABB(new Vector3(-218.31f, 2.916f, 421.783f), new Vector3(-101.31f, 23.5884f, 550.91f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet14, new AABB(new Vector3(359.96f, 22.3207f, -100.15f), new Vector3(465.34f, 45.5637f, 15.77f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet15, new AABB(new Vector3(-52.12f, 9.1839f, 385.49f), new Vector3(64f, 32.127f, 499.2f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet16, new AABB(new Vector3(-218.11f, 2.972f, 346.956f), new Vector3(-97.602f, 23.84f, 494.928f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet17, new AABB(new Vector3(-87.67f, 7.3073f, 386.73f), new Vector3(12.33f, 27.3073f, 486.73f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet18, new AABB(new Vector3(-218.0169f, 2.6888f, 302.99f), new Vector3(-100.44f, 23.4539f, 444.8499f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet19, new AABB(new Vector3(-193.26f, 2.0427f, 278.94f), new Vector3(-93.26f, 22.0427f, 378.94f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet20, new AABB(new Vector3(303.81f, 11.4177f, -30.79f), new Vector3(441.28f, 32.4066f, 85.67f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet21, new AABB(new Vector3(279.31f, 10.6916f, 26.23f), new Vector3(400.03f, 32.3123f, 167.4351f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet22, new AABB(new Vector3(252.33f, 10.5311f, -30.665f), new Vector3(387.5006f, 31.535f, 89.9573f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet23, new AABB(new Vector3(-208.563f, 0.8045f, 175.507f), new Vector3(-76.792f, 21.878f, 316.75f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet24, new AABB(new Vector3(-274.718f, 2.2608f, 104.002f), new Vector3(-141.16f, 23.887f, 245.64f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet25, new AABB(new Vector3(-222.63f, 2.0605f, 141.09f), new Vector3(-122.63f, 22.0605f, 241.09f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet26, new AABB(new Vector3(207.8f, 5.6797f, -69.775f), new Vector3(328.486f, 28.7062f, 74.989f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet27, new AABB(new Vector3(-294.91f, 2.7608f, 6.41f), new Vector3(-194.91f, 22.7608f, 106.41f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet28, new AABB(new Vector3(-347.2f, 4.7616f, -349.06f), new Vector3(-200.76f, 26.4642f, -215.98f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet29, new AABB(new Vector3(14.5f, 10.9462f, -447.6f), new Vector3(114.5f, 30.9462f, -347.6f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet30, new AABB(new Vector3(-295.08f, 4.6464f, -310.23f), new Vector3(-189.72f, 24.7163f, -204.88f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet31, new AABB(new Vector3(185.3997f, 5.9003f, -102.0897f), new Vector3(287.681f, 26.1516f, 16.781f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet32, new AABB(new Vector3(-291.78f, 2.2301f, -193.956f), new Vector3(-169.88f, 23.3948f, -48.3f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet33, new AABB(new Vector3(169.56f, 6.1394f, -179.89f), new Vector3(285.47f, 26.438f, -41.5441f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet34, new AABB(new Vector3(-10.99f, -0.99f, 11.64f), new Vector3(127.4632f, 19.478f, 134.64f)), new List<LocationHideObjectDescription>() { hideObject5, hideObject6, hideObject16, hideObject17, hideObject18, hideObject19, hideObject23, hideObject24 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet35, new AABB(new Vector3(60.1265f, -0.61f, -22.8443f), new Vector3(160.1265f, 19.39f, 77.1557f)), new List<LocationHideObjectDescription>() { hideObject12, hideObject13, hideObject14, hideObject15, hideObject20, hideObject21, hideObject22, hideObject23, hideObject24 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet36, new AABB(new Vector3(113.65f, 3.0895f, -250.8f), new Vector3(242.97f, 24.3922f, -111.722f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet37, new AABB(new Vector3(39.95f, 2.7829f, -295.04f), new Vector3(188.48f, 24.9546f, -163.77f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet38, new AABB(new Vector3(52.08f, -0.892f, -162.74f), new Vector3(195.87f, 20.305f, -15.0694f)), new List<LocationHideObjectDescription>() { hideObject3, hideObject4, hideObject9, hideObject10, hideObject11, hideObject12, hideObject13 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet39, new AABB(new Vector3(-18.5394f, 5.2469f, -309.8329f), new Vector3(122.207f, 25.581f, -199.477f)), new List<LocationHideObjectDescription>() { hideObject2 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet40, new AABB(new Vector3(37.92f, 4.9042f, -278.58f), new Vector3(137.92f, 24.9042f, -178.58f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet41, new AABB(new Vector3(66.09f, -0.549f, -106.83f), new Vector3(166.09f, 19.451f, -6.83f)), new List<LocationHideObjectDescription>() { hideObject9, hideObject11, hideObject12, hideObject13 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet42, new AABB(new Vector3(-84.6423f, -0.59f, -54.65f), new Vector3(59.12f, 19.539f, 94.2721f)), new List<LocationHideObjectDescription>() { hideObject3, hideObject4, hideObject5, hideObject6, hideObject7, hideObject8, hideObject16, hideObject17, hideObject18, hideObject19 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet43, new AABB(new Vector3(-8.52f, -0.401f, -205.56f), new Vector3(135.68f, 21.818f, -65.37f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet44, new AABB(new Vector3(-39.4639f, -0.585f, -127.6f), new Vector3(105.2861f, 19.575f, 17.89f)), new List<LocationHideObjectDescription>() { hideObject3, hideObject4, hideObject5, hideObject7, hideObject8, hideObject9, hideObject10, hideObject11, hideObject12, hideObject13, hideObject14, hideObject15, hideObject20, hideObject21, hideObject22 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet45, new AABB(new Vector3(-140.5452f, -0.486f, -82.7829f), new Vector3(9.2861f, 20.917f, 59.4902f)), new List<LocationHideObjectDescription>() { hideObject5, hideObject6 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet46, new AABB(new Vector3(-69.7139f, -0.467f, -154.35f), new Vector3(74.8f, 22.414f, -5.0106f)), new List<LocationHideObjectDescription>() { hideObject3, hideObject4, hideObject7, hideObject8, hideObject9, hideObject10, hideObject11 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet47, new AABB(new Vector3(-50.79f, 0.858f, -214.8f), new Vector3(74.4f, 22.999f, -77.8331f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet48, new AABB(new Vector3(-173.3923f, -0.523f, -137.2279f), new Vector3(-40.2952f, 19.508f, -2.4645f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet49, new AABB(new Vector3(-90.49f, -0.383f, -133.77f), new Vector3(9.51f, 19.617f, -33.77f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet50, new AABB(new Vector3(-126.9f, 1.09f, -234.19f), new Vector3(15.67f, 24.136f, -89.56f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet51, new AABB(new Vector3(-140.56f, -0.573f, -167.4797f), new Vector3(-40.56f, 19.427f, -67.4797f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet52, new AABB(new Vector3(-182.99f, 5.9351f, -301.61f), new Vector3(-35.5f, 26.1672f, -160.6171f)), new List<LocationHideObjectDescription>() { hideObject1 }),
            new LocationBreakableColliderSetDescription(breakableColliderSet53, new AABB(new Vector3(-229.2065f, -0.508f, -194.9863f), new Vector3(-94.3924f, 20.13f, -60.2279f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet54, new AABB(new Vector3(-235.9192f, 2.426f, -252.6906f), new Vector3(-94.4f, 24.97f, -116.0999f)), new List<LocationHideObjectDescription>() {  }),
            new LocationBreakableColliderSetDescription(breakableColliderSet55, new AABB(new Vector3(-205.31f, 4.271f, -285.04f), new Vector3(-55.4152f, 25.9147f, -139.5693f)), new List<LocationHideObjectDescription>() {  }),
        };
        var cashLootNodes = new Enum<LocationCashLootNodeDescription>()
        {
        };
        var cargoDeliveryPoints = new Enum<LocationCargoDeliveryPointDescription>()
        {
        };
        var locationEventCargos = new Enum<LocationEventCargoSpawnPointDescription>()
        {
        };
        var entityId = 330;
        return (entityId, containers,  doors, kinematicObjectsControllers, locationBreakableCollidersSets, alarms, securityPanels, buildings, metalDetectors, hideObjects, npcDescriptions, switches, triggers, craftWorkbenchDescription, cargoSellerDescriptions, bollards, clanBattleConquestObjects, sectorLocationClanBattleConquestObject, locationCargoExchangerDescriptions, settlementCashDescription, tattooUnlockDescriptions, prisonEscapes, locationBreakableColliderSets, cashLootNodes, cargoDeliveryPoints, locationEventCargos);
    }
}
}
