using System;
using System.Collections.Generic;
using System.Numerics;
using Framework.Replication.Enum;
using Framework.Replication.EnumItem;
using Models.HitBox.Types;
using Models.MathUtils;
using Models.Physics.Shapes.Data.Hull;
using Models.Physics.Types;
using Models.References.Colliders;
using Models.References.Inventory;
using Models.References.Physics;

namespace Models.References.HangarStorage
{
    public class StorageDescription : EnumItem
    {
        private static readonly Enum<StorageDescription> _enum = new();
        public static IEnum<StorageDescription> Enum => _enum;

        private readonly Func<InventoryItemDescription> _inventoryItem;

        private StorageDescription(string id, CollidersDescription colliders, IReadOnlyList<OBB> obbs, Func<InventoryItemDescription> inventoryItem, InventoryCellsSizeDescription inventoryCellsSizeDescription, Vector3 centerPoint, long breachTime, Vector3 lootSpawnPoint) : base(id, _enum.Count)
        {
            Colliders = colliders;
            Obbs = obbs;
            _inventoryItem = inventoryItem;
            InventoryCellsSizeDescription = inventoryCellsSizeDescription;
            CenterPoint = centerPoint;
            BreachTime = breachTime;
            LootSpawnPoint = lootSpawnPoint;
            _enum.Add(this);
        }
        
        public CollidersDescription Colliders { get; }
        public IReadOnlyList<OBB> Obbs { get; }
        public InventoryItemDescription GetInventoryItem() => _inventoryItem();
        public InventoryCellsSizeDescription InventoryCellsSizeDescription { get; }
        public Vector3 CenterPoint { get; }
        public long BreachTime { get; }
        public Vector3 LootSpawnPoint { get; }

        public static StorageDescription StorageSmall { get; } = new("small",
            colliders: new CollidersDescription(
                boxes: new []
                {
                    new BoxColliderDescription(new Vector3(0f, 0.457f, .016f), Quaternion.Identity, new Vector3(.95f, .913f, .62f), 0, SurfaceType.Metal, HitType.Default, PhysicsMaterialSettings.GeometryDefaultRestitution, PhysicsMaterialSettings.GeometryDefaultFriction)
                },
                triangles: Array.Empty<TriangleColliderDescription>(),
                capsules: Array.Empty<CapsuleColliderDescription>(),
                hulls: Array.Empty<HullColliderDescription>(),
                hullData: Array.Empty<HullShapeData>()),
            obbs: new OBB[]
            {
                new OBB(new Vector3(0f, 0.457f, .016f), new Vector3(.475f, .456f, .31f), Quaternion.Identity)
            },
            inventoryItem: () => InventoryItemDescription.StorageSmall,
            inventoryCellsSizeDescription: InventoryCellsSizeDescription.StorageSmall,
            centerPoint: new Vector3(0f, 0.457f, .016f),
            breachTime: 1000,
            lootSpawnPoint: new Vector3(0f, 0f, 1.5f));

        public static StorageDescription StorageMedium { get; } = new("medium",
            colliders: new CollidersDescription(
                boxes: new []
                {
                    new BoxColliderDescription(new Vector3(0f, 0.772f, .016f), Quaternion.Identity, new Vector3(.95f, 1.544f, .62f), 0, SurfaceType.Metal, HitType.Default, PhysicsMaterialSettings.GeometryDefaultRestitution, PhysicsMaterialSettings.GeometryDefaultFriction)
                },
                triangles: Array.Empty<TriangleColliderDescription>(),
                capsules: Array.Empty<CapsuleColliderDescription>(),
                hulls: Array.Empty<HullColliderDescription>(),
                hullData: Array.Empty<HullShapeData>()),
            obbs: new OBB[]
            {
                new OBB(new Vector3(0f, 0.772f, .016f), new Vector3(.475f, .772f, .31f), Quaternion.Identity)
            },
            inventoryItem: () => InventoryItemDescription.StorageMedium,
            inventoryCellsSizeDescription: InventoryCellsSizeDescription.StorageMedium,
            centerPoint: new Vector3(0f, 0.457f, .016f),
            breachTime: 1000,
            lootSpawnPoint: new Vector3(0f, 0f, 1.5f));

        public static StorageDescription StorageBig { get; } = new("big",
            colliders: new CollidersDescription(
                boxes: new []
                {
                    new BoxColliderDescription(new Vector3(0f, 0.887f, .016f), Quaternion.Identity, new Vector3(.95f, 1.775f, .62f), 0, SurfaceType.Metal, HitType.Default, PhysicsMaterialSettings.GeometryDefaultRestitution, PhysicsMaterialSettings.GeometryDefaultFriction)
                },
                triangles: Array.Empty<TriangleColliderDescription>(),
                capsules: Array.Empty<CapsuleColliderDescription>(),
                hulls: Array.Empty<HullColliderDescription>(),
                hullData: Array.Empty<HullShapeData>()),
            obbs: new OBB[]
            {
                new OBB(new Vector3(0f, 0.887f, .016f), new Vector3(.475f, .887f, .31f), Quaternion.Identity)
            },
            inventoryItem: () => InventoryItemDescription.StorageBig,
            inventoryCellsSizeDescription: InventoryCellsSizeDescription.StorageBig,
            centerPoint: new Vector3(0f, 0.887f, .016f),
            breachTime: 1000,
            lootSpawnPoint: new Vector3(0f, 0f, 1.5f)
		);
    }
}