using Framework.Core.Identified;

namespace Models.References.PlotBuildingDefenseBlock
{
    public class PlotBuildingRaidStateDescription : Identified 
    {
        private PlotBuildingRaidStateDescription(string id) : base(id) { }

        public static readonly PlotBuildingRaidStateDescription Started = new("started");
        public static readonly PlotBuildingRaidStateDescription InProgress = new("in_progress");
        public static readonly PlotBuildingRaidStateDescription Finished = new("finished");
    }
}