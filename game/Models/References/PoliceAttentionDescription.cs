using System.Collections.Generic;
using Models.References.PoliceAttention.ValueByPartySize;

namespace Models.References
{
    public static class PoliceAttentionDescription
    {
        public static int ShotPoints = 1;
        public static int ExplosionFragGrenade = 250;
        public static IValueByPartySize<int> StealItemPoints = new ValueByPartySize<int>(new[] { 4, 2, 2, 2 });
        public static IValueByPartySize<int> UnlockObjectPoints = new ValueByPartySize<int>(new[] { 90, 45, 30, 15 });
        public static IValueByPartySize<int> LootContainerPoints = new ValueByPartySize<int>(new[] { 10, 5, 3, 2 });
        public static IValueByPartySize<int> LootNodePoints = new ValueByPartySize<int>(new[] { 10, 5, 3, 2 });
        public static IValueByPartySize<int> HackSecurityPanel = new ValueByPartySize<int>(new[] { 90, 45, 30, 15 });
        public static IValueByPartySize<int> BreakCameraPoints = new ValueByPartySize<int>(new[] { 60, 30, 20, 6 });
        public static IValueByPartySize<int> InterruptArrestingPoints = new ValueByPartySize<int>(new[] { 100, 50, 33, 5 });

        public static readonly IReadOnlyDictionary<PlayerRoleDescription, IValueByPartySize<int>> DamagePointsByRoles = new Dictionary<PlayerRoleDescription, IValueByPartySize<int>>()
        {
            [PlayerRoleDescription.Citizen] = new ValueByPartySize<int>(new[] { 20, 20, 20, 20 }),
            [PlayerRoleDescription.Robber] = new ValueByPartySize<int>(new[] { 0, 0, 0, 0 }),
            [PlayerRoleDescription.Cop] = new ValueByPartySize<int>(new[] { 7, 4, 2, 2 }),
        };
    
        public static readonly IReadOnlyDictionary<PlayerRoleDescription, IValueByPartySize<int>> DamagePointsByRolesKnockedOut = new Dictionary<PlayerRoleDescription, IValueByPartySize<int>>()
        {
            [PlayerRoleDescription.Citizen] = new ValueByPartySize<int>(new[] { 0, 0, 0, 0 }),
            [PlayerRoleDescription.Robber] = new ValueByPartySize<int>(new[] { 0, 0, 0, 0 }),
            [PlayerRoleDescription.Cop] = new ValueByPartySize<int>(new[] { 0, 0, 0, 0 }),
        };

        public static readonly IReadOnlyDictionary<PlayerRoleDescription, IValueByPartySize<int>> KillPointsByRoles = new Dictionary<PlayerRoleDescription, IValueByPartySize<int>>()
        {
            [PlayerRoleDescription.Citizen] = new ValueByPartySize<int>(new[] { 1600, 1600, 1600, 1600 }),
            [PlayerRoleDescription.Robber] = new ValueByPartySize<int>(new[] { 50, 25, 17, 12 }),
            [PlayerRoleDescription.Cop] = new ValueByPartySize<int>(new[] { 500, 250, 165, 125 }),
        };
    
        public static readonly IReadOnlyDictionary<PlayerRoleDescription, IValueByPartySize<int>> KnockoutPointsByRoles = new Dictionary<PlayerRoleDescription, IValueByPartySize<int>>()
        {
            [PlayerRoleDescription.Citizen] = new ValueByPartySize<int>(new[] { 1600, 1600, 1600, 1600 }),
            [PlayerRoleDescription.Robber] = new ValueByPartySize<int>(new[] { 50, 25, 17, 12 }),
            [PlayerRoleDescription.Cop] = new ValueByPartySize<int>(new[] { 500, 250, 165, 125 }),
        };

        public static readonly IValueByPartySize<int> KillVigilantNpcPoints = new ValueByPartySize<int>(new[] { 2500, 2500, 2500, 2500 });
        public static readonly IValueByPartySize<int> MeleeHitVigilantNpcPoints = new ValueByPartySize<int>(new[] { 50, 50, 50, 50 });
        public static readonly IValueByPartySize<int> BreakColliderPoints =  new ValueByPartySize<int>(new[] { 30, 15, 10, 7 });
    }
}