using System.Collections.Generic;
using Models.References.Location;

namespace Models.References
{
    public static class BattleSettings
    {
        private static readonly Dictionary<BattleModeDescription, Dictionary<LocationDescription, BattleSettingsDescription>> _battleSettingsByMode = new()
        {
            {BattleModeDescription.Settlement, new Dictionary<LocationDescription, BattleSettingsDescription>()
            {
                {LocationDescription.Settlement, BattleSettingsDescription.Settlement},
            }},
            {BattleModeDescription.Shooter, new Dictionary<LocationDescription, BattleSettingsDescription>()
            {
                {LocationDescription.Hub, BattleSettingsDescription.HubShooter},
                {LocationDescription.City, BattleSettingsDescription.CityShooter},
                {LocationDescription.Test, BattleSettingsDescription.TestShooter},
                {LocationDescription.Blocking, BattleSettingsDescription.CityShooter}
            }},
            {BattleModeDescription.ClanBattle, new Dictionary<LocationDescription, BattleSettingsDescription>()
            {
                {LocationDescription.Settlement, BattleSettingsDescription.SettlementClanBattle},
                {LocationDescription.City, BattleSettingsDescription.CityClanBattle},
                {LocationDescription.Hub, BattleSettingsDescription.HubClanBattle},
            }},
            {BattleModeDescription.Prison, new Dictionary<LocationDescription, BattleSettingsDescription>()
            {
                {LocationDescription.Prison, BattleSettingsDescription.Prison},
            }},
        };

        public static bool TryGetBattleSettings(BattleModeDescription battleMode, LocationDescription location, out BattleSettingsDescription battleSettings)
        {
            if (_battleSettingsByMode.TryGetValue(battleMode, out var battleSettingsByLocation) &&
                battleSettingsByLocation.TryGetValue(location, out battleSettings))
            {
                return true;
            }

            battleSettings = null;
            return false;
        }
    }
}