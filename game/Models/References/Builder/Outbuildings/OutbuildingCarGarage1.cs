using System.Collections.Generic;
using System.Numerics;
using Models.References.Builder.Colliders;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.Inventory;
using Models.References.Location;
using Models.References.Plot;
using Models.References.Door;
using Models.References.KinematicObject;
using Models.References.Physics;
using Models.References.Switch;
using Models.References.Trigger;
using Models.MathUtils;
using Models.Physics.Types;
using Models.HitBox.Types;
using System;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.References.Colliders;
#if UNITY_MATH
using Unity.Collections;
using Unity.Mathematics;
#endif

namespace Models.References.Builder
{
/// <auto-generated>This is autogenerated class from <see cref="PlotOutbuildingSchemeDescriptionFile"/></auto-generated>
public partial class OutbuildingDescription
{
    public static OutbuildingDescription CarGarage1 { get; } = new OutbuildingDescription(
        id: "car_garage_1",
        colliders: OutbuildingCarGarage1Colliders.Main,
        obbs: new OBB[]{ new OBB(new Vector3(0.0927f, 2.6763f, 0.3274f), new Vector3(6.9627f, 3.1781f, 5.8781f), new Quaternion(0f, 0f, 0f, 1f)) },
        inventoryItem: () => InventoryItemDescription.CarGarage1,
        furnitureSlotsInteractPosition: new Vector3(6.4561f, 0.1216f, 1.7078f),
        carGarage: new PlotGarageDescription<PlotCarSlotDescription>(new Vector3(2.5021f, 1.791f, -4.2551f), new Dictionary<PlotCarSlotDescription, PlotGarageSlotDescription>{ { PlotCarSlotDescription.Slot0, new PlotGarageSlotDescription(new Vector3(2.6159f, 0.1115f, 0.8025f), new Quaternion(0f, 1.400709E-06f, 0f, 1f), 0f) },{ PlotCarSlotDescription.Slot1, new PlotGarageSlotDescription(new Vector3(-1.2981f, 0.1116f, 0.8015f), new Quaternion(0f, 1.400709E-06f, 0f, 1f), 0f) } }),
        helicopterGarage: null,
        storageSlots: new Dictionary<StorageSlotDescription, PlotConstructionSlotDescription>()
        {
            {StorageSlotDescription.Slot0, new PlotConstructionSlotDescription(new Vector3(6.4331f, 0.1635f, 3.5278f), new Quaternion(0f, -0.7071058f, 0f, 0.7071078f))},
        },
        shelvingSlots: new Dictionary<ShelvingSlotDescription, PlotConstructionSlotDescription>()
        {
            {ShelvingSlotDescription.Slot0, new PlotConstructionSlotDescription(new Vector3(6.0621f, 0.125f, -0.6722f), new Quaternion(0f, -0.7071068f, 0f, 0.7071068f))},
            {ShelvingSlotDescription.Slot1, new PlotConstructionSlotDescription(new Vector3(6.0491f, 0.127f, -2.8122f), new Quaternion(0f, -0.7071068f, 0f, 0.7071068f))},
        },
        doors: new Dictionary<OutbuildingDoorSlotDescription, PlotDoorDescription>()
        {
            {OutbuildingDoorSlotDescription.Slot0, new PlotDoorDescription(DoorDescription.DoorSingleMetal_02, new Vector3(4.3754f, 0.0814f, -4.3949f), new Vector3(4.3754f, 1.4514f, -4.3949f), new Quaternion(0f, -1f, 0f, 1.788139E-07f), new OBB[] {  }, 3)},
        },
        kinematicObjectsControllers: new KinematicObjectsControllerDescription[]
        {
            new KinematicObjectsControllerDescription(
				new KinematicObjectDescription[] {
						new KinematicObjectDescription(
						new CollidersDescription(
							boxes: new BoxColliderDescription[] {
								new BoxColliderDescription(new Vector3(0.3249f, 0.2f, -1.93f), new Quaternion(0f, -8.869998E-14f, 0f, 1f), new Vector3(9.3998f, 3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f)
							}, 
							triangles: Array.Empty<TriangleColliderDescription>(), 
							capsules: Array.Empty<CapsuleColliderDescription>(), 
							hulls: Array.Empty<HullColliderDescription>(), 
							hullData: Array.Empty<HullShapeData>()))
					}, 
				new KinematicObjectMovementDescription[] {
						new KinematicObjectMovementDescription(new Vector3(0.8504f, 1.4285f, 3.3421f), new Quaternion(0f, 1f, 0f, -1.698733E-06f), new Vector3(0.8504f, 1.4285f, 3.3421f), new Quaternion(-1.034123E-06f, 0.7933533f, -0.6087614f, -1.347695E-06f))
					}, 
				3000, 500, KinematicObjectMovementStateDescription.Point1, false, 
				new OBB[] { new OBB(new Vector3(0.488f, 1.8223f, 5.2621f), new Vector3(6.0277f, 2.6189f, 2.5f), new Quaternion(0f, 1f, 0f, -1.66893E-06f)) }, 
				new OBB[] { new OBB(new Vector3(0.5255f, 1.6942f, 0.8249f), new Vector3(5.95f, 2.7657f, 4.9878f), new Quaternion(0f, 1f, 0f, -1.66893E-06f)), new OBB(new Vector3(0.5255f, 1.4824f, 13.101f), new Vector3(12.5f, 2.9775f, 7.4978f), new Quaternion(0f, 1f, 0f, -1.66893E-06f)) }, 
				new HashSet<KinematicObjectMovementStateDescription> { KinematicObjectMovementStateDescription.Point1 }),
        },
        defenseSettings: PlotBuildingDefenseSettingsDescription.CarGarage1,
        defenseInteractionPoint: new Vector3(-0.3439f, 1.361f, -4.6571f)
    );
}
}
