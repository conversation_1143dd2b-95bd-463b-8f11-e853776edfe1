using System.Collections.Generic;
using System.Numerics;
using Models.References.Builder.Colliders;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.Inventory;
using Models.References.Location;
using Models.References.Plot;
using Models.References.Door;
using Models.References.KinematicObject;
using Models.References.Physics;
using Models.References.Switch;
using Models.References.Trigger;
using Models.MathUtils;
using Models.Physics.Types;
using Models.HitBox.Types;
using System;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.References.Colliders;
#if UNITY_MATH
using Unity.Collections;
using Unity.Mathematics;
#endif

namespace Models.References.Builder
{
/// <auto-generated>This is autogenerated class from <see cref="PlotOutbuildingSchemeDescriptionFile"/></auto-generated>
public partial class OutbuildingDescription
{
    public static OutbuildingDescription CarGarage0 { get; } = new OutbuildingDescription(
        id: "car_garage_0",
        colliders: OutbuildingCarGarage0Colliders.Main,
        obbs: new OBB[]{ new OBB(new Vector3(-0.4678f, 2.6763f, 0.547f), new Vector3(4.7478f, 3.1781f, 5.8325f), new Quaternion(0f, 0f, 0f, 1f)) },
        inventoryItem: () => InventoryItemDescription.CarGarage0,
        furnitureSlotsInteractPosition: new Vector3(3.4097f, 0.1235f, 1.1468f),
        carGarage: new PlotGarageDescription<PlotCarSlotDescription>(new Vector3(-0.6243f, 1.774f, -3.9762f), new Dictionary<PlotCarSlotDescription, PlotGarageSlotDescription>{ { PlotCarSlotDescription.Slot0, new PlotGarageSlotDescription(new Vector3(-0.3415f, 0.1116f, 0.9334f), new Quaternion(0f, -5.960464E-08f, 0f, 1f), 0f) } }),
        helicopterGarage: null,
        storageSlots: new Dictionary<StorageSlotDescription, PlotConstructionSlotDescription>()
        {
            {StorageSlotDescription.Slot0, new PlotConstructionSlotDescription(new Vector3(3.3597f, 0.1635f, 3.5308f), new Quaternion(0f, -0.7071068f, 0f, 0.7071068f))},
        },
        shelvingSlots: new Dictionary<ShelvingSlotDescription, PlotConstructionSlotDescription>()
        {
            {ShelvingSlotDescription.Slot0, new PlotConstructionSlotDescription(new Vector3(2.9597f, 0.128f, -1.1832f), new Quaternion(0f, -0.7071068f, 0f, 0.7071068f))},
        },
        doors: new Dictionary<OutbuildingDoorSlotDescription, PlotDoorDescription>()
        {
            {OutbuildingDoorSlotDescription.Slot0, new PlotDoorDescription(DoorDescription.DoorSingleMetal_02, new Vector3(1.5462f, 0.13f, -4.1153f), new Vector3(1.5462f, 1.5f, -4.1153f), new Quaternion(0f, -1f, 0f, 2.086163E-07f), new OBB[] {  }, 3)},
        },
        kinematicObjectsControllers: new KinematicObjectsControllerDescription[]
        {
            new KinematicObjectsControllerDescription(
				new KinematicObjectDescription[] {
						new KinematicObjectDescription(
						new CollidersDescription(
							boxes: new BoxColliderDescription[] {
								new BoxColliderDescription(new Vector3(0f, 0.2f, -1.5455f), new Quaternion(0f, 7.993606E-14f, 0f, 1f), new Vector3(5.5f, 3f, 0.1f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f)
							}, 
							triangles: Array.Empty<TriangleColliderDescription>(), 
							capsules: Array.Empty<CapsuleColliderDescription>(), 
							hulls: Array.Empty<HullColliderDescription>(), 
							hullData: Array.Empty<HullShapeData>()))
					}, 
				new KinematicObjectMovementDescription[] {
						new KinematicObjectMovementDescription(new Vector3(-0.3555f, 1.4601f, 4.033f), new Quaternion(0f, 1f, 0f, -5.751848E-06f), new Vector3(-0.3555f, 1.8601f, 4.033f), new Quaternion(-3.501503E-06f, 0.7933533f, -0.6087614f, -4.563248E-06f))
					}, 
				3000, 500, KinematicObjectMovementStateDescription.Point1, false, 
				new OBB[] { new OBB(new Vector3(-0.3931f, 1.4601f, 5.593f), new Vector3(3.8684f, 4f, 2.5f), new Quaternion(0f, -1f, 0f, 5.692244E-06f)) }, 
				new OBB[] { new OBB(new Vector3(-0.3557f, 1.4601f, 0.8022f), new Vector3(3.6904f, 2.5f, 4.7782f), new Quaternion(0f, -1f, 0f, 5.692244E-06f)), new OBB(new Vector3(-0.3556f, 1.4601f, 12.9565f), new Vector3(12.5f, 2.5f, 7.4703f), new Quaternion(0f, -1f, 0f, 5.692244E-06f)) }, 
				new HashSet<KinematicObjectMovementStateDescription> { KinematicObjectMovementStateDescription.Point1 }),
        },
        defenseSettings: PlotBuildingDefenseSettingsDescription.CarGarage0,
        defenseInteractionPoint: new Vector3(-0.6403f, 1.657f, -4.3862f)
    );
}
}
