using System;
using System.Numerics;
using Models.Physics.Types;
using Models.HitBox.Types;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.References.Colliders;

#if UNITY_MATH
using Unity.Collections;
using Unity.Mathematics;
#endif

namespace Models.References.Builder.Colliders
{
	/// <auto-generated>This is autogenerated class from <see cref="CollidersFile"/></auto-generated>
	public static class MainBuildingHome3Colliders
	{
		public static readonly CollidersDescription Main = new CollidersDescription(
			boxes: new BoxColliderDescription[] {
				new BoxColliderDescription(new Vector3(-16.6f, 1.9889f, -3.8377f), new Quaternion(-0.7071066f, -2.95028E-07f, -2.950278E-07f, 0.707107f), new Vector3(0.4536f, 0.3657f, 0.028f), RigidBodyType.DisabledShots, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.6f, 2.1473f, -4.0694f), new Quaternion(-0.25882f, -5.222248E-07f, -1.228893E-07f, 0.9659256f), new Vector3(0.4536f, 0.3436f, 0.0209f), RigidBodyType.DisabledShots, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4648f, 1.9524f, -0.699f), new Quaternion(0f, 0.9747819f, 0f, -0.2231596f), new Vector3(0.4879f, 0.2658f, 0.4612f), 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4345f, 1.9525f, -0.083f), new Quaternion(0f, 0.6252205f, 0f, 0.7804482f), new Vector3(0.4879f, 0.2658f, 0.4612f), 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.134f, 1.4401f, -0.4669f), new Quaternion(0f, 0.005538847f, 0f, 0.9999847f), new Vector3(0.3085f, 0.7393f, 0.6602f), RigidBodyType.DisabledCollisions, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5453f, 1.4401f, -0.7646f), new Quaternion(0f, -0.1917905f, 0f, 0.981436f), new Vector3(0.3085f, 0.7393f, 0.6602f), RigidBodyType.DisabledCollisions, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.599f, 1.4401f, -0.0565f), new Quaternion(0f, -0.03641066f, 0f, 0.999337f), new Vector3(0.3085f, 0.7393f, 0.6602f), RigidBodyType.DisabledCollisions, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.5115f, 2.4104f, -4.4759f), new Quaternion(-0.002154957f, 0.7071035f, -0.7071036f, -0.002154957f), new Vector3(0.2254f, 2.78f, 2.3923f), RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact | RigidBodyType.DisabledGrenadeCollisions, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.484f, 1.1888f, -3.1111f), new Quaternion(-0.002154957f, 0.7071036f, -0.7071035f, -0.002154957f), new Vector3(0.8599f, 0.0908f, 0.0511f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.5006f, 1.1888f, -5.841f), new Quaternion(-0.002154957f, 0.7071036f, -0.7071035f, -0.002154957f), new Vector3(0.8599f, 0.0908f, 0.0511f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.4836f, 2.0211f, -3.1111f), new Quaternion(0f, -0.7049485f, 0f, 0.7092585f), new Vector3(0.08f, 1.6263f, 0.08f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.5002f, 2.0211f, -5.841f), new Quaternion(0f, -0.7049485f, 0f, 0.7092585f), new Vector3(0.08f, 1.6263f, 0.08f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.4922f, 1.7347f, -4.476f), new Quaternion(0f, -0.7049485f, 0f, 0.7092585f), new Vector3(2.74f, 0.0623f, 0.0384f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.4922f, 1.2753f, -4.476f), new Quaternion(0f, -0.7049485f, 0f, 0.7092585f), new Vector3(2.74f, 0.0623f, 0.0384f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.4922f, 1.5098f, -4.4792f), new Quaternion(0f, -0.7049485f, 0f, 0.7092585f), new Vector3(2.6583f, 0.33f, 0.0384f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.4922f, 2.6966f, -4.4752f), new Quaternion(0.02203088f, -0.7046084f, 0.02189701f, 0.7089162f), new Vector3(2.6029f, 1.7548f, 0.0384f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.6428f, 1.9784f, 3.4316f), new Quaternion(0.006706044f, 0.7070748f, 0.006706046f, 0.7070752f), new Vector3(0.2f, 3.9602f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.6381f, 2.8771f, 3.7983f), new Quaternion(0.2705979f, 0.6532814f, 0.270598f, 0.6532817f), new Vector3(0.08f, 0.8957f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.8381f, 2.8771f, 3.7983f), new Quaternion(0.2705979f, 0.6532814f, 0.270598f, 0.6532817f), new Vector3(0.08f, 0.8957f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.6381f, 2.8771f, 8.919f), new Quaternion(-0.2705979f, -0.6532814f, 0.2705981f, 0.6532817f), new Vector3(0.08f, 0.8957f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.8381f, 2.8771f, 8.919f), new Quaternion(-0.2705979f, -0.6532814f, 0.2705981f, 0.6532817f), new Vector3(0.08f, 0.8957f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.4735f, 2.8771f, 9.2837f), new Quaternion(0.3826834f, 0.9238796f, -3.166497E-07f, -6.258487E-07f), new Vector3(0.08f, 0.8957f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.0028f, 2.8771f, 3.4336f), new Quaternion(1.411885E-06f, 3.226102E-06f, 0.3826836f, 0.9238795f), new Vector3(0.08f, 0.8957f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.4735f, 2.8771f, 3.4337f), new Quaternion(0.3826834f, 0.9238796f, -3.166497E-07f, -6.258487E-07f), new Vector3(0.08f, 0.8957f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.0028f, 2.8771f, 9.2836f), new Quaternion(1.411885E-06f, 3.226102E-06f, 0.3826836f, 0.9238795f), new Vector3(0.08f, 0.8957f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.8381f, 1.5483f, 9.2836f), new Quaternion(0f, 0.7071066f, 0f, 0.707107f), new Vector3(0.2f, 3.4f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.8381f, 3.1983f, 6.3587f), new Quaternion(-0.4999995f, 0.5000007f, -0.4999992f, 0.5000008f), new Vector3(0.1f, 5.6501f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2381f, 3.1983f, 3.4336f), new Quaternion(-1.251698E-06f, 7.376075E-07f, -0.7071056f, 0.7071079f), new Vector3(0.1f, 7f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.1506f, 3.9102f, 3.4629f), new Quaternion(0f, 0f, -0.7071068f, 0.7071068f), new Vector3(0.1f, 7.19f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.6382f, 3.1983f, 6.3586f), new Quaternion(-0.4999995f, 0.5000007f, -0.4999992f, 0.5000008f), new Vector3(0.1f, 5.65f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.6556f, 3.5634f, 6.5958f), new Quaternion(0.5300747f, 0.4679967f, 0.5300746f, 0.4679968f), new Vector3(0.1f, 6.3437f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.6759f, 3.364f, 8.8381f), new Quaternion(0.04389585f, -0.04389583f, 0.7057431f, 0.7057429f), new Vector3(0.05f, 2.546f, 2.0152f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.6759f, 3.6423f, 6.7836f), new Quaternion(0.03435596f, -0.03435596f, 0.7062718f, 0.7062716f), new Vector3(0.05f, 2.546f, 2.408f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.6534f, 3.9196f, 4.5984f), new Quaternion(0.03435597f, -0.03435596f, 0.7062718f, 0.7062716f), new Vector3(0.05f, 2.546f, 2.5224f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.0783f, 3.3663f, 8.8116f), new Quaternion(0.04389585f, -0.04389583f, 0.7057431f, 0.7057429f), new Vector3(0.05f, 2.348f, 1.9628f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.0784f, 3.6443f, 6.752f), new Quaternion(0.03435596f, -0.03435596f, 0.7062718f, 0.7062716f), new Vector3(0.05f, 2.348f, 2.3454f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.709f, 3.6559f, 6.6288f), new Quaternion(0.03435596f, -0.03435596f, 0.7062718f, 0.7062716f), new Vector3(0.05f, 2.8484f, 2.4404f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.0503f, 3.9206f, 4.5775f), new Quaternion(0.03435596f, -0.03435596f, 0.7062718f, 0.7062716f), new Vector3(0.05f, 2.348f, 2.4381f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.7022f, 3.9289f, 4.4891f), new Quaternion(0.03435596f, -0.03435596f, 0.7062718f, 0.7062716f), new Vector3(0.05f, 2.7964f, 2.2898f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2556f, 3.5634f, 6.5958f), new Quaternion(0.5300747f, 0.4679967f, 0.5300746f, 0.4679968f), new Vector3(0.1f, 6.3437f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.8556f, 3.5634f, 6.5958f), new Quaternion(0.5300747f, 0.4679967f, 0.5300746f, 0.4679968f), new Vector3(0.1f, 6.3437f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.2556f, 3.5634f, 6.5958f), new Quaternion(0.5300747f, 0.4679967f, 0.5300746f, 0.4679968f), new Vector3(0.1f, 6.3437f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2381f, 3.1983f, 9.2836f), new Quaternion(0f, 0f, -0.7071068f, 0.7071068f), new Vector3(0.1f, 7f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.8428f, 2.0022f, 3.4336f), new Quaternion(0f, 0.7071066f, 0f, 0.7071069f), new Vector3(0.2f, 4.0078f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.6163f, 1.5947f, 9.2836f), new Quaternion(0.007387998f, -0.7070664f, 0.007387963f, 0.70707f), new Vector3(0.2f, 3.3089f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4381f, 0.9499f, -4.7642f), new Quaternion(0f, 0.7071066f, 0f, 0.7071069f), new Vector3(15.2f, 0.25f, 5.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4381f, 0.7499f, -4.7642f), new Quaternion(0f, 0.7071066f, 0f, 0.707107f), new Vector3(15f, 0.15f, 5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.2881f, 0.4144f, -2.6443f), new Quaternion(0f, 0.7071066f, 0f, 0.7071071f), new Vector3(5.1996f, 0.821f, 3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1881f, 0.9499f, -2.6943f), new Quaternion(0f, 0.7071066f, 0f, 0.7071069f), new Vector3(5.5f, 0.25f, 3.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.7231f, 0.9894f, -5.4443f), new Quaternion(0f, 0.7071066f, 0f, 0.7071069f), new Vector3(0.07f, 1.971f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.5881f, 0.9894f, -4.4142f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.07f, 1.971f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.5877f, 1.9394f, 0.0562f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.07f, 3.8709f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.3877f, 0.9545f, 0.0562f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.07f, 1.901f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.2055f, 3.8399f, 0.0559f), new Quaternion(-2.980233E-07f, -1.192093E-07f, 0.707107f, 0.7071066f), new Vector3(0.07f, 3.1652f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1813f, 4.0525f, 0.056f), new Quaternion(0f, 0f, 0.6755902f, 0.7372774f), new Vector3(0.07f, 3.2753f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.2662f, 4.0228f, -0.6293f), new Quaternion(-0.5213337f, -0.4777143f, 0.4777145f, 0.521334f), new Vector3(0.03f, 1.9413f, 1.7527f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.8872f, 4.1803f, -0.6293f), new Quaternion(-0.5168856f, -0.4825236f, 0.4825237f, 0.5168859f), new Vector3(0.03f, 1.9413f, 1.7527f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.2611f, 4.0223f, -2.5299f), new Quaternion(-0.515287f, -0.4721735f, 0.4831918f, 0.5273112f), new Vector3(0.03f, 1.7498f, 1.7621f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.8182f, 4.1763f, -2.5254f), new Quaternion(-0.5230975f, -0.488524f, 0.4764451f, 0.5106012f), new Vector3(0.03f, 1.7498f, 1.7621f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.2417f, 4.0206f, -4.5783f), new Quaternion(-0.5213338f, -0.4777142f, 0.4777145f, 0.521334f), new Vector3(0.03f, 2.279f, 1.7974f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.8631f, 4.1783f, -4.5784f), new Quaternion(-0.5168858f, -0.4825236f, 0.4825237f, 0.5168858f), new Vector3(0.03f, 2.279f, 1.7974f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1812f, 4.0525f, -5.4441f), new Quaternion(0f, 0f, 0.6755902f, 0.7372774f), new Vector3(0.07f, 3.2753f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1812f, 4.0525f, -3.4441f), new Quaternion(0f, 0f, 0.6755902f, 0.7372774f), new Vector3(0.07f, 3.2753f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1812f, 4.0525f, -1.6441f), new Quaternion(0f, 0f, 0.6755902f, 0.7372774f), new Vector3(0.07f, 3.2753f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.2055f, 3.8399f, -5.4441f), new Quaternion(-2.980233E-07f, -1.192093E-07f, 0.707107f, 0.7071066f), new Vector3(0.07f, 3.1652f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.5877f, 3.8399f, -2.6938f), new Quaternion(0.5000001f, 0.5000002f, -0.4999999f, -0.4999999f), new Vector3(0.07f, 5.4299f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.388f, 0.5121f, 1.8059f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.07f, 1.0256f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.3877f, 1.4532f, 0.9848f), new Quaternion(0.8598523f, 0f, 0f, 0.5105429f), new Vector3(0.09f, 2.0514f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.8527f, 1.4532f, 0.9848f), new Quaternion(0.8598523f, 0f, 0f, 0.5105429f), new Vector3(0.09f, 2.0514f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.5877f, 1.9399f, -2.6939f), new Quaternion(-0.7071064f, -2.980232E-08f, -2.980232E-08f, 0.7071072f), new Vector3(0.09f, 5.5899f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.5877f, 0.9544f, -2.1788f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.07f, 1.9008f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7531f, 0.9894f, -5.4443f), new Quaternion(0f, 0.7071066f, 0f, 0.707107f), new Vector3(0.07f, 1.971f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.6531f, 0.9894f, -5.4443f), new Quaternion(0f, 0.7071066f, 0f, 0.707107f), new Vector3(0.07f, 1.971f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7531f, 0.9894f, 0.0557f), new Quaternion(0f, 0.7071066f, 0f, 0.707107f), new Vector3(0.07f, 1.971f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.5877f, 1.9394f, -5.4439f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.07f, 3.8709f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.2105f, 1.9399f, -5.4441f), new Quaternion(2.682208E-07f, -1.490116E-07f, -0.7071068f, 0.7071068f), new Vector3(0.07f, 3.1551f, 0.09f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.0329f, 1.9399f, 0.0559f), new Quaternion(2.682208E-07f, -1.490116E-07f, -0.7071068f, 0.7071068f), new Vector3(0.07f, 0.7997f, 0.09f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.3103f, 1.94f, 0.056f), new Quaternion(2.682209E-07f, -1.490116E-07f, -0.7071068f, 0.7071068f), new Vector3(0.07f, 1.0051f, 0.09f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.8531f, 0.9545f, 0.0557f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.07f, 1.9011f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.853f, 0.5121f, 1.8058f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.07f, 1.0256f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.588f, 0.9894f, -1.0093f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.07f, 1.971f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.6379f, 0.4055f, -1.0547f), new Quaternion(0f, -0.003088445f, 0f, 0.9999952f), new Vector3(0.042f, 1.2445f, 2.1785f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.6221f, 0.3933f, -3.3174f), new Quaternion(0.008632095f, -2.384186E-07f, 3.026799E-09f, 0.9999627f), new Vector3(0.042f, 0.821f, 2.2529f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.6171f, 0.3763f, -4.9424f), new Quaternion(-0.006468955f, -0.003683239f, 0.005553341f, 0.9999569f), new Vector3(0.042f, 0.821f, 1.0316f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.1222f, 0.3989f, -5.4465f), new Quaternion(-0.0006850613f, -0.7065555f, 0.008498097f, 0.7076064f), new Vector3(0.042f, 0.8363f, 1.0316f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.7417f, 0.4858f, -5.5089f), new Quaternion(0.01526914f, -0.7065645f, -0.03415231f, 0.7066591f), new Vector3(0.042f, 1.0141f, 2.048f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1978f, 0.4015f, 0.0314f), new Quaternion(-0.01403217f, -0.7065901f, -0.004830828f, 0.7074675f), new Vector3(0.042f, 0.7961f, 3.1275f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.5879f, 0.9544f, -3.344f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.07f, 1.9008f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 2.9749f, -0.3443f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 1.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7264f, 2.6039f, -0.4594f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1364f, 0.1147f, 0.35f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7165f, 2.6563f, -0.4594f), new Quaternion(0f, 0f, -0.04361941f, 0.9990482f), new Vector3(0.15f, 0.02f, 0.4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7264f, 2.5328f, -0.6094f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1364f, 0.0275f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7264f, 2.5328f, -0.3094f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1364f, 0.0275f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7264f, 2.3401f, -0.4594f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1364f, 0.3577f, 0.35f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8356f, 2.635f, -2.0659f), new Quaternion(-1.081036E-07f, -0.7071052f, -8.632883E-07f, 0.7071085f), new Vector3(0.6f, 0.05f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8031f, 2.6313f, -1.8659f), new Quaternion(-0.500001f, -0.4999986f, -0.499999f, 0.5000015f), new Vector3(0.0117f, 0.066f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8031f, 2.6313f, -1.9994f), new Quaternion(-0.500001f, -0.4999986f, -0.499999f, 0.5000015f), new Vector3(0.0117f, 0.066f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8031f, 2.6313f, -2.1328f), new Quaternion(-0.500001f, -0.4999986f, -0.499999f, 0.5000015f), new Vector3(0.0117f, 0.066f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8031f, 2.5503f, -2.1328f), new Quaternion(0.7071086f, -3.469909E-07f, 0.707105f, -4.081955E-07f), new Vector3(0.025f, 0.2f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2763f, 1.9777f, -2.5726f), new Quaternion(-0.4945267f, 0.5054144f, -0.5054138f, -0.4945264f), new Vector3(0.025f, 0.2f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8031f, 2.5503f, -1.8694f), new Quaternion(0.7071086f, -3.469909E-07f, 0.707105f, -4.081955E-07f), new Vector3(0.025f, 0.2f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8031f, 2.4216f, -2.1328f), new Quaternion(0.7071086f, -3.469908E-07f, 0.707105f, -4.081954E-07f), new Vector3(0.02f, 0.1853f, 0.004f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.405f, 1.9777f, -2.5754f), new Quaternion(-0.4945268f, 0.5054144f, -0.5054138f, -0.4945264f), new Vector3(0.02f, 0.1853f, 0.004f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8031f, 2.4216f, -1.8694f), new Quaternion(0.7071086f, -3.469908E-07f, 0.707105f, -4.081954E-07f), new Vector3(0.02f, 0.1853f, 0.004f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8031f, 2.2556f, -2.1328f), new Quaternion(0.7071086f, -3.469908E-07f, 0.707105f, -4.081954E-07f), new Vector3(0.08f, 0.15f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.571f, 1.9777f, -2.579f), new Quaternion(-0.4945267f, 0.5054144f, -0.5054138f, -0.4945264f), new Vector3(0.08f, 0.15f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8031f, 2.2866f, -1.8694f), new Quaternion(0.7071086f, -3.469909E-07f, 0.707105f, -4.081955E-07f), new Vector3(0.0494f, 0.0881f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.0638f, 2.3988f, -0.2969f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1364f, 0.475f, 0.025f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.0638f, 2.3988f, -0.6219f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1364f, 0.475f, 0.025f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.0702f, 2.6488f, -0.4594f), new Quaternion(-0.7071068f, 1.44353E-06f, 1.44353E-06f, 0.7071068f), new Vector3(0.1492f, 0.35f, 0.025f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.0638f, 2.5826f, -0.4594f), new Quaternion(-0.7071068f, 1.44353E-06f, 1.44353E-06f, 0.7071068f), new Vector3(0.1364f, 0.3f, 0.1073f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.0638f, 2.1738f, -0.4594f), new Quaternion(-0.7071068f, 1.44353E-06f, 1.44353E-06f, 0.7071068f), new Vector3(0.1364f, 0.3f, 0.025f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1232f, 2.3579f, -0.4593f), new Quaternion(-0.5098917f, 0.4950984f, -0.4893498f, 0.5053957f), new Vector3(0.3429f, 0.3f, 0.025f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1592f, 2.4957f, -0.4543f), new Quaternion(-0.01046026f, 0.7106351f, 0.01134519f, 0.7033916f), new Vector3(0.08f, 0.02f, 0.01f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1434f, 2.4961f, -0.4094f), new Quaternion(-0.01541883f, 0.9998679f, 0.0006258822f, -0.005122304f), new Vector3(0.0406f, 0.02f, 0.01f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1444f, 2.4962f, -0.4994f), new Quaternion(-0.01541883f, 0.9998679f, 0.0006258822f, -0.005122304f), new Vector3(0.0406f, 0.02f, 0.01f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 2.9749f, 0.1807f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.3f, 1.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9132f, 2.9749f, -10.9193f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.3f, 1.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 2.9749f, 0.1807f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1f, 1.5f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9132f, 2.9749f, -10.9193f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1f, 1.5f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 3.675f, -5.2652f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.3f, 0.6f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4123f, 3.675f, -12.4891f), new Quaternion(0f, -0.707105f, 0f, 0.7071086f), new Vector3(0.3f, 0.6f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 3.525f, -3.7685f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.3f, 0.7001f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 3.675f, -5.2652f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1f, 0.6f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4124f, 3.675f, -12.4891f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.1f, 0.6f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 3.525f, -3.7685f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1f, 0.7001f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 3.675f, -6.4152f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.3f, 0.6f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.4624f, 3.675f, -12.4891f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.3f, 0.6f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 3.525f, -4.9185f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.3f, 0.7001f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 3.675f, -6.4152f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1f, 0.6f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.4624f, 3.675f, -12.4892f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.1f, 0.6f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 3.525f, -4.9185f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1f, 0.7001f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 3.4f, -5.8402f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071072f), new Vector3(0.3f, 1.1f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4374f, 3.4f, -12.4891f), new Quaternion(0.500001f, -0.4999988f, 0.4999983f, 0.500002f), new Vector3(0.3f, 1.9f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 3.2f, -4.3435f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071072f), new Vector3(0.3f, 1.1f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 3.4f, -5.8402f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071072f), new Vector3(0.1f, 1.1f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4374f, 3.4f, -12.4891f), new Quaternion(0.500001f, -0.4999987f, 0.4999983f, 0.500002f), new Vector3(0.1f, 1.9f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 3.2f, -4.3435f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071072f), new Vector3(0.1f, 1.1f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 3.6565f, -5.8402f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071071f), new Vector3(0.05f, 1.1915f, 0.6013f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9055f, 3.3465f, 1.0101f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071071f), new Vector3(0.0425f, 1.6354f, 0.729f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9053f, 3.346f, -10.102f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071071f), new Vector3(0.0425f, 1.6354f, 0.729f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4374f, 3.6565f, -12.4891f), new Quaternion(0.5000011f, -0.4999987f, 0.4999983f, 0.500002f), new Vector3(0.05f, 1.9915f, 0.6013f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 3.5407f, -4.3435f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071071f), new Vector3(0.05f, 1.1915f, 0.6328f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 3.95f, -5.8402f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071072f), new Vector3(0.3f, 1.1f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 3.95f, -12.4891f), new Quaternion(0.500001f, -0.4999988f, 0.4999983f, 0.500002f), new Vector3(0.3f, 1.9f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 3.85f, -4.3435f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071072f), new Vector3(0.3f, 1.1f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 3.95f, -5.8402f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071072f), new Vector3(0.1f, 1.1f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 3.95f, -12.4891f), new Quaternion(0.500001f, -0.4999987f, 0.4999983f, 0.500002f), new Vector3(0.1f, 1.9f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 3.85f, -4.3435f), new Quaternion(0.7071065f, 1.44353E-06f, -1.443529E-06f, 0.7071072f), new Vector3(0.1f, 1.1f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 2.9749f, 1.8307f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.3f, 1.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9132f, 2.9749f, -9.2693f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.3f, 1.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 2.9749f, 1.8307f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1f, 1.5f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9055f, 2.9749f, 1.0176f), new Quaternion(-0.7071067f, 1.430511E-06f, 1.311302E-06f, 0.7071068f), new Vector3(0.05f, 1.6455f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9055f, 2.9749f, -10.0933f), new Quaternion(-0.7071067f, 1.430511E-06f, 1.311302E-06f, 0.7071068f), new Vector3(0.05f, 1.6455f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9132f, 2.9749f, -9.2693f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1f, 1.5f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 2.2499f, 1.0057f), new Quaternion(0.7071067f, 1.44353E-06f, -1.44353E-06f, 0.7071068f), new Vector3(0.3f, 1.6f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9132f, 2.2499f, -10.0943f), new Quaternion(0.7071067f, 1.44353E-06f, -1.44353E-06f, 0.7071068f), new Vector3(0.3f, 1.6f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 2.2499f, 0.9982f), new Quaternion(0.7071067f, 1.443529E-06f, -1.443529E-06f, 0.7071068f), new Vector3(0.1f, 1.585f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9132f, 2.2499f, -10.1018f), new Quaternion(0.7071067f, 1.443529E-06f, -1.443529E-06f, 0.7071068f), new Vector3(0.1f, 1.585f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4379f, 2.0499f, 2.7783f), new Quaternion(0.5000012f, -0.4999987f, 0.4999989f, 0.5000013f), new Vector3(0.3f, 3.2997f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4379f, 2.0499f, 2.7783f), new Quaternion(0.5000011f, -0.4999987f, 0.4999989f, 0.5000013f), new Vector3(0.1f, 3.2997f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2658f, 2.7452f, 2.9116f), new Quaternion(0.5000011f, -0.4999987f, 0.4999989f, 0.5000013f), new Vector3(0.0701f, 1.644f, 0.06f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 3.6999f, 1.0057f), new Quaternion(0.7071067f, 1.44353E-06f, -1.443529E-06f, 0.7071068f), new Vector3(0.3f, 1.6f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9132f, 3.6999f, -10.0943f), new Quaternion(0.7071067f, 1.44353E-06f, -1.443529E-06f, 0.7071068f), new Vector3(0.3f, 1.6f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 3.6999f, 1.0057f), new Quaternion(0.7071068f, 1.44353E-06f, -1.44353E-06f, 0.7071068f), new Vector3(0.1f, 1.6f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9132f, 3.6999f, -10.0943f), new Quaternion(0.7071068f, 1.44353E-06f, -1.44353E-06f, 0.7071068f), new Vector3(0.1f, 1.6f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4381f, 3.4982f, 3.0641f), new Quaternion(0.500001f, -0.499999f, 0.499999f, 0.5000011f), new Vector3(0.3f, 3.3f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4381f, 3.4982f, 3.0641f), new Quaternion(0.5000009f, -0.499999f, 0.499999f, 0.5000011f), new Vector3(0.1f, 3.2f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 2.9749f, 2.4057f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 1.5f, 1.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 2.6749f, 1.0557f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 1.5f, 3.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 2.7249f, -3.0422f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 3.3f, 4.3959f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 2.2249f, -5.8402f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 2.3001f, 1.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.7908f, 2.2903f, -5.8402f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1825f, 0.0483f, 0.7521f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.177f, 2.825f, -4.629f), new Quaternion(0f, 0.7071082f, 0f, 0.7071053f), new Vector3(0.3f, 0.05f, 1.2787f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.177f, 3.325f, -4.629f), new Quaternion(0f, 0.7071082f, 0f, 0.7071053f), new Vector3(0.3f, 0.05f, 1.2787f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8528f, 2.6843f, -5.8404f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.0618f, 0.7301f, 0.6156f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8493f, 2.6843f, -5.8404f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.0585f, 0.6914f, 0.5829f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 4.175f, -5.8402f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 0.3999f, 1.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 4.3247f, -6.242f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(1.3f, 0.1004f, 3.5047f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 4.175f, -12.4891f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.25f, 0.3999f, 2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 4.3681f, -11.6834f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.1f, 0.0137f, 4.8864f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 4.3681f, -9.5834f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.1f, 0.0137f, 4.8864f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 4.3681f, -7.4834f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.1f, 0.0137f, 4.8864f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 4.3681f, -5.3834f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.1f, 0.0137f, 4.8864f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 4.3681f, -3.2834f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.1f, 0.0137f, 4.8864f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 4.3681f, -1.1834f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.1f, 0.0137f, 4.8864f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 4.3681f, 0.9166f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.1f, 0.0137f, 4.8864f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 4.3681f, 3.0166f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.1f, 0.0137f, 4.8864f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4371f, 2.6548f, -12.4889f), new Quaternion(0f, -0.7071049f, 0f, 0.7071086f), new Vector3(0.25f, 1.4404f, 2.0001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 4.125f, -4.3435f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 0.4999f, 1.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9124f, 2.5999f, -4.3434f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 1.15f, 1.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9123f, 3.1999f, -3.0437f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 2.35f, 1.3991f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4131f, 2.7749f, -7.4169f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 3.2001f, 1.6547f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4167f, 1.1248f, -7.4169f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.2572f, 0.1002f, 1.6547f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.413f, 2.3748f, -7.1103f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.0573f, 2.6002f, 1.3092f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4174f, 1.1248f, -4.6646f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.2585f, 0.1002f, 0.85f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.903f, 1.1248f, 0.3032f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1314f, 0.1002f, 4.7226f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4441f, 1.1248f, 2.573f), new Quaternion(0f, 0.7071082f, 0f, 0.7071053f), new Vector3(0.1314f, 0.1002f, 4.8653f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.444f, 1.1248f, -12.158f), new Quaternion(0f, 0.7071082f, 0f, 0.7071053f), new Vector3(0.1314f, 0.1002f, 4.8653f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.985f, 1.1248f, 0.9456f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1314f, 0.1002f, 3.4378f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.985f, 1.1248f, -7.4198f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.1314f, 0.1002f, 10.0034f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 1.1248f, -4.3727f), new Quaternion(0f, -0.7071056f, 0f, 0.707108f), new Vector3(0.2662f, 0.1002f, 1.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8884f, 1.1248f, -5.6166f), new Quaternion(0f, 1f, 0f, -1.251698E-06f), new Vector3(0.1088f, 0.1002f, 2.3613f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4131f, 2.7749f, -4.6646f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 3.2001f, 0.85f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4131f, 2.4249f, -5.0646f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.27f, 2.5001f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4131f, 2.4249f, -6.6146f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.27f, 2.5001f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4131f, 3.6999f, -5.8396f), new Quaternion(0.7071067f, 1.44353E-06f, -1.44353E-06f, 0.7071068f), new Vector3(0.27f, 1.6f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 2.7249f, -9.4023f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 3.3001f, 5.9239f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 2.7749f, -8.1193f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(1.3f, 3.2001f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 1.1248f, -8.1193f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(1.3f, 0.1002f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.6123f, 3.1547f, -12.4891f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(1.6501f, 2.4403f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4377f, 1.9001f, -12.2288f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(4.8008f, 0.069f, 0.2702f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4377f, 2.008f, 2.5747f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(4.836f, 0.069f, 0.2717f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.438f, 4.075f, 2.8514f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(4.836f, 0.069f, 0.4449f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.438f, 4.051f, 2.637f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(4.836f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.0568f, 4.051f, 2.822f), new Quaternion(0f, -0.7071057f, 0f, 0.7071079f), new Vector3(0.34f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.057f, 4.368f, -4.974f), new Quaternion(0f, -0.7071057f, 0f, 0.7071079f), new Vector3(14.7984f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.822f, 4.051f, 2.822f), new Quaternion(0f, -0.7071057f, 0f, 0.7071079f), new Vector3(0.34f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8222f, 4.368f, -4.974f), new Quaternion(0f, -0.7071057f, 0f, 0.7071079f), new Vector3(14.7984f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.822f, 4.218f, 2.5344f), new Quaternion(0.3535532f, -0.6123713f, -0.3535542f, 0.6123733f), new Vector3(0.4029f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.057f, 4.218f, 2.534f), new Quaternion(0.3535532f, -0.6123713f, -0.3535542f, 0.6123733f), new Vector3(0.4029f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.438f, 4.051f, 3.007f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(4.836f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.438f, 4.369f, 2.441f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(4.836f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.438f, 4.369f, -12.348f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(4.836f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.438f, 4.075f, 2.8514f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(4.836f, 0.03f, 0.4449f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.438f, 4.2504f, 2.5476f), new Quaternion(0.5000004f, 1.767955E-06f, -1.02073E-06f, 0.8660252f), new Vector3(4.836f, 0.069f, 0.4449f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8131f, 1.4547f, -12.245f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.05f, 0.8218f, 0.2702f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2131f, 1.4547f, -12.245f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.05f, 0.8218f, 0.2702f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.6131f, 1.4547f, -12.245f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.05f, 0.8218f, 0.2702f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.068f, 1.4547f, -12.245f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.05f, 0.8218f, 0.2702f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8133f, 2.5547f, -12.2387f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.049f, 1.2403f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5879f, 2.8198f, -12.1037f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.5001f, 1.7704f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0403f, 2.7644f, -12.364f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.3f, 0.45f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.442f, 3.0061f, -12.364f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.4f, 0.35f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.442f, 3.0061f, -12.3652f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.45f, 0.4f, 0.015f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5214f, 2.456f, -12.364f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.25f, 0.3f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5214f, 2.456f, -12.3662f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.3f, 0.35f, 0.0156f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5879f, 3.4999f, -11.6037f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.5001f, 0.4101f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5879f, 3.4999f, -11.0156f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.5001f, 0.4101f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5879f, 3.4999f, -10.4274f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.5001f, 0.4101f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5879f, 2.8198f, -12.3542f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.5f, 1.7704f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5876f, 2.5547f, -12.229f), new Quaternion(0.7071067f, 1.430511E-06f, -1.311302E-06f, 0.7071069f), new Vector3(0.5001f, 0.2305f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5876f, 2.8547f, -12.229f), new Quaternion(0.7071067f, 1.430511E-06f, -1.311302E-06f, 0.7071069f), new Vector3(0.5001f, 0.2305f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5876f, 2.2547f, -12.229f), new Quaternion(0.7071067f, 1.430511E-06f, -1.311302E-06f, 0.7071069f), new Vector3(0.5001f, 0.2305f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5879f, 1.9446f, -12.229f), new Quaternion(0.7071067f, 1.430511E-06f, -1.311302E-06f, 0.7071069f), new Vector3(0.5001f, 0.2305f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2623f, 3.1547f, -12.489f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(1.65f, 2.4403f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.6624f, 4.1386f, -8.1189f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(3.2508f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.047f, 4.1386f, -8.1189f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.02f, 0.08f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2778f, 4.1386f, -8.1189f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.02f, 0.08f, 0.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 2.7749f, -4.3646f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(1.3f, 3.2001f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.024f, 3.0517f, -4.2375f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.01f, 0.01f, 0.0259f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9125f, 2.9749f, -7.0939f), new Quaternion(0f, 1f, 0f, -5.364418E-07f), new Vector3(0.25f, 1.5f, 4.301f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.3088f, 2.6746f, -7.4099f), new Quaternion(0f, -1.490116E-08f, 0f, 1f), new Vector3(0.063f, 1.1838f, 0.84f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.287f, 3.0349f, -7.622f), new Quaternion(0f, -1.490116E-08f, 0f, 1f), new Vector3(0.029f, 0.3421f, 0.21f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.287f, 2.9808f, -7.3595f), new Quaternion(-0.02312748f, 0f, -2.118759E-08f, 0.9997326f), new Vector3(0.029f, 0.3421f, 0.21f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.287f, 2.5133f, -7.5994f), new Quaternion(0.05439004f, -7.450581E-08f, 5.215406E-08f, 0.9985198f), new Vector3(0.029f, 0.3421f, 0.21f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.287f, 2.5133f, -7.3434f), new Quaternion(-0.01349991f, 1.043081E-07f, -1.117587E-08f, 0.9999089f), new Vector3(0.029f, 0.3421f, 0.21f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.287f, 2.8121f, -7.1234f), new Quaternion(-0.01349991f, 1.043081E-07f, -1.117587E-08f, 0.9999089f), new Vector3(0.029f, 0.3421f, 0.21f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.287f, 2.7793f, -7.5674f), new Quaternion(-0.01349991f, 1.043081E-07f, -1.117587E-08f, 0.9999089f), new Vector3(0.029f, 0.1293f, 0.1324f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2896f, 3.0858f, -7.1413f), new Quaternion(0.04437379f, -1.490116E-08f, 4.470349E-08f, 0.999015f), new Vector3(0.029f, 0.1293f, 0.1324f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9128f, 2.9749f, -11.6542f), new Quaternion(0f, 1f, 0f, -5.364417E-07f), new Vector3(0.25f, 1.5f, 1.4198f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 1.6499f, 0.9957f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.25f, 1.15f, 3.68f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 1.4999f, 0.9957f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.25f, 0.85f, 3.68f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.4131f, 2.0342f, 2.8669f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.75f, 0.0815f, 0.3724f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.438f, 2.0342f, 2.9909f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(3.3001f, 0.0815f, 0.1251f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.463f, 2.0342f, 2.8669f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.7503f, 0.0815f, 0.3724f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.622f, 2.0899f, 3.1126f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.8f, 0.03f, 0.4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9719f, 1.9613f, 3.0486f), new Quaternion(-1.712075E-08f, 0.9732401f, -0.2297909f, -7.251202E-08f), new Vector3(0.1f, 0.0543f, 0.4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.272f, 1.9613f, 3.0486f), new Quaternion(-1.712075E-08f, 0.9732401f, -0.2297909f, -7.251202E-08f), new Vector3(0.1f, 0.0543f, 0.4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.622f, 2.0536f, 3.1897f), new Quaternion(0f, 0.7071065f, 0f, 0.7071071f), new Vector3(0.1f, 0.0543f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.237f, 2.3949f, 3.1126f), new Quaternion(-0.7071065f, 0.7071072f, 5.268354E-08f, -5.268358E-08f), new Vector3(0.58f, 0.03f, 0.4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.622f, 2.3949f, 2.8069f), new Quaternion(-0.7071065f, 0.7071071f, 5.268354E-08f, -5.268358E-08f), new Vector3(0.64f, 0.8f, 0.2114f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.622f, 2.5936f, 2.6731f), new Quaternion(-0.7071065f, 0.7071071f, 5.268354E-08f, -5.268358E-08f), new Vector3(0.2427f, 0.8f, 0.0562f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.872f, 2.6163f, 2.6693f), new Quaternion(-0.7071065f, 0.7071071f, 5.268354E-08f, -5.268358E-08f), new Vector3(0.1227f, 0.2688f, 0.0562f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.622f, 2.2776f, 2.7084f), new Quaternion(-0.7059888f, 0.7059893f, -0.03974281f, -0.03974296f), new Vector3(0.4011f, 0.8f, 0.0596f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4346f, 2.3949f, 3.2728f), new Quaternion(-0.7071065f, 0.7071072f, 5.268354E-08f, -5.268358E-08f), new Vector3(0.58f, 0.3853f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.8199f, 2.3949f, 3.2728f), new Quaternion(-0.7071065f, 0.7071072f, 5.268354E-08f, -5.268358E-08f), new Vector3(0.58f, 0.3853f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.007f, 2.3949f, 3.1126f), new Quaternion(-0.7071065f, 0.7071072f, 5.268354E-08f, -5.268358E-08f), new Vector3(0.58f, 0.03f, 0.4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.622f, 2.6999f, 3.1126f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.8f, 0.03f, 0.4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4381f, 3.5504f, 3.2399f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(4.8f, 0.0545f, 0.5591f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4381f, 1.5083f, 2.7438f), new Quaternion(-3.249877E-09f, 0.9990482f, 0.04361914f, -7.443489E-08f), new Vector3(5.3f, 1.0458f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4368f, 1.5279f, 2.6753f), new Quaternion(0f, 1f, 0f, 0f), new Vector3(5.2623f, 0.9059f, 0.3209f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4431f, 1.9905f, 2.6897f), new Quaternion(0f, 1f, 0f, 0f), new Vector3(5.2538f, 0.069f, 0.2921f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5881f, 2.7575f, 2.9314f), new Quaternion(-6.493568E-09f, 0.9961948f, 0.08715522f, -7.422227E-08f), new Vector3(1f, 1.502f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 2.8016f, 3.0877f), new Quaternion(-6.493568E-09f, 0.9961948f, 0.08715522f, -7.422227E-08f), new Vector3(0.25f, 2.7599f, 0.0445f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 2.7555f, 3.0797f), new Quaternion(-6.493568E-09f, 0.9961948f, 0.08715522f, -7.422227E-08f), new Vector3(0.25f, 2.7599f, 0.0448f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0631f, 2.7576f, 2.9314f), new Quaternion(-6.493567E-09f, 0.9961947f, 0.08715522f, -7.422226E-08f), new Vector3(0.05f, 1.5022f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0631f, 2.6886f, 2.9192f), new Quaternion(-6.493567E-09f, 0.9961947f, 0.08715523f, -7.422226E-08f), new Vector3(0.08f, 1.562f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.8131f, 2.7589f, 2.9317f), new Quaternion(-6.493567E-09f, 0.9961947f, 0.08715522f, -7.422226E-08f), new Vector3(0.05f, 1.5048f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.8131f, 2.6886f, 2.9192f), new Quaternion(-6.493567E-09f, 0.9961947f, 0.08715523f, -7.422226E-08f), new Vector3(0.08f, 1.562f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.277f, 3.1181f, 2.9845f), new Quaternion(-6.493567E-09f, 0.9961947f, 0.08715522f, -7.422226E-08f), new Vector3(1.6906f, 0.7698f, 0.0275f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.277f, 2.3373f, 2.8406f), new Quaternion(-6.493567E-09f, 0.9961947f, 0.08715523f, -7.422226E-08f), new Vector3(1.6905f, 0.8122f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.6199f, 2.6916f, 2.9029f), new Quaternion(-6.493568E-09f, 0.9961948f, 0.08715522f, -7.422227E-08f), new Vector3(1.6394f, 1.562f, 0.0151f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4379f, 2.72f, 2.925f), new Quaternion(-6.493567E-09f, 0.9961947f, 0.08715522f, -7.422226E-08f), new Vector3(0.1f, 1.6351f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.2881f, 2.7589f, 2.9316f), new Quaternion(-6.493567E-09f, 0.9961948f, 0.08715522f, -7.422227E-08f), new Vector3(1f, 1.5049f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4381f, 3.9479f, 3.1413f), new Quaternion(-6.493567E-09f, 0.9961948f, 0.08715522f, -7.422227E-08f), new Vector3(5.3005f, 0.9098f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 1.6496f, -7.3543f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.25f, 1.1507f, 10.0199f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.5971f, 0.7189f, -10.2084f), new Quaternion(3.962108E-06f, -0.7071007f, 1.308352E-06f, 0.7071129f), new Vector3(1f, 0.6336f, 0.1339f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.1552f, 0.5532f, -11.5834f), new Quaternion(-3.726781E-06f, 1f, 1.876488E-06f, -9.715556E-06f), new Vector3(0.75f, 0.3022f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.2372f, 0.1833f, -11.5834f), new Quaternion(-3.726782E-06f, 1f, 1.876488E-06f, -9.715557E-06f), new Vector3(0.9141f, 0.4377f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.3077f, 0.3373f, -10.2084f), new Quaternion(3.96211E-06f, -0.7071007f, 1.308351E-06f, 0.707113f), new Vector3(2.5f, 0.1296f, 0.945f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.5971f, 0.7189f, -9.2084f), new Quaternion(3.962108E-06f, -0.7071007f, 1.308352E-06f, 0.7071129f), new Vector3(1f, 0.6336f, 0.1339f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.1552f, 0.5532f, -8.8334f), new Quaternion(-3.726782E-06f, 1f, 1.876488E-06f, -9.715556E-06f), new Vector3(0.75f, 0.3022f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.2373f, 0.1833f, -8.8334f), new Quaternion(-3.726782E-06f, 1f, 1.876488E-06f, -9.715557E-06f), new Vector3(0.9141f, 0.4377f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.1551f, 0.4665f, -10.2084f), new Quaternion(3.962109E-06f, -0.7071007f, 1.308351E-06f, 0.7071129f), new Vector3(0.8333f, 0.1296f, 0.7501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.0662f, 0.4663f, -11.0534f), new Quaternion(4.033238E-06f, -0.7481081f, 1.069152E-06f, 0.663577f), new Vector3(0.8333f, 0.1296f, 0.7501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.1551f, 0.5203f, -9.3702f), new Quaternion(0.04474492f, -0.7056835f, -0.04474047f, 0.7056962f), new Vector3(0.8333f, 0.1296f, 0.7501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7258f, 0.5116f, -10.2084f), new Quaternion(3.962108E-06f, -0.7071007f, 1.308352E-06f, 0.7071129f), new Vector3(3.0408f, 1.1045f, 0.1089f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.5971f, 0.7189f, -11.2084f), new Quaternion(3.962108E-06f, -0.7071007f, 1.308352E-06f, 0.7071129f), new Vector3(1f, 0.6336f, 0.1339f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4373f, 1.3968f, -12.3923f), new Quaternion(-0.06162845f, -0.7044161f, 0.06162845f, 0.704416f), new Vector3(0.25f, 1.1362f, 5.3002f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 4.0499f, 1.1281f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.25f, 0.65f, 3.9448f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 4.4249f, -4.6551f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.25f, 0.1f, 15.9181f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 4.4249f, -4.6551f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.25f, 0.1f, 15.9181f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4381f, 4.4499f, -4.6551f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(4.8f, 0.05f, 15.9181f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4381f, 4.2749f, 3.6238f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(5.3f, 0.4f, 0.6396f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.5481f, 4.7462f, 0.4846f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(1.3f, 0.4f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.5481f, 4.7351f, 0.4846f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.7599f, 0.5527f, 0.8769f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.5481f, 5.0095f, 0.4846f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.8204f, 0.0341f, 0.9466f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.5481f, 4.8213f, 1.3565f), new Quaternion(-9.724939E-09f, 0.9914449f, 0.1305259f, -7.386838E-08f), new Vector3(1.3f, 0.1782f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.5482f, 4.671f, 1.3565f), new Quaternion(9.72494E-09f, 0.9914449f, -0.1305259f, -7.386838E-08f), new Vector3(1.3f, 0.1782f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.5482f, 4.7462f, 1.3795f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(1.3f, 0.2447f, 0.2898f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4381f, 4.2749f, -12.7339f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(5.3f, 0.4f, 0.2397f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4377f, 4.3999f, -4.6335f), new Quaternion(0.7071056f, 0.7071079f, 5.268348E-08f, -5.268362E-08f), new Vector3(0.05f, 4.8008f, 15.4613f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9123f, 4.0499f, -8.6538f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.25f, 0.65f, 7.4206f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9631f, 3.8999f, 1.1281f), new Quaternion(0f, 1f, 0f, -7.450581E-08f), new Vector3(0.25f, 0.95f, 3.9448f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.9131f, 4.0249f, -1.5943f), new Quaternion(0f, 1f, 0f, -7.45058E-08f), new Vector3(0.25f, 0.7f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1381f, 0.0621f, 1.6308f), new Quaternion(0f, 0.7071069f, 0f, 0.7071066f), new Vector3(0.35f, 0.1256f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1381f, 0.4627f, 0.2308f), new Quaternion(0f, 0.7071069f, 0f, 0.7071066f), new Vector3(0.35f, 0.9245f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1381f, 0.2621f, 0.9308f), new Quaternion(0f, 0.7071069f, 0f, 0.7071066f), new Vector3(0.35f, 0.5256f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1381f, 0.3621f, 0.5808f), new Quaternion(0f, 0.7071069f, 0f, 0.7071066f), new Vector3(0.35f, 0.7256f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.1381f, 0.1621f, 1.2808f), new Quaternion(0f, 0.7071069f, 0f, 0.7071066f), new Vector3(0.35f, 0.3256f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4131f, 4.0249f, -5.8396f), new Quaternion(0f, 1f, 0f, -7.45058E-08f), new Vector3(0.25f, 0.7f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4881f, 1.1499f, -9.0943f), new Quaternion(0f, 3.766269E-06f, 0f, 1f), new Vector3(0.7f, 0.15f, 1.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5339f, 1.6499f, -9.1193f), new Quaternion(0f, 3.766269E-06f, 0f, 1f), new Vector3(0.6084f, 0.03f, 1.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4881f, 2.1999f, -9.8943f), new Quaternion(-0.7071067f, 2.663154E-06f, 2.663154E-06f, 0.7071069f), new Vector3(0.7f, 0.1f, 1.9499f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4881f, 2.1999f, -8.2943f), new Quaternion(-0.7071067f, 2.663154E-06f, 2.663154E-06f, 0.7071069f), new Vector3(0.7f, 0.1f, 1.9499f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8331f, 2.1999f, -9.0943f), new Quaternion(-0.500002f, -0.4999983f, -0.4999981f, 0.5000017f), new Vector3(1.5f, 0.01f, 1.95f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4703f, 3.0191f, -9.094f), new Quaternion(-0.500002f, -0.4999983f, -0.4999982f, 0.5000017f), new Vector3(1.5f, 0.023f, 0.0251f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4725f, 2.9856f, -8.461f), new Quaternion(-0.500002f, -0.4999982f, -0.4999981f, 0.5000017f), new Vector3(0.0274f, 0.01f, 0.0815f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4725f, 2.9856f, -8.5751f), new Quaternion(-0.500002f, -0.4999982f, -0.4999981f, 0.5000017f), new Vector3(0.0274f, 0.01f, 0.0815f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4725f, 2.9856f, -8.73f), new Quaternion(-0.500002f, -0.4999982f, -0.4999981f, 0.5000017f), new Vector3(0.0274f, 0.01f, 0.0815f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4725f, 2.9856f, -8.9309f), new Quaternion(-0.500002f, -0.4999982f, -0.4999981f, 0.5000017f), new Vector3(0.0274f, 0.01f, 0.0815f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4729f, 2.9856f, -9.5546f), new Quaternion(-0.500002f, -0.4999982f, -0.4999981f, 0.5000017f), new Vector3(0.0274f, 0.01f, 0.0815f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4725f, 2.9856f, -9.2455f), new Quaternion(-0.500002f, -0.4999982f, -0.4999981f, 0.5000017f), new Vector3(0.0274f, 0.01f, 0.0815f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4881f, 3.4999f, -9.0943f), new Quaternion(0f, 3.766269E-06f, 0f, 1f), new Vector3(0.7f, 0.65f, 1.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.588f, 3.2848f, -11.1408f), new Quaternion(0f, 3.766269E-06f, 0f, 1f), new Vector3(0.5002f, 0.02f, 2.4068f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.588f, 3.7149f, -11.1508f), new Quaternion(0f, 3.766269E-06f, 0f, 1f), new Vector3(0.5002f, 0.02f, 2.4268f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1281f, 3.4999f, -9.4943f), new Quaternion(0f, 3.766269E-06f, 0f, 1f), new Vector3(0.02f, 0.55f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1154f, 2.1999f, -9.4948f), new Quaternion(0f, 0.015919f, 0f, 0.9998733f), new Vector3(0.02f, 1.9499f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.7381f, 2.1999f, -8.3038f), new Quaternion(0.7240911f, 1.173993E-06f, 0.6897044f, -1.5768E-06f), new Vector3(0.02f, 1.9499f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4132f, 2.2191f, -8.2882f), new Quaternion(-2.258564E-06f, 0.724125f, 6.648001E-05f, 0.6896689f), new Vector3(0.0626f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4119f, 2.1391f, -8.2624f), new Quaternion(0.5120326f, 0.5120318f, 0.4876719f, 0.4876701f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4132f, 2.0591f, -8.2882f), new Quaternion(-2.258564E-06f, 0.724125f, 6.648001E-05f, 0.6896689f), new Vector3(0.0626f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1148f, 3.4999f, -8.6938f), new Quaternion(6.752965E-07f, 0.9998626f, 1.779503E-06f, 0.01658147f), new Vector3(0.02f, 0.55f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4631f, 1.5249f, -2.9977f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.75f, 0.9f, 0.9235f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.255f, 1.5502f, -3.0014f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.3577f, 0.5389f, 0.7506f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.401f, 1.5272f, -3.0004f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.6592f, 0.3609f, 0.6842f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.06f, 1.7952f, -3.3514f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.1f, 0.02f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.02f, 1.7952f, -3.0014f), new Quaternion(-1.302075E-06f, 0.7071085f, -1.406969E-07f, 0.7071051f), new Vector3(0.6806f, 0.02f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.06f, 1.7952f, -2.6514f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.1f, 0.02f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1381f, 1.5249f, -2.0359f), new Quaternion(-1.020195E-06f, 1f, 8.21217E-07f, -8.046627E-07f), new Vector3(0.1f, 0.9f, 1.0001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0795f, 1.5022f, -2.3364f), new Quaternion(-1.020195E-06f, 1f, 8.212168E-07f, -8.046626E-07f), new Vector3(0.02f, 0.75f, 0.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0662f, 1.8087f, -2.1243f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0248f, 1.7287f, -2.1243f), new Quaternion(0.7070879f, 0.7071258f, 2.873516E-05f, -1.09585E-06f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0662f, 1.6487f, -2.1243f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.336f, 3.2302f, -1.8174f), new Quaternion(8.046622E-07f, -4.917385E-07f, 1f, -1.020195E-06f), new Vector3(0.02f, 0.95f, 0.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.336f, 3.2302f, -3.7584f), new Quaternion(8.046622E-07f, -4.917385E-07f, 1f, -1.020195E-06f), new Vector3(0.02f, 0.95f, 0.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.402f, 1.5897f, -6.0152f), new Quaternion(8.046622E-07f, -4.917386E-07f, 1f, -1.020195E-06f), new Vector3(0.02f, 0.6f, 0.35f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.402f, 1.5897f, -5.6652f), new Quaternion(8.046622E-07f, -4.917386E-07f, 1f, -1.020195E-06f), new Vector3(0.02f, 0.6f, 0.35f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3227f, 2.8242f, -3.9705f), new Quaternion(8.046622E-07f, -4.917385E-07f, 1f, -1.020195E-06f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2813f, 2.9042f, -3.9705f), new Quaternion(-1.382649E-06f, 2.054654E-06f, -0.7071071f, 0.7071065f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3227f, 2.9842f, -3.9705f), new Quaternion(8.046622E-07f, -4.917385E-07f, 1f, -1.020195E-06f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4135f, 1.8712f, -5.9256f), new Quaternion(8.046624E-07f, -4.917385E-07f, 1f, -1.020195E-06f), new Vector3(0.0719f, 0.01f, 0.1339f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.402f, 1.8712f, -5.7548f), new Quaternion(1f, -1.020195E-06f, -1.192093E-06f, 4.917389E-07f), new Vector3(0.049f, 0.01f, 0.1339f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0735f, 1.5022f, -1.8366f), new Quaternion(-1.127622E-06f, 0.9999289f, 6.66082E-07f, 0.01192803f), new Vector3(0.02f, 0.75f, 0.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0336f, 1.8087f, -2.0478f), new Quaternion(-1.127622E-06f, 0.9999289f, 6.66082E-07f, 0.01192803f), new Vector3(0.0497f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0138f, 1.7287f, -2.0473f), new Quaternion(0.707037f, 0.7070757f, 0.008463365f, 0.008433761f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0552f, 1.6487f, -2.0483f), new Quaternion(-1.127622E-06f, 0.9999289f, 6.66082E-07f, 0.01192803f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.33f, 3.2302f, -2.3174f), new Quaternion(0.01192964f, -2.082018E-07f, 0.9999289f, -9.060319E-07f), new Vector3(0.02f, 0.95f, 0.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.7645f, 1.5249f, -2.0859f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.15f, 0.9f, 1.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8031f, 2.6313f, -2.2662f), new Quaternion(-0.500001f, -0.4999986f, -0.499999f, 0.5000015f), new Vector3(0.0117f, 0.066f, 0.005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3227f, 2.8242f, -2.0295f), new Quaternion(8.046622E-07f, -4.917385E-07f, 1f, -1.020195E-06f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2813f, 2.9042f, -2.0295f), new Quaternion(-1.382649E-06f, 2.054654E-06f, -0.7071071f, 0.7071065f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3227f, 2.9842f, -2.0295f), new Quaternion(8.046622E-07f, -4.917385E-07f, 1f, -1.020195E-06f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2901f, 2.8242f, -2.1062f), new Quaternion(0.01192964f, -2.082019E-07f, 0.9999288f, -9.060319E-07f), new Vector3(0.0497f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2703f, 2.9042f, -2.1067f), new Quaternion(-0.008436556f, 0.008436803f, -0.7070569f, 0.7070561f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3117f, 2.9842f, -2.1057f), new Quaternion(0.01192964f, -2.082019E-07f, 0.9999288f, -9.060319E-07f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1118f, 3.2882f, -9.1704f), new Quaternion(1.072884E-06f, -1.001942E-06f, 1f, -1.027301E-06f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0889f, 2.0592f, -9.1717f), new Quaternion(0.01591632f, -1.018165E-06f, 0.9998734f, -1.011225E-06f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4051f, 2.2192f, -6.5481f), new Quaternion(-1.028604E-06f, 0.9997171f, 1.022334E-06f, 0.02378749f), new Vector3(0.1449f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.085f, 3.2882f, -9.0161f), new Quaternion(-0.01658413f, -7.800125E-07f, 0.9998625f, -1.479337E-06f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0708f, 3.3682f, -9.1704f), new Quaternion(-1.16229E-06f, 2.622604E-06f, -0.7071071f, 0.7071065f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0479f, 2.1392f, -9.173f), new Quaternion(-0.01125488f, 0.01125631f, -0.7070176f, 0.7070168f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.3381f, 2.1392f, -6.5449f), new Quaternion(0.7069051f, 0.7069083f, 0.01682198f, 0.01682034f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4725f, 2.1392f, -6.5513f), new Quaternion(0.7069051f, 0.7069083f, 0.01682198f, 0.01682034f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0441f, 3.3682f, -9.0147f), new Quaternion(0.01172599f, -0.01172504f, -0.7070096f, 0.7070096f), new Vector3(0.15f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1118f, 3.4482f, -9.1704f), new Quaternion(1.072884E-06f, -1.001942E-06f, 1f, -1.027301E-06f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0889f, 2.2192f, -9.1717f), new Quaternion(0.01591632f, -1.018165E-06f, 0.9998734f, -1.011225E-06f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4051f, 2.0592f, -6.5481f), new Quaternion(-1.028604E-06f, 0.9997171f, 1.022334E-06f, 0.02378749f), new Vector3(0.1449f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.085f, 3.4482f, -9.0161f), new Quaternion(-0.01658413f, -7.800125E-07f, 0.9998625f, -1.479337E-06f), new Vector3(0.0928f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4395f, 1.5249f, -1.6184f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.5f, 0.9f, 0.1651f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.246f, 1.5556f, 1.9566f), new Quaternion(-1.178185E-06f, 0.9746042f, 5.719035E-07f, 0.2239347f), new Vector3(0.8f, 0.9607f, 0.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.246f, 2.0574f, 1.9566f), new Quaternion(-1.178185E-06f, 0.9746042f, 5.719036E-07f, 0.2239347f), new Vector3(0.6866f, 0.0428f, 0.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.146f, 1.5752f, 1.7496f), new Quaternion(-1.178185E-06f, 0.9746042f, 5.719036E-07f, 0.2239347f), new Vector3(0.4f, 0.95f, 0.1904f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.872f, 1.9092f, 1.8736f), new Quaternion(0.6891483f, 0.6891502f, 0.1583461f, 0.1583453f), new Vector3(0.2114f, 0.0346f, 0.1904f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.912f, 1.5752f, 1.8816f), new Quaternion(-1.211186E-06f, 0.9589542f, 4.982213E-07f, 0.2835609f), new Vector3(0.1628f, 0.95f, 0.2052f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9757f, 1.9817f, 2.0877f), new Quaternion(0.1601722f, 0.5497366f, 0.6887264f, 0.4447365f), new Vector3(0.0368f, 0.6f, 0.2237f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5164f, 1.9817f, 1.8254f), new Quaternion(-0.6887268f, -0.4447383f, 0.1601701f, 0.5497351f), new Vector3(0.0368f, 0.6f, 0.2237f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9596f, 1.9682f, 2.0955f), new Quaternion(-0.02354105f, 0.646111f, 0.7067143f, 0.2873003f), new Vector3(0.0368f, 0.6f, 0.2237f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5324f, 1.9682f, 1.8176f), new Quaternion(-0.7067142f, -0.2873023f, -0.02354319f, 0.6461102f), new Vector3(0.0368f, 0.6f, 0.2237f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.395f, 1.5752f, 1.6476f), new Quaternion(0.9865505f, 7.108133E-07f, 0.1634575f, -7.568988E-07f), new Vector3(0.1628f, 0.95f, 0.2052f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2245f, 1.5249f, -1.9359f), new Quaternion(-1.020195E-06f, 1f, 8.21217E-07f, -8.046627E-07f), new Vector3(0.07f, 0.9f, 0.47f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6395f, 1.5249f, -1.9709f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.1f, 0.9f, 0.4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6225f, 2.0059f, -2.0199f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.02f, 0.0619f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6225f, 2.0059f, -1.9029f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.02f, 0.0619f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4825f, 2.0059f, -2.0199f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.02f, 0.0209f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4825f, 2.0059f, -1.9029f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.02f, 0.0209f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5455f, 2.0259f, -2.0199f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0291f, 0.0537f, 0.0291f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5455f, 2.0259f, -1.9029f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0291f, 0.0537f, 0.0291f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5455f, 2.0579f, -2.0199f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0139f, 0.0398f, 0.0139f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5455f, 2.0579f, -1.9029f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0139f, 0.0398f, 0.0139f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5455f, 2.0719f, -2.0199f), new Quaternion(-6.282719E-07f, 0.9238802f, 1.149117E-06f, 0.3826819f), new Vector3(0.01f, 0.01f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5455f, 2.0719f, -1.9029f), new Quaternion(-6.282719E-07f, 0.9238802f, 1.149117E-06f, 0.3826819f), new Vector3(0.01f, 0.01f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5455f, 2.0719f, -2.0199f), new Quaternion(3.68294E-07f, 0.3826838f, 1.256804E-06f, 0.9238794f), new Vector3(0.01f, 0.01f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5455f, 2.0719f, -1.9029f), new Quaternion(3.68294E-07f, 0.3826838f, 1.256804E-06f, 0.9238794f), new Vector3(0.01f, 0.01f, 0.07f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5425f, 2.0269f, -2.0199f), new Quaternion(0.7071054f, 0.7071082f, -1.192093E-07f, -1.579523E-06f), new Vector3(0.02f, 0.14f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5425f, 2.0269f, -1.9029f), new Quaternion(0.7071054f, 0.7071082f, -1.192093E-07f, -1.579523E-06f), new Vector3(0.02f, 0.14f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4745f, 1.5249f, -1.7359f), new Quaternion(-1.406981E-07f, 0.7071071f, 1.302076E-06f, 0.7071065f), new Vector3(0.07f, 0.9f, 0.43f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4395f, 1.8489f, -1.9709f), new Quaternion(0.5000002f, 0.5000003f, 0.500001f, 0.4999986f), new Vector3(0.0331f, 0.5279f, 0.36f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4395f, 1.5249f, -2.3534f), new Quaternion(-1.020195E-06f, 1f, 8.21217E-07f, -8.046627E-07f), new Vector3(0.5f, 0.9f, 0.365f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2045f, 1.9849f, -2.1185f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.03f, 0.02f, 0.835f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6745f, 1.9849f, -2.1184f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046626E-07f), new Vector3(0.03f, 0.02f, 0.835f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4395f, 1.9849f, -2.521f), new Quaternion(-1.407003E-07f, 0.707107f, 1.302075E-06f, 0.7071066f), new Vector3(0.03f, 0.02f, 0.44f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4395f, 1.9849f, -1.7159f), new Quaternion(-1.407003E-07f, 0.707107f, 1.302075E-06f, 0.7071066f), new Vector3(0.03f, 0.02f, 0.44f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.073f, 1.9399f, -2.4826f), new Quaternion(-1.989812E-07f, 2.503395E-06f, 1.841411E-06f, 1f), new Vector3(0.03f, 0.07f, 1.8935f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4481f, 1.9399f, -1.5209f), new Quaternion(1.161374E-06f, -0.7071052f, 1.442776E-06f, 0.7071085f), new Vector3(0.03f, 0.07f, 0.7801f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4381f, 1.9399f, -3.8345f), new Quaternion(1.161374E-06f, -0.7071052f, 1.442775E-06f, 0.7071085f), new Vector3(0.8102f, 0.07f, 1.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7325f, 1.1249f, -3.85f), new Quaternion(1.161374E-06f, -0.7071052f, 1.442775E-06f, 0.7071084f), new Vector3(0.3931f, 0.1f, 0.1119f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7256f, 1.9399f, -3.8344f), new Quaternion(1.161374E-06f, -0.7071052f, 1.442775E-06f, 0.7071084f), new Vector3(0.3772f, 0.07f, 0.1251f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7325f, 1.3772f, -3.8496f), new Quaternion(1.161374E-06f, -0.7071052f, 1.442775E-06f, 0.7071084f), new Vector3(0.3924f, 0.04f, 0.1119f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7324f, 1.6772f, -3.8498f), new Quaternion(1.161374E-06f, -0.7071052f, 1.442775E-06f, 0.7071084f), new Vector3(0.3927f, 0.04f, 0.1119f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7945f, 1.9399f, -3.5774f), new Quaternion(1.495219E-06f, -0.8660245f, 1.093027E-06f, 0.5000016f), new Vector3(0.25f, 0.07f, 0.1589f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7945f, 1.5422f, -3.5774f), new Quaternion(1.495219E-06f, -0.8660244f, 1.093027E-06f, 0.5000017f), new Vector3(0.03f, 0.7973f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7945f, 1.9399f, -4.0917f), new Quaternion(7.483837E-07f, -0.4999985f, 1.694199E-06f, 0.8660263f), new Vector3(0.25f, 0.07f, 0.1589f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7945f, 1.5422f, -4.0917f), new Quaternion(7.483837E-07f, -0.4999984f, 1.694199E-06f, 0.8660263f), new Vector3(0.03f, 0.7973f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7947f, 1.6772f, -4.1068f), new Quaternion(7.483838E-07f, -0.4999985f, 1.694199E-06f, 0.8660263f), new Vector3(0.2238f, 0.04f, 0.1438f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7947f, 1.6772f, -3.5925f), new Quaternion(0.8660265f, -3.151595E-06f, -0.4999982f, 1.80304E-06f), new Vector3(0.2238f, 0.04f, 0.1438f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7947f, 1.3772f, -3.5925f), new Quaternion(0.8660265f, -3.151595E-06f, -0.4999982f, 1.80304E-06f), new Vector3(0.2238f, 0.04f, 0.1438f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7947f, 1.1249f, -3.5925f), new Quaternion(0.8660265f, -3.151595E-06f, -0.4999982f, 1.80304E-06f), new Vector3(0.2238f, 0.1f, 0.1438f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7947f, 1.3772f, -4.1068f), new Quaternion(7.483838E-07f, -0.4999985f, 1.694199E-06f, 0.8660263f), new Vector3(0.2238f, 0.04f, 0.1438f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7947f, 1.1249f, -4.1068f), new Quaternion(7.483837E-07f, -0.4999984f, 1.694199E-06f, 0.8660263f), new Vector3(0.2238f, 0.1f, 0.1438f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4631f, 1.5249f, -3.8496f), new Quaternion(0f, 1.072884E-06f, 0f, 1f), new Vector3(0.75f, 0.9f, 0.7801f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3151f, 2.0346f, -12.2155f), new Quaternion(0f, -0.6226013f, 0f, 0.7825393f), new Vector3(0.07f, 0.2f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3241f, 2.0181f, -12.2545f), new Quaternion(0f, -0.6226012f, 0f, 0.7825392f), new Vector3(0.01f, 0.1671f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4594f, 2.11f, -12.2233f), new Quaternion(0f, -0.6226012f, 0f, 0.7825394f), new Vector3(0.01f, 0.0165f, 0.01f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.2418f, 2.007f, -12.2153f), new Quaternion(0f, -0.6226012f, 0f, 0.7825392f), new Vector3(0.0421f, 0.123f, 0.1232f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.308f, 2.1175f, -12.2f), new Quaternion(0f, -0.6226012f, 0f, 0.7825394f), new Vector3(0.0421f, 0.0168f, 0.2591f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.2592f, 2.1175f, -12.1966f), new Quaternion(0f, -0.6226044f, 5.958549E-10f, 0.7825369f), new Vector3(0.0156f, 0.0188f, 0.0024f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3812f, 2.0266f, -12.1877f), new Quaternion(0f, -0.6226012f, 0f, 0.7825394f), new Vector3(0.0492f, 0.1437f, 0.1439f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4634f, 1.1249f, -3.8496f), new Quaternion(2.60415E-06f, -0.7071061f, 2.813971E-07f, 0.7071075f), new Vector3(0.78f, 0.1f, 1.35f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3874f, 1.0622f, -0.715f), new Quaternion(2.615928E-06f, -0.7461212f, 1.330547E-07f, 0.6658103f), new Vector3(3.6045f, 0.0392f, 2.1554f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.227f, 1.0622f, -10.2354f), new Quaternion(2.615928E-06f, -0.7461212f, 1.330547E-07f, 0.6658103f), new Vector3(2.3393f, 0.0392f, 2.9852f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.194f, 1.0622f, -6.3784f), new Quaternion(2.594547E-06f, -0.6855979f, 3.593121E-07f, 0.7279805f), new Vector3(0.4651f, 0.0392f, 0.8796f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.244f, 1.0622f, -1.5394f), new Quaternion(-1.99537E-06f, 0.9996366f, 1.696845E-06f, 0.0269581f), new Vector3(0.7961f, 0.0392f, 1.3904f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4634f, 1.3772f, -3.8496f), new Quaternion(2.60415E-06f, -0.7071062f, 2.813972E-07f, 0.7071075f), new Vector3(0.78f, 0.04f, 1.35f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4634f, 1.6772f, -3.8496f), new Quaternion(2.60415E-06f, -0.7071062f, 2.813971E-07f, 0.7071074f), new Vector3(0.78f, 0.04f, 1.35f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.591f, 3.2332f, -3.7664f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.707106f), new Vector3(0.5909f, 1f, 0.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.591f, 3.2332f, -2.0663f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.707106f), new Vector3(1.0608f, 1f, 0.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.591f, 3.3042f, -3.0244f), new Quaternion(-1.406999E-07f, 0.7071075f, 1.302074E-06f, 0.707106f), new Vector3(0.75f, 0.8577f, 0.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.544f, 2.8152f, -3.0334f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.707106f), new Vector3(0.8208f, 0.1211f, 0.6543f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.269f, 2.8142f, -3.3004f), new Quaternion(-1.407E-07f, 0.7071077f, 1.302074E-06f, 0.7071059f), new Vector3(0.138f, 0.0195f, 0.1219f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.588f, 2.6692f, -3.0334f), new Quaternion(-1.406999E-07f, 0.7071075f, 1.302075E-06f, 0.707106f), new Vector3(0.7447f, 0.0195f, 0.3619f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.269f, 2.8142f, -2.7934f), new Quaternion(0.5000002f, 0.5000008f, 0.5000001f, 0.4999988f), new Vector3(0.0568f, 0.0195f, 0.1219f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.269f, 2.8142f, -2.7544f), new Quaternion(0.5000002f, 0.5000008f, 0.5000001f, 0.4999988f), new Vector3(0.0568f, 0.0195f, 0.1219f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.269f, 2.8142f, -2.7154f), new Quaternion(0.5000002f, 0.5000008f, 0.5000001f, 0.4999988f), new Vector3(0.0568f, 0.0195f, 0.1219f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.606f, 2.7102f, -3.0334f), new Quaternion(-1.407001E-07f, 0.7071076f, 1.302075E-06f, 0.707106f), new Vector3(0.8208f, 0.0894f, 0.5303f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.312f, 2.7512f, -3.0334f), new Quaternion(-0.1830135f, 0.6830133f, 0.183015f, 0.6830114f), new Vector3(0.8208f, 0.0894f, 0.1669f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4755f, 1.9802f, -3.0261f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.792f, 0.01f, 0.6401f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4706f, 1.9952f, -3.4011f), new Quaternion(-1.407001E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.05f, 0.02f, 0.65f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4706f, 1.9952f, -2.6511f), new Quaternion(-1.407001E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.05f, 0.02f, 0.65f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1705f, 1.9952f, -3.0261f), new Quaternion(8.212198E-07f, 2.086163E-07f, 1.020196E-06f, 1f), new Vector3(0.05f, 0.02f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.7706f, 1.9952f, -3.0261f), new Quaternion(8.212198E-07f, 2.086163E-07f, 1.020196E-06f, 1f), new Vector3(0.05f, 0.02f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3573f, 1.9669f, -3.2873f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6045f, 1.9669f, -3.2873f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6045f, 1.9669f, -2.9961f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3572f, 1.9669f, -2.9961f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3573f, 1.9682f, -3.1394f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6045f, 1.9682f, -3.1394f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6046f, 1.9682f, -2.8481f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3572f, 1.9682f, -2.8482f), new Quaternion(-1.407E-07f, 0.7071075f, 1.302075E-06f, 0.7071061f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.277f, 1.9682f, -3.2124f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5241f, 1.9682f, -3.2124f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5242f, 1.9682f, -2.9212f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2769f, 1.9682f, -2.9211f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4309f, 1.9682f, -3.2124f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.678f, 1.9682f, -3.2124f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.678f, 1.9682f, -2.9211f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4307f, 1.9682f, -2.9211f), new Quaternion(-1.020195E-06f, 1f, 8.212169E-07f, -8.046627E-07f), new Vector3(0.0811f, 0.0742f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3933f, 1.9881f, -2.7546f), new Quaternion(-1.020196E-06f, 1f, 8.212187E-07f, -6.966294E-07f), new Vector3(0.0533f, 0.0383f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5646f, 1.9881f, -2.7547f), new Quaternion(-1.020196E-06f, 1f, 8.212187E-07f, -6.966294E-07f), new Vector3(0.0533f, 0.0383f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4784f, 1.9881f, -2.7547f), new Quaternion(-1.020196E-06f, 1f, 8.212187E-07f, -6.966294E-07f), new Vector3(0.0533f, 0.0383f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6497f, 1.9881f, -2.7547f), new Quaternion(-1.020196E-06f, 1f, 8.212187E-07f, -6.966294E-07f), new Vector3(0.0533f, 0.0383f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2868f, 1.9881f, -2.7546f), new Quaternion(-1.020196E-06f, 1f, 8.212187E-07f, -6.966294E-07f), new Vector3(0.0533f, 0.0383f, 0.0112f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.0028f, 1.5068f, 2.3284f), new Quaternion(0f, 0.7071077f, 0f, 0.7071059f), new Vector3(0.05f, 0.85f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1528f, 1.5068f, 2.3284f), new Quaternion(0f, 0.7071077f, 0f, 0.7071059f), new Vector3(0.05f, 0.85f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.0028f, 1.5068f, 1.6284f), new Quaternion(0f, 0.7071077f, 0f, 0.7071059f), new Vector3(0.05f, 0.85f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1528f, 1.5068f, 1.6284f), new Quaternion(0f, 0.7071077f, 0f, 0.7071059f), new Vector3(0.05f, 0.85f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.0028f, 1.2068f, 1.9784f), new Quaternion(0.5000006f, -0.5000006f, 0.4999993f, -0.4999995f), new Vector3(0.05f, 0.65f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1528f, 1.2068f, 1.9784f), new Quaternion(0.5000006f, -0.5000006f, 0.4999993f, -0.4999995f), new Vector3(0.05f, 0.65f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.0028f, 1.5833f, 1.9784f), new Quaternion(0.5000006f, -0.5000006f, 0.4999993f, -0.4999995f), new Vector3(0.05f, 0.65f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1528f, 1.6068f, 1.9784f), new Quaternion(0.5000006f, -0.5000006f, 0.4999993f, -0.4999995f), new Vector3(0.05f, 0.65f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.0028f, 1.9068f, 1.9784f), new Quaternion(0.5000007f, -0.5000006f, 0.4999993f, -0.4999995f), new Vector3(0.05f, 0.65f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1528f, 1.9068f, 1.9784f), new Quaternion(0.5000006f, -0.5000006f, 0.4999993f, -0.4999995f), new Vector3(0.05f, 0.65f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5778f, 1.9068f, 2.3284f), new Quaternion(-9.063481E-07f, 9.164216E-07f, -0.7071069f, 0.7071067f), new Vector3(0.05f, 2.8f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5778f, 1.9068f, 1.6284f), new Quaternion(-9.063481E-07f, 9.164216E-07f, -0.7071069f, 0.7071067f), new Vector3(0.05f, 2.8f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5778f, 1.9568f, 1.9034f), new Quaternion(-9.063481E-07f, 9.164218E-07f, -0.7071068f, 0.7071068f), new Vector3(0.05f, 3f, 1.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5778f, 1.6333f, 1.9784f), new Quaternion(-9.063481E-07f, 9.164218E-07f, -0.7071068f, 0.7071068f), new Vector3(0.05f, 2.9f, 0.65f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.0008f, 1.6638f, 1.9304f), new Quaternion(-9.063481E-07f, 9.164216E-07f, -0.7071068f, 0.7071068f), new Vector3(0.01f, 0.25f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.7808f, 1.6638f, 1.9264f), new Quaternion(0.2136596f, -0.2136596f, -0.6740547f, 0.6740546f), new Vector3(0.01f, 0.25f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8004f, 4.0202f, -5.8342f), new Quaternion(0f, 1f, 0f, -1.117587E-06f), new Vector3(0.0892f, 0.02f, 1.465f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4168f, 4.0216f, -12.3369f), new Quaternion(0f, 0.7071086f, 0f, 0.707105f), new Vector3(0.0754f, 0.0229f, 2.2471f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 1.115f, -7.3396f), new Quaternion(0f, 1f, 0f, -5.364419E-07f), new Vector3(1.3f, 0.02f, 1.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 1.15f, -6.7397f), new Quaternion(0f, 1f, 0f, -5.364418E-07f), new Vector3(1.3f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 1.0899f, -7.3396f), new Quaternion(3.793209E-07f, 0.7071081f, 0.7071055f, -3.793223E-07f), new Vector3(1.3f, 1.3f, 0.0301f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 1.15f, -7.9396f), new Quaternion(0f, 1f, 0f, -5.364418E-07f), new Vector3(1.3f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5882f, 2.375f, -7.9646f), new Quaternion(0.5f, 0.5000001f, 0.5000001f, 0.5f), new Vector3(2.4001f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.563f, 2.375f, -6.7397f), new Quaternion(4.278496E-06f, 4.278496E-06f, 0.7071068f, 0.7071068f), new Vector3(2.4001f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8131f, 2.375f, -7.9396f), new Quaternion(0.7071068f, 0.7071068f, -1.407787E-05f, -1.407787E-05f), new Vector3(2.4001f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.7881f, 2.375f, -7.9646f), new Quaternion(0.5f, 0.5000001f, 0.5000001f, 0.5f), new Vector3(2.4001f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5631f, 2.375f, -7.9396f), new Quaternion(4.278496E-06f, 4.278496E-06f, 0.7071068f, 0.7071068f), new Vector3(2.4001f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8127f, 2.375f, -6.7397f), new Quaternion(0.7071068f, 0.7071068f, -1.407787E-05f, -1.407787E-05f), new Vector3(2.4001f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8131f, 3.525f, -7.3397f), new Quaternion(0.4999999f, -0.5f, 0.5f, 0.5f), new Vector3(1.0997f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5631f, 3.525f, -7.3397f), new Quaternion(0.4999999f, -0.5f, 0.5f, 0.5f), new Vector3(1.0997f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 3.525f, -7.9646f), new Quaternion(7.208445E-07f, 0.7071067f, -0.7071069f, 7.208436E-07f), new Vector3(1.0997f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1878f, 3.55f, -6.7147f), new Quaternion(7.208446E-07f, 0.7071068f, -0.7071068f, 7.208437E-07f), new Vector3(1.1997f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1881f, 2.375f, -7.9796f), new Quaternion(0.5f, 0.5000001f, 0.5000001f, 0.5f), new Vector3(2.4001f, 0.02f, 1.0998f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5481f, 2.375f, -7.3396f), new Quaternion(4.278496E-06f, 4.278496E-06f, 0.7071068f, 0.7071068f), new Vector3(2.4001f, 0.02f, 1.0998f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.8279f, 2.375f, -7.3397f), new Quaternion(0.7071068f, 0.7071068f, -1.407787E-05f, -1.407787E-05f), new Vector3(2.4001f, 0.02f, 1.0998f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.808f, 2.2011f, -7.3397f), new Quaternion(0.7071068f, 0.7071068f, -1.407787E-05f, -1.407787E-05f), new Vector3(2.0523f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.7812f, 2.2011f, -7.0826f), new Quaternion(0.7071068f, 0.7071068f, -1.407787E-05f, -1.407787E-05f), new Vector3(0.625f, 0.0835f, 0.3056f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.709f, 3.2794f, -7.3397f), new Quaternion(0.9659258f, 0.2588194f, -1.923073E-05f, -5.152869E-06f), new Vector3(0.2626f, 0.03f, 0.03f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.7339f, 1.965f, -7.8853f), new Quaternion(-0.2705946f, 0.6532866f, 0.6532783f, 0.2705972f), new Vector3(0.2292f, 0.4892f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.7339f, 2.265f, -7.8853f), new Quaternion(-0.2705946f, 0.6532866f, 0.6532783f, 0.2705972f), new Vector3(0.2292f, 0.4892f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.7339f, 2.465f, -7.8853f), new Quaternion(-0.2705946f, 0.6532866f, 0.6532783f, 0.2705972f), new Vector3(0.2292f, 0.4892f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5882f, 1.15f, -7.3396f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(1.0998f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.7881f, 1.15f, -7.3396f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(1.0998f, 0.05f, 0.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6233f, 1.6057f, -5.8402f), new Quaternion(0f, 2.041459E-06f, 0f, 1f), new Vector3(0.45f, 0.7f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5078f, 1.8161f, 0.9006f), new Quaternion(-0.1859364f, 0.700621f, -0.6822227f, 0.09555167f), new Vector3(0.1361f, 0.731f, 0.9677f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.085f, 1.5151f, 0.8451f), new Quaternion(0.6635032f, 0.7453401f, -0.04323832f, -0.04860146f), new Vector3(0.1078f, 0.6833f, 0.7759f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0875f, 1.4375f, 0.8454f), new Quaternion(0.6635032f, 0.7453401f, -0.04323832f, -0.04860146f), new Vector3(0.0611f, 0.7088f, 0.8214f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.7516f, 1.4234f, 0.3855f), new Quaternion(-0.5383376f, 0.4342096f, 0.6136336f, 0.380931f), new Vector3(0.049f, 0.0489f, 0.7223f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1581f, 1.6757f, 0.4863f), new Quaternion(-0.5001352f, 0.4913183f, 0.6132923f, 0.3637909f), new Vector3(0.1067f, 0.7665f, 0.0412f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.6462f, 1.431f, 1.204f), new Quaternion(0.3809311f, 0.6136327f, -0.4342099f, 0.5383382f), new Vector3(0.0498f, 0.0489f, 0.7395f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0624f, 1.6882f, 1.2126f), new Quaternion(-0.08590631f, 0.7066901f, 0.0967092f, 0.6955981f), new Vector3(0.1067f, 0.0412f, 0.7767f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4997f, 1.2726f, 0.547f), new Quaternion(-0.3157772f, 0.6599426f, 0.3599434f, 0.5789657f), new Vector3(0.052f, 0.089f, 0.4086f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4084f, 1.2731f, 1.2404f), new Quaternion(-0.3160021f, 0.6598029f, 0.3601997f, 0.5788429f), new Vector3(0.052f, 0.0887f, 0.4101f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5578f, 2.8871f, -4.6484f), new Quaternion(0f, 0.9993318f, 0f, -0.036553f), new Vector3(0.3486f, 0.0752f, 0.2645f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5526f, 2.9734f, -4.6571f), new Quaternion(0f, -0.03592253f, 0f, 0.9993547f), new Vector3(0.3486f, 0.0752f, 0.2645f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.499f, 2.1202f, 2.517f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.1449f, 0.1554f, 0.1449f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.918f, 2.0123f, -12.198f), new Quaternion(0f, 0.4491668f, 0f, 0.893448f), new Vector3(0.1449f, 0.1554f, 0.1449f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.485f, 1.3123f, -9.2298f), new Quaternion(0f, -0.6853183f, 0f, 0.7282437f), new Vector3(0.45f, 0.1747f, 0.25f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5169f, 1.7523f, -9.2068f), new Quaternion(0f, -0.5110886f, 0f, 0.8595281f), new Vector3(0.45f, 0.1747f, 0.25f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5019f, 1.3693f, -8.642f), new Quaternion(0f, -0.7219267f, 0f, 0.6919696f), new Vector3(0.5085f, 0.2888f, 0.415f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5355f, 3.9619f, -8.6324f), new Quaternion(0f, -0.5010235f, 0f, 0.8654337f), new Vector3(0.5085f, 0.2888f, 0.415f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5952f, 3.9619f, -9.3377f), new Quaternion(0f, -0.7825589f, 0f, 0.6225767f), new Vector3(0.5085f, 0.2888f, 0.415f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5558f, 1.7474f, -8.4542f), new Quaternion(0f, -0.0567978f, 0f, 0.9983857f), new Vector3(0.2879f, 0.1519f, 0.1359f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.3111f, 0.3082f, -5.6695f), new Quaternion(0f, 0.99857f, 0f, 0.05346115f), new Vector3(0.6f, 0.6f, 0.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.334f, 0.3082f, -8.2094f), new Quaternion(0f, 0.7814384f, 0f, 0.6239825f), new Vector3(0.6f, 0.6f, 0.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.3739f, 0.3082f, 0.6282f), new Quaternion(0f, 0.2367843f, 0f, 0.9715623f), new Vector3(0.6f, 0.6f, 0.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.3491f, 0.9079f, 0.5902f), new Quaternion(-3.540697E-07f, -0.1548121f, 2.963301E-07f, 0.9879439f), new Vector3(0.6f, 0.6f, 0.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.584f, 0.3082f, -4.6941f), new Quaternion(0f, 0.9423233f, 0f, -0.3347042f), new Vector3(0.6f, 0.6f, 0.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.7358f, 0.0743f, -6.5457f), new Quaternion(0.05228844f, 0.03714294f, -0.03368257f, 0.9973725f), new Vector3(0.3f, 0.2f, 0.4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.012f, 0.1091f, -3.994f), new Quaternion(0f, -0.8305377f, 0f, 0.5569625f), new Vector3(0.3f, 0.2f, 0.4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.1694f, 0.1978f, -2.834f), new Quaternion(0.05496059f, 0.8927765f, -0.06909005f, -0.4417647f), new Vector3(0.3945f, 0.4f, 0.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.6398f, 1.3942f, -11.0138f), new Quaternion(0f, 0.7071066f, 0f, 0.707107f), new Vector3(2.016f, 0.6382f, 2.4065f), 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.0078f, 2.8833f, -12.014f), new Quaternion(-0.1242298f, 0.6961104f, 0.1242306f, 0.6961064f), new Vector3(0.0404f, 0.0527f, 1.9641f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.0078f, 2.8833f, -8.0972f), new Quaternion(-0.1242298f, 0.6961104f, 0.1242306f, 0.6961064f), new Vector3(0.0404f, 0.0527f, 1.9641f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.9344f, 3.4174f, -12.0132f), new Quaternion(-0.4282276f, 0.5626916f, 0.4282301f, 0.5626885f), new Vector3(0.0294f, 0.033f, 0.3635f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.9343f, 3.4174f, -8.0964f), new Quaternion(-0.4282276f, 0.5626916f, 0.4282301f, 0.5626885f), new Vector3(0.0294f, 0.033f, 0.3635f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.7338f, 3.7186f, -10.0985f), new Quaternion(-0.7043193f, 0.06270131f, 0.7043235f, 0.06270094f), new Vector3(3.862f, 0.033f, 1.7477f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.7416f, 0.1528f, -5.9307f), new Quaternion(0f, 0.3242757f, 0f, 0.9459627f), new Vector3(0.5445f, 0.3171f, 0.5328f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.2625f, 0.1628f, -3.5895f), new Quaternion(0f, -0.7091429f, 0f, 0.7050648f), new Vector3(0.5445f, 0.3171f, 0.5328f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.3451f, 0.5584f, -4.7106f), new Quaternion(0.4418415f, 0.5676576f, -0.4501727f, 0.5290421f), new Vector3(0.8802f, 0.3277f, 0.8484f), RigidBodyType.DisabledImpact, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.9077f, 5.2028f, 2.7338f), new Quaternion(-0.1953232f, 0.6867366f, 0.2210164f, 0.6643745f), new Vector3(0.1434f, 0.3526f, 0.2094f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.0048f, 4.8236f, 2.728f), new Quaternion(0.2373976f, 0.6837783f, -0.2300057f, 0.6505283f), new Vector3(0.0619f, 0.7749f, 0.0589f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.5218f, 5.2807f, 2.72f), new Quaternion(0.1745377f, 0.7025328f, -0.1640213f, 0.6701353f), new Vector3(0.0392f, 0.7363f, 0.0352f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.363f, 5.6659f, 2.7219f), new Quaternion(-0.1339902f, 0.7040146f, 0.1575395f, 0.6794052f), new Vector3(0.111f, 0.1905f, 0.1255f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.1659f, 5.1195f, -3.602f), new Quaternion(-0.2976465f, 0.5804273f, 0.2399213f, 0.7189913f), new Vector3(3.3483f, 1.7481f, 0.0766f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.1049f, 5.214f, -9.586f), new Quaternion(-0.2051521f, 0.7800896f, 0.3225963f, 0.4952823f), new Vector3(1.7361f, 1.7483f, 0.0754f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.6215f, 5.164f, -7.0097f), new Quaternion(-0.2751692f, 0.6419719f, 0.2654009f, 0.6646175f), new Vector3(1.7361f, 1.7483f, 0.0754f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.7174f, 2.035f, -1.6111f), new Quaternion(0f, 0.9212312f, 0f, -0.3890156f), new Vector3(0.105f, 0.1201f, 0.105f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.2067f, 0.6988f, -8.3069f), new Quaternion(0f, -0.1450085f, 0f, 0.9894304f), new Vector3(0.105f, 0.1201f, 0.105f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.0824f, 1.7573f, -3.7011f), new Quaternion(0f, 0.8439427f, 0f, 0.5364333f), new Vector3(0.105f, 0.1201f, 0.105f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6251f, 1.0966f, -4.6068f), new Quaternion(0.03507579f, 0f, 0f, 0.9993847f), new Vector3(0.3868f, 0.0334f, 0.0611f), 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6251f, 1.8739f, -4.5522f), new Quaternion(0.03507579f, 0f, 0f, 0.9993847f), new Vector3(0.0192f, 1.5322f, 0.0183f), 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0543f, 1.7149f, -4.6194f), new Quaternion(0f, 1f, 0f, 5.066395E-07f), new Vector3(0.484f, 0.498f, 0.2322f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.6203f, 2.5175f, 2.9738f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(1.0313f, 0.7809f, 0.1699f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.005f, 0.4026f, -0.7235f), new Quaternion(0f, 0.6772254f, 0f, 0.7357758f), new Vector3(1.1569f, 0.5124f, 0.9671f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.3073f, 0.8917f, -0.742f), new Quaternion(-0.1104929f, 0.6695455f, 0.1017003f, 0.727432f), new Vector3(1.0552f, 0.6977f, 0.3586f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.8091f, 1.0206f, -2.1549f), new Quaternion(0f, -0.802618f, 0f, 0.5964935f), new Vector3(1.1106f, 0.0306f, 1.0785f), 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.5364f, 0.6739f, -5.8178f), new Quaternion(0f, 0.8660245f, 0f, 0.5000017f), new Vector3(0.3413f, 1.344f, 0.3281f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.5264f, 1.4395f, -5.8171f), new Quaternion(0f, 0.8660244f, 0f, 0.5000016f), new Vector3(0.2152f, 0.1812f, 0.1714f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.3284f, 0.6739f, 3.0512f), new Quaternion(0f, 0.8660245f, 0f, 0.5000017f), new Vector3(0.3413f, 1.344f, 0.3281f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.3184f, 1.4395f, 3.0519f), new Quaternion(0f, 0.8660244f, 0f, 0.5000016f), new Vector3(0.2152f, 0.1812f, 0.1714f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(2.5014f, 2.5245f, 25.2402f), new Quaternion(0f, -1.758337E-06f, 0f, 1f), new Vector3(0.9771f, 6.4211f, 0.9607f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.425f, 4.0714f, -10.536f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(0.231f, 0.1217f, 0.231f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.407f, 4.0941f, -10.436f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(1.7359f, 0.0213f, 1.7359f), RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact | RigidBodyType.DisabledGrenadeCollisions, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.137f, 0.5532f, 0.9396f), new Quaternion(0.8598523f, 0f, 0f, 0.510543f), new Vector3(1.4979f, 2.0514f, 0.07f), RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact | RigidBodyType.DisabledGrenadeCollisions, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4382f, 0.3371f, -4.7644f), new Quaternion(0f, 0.7071066f, 0f, 0.707107f), new Vector3(14.8816f, 0.6756f, 4.8817f), RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact | RigidBodyType.DisabledGrenadeCollisions, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4364f, 1.9388f, -1.9158f), new Quaternion(-0.06064457f, 0.7077463f, -0.03710109f, 0.7028805f), new Vector3(0.1741f, 0.1439f, 0.1819f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.4373f, 2.0233f, -1.7108f), new Quaternion(-0.1107736f, 0.7016492f, -0.08694389f, 0.6984686f), new Vector3(0.1945f, 0.0266f, 0.0303f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5805f, 2.0876f, -2.9103f), new Quaternion(0f, 0.9967048f, 0f, -0.08111512f), new Vector3(0.1741f, 0.1439f, 0.1819f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.3683f, 2.1436f, -2.9451f), new Quaternion(-0.07081147f, 0.9941862f, 0.005762872f, -0.08091016f), new Vector3(0.1945f, 0.0266f, 0.0303f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.6632f, 3.801f, -2.2806f), new Quaternion(0f, 0.9043509f, 0f, -0.4267898f), new Vector3(0.1741f, 0.1439f, 0.1819f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5265f, 3.857f, -2.4466f), new Quaternion(-0.06425013f, 0.9020656f, 0.03032153f, -0.4257113f), new Vector3(0.1945f, 0.0266f, 0.0303f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f)
			}, 
			triangles: Array.Empty<TriangleColliderDescription>(), 
			capsules: new CapsuleColliderDescription[] {
				new CapsuleColliderDescription(new Vector3(-13.484f, 1.1443f, -3.1109f), new Quaternion(0.5015215f, -0.4984739f, 0.498474f, 0.5015213f), 0.74f, 0.08f, RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact | RigidBodyType.DisabledGrenadeCollisions, SurfaceType.Default, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(-13.5006f, 1.1443f, -5.8412f), new Quaternion(0.5015215f, -0.4984739f, 0.498474f, 0.5015213f), 0.74f, 0.08f, RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact | RigidBodyType.DisabledGrenadeCollisions, SurfaceType.Default, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(-8.8708f, 0.505f, -2.1293f), new Quaternion(0f, 0.707107f, 0f, 0.7071066f), 0.54f, 0.42f, RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f)
			}, 
			hulls: new HullColliderDescription[] {
				new HullColliderDescription(new Vector3(-13.8838f, 5.4993f, 2.7403f), new Quaternion(0.00880414f, 0.7213676f, 0.009171992f, 0.6924357f), 0, 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new HullColliderDescription(new Vector3(-17.0543f, 1.3142f, -4.8963f), new Quaternion(0f, 1f, 0f, 5.066394E-07f), 1, 0, SurfaceType.Metal, HitType.Default, 1f, 1f)
			}, 
			hullData: new HullShapeData[] {
#if UNITY_MATH
				new HullShapeData { VertexCount = 15, Vertices = new NativeArray<float3>(new float3[] { new (-0.2696944f, 0.3889919f, -0.1813903f), new (-0.05692872f, 0.456517f, -0.2128776f), new (-0.005408152f, -0.0618065f, -0.1249627f), new (-0.4292057f, 0.2444258f, -0.1139779f), new (0.05673893f, -0.456517f, 0.2128776f), new (0.2696779f, -0.3889874f, 0.1813881f), new (0.1674057f, 0.4336572f, -0.2022177f), new (-0.358436f, -0.3248217f, 0.151467f), new (-0.5037109f, 0.05145115f, -0.0239923f), new (-0.4784535f, -0.1517109f, 0.07074386f), new (0.4292004f, -0.2444109f, 0.1139708f), new (-0.1674293f, -0.4335943f, 0.2021884f), new (0.5037109f, -0.0514226f, 0.02397892f), new (0.4784518f, 0.1517537f, -0.07076386f), new (0.3584258f, 0.3248767f, -0.1514926f) }, Allocator.Persistent), EdgeCount = 56, Edges = new NativeArray<HullHalfEdgeData>(new HullHalfEdgeData[] { new() { FacePtr = 0, NextPtr = 2, OriginVertexPtr = 0, PrevPtr = 4, TwinPtr = 1 }, new() { FacePtr = 12, NextPtr = 6, OriginVertexPtr = 1, PrevPtr = 22, TwinPtr = 0 }, new() { FacePtr = 0, NextPtr = 4, OriginVertexPtr = 1, PrevPtr = 0, TwinPtr = 3 }, new() { FacePtr = 4, NextPtr = 23, OriginVertexPtr = 2, PrevPtr = 24, TwinPtr = 2 }, new() { FacePtr = 0, NextPtr = 0, OriginVertexPtr = 2, PrevPtr = 2, TwinPtr = 5 }, new() { FacePtr = 1, NextPtr = 8, OriginVertexPtr = 0, PrevPtr = 7, TwinPtr = 4 }, new() { FacePtr = 12, NextPtr = 35, OriginVertexPtr = 0, PrevPtr = 1, TwinPtr = 7 }, new() { FacePtr = 1, NextPtr = 5, OriginVertexPtr = 3, PrevPtr = 8, TwinPtr = 6 }, new() { FacePtr = 1, NextPtr = 7, OriginVertexPtr = 2, PrevPtr = 5, TwinPtr = 9 }, new() { FacePtr = 7, NextPtr = 17, OriginVertexPtr = 3, PrevPtr = 34, TwinPtr = 8 }, new() { FacePtr = 2, NextPtr = 12, OriginVertexPtr = 4, PrevPtr = 14, TwinPtr = 11 }, new() { FacePtr = 9, NextPtr = 40, OriginVertexPtr = 2, PrevPtr = 39, TwinPtr = 10 }, new() { FacePtr = 2, NextPtr = 14, OriginVertexPtr = 2, PrevPtr = 10, TwinPtr = 13 }, new() { FacePtr = 5, NextPtr = 28, OriginVertexPtr = 5, PrevPtr = 26, TwinPtr = 12 }, new() { FacePtr = 2, NextPtr = 10, OriginVertexPtr = 5, PrevPtr = 12, TwinPtr = 15 }, new() { FacePtr = 12, NextPtr = 27, OriginVertexPtr = 4, PrevPtr = 41, TwinPtr = 14 }, new() { FacePtr = 3, NextPtr = 18, OriginVertexPtr = 8, PrevPtr = 20, TwinPtr = 17 }, new() { FacePtr = 7, NextPtr = 34, OriginVertexPtr = 2, PrevPtr = 9, TwinPtr = 16 }, new() { FacePtr = 3, NextPtr = 20, OriginVertexPtr = 2, PrevPtr = 16, TwinPtr = 19 }, new() { FacePtr = 6, NextPtr = 30, OriginVertexPtr = 9, PrevPtr = 32, TwinPtr = 18 }, new() { FacePtr = 3, NextPtr = 16, OriginVertexPtr = 9, PrevPtr = 18, TwinPtr = 21 }, new() { FacePtr = 12, NextPtr = 33, OriginVertexPtr = 8, PrevPtr = 35, TwinPtr = 20 }, new() { FacePtr = 12, NextPtr = 1, OriginVertexPtr = 6, PrevPtr = 55, TwinPtr = 23 }, new() { FacePtr = 4, NextPtr = 24, OriginVertexPtr = 1, PrevPtr = 3, TwinPtr = 22 }, new() { FacePtr = 4, NextPtr = 3, OriginVertexPtr = 6, PrevPtr = 23, TwinPtr = 25 }, new() { FacePtr = 14, NextPtr = 54, OriginVertexPtr = 2, PrevPtr = 51, TwinPtr = 24 }, new() { FacePtr = 5, NextPtr = 13, OriginVertexPtr = 10, PrevPtr = 28, TwinPtr = 27 }, new() { FacePtr = 12, NextPtr = 52, OriginVertexPtr = 5, PrevPtr = 15, TwinPtr = 26 }, new() { FacePtr = 5, NextPtr = 26, OriginVertexPtr = 2, PrevPtr = 13, TwinPtr = 29 }, new() { FacePtr = 13, NextPtr = 45, OriginVertexPtr = 10, PrevPtr = 53, TwinPtr = 28 }, new() { FacePtr = 6, NextPtr = 32, OriginVertexPtr = 2, PrevPtr = 19, TwinPtr = 31 }, new() { FacePtr = 8, NextPtr = 38, OriginVertexPtr = 7, PrevPtr = 36, TwinPtr = 30 }, new() { FacePtr = 6, NextPtr = 19, OriginVertexPtr = 7, PrevPtr = 30, TwinPtr = 33 }, new() { FacePtr = 12, NextPtr = 37, OriginVertexPtr = 9, PrevPtr = 21, TwinPtr = 32 }, new() { FacePtr = 7, NextPtr = 9, OriginVertexPtr = 8, PrevPtr = 17, TwinPtr = 35 }, new() { FacePtr = 12, NextPtr = 21, OriginVertexPtr = 3, PrevPtr = 6, TwinPtr = 34 }, new() { FacePtr = 8, NextPtr = 31, OriginVertexPtr = 11, PrevPtr = 38, TwinPtr = 37 }, new() { FacePtr = 12, NextPtr = 41, OriginVertexPtr = 7, PrevPtr = 33, TwinPtr = 36 }, new() { FacePtr = 8, NextPtr = 36, OriginVertexPtr = 2, PrevPtr = 31, TwinPtr = 39 }, new() { FacePtr = 9, NextPtr = 11, OriginVertexPtr = 11, PrevPtr = 40, TwinPtr = 38 }, new() { FacePtr = 9, NextPtr = 39, OriginVertexPtr = 4, PrevPtr = 11, TwinPtr = 41 }, new() { FacePtr = 12, NextPtr = 15, OriginVertexPtr = 11, PrevPtr = 37, TwinPtr = 40 }, new() { FacePtr = 12, NextPtr = 49, OriginVertexPtr = 12, PrevPtr = 52, TwinPtr = 43 }, new() { FacePtr = 10, NextPtr = 44, OriginVertexPtr = 13, PrevPtr = 46, TwinPtr = 42 }, new() { FacePtr = 10, NextPtr = 46, OriginVertexPtr = 12, PrevPtr = 43, TwinPtr = 45 }, new() { FacePtr = 13, NextPtr = 53, OriginVertexPtr = 2, PrevPtr = 29, TwinPtr = 44 }, new() { FacePtr = 10, NextPtr = 43, OriginVertexPtr = 2, PrevPtr = 44, TwinPtr = 47 }, new() { FacePtr = 11, NextPtr = 50, OriginVertexPtr = 13, PrevPtr = 48, TwinPtr = 46 }, new() { FacePtr = 11, NextPtr = 47, OriginVertexPtr = 14, PrevPtr = 50, TwinPtr = 49 }, new() { FacePtr = 12, NextPtr = 55, OriginVertexPtr = 13, PrevPtr = 42, TwinPtr = 48 }, new() { FacePtr = 11, NextPtr = 48, OriginVertexPtr = 2, PrevPtr = 47, TwinPtr = 51 }, new() { FacePtr = 14, NextPtr = 25, OriginVertexPtr = 14, PrevPtr = 54, TwinPtr = 50 }, new() { FacePtr = 12, NextPtr = 42, OriginVertexPtr = 10, PrevPtr = 27, TwinPtr = 53 }, new() { FacePtr = 13, NextPtr = 29, OriginVertexPtr = 12, PrevPtr = 45, TwinPtr = 52 }, new() { FacePtr = 14, NextPtr = 51, OriginVertexPtr = 6, PrevPtr = 25, TwinPtr = 55 }, new() { FacePtr = 12, NextPtr = 22, OriginVertexPtr = 14, PrevPtr = 49, TwinPtr = 54 } }, Allocator.Persistent), FaceCount = 15, Faces = new NativeArray<HullFaceData>(new HullFaceData[] { new() { EdgesPtr = 0, VertexCount = 3 }, new() { EdgesPtr = 5, VertexCount = 3 }, new() { EdgesPtr = 10, VertexCount = 3 }, new() { EdgesPtr = 16, VertexCount = 3 }, new() { EdgesPtr = 24, VertexCount = 3 }, new() { EdgesPtr = 26, VertexCount = 3 }, new() { EdgesPtr = 19, VertexCount = 3 }, new() { EdgesPtr = 34, VertexCount = 3 }, new() { EdgesPtr = 36, VertexCount = 3 }, new() { EdgesPtr = 39, VertexCount = 3 }, new() { EdgesPtr = 44, VertexCount = 3 }, new() { EdgesPtr = 48, VertexCount = 3 }, new() { EdgesPtr = 15, VertexCount = 14 }, new() { EdgesPtr = 53, VertexCount = 3 }, new() { EdgesPtr = 51, VertexCount = 3 } }, Allocator.Persistent), Planes = new NativeArray<PlaneData>(new PlaneData[] { new() { Normal = new (-0.08949696f, -0.175195f, -0.9804575f), Distance = 0.1338328f }, new() { Normal = new (-0.192496f, -0.2321965f, -0.9534307f), Distance = 0.1345356f }, new() { Normal = new (0.08986109f, -0.6394207f, -0.7635877f), Distance = 0.1344544f }, new() { Normal = new (-0.2726621f, -0.4342416f, -0.8585392f), Distance = 0.135599f }, new() { Normal = new (0.03011453f, -0.1642393f, -0.9859607f), Distance = 0.1331965f }, new() { Normal = new (0.19138f, -0.5802952f, -0.7916004f), Distance = 0.1337515f }, new() { Normal = new (-0.2325772f, -0.5388095f, -0.8096864f), Distance = 0.1357404f }, new() { Normal = new (-0.2581619f, -0.3246326f, -0.9099264f), Distance = 0.1351675f }, new() { Normal = new (-0.1459494f, -0.6169204f, -0.7733744f), Distance = 0.135562f }, new() { Normal = new (-0.03065124f, -0.6527308f, -0.7569696f), Distance = 0.1351017f }, new() { Normal = new (0.2668632f, -0.3798427f, -0.8857221f), Distance = 0.1327157f }, new() { Normal = new (0.2271528f, -0.2777701f, -0.9334053f), Distance = 0.1325804f }, new() { Normal = new (-4.361526E-07f, 0.4226175f, 0.9063082f), Distance = 5.838683E-07f }, new() { Normal = new (0.2542808f, -0.4876336f, -0.8351974f), Distance = 0.1331323f }, new() { Normal = new (0.1429099f, -0.2009555f, -0.96912f), Distance = 0.1327513f } }, Allocator.Persistent), FaceMaxVertexCount = 14, LocalBoundingBoxSize = new (1.009422f, 0.9150341f, 0.4277551f) },
#else
				new HullShapeData { VertexCount = 15, Vertices = new Vector3[] { new (-0.2696944f, 0.3889919f, -0.1813903f), new (-0.05692872f, 0.456517f, -0.2128776f), new (-0.005408152f, -0.0618065f, -0.1249627f), new (-0.4292057f, 0.2444258f, -0.1139779f), new (0.05673893f, -0.456517f, 0.2128776f), new (0.2696779f, -0.3889874f, 0.1813881f), new (0.1674057f, 0.4336572f, -0.2022177f), new (-0.358436f, -0.3248217f, 0.151467f), new (-0.5037109f, 0.05145115f, -0.0239923f), new (-0.4784535f, -0.1517109f, 0.07074386f), new (0.4292004f, -0.2444109f, 0.1139708f), new (-0.1674293f, -0.4335943f, 0.2021884f), new (0.5037109f, -0.0514226f, 0.02397892f), new (0.4784518f, 0.1517537f, -0.07076386f), new (0.3584258f, 0.3248767f, -0.1514926f) }, EdgeCount = 56, Edges = new HullHalfEdgeData[] { new() { FacePtr = 0, NextPtr = 2, OriginVertexPtr = 0, PrevPtr = 4, TwinPtr = 1 }, new() { FacePtr = 12, NextPtr = 6, OriginVertexPtr = 1, PrevPtr = 22, TwinPtr = 0 }, new() { FacePtr = 0, NextPtr = 4, OriginVertexPtr = 1, PrevPtr = 0, TwinPtr = 3 }, new() { FacePtr = 4, NextPtr = 23, OriginVertexPtr = 2, PrevPtr = 24, TwinPtr = 2 }, new() { FacePtr = 0, NextPtr = 0, OriginVertexPtr = 2, PrevPtr = 2, TwinPtr = 5 }, new() { FacePtr = 1, NextPtr = 8, OriginVertexPtr = 0, PrevPtr = 7, TwinPtr = 4 }, new() { FacePtr = 12, NextPtr = 35, OriginVertexPtr = 0, PrevPtr = 1, TwinPtr = 7 }, new() { FacePtr = 1, NextPtr = 5, OriginVertexPtr = 3, PrevPtr = 8, TwinPtr = 6 }, new() { FacePtr = 1, NextPtr = 7, OriginVertexPtr = 2, PrevPtr = 5, TwinPtr = 9 }, new() { FacePtr = 7, NextPtr = 17, OriginVertexPtr = 3, PrevPtr = 34, TwinPtr = 8 }, new() { FacePtr = 2, NextPtr = 12, OriginVertexPtr = 4, PrevPtr = 14, TwinPtr = 11 }, new() { FacePtr = 9, NextPtr = 40, OriginVertexPtr = 2, PrevPtr = 39, TwinPtr = 10 }, new() { FacePtr = 2, NextPtr = 14, OriginVertexPtr = 2, PrevPtr = 10, TwinPtr = 13 }, new() { FacePtr = 5, NextPtr = 28, OriginVertexPtr = 5, PrevPtr = 26, TwinPtr = 12 }, new() { FacePtr = 2, NextPtr = 10, OriginVertexPtr = 5, PrevPtr = 12, TwinPtr = 15 }, new() { FacePtr = 12, NextPtr = 27, OriginVertexPtr = 4, PrevPtr = 41, TwinPtr = 14 }, new() { FacePtr = 3, NextPtr = 18, OriginVertexPtr = 8, PrevPtr = 20, TwinPtr = 17 }, new() { FacePtr = 7, NextPtr = 34, OriginVertexPtr = 2, PrevPtr = 9, TwinPtr = 16 }, new() { FacePtr = 3, NextPtr = 20, OriginVertexPtr = 2, PrevPtr = 16, TwinPtr = 19 }, new() { FacePtr = 6, NextPtr = 30, OriginVertexPtr = 9, PrevPtr = 32, TwinPtr = 18 }, new() { FacePtr = 3, NextPtr = 16, OriginVertexPtr = 9, PrevPtr = 18, TwinPtr = 21 }, new() { FacePtr = 12, NextPtr = 33, OriginVertexPtr = 8, PrevPtr = 35, TwinPtr = 20 }, new() { FacePtr = 12, NextPtr = 1, OriginVertexPtr = 6, PrevPtr = 55, TwinPtr = 23 }, new() { FacePtr = 4, NextPtr = 24, OriginVertexPtr = 1, PrevPtr = 3, TwinPtr = 22 }, new() { FacePtr = 4, NextPtr = 3, OriginVertexPtr = 6, PrevPtr = 23, TwinPtr = 25 }, new() { FacePtr = 14, NextPtr = 54, OriginVertexPtr = 2, PrevPtr = 51, TwinPtr = 24 }, new() { FacePtr = 5, NextPtr = 13, OriginVertexPtr = 10, PrevPtr = 28, TwinPtr = 27 }, new() { FacePtr = 12, NextPtr = 52, OriginVertexPtr = 5, PrevPtr = 15, TwinPtr = 26 }, new() { FacePtr = 5, NextPtr = 26, OriginVertexPtr = 2, PrevPtr = 13, TwinPtr = 29 }, new() { FacePtr = 13, NextPtr = 45, OriginVertexPtr = 10, PrevPtr = 53, TwinPtr = 28 }, new() { FacePtr = 6, NextPtr = 32, OriginVertexPtr = 2, PrevPtr = 19, TwinPtr = 31 }, new() { FacePtr = 8, NextPtr = 38, OriginVertexPtr = 7, PrevPtr = 36, TwinPtr = 30 }, new() { FacePtr = 6, NextPtr = 19, OriginVertexPtr = 7, PrevPtr = 30, TwinPtr = 33 }, new() { FacePtr = 12, NextPtr = 37, OriginVertexPtr = 9, PrevPtr = 21, TwinPtr = 32 }, new() { FacePtr = 7, NextPtr = 9, OriginVertexPtr = 8, PrevPtr = 17, TwinPtr = 35 }, new() { FacePtr = 12, NextPtr = 21, OriginVertexPtr = 3, PrevPtr = 6, TwinPtr = 34 }, new() { FacePtr = 8, NextPtr = 31, OriginVertexPtr = 11, PrevPtr = 38, TwinPtr = 37 }, new() { FacePtr = 12, NextPtr = 41, OriginVertexPtr = 7, PrevPtr = 33, TwinPtr = 36 }, new() { FacePtr = 8, NextPtr = 36, OriginVertexPtr = 2, PrevPtr = 31, TwinPtr = 39 }, new() { FacePtr = 9, NextPtr = 11, OriginVertexPtr = 11, PrevPtr = 40, TwinPtr = 38 }, new() { FacePtr = 9, NextPtr = 39, OriginVertexPtr = 4, PrevPtr = 11, TwinPtr = 41 }, new() { FacePtr = 12, NextPtr = 15, OriginVertexPtr = 11, PrevPtr = 37, TwinPtr = 40 }, new() { FacePtr = 12, NextPtr = 49, OriginVertexPtr = 12, PrevPtr = 52, TwinPtr = 43 }, new() { FacePtr = 10, NextPtr = 44, OriginVertexPtr = 13, PrevPtr = 46, TwinPtr = 42 }, new() { FacePtr = 10, NextPtr = 46, OriginVertexPtr = 12, PrevPtr = 43, TwinPtr = 45 }, new() { FacePtr = 13, NextPtr = 53, OriginVertexPtr = 2, PrevPtr = 29, TwinPtr = 44 }, new() { FacePtr = 10, NextPtr = 43, OriginVertexPtr = 2, PrevPtr = 44, TwinPtr = 47 }, new() { FacePtr = 11, NextPtr = 50, OriginVertexPtr = 13, PrevPtr = 48, TwinPtr = 46 }, new() { FacePtr = 11, NextPtr = 47, OriginVertexPtr = 14, PrevPtr = 50, TwinPtr = 49 }, new() { FacePtr = 12, NextPtr = 55, OriginVertexPtr = 13, PrevPtr = 42, TwinPtr = 48 }, new() { FacePtr = 11, NextPtr = 48, OriginVertexPtr = 2, PrevPtr = 47, TwinPtr = 51 }, new() { FacePtr = 14, NextPtr = 25, OriginVertexPtr = 14, PrevPtr = 54, TwinPtr = 50 }, new() { FacePtr = 12, NextPtr = 42, OriginVertexPtr = 10, PrevPtr = 27, TwinPtr = 53 }, new() { FacePtr = 13, NextPtr = 29, OriginVertexPtr = 12, PrevPtr = 45, TwinPtr = 52 }, new() { FacePtr = 14, NextPtr = 51, OriginVertexPtr = 6, PrevPtr = 25, TwinPtr = 55 }, new() { FacePtr = 12, NextPtr = 22, OriginVertexPtr = 14, PrevPtr = 49, TwinPtr = 54 } }, FaceCount = 15, Faces = new HullFaceData[] { new() { EdgesPtr = 0, VertexCount = 3 }, new() { EdgesPtr = 5, VertexCount = 3 }, new() { EdgesPtr = 10, VertexCount = 3 }, new() { EdgesPtr = 16, VertexCount = 3 }, new() { EdgesPtr = 24, VertexCount = 3 }, new() { EdgesPtr = 26, VertexCount = 3 }, new() { EdgesPtr = 19, VertexCount = 3 }, new() { EdgesPtr = 34, VertexCount = 3 }, new() { EdgesPtr = 36, VertexCount = 3 }, new() { EdgesPtr = 39, VertexCount = 3 }, new() { EdgesPtr = 44, VertexCount = 3 }, new() { EdgesPtr = 48, VertexCount = 3 }, new() { EdgesPtr = 15, VertexCount = 14 }, new() { EdgesPtr = 53, VertexCount = 3 }, new() { EdgesPtr = 51, VertexCount = 3 } }, Planes = new PlaneData[] { new() { Normal = new (-0.08949696f, -0.175195f, -0.9804575f), Distance = 0.1338328f }, new() { Normal = new (-0.192496f, -0.2321965f, -0.9534307f), Distance = 0.1345356f }, new() { Normal = new (0.08986109f, -0.6394207f, -0.7635877f), Distance = 0.1344544f }, new() { Normal = new (-0.2726621f, -0.4342416f, -0.8585392f), Distance = 0.135599f }, new() { Normal = new (0.03011453f, -0.1642393f, -0.9859607f), Distance = 0.1331965f }, new() { Normal = new (0.19138f, -0.5802952f, -0.7916004f), Distance = 0.1337515f }, new() { Normal = new (-0.2325772f, -0.5388095f, -0.8096864f), Distance = 0.1357404f }, new() { Normal = new (-0.2581619f, -0.3246326f, -0.9099264f), Distance = 0.1351675f }, new() { Normal = new (-0.1459494f, -0.6169204f, -0.7733744f), Distance = 0.135562f }, new() { Normal = new (-0.03065124f, -0.6527308f, -0.7569696f), Distance = 0.1351017f }, new() { Normal = new (0.2668632f, -0.3798427f, -0.8857221f), Distance = 0.1327157f }, new() { Normal = new (0.2271528f, -0.2777701f, -0.9334053f), Distance = 0.1325804f }, new() { Normal = new (-4.361526E-07f, 0.4226175f, 0.9063082f), Distance = 5.838683E-07f }, new() { Normal = new (0.2542808f, -0.4876336f, -0.8351974f), Distance = 0.1331323f }, new() { Normal = new (0.1429099f, -0.2009555f, -0.96912f), Distance = 0.1327513f } }, FaceMaxVertexCount = 14, LocalBoundingBoxSize = new (1.009422f, 0.9150341f, 0.4277551f) },
#endif
#if UNITY_MATH
				new HullShapeData { VertexCount = 14, Vertices = new NativeArray<float3>(new float3[] { new (-0.1705244f, 0.2392914f, 0.2660305f), new (0f, 0.2392914f, 0.3885902f), new (0.1705245f, 0.2392914f, 0.2660306f), new (-3.106324E-10f, -0.2392914f, 0.2920158f), new (0.2056924f, -0.2392914f, -0.3885902f), new (-0.2408359f, 0.2392914f, -0.3885902f), new (0.2408359f, 0.2392914f, -0.3885902f), new (0.145641f, -0.2392914f, 0.1694561f), new (-0.145641f, -0.2392914f, 0.1694561f), new (0.19956f, -0.2392914f, -0.03217292f), new (-0.2408359f, 0.2392914f, 0.06440151f), new (-0.2056924f, -0.2392914f, -0.3885902f), new (0.2408359f, 0.2392914f, 0.06440157f), new (-0.19956f, -0.2392914f, -0.0321729f) }, Allocator.Persistent), EdgeCount = 54, Edges = new NativeArray<HullHalfEdgeData>(new HullHalfEdgeData[] { new() { FacePtr = 0, NextPtr = 2, OriginVertexPtr = 0, PrevPtr = 19, TwinPtr = 1 }, new() { FacePtr = 13, NextPtr = 52, OriginVertexPtr = 1, PrevPtr = 7, TwinPtr = 0 }, new() { FacePtr = 0, NextPtr = 36, OriginVertexPtr = 1, PrevPtr = 0, TwinPtr = 3 }, new() { FacePtr = 1, NextPtr = 6, OriginVertexPtr = 2, PrevPtr = 4, TwinPtr = 2 }, new() { FacePtr = 1, NextPtr = 3, OriginVertexPtr = 3, PrevPtr = 6, TwinPtr = 5 }, new() { FacePtr = 5, NextPtr = 20, OriginVertexPtr = 2, PrevPtr = 22, TwinPtr = 4 }, new() { FacePtr = 1, NextPtr = 4, OriginVertexPtr = 1, PrevPtr = 3, TwinPtr = 7 }, new() { FacePtr = 13, NextPtr = 1, OriginVertexPtr = 3, PrevPtr = 52, TwinPtr = 6 }, new() { FacePtr = 2, NextPtr = 10, OriginVertexPtr = 5, PrevPtr = 29, TwinPtr = 9 }, new() { FacePtr = 0, NextPtr = 27, OriginVertexPtr = 6, PrevPtr = 34, TwinPtr = 8 }, new() { FacePtr = 2, NextPtr = 48, OriginVertexPtr = 6, PrevPtr = 8, TwinPtr = 11 }, new() { FacePtr = 10, NextPtr = 35, OriginVertexPtr = 4, PrevPtr = 43, TwinPtr = 10 }, new() { FacePtr = 3, NextPtr = 21, OriginVertexPtr = 9, PrevPtr = 38, TwinPtr = 13 }, new() { FacePtr = 7, NextPtr = 32, OriginVertexPtr = 7, PrevPtr = 30, TwinPtr = 12 }, new() { FacePtr = 4, NextPtr = 16, OriginVertexPtr = 10, PrevPtr = 18, TwinPtr = 15 }, new() { FacePtr = 11, NextPtr = 46, OriginVertexPtr = 8, PrevPtr = 44, TwinPtr = 14 }, new() { FacePtr = 4, NextPtr = 18, OriginVertexPtr = 8, PrevPtr = 14, TwinPtr = 17 }, new() { FacePtr = 14, NextPtr = 41, OriginVertexPtr = 0, PrevPtr = 53, TwinPtr = 16 }, new() { FacePtr = 4, NextPtr = 14, OriginVertexPtr = 0, PrevPtr = 16, TwinPtr = 19 }, new() { FacePtr = 0, NextPtr = 0, OriginVertexPtr = 10, PrevPtr = 27, TwinPtr = 18 }, new() { FacePtr = 5, NextPtr = 22, OriginVertexPtr = 3, PrevPtr = 5, TwinPtr = 21 }, new() { FacePtr = 3, NextPtr = 40, OriginVertexPtr = 7, PrevPtr = 12, TwinPtr = 20 }, new() { FacePtr = 5, NextPtr = 5, OriginVertexPtr = 7, PrevPtr = 20, TwinPtr = 23 }, new() { FacePtr = 8, NextPtr = 31, OriginVertexPtr = 2, PrevPtr = 37, TwinPtr = 22 }, new() { FacePtr = 6, NextPtr = 26, OriginVertexPtr = 11, PrevPtr = 28, TwinPtr = 25 }, new() { FacePtr = 12, NextPtr = 50, OriginVertexPtr = 10, PrevPtr = 47, TwinPtr = 24 }, new() { FacePtr = 6, NextPtr = 28, OriginVertexPtr = 10, PrevPtr = 24, TwinPtr = 27 }, new() { FacePtr = 0, NextPtr = 19, OriginVertexPtr = 5, PrevPtr = 9, TwinPtr = 26 }, new() { FacePtr = 6, NextPtr = 24, OriginVertexPtr = 5, PrevPtr = 26, TwinPtr = 29 }, new() { FacePtr = 2, NextPtr = 8, OriginVertexPtr = 11, PrevPtr = 48, TwinPtr = 28 }, new() { FacePtr = 7, NextPtr = 13, OriginVertexPtr = 12, PrevPtr = 32, TwinPtr = 31 }, new() { FacePtr = 8, NextPtr = 37, OriginVertexPtr = 7, PrevPtr = 23, TwinPtr = 30 }, new() { FacePtr = 7, NextPtr = 30, OriginVertexPtr = 9, PrevPtr = 13, TwinPtr = 33 }, new() { FacePtr = 9, NextPtr = 39, OriginVertexPtr = 12, PrevPtr = 42, TwinPtr = 32 }, new() { FacePtr = 0, NextPtr = 9, OriginVertexPtr = 12, PrevPtr = 36, TwinPtr = 35 }, new() { FacePtr = 10, NextPtr = 43, OriginVertexPtr = 6, PrevPtr = 11, TwinPtr = 34 }, new() { FacePtr = 0, NextPtr = 34, OriginVertexPtr = 2, PrevPtr = 2, TwinPtr = 37 }, new() { FacePtr = 8, NextPtr = 23, OriginVertexPtr = 12, PrevPtr = 31, TwinPtr = 36 }, new() { FacePtr = 3, NextPtr = 12, OriginVertexPtr = 4, PrevPtr = 49, TwinPtr = 39 }, new() { FacePtr = 9, NextPtr = 42, OriginVertexPtr = 9, PrevPtr = 33, TwinPtr = 38 }, new() { FacePtr = 3, NextPtr = 45, OriginVertexPtr = 3, PrevPtr = 21, TwinPtr = 41 }, new() { FacePtr = 14, NextPtr = 53, OriginVertexPtr = 8, PrevPtr = 17, TwinPtr = 40 }, new() { FacePtr = 9, NextPtr = 33, OriginVertexPtr = 4, PrevPtr = 39, TwinPtr = 43 }, new() { FacePtr = 10, NextPtr = 11, OriginVertexPtr = 12, PrevPtr = 35, TwinPtr = 42 }, new() { FacePtr = 11, NextPtr = 15, OriginVertexPtr = 13, PrevPtr = 46, TwinPtr = 45 }, new() { FacePtr = 3, NextPtr = 51, OriginVertexPtr = 8, PrevPtr = 40, TwinPtr = 44 }, new() { FacePtr = 11, NextPtr = 44, OriginVertexPtr = 10, PrevPtr = 15, TwinPtr = 47 }, new() { FacePtr = 12, NextPtr = 25, OriginVertexPtr = 13, PrevPtr = 50, TwinPtr = 46 }, new() { FacePtr = 2, NextPtr = 29, OriginVertexPtr = 4, PrevPtr = 10, TwinPtr = 49 }, new() { FacePtr = 3, NextPtr = 38, OriginVertexPtr = 11, PrevPtr = 51, TwinPtr = 48 }, new() { FacePtr = 12, NextPtr = 47, OriginVertexPtr = 11, PrevPtr = 25, TwinPtr = 51 }, new() { FacePtr = 3, NextPtr = 49, OriginVertexPtr = 13, PrevPtr = 45, TwinPtr = 50 }, new() { FacePtr = 13, NextPtr = 7, OriginVertexPtr = 0, PrevPtr = 1, TwinPtr = 53 }, new() { FacePtr = 14, NextPtr = 17, OriginVertexPtr = 3, PrevPtr = 41, TwinPtr = 52 } }, Allocator.Persistent), FaceCount = 15, Faces = new NativeArray<HullFaceData>(new HullFaceData[] { new() { EdgesPtr = 9, VertexCount = 7 }, new() { EdgesPtr = 4, VertexCount = 3 }, new() { EdgesPtr = 10, VertexCount = 4 }, new() { EdgesPtr = 40, VertexCount = 7 }, new() { EdgesPtr = 14, VertexCount = 3 }, new() { EdgesPtr = 20, VertexCount = 3 }, new() { EdgesPtr = 24, VertexCount = 3 }, new() { EdgesPtr = 30, VertexCount = 3 }, new() { EdgesPtr = 37, VertexCount = 3 }, new() { EdgesPtr = 33, VertexCount = 3 }, new() { EdgesPtr = 43, VertexCount = 3 }, new() { EdgesPtr = 44, VertexCount = 3 }, new() { EdgesPtr = 47, VertexCount = 3 }, new() { EdgesPtr = 7, VertexCount = 3 }, new() { EdgesPtr = 53, VertexCount = 3 } }, Allocator.Persistent), Planes = new NativeArray<PlaneData>(new PlaneData[] { new() { Normal = new (0f, 1f, 0f), Distance = 0.2392914f }, new() { Normal = new (0.57594f, -0.1617043f, 0.8013394f), Distance = 0.2726982f }, new() { Normal = new (0f, 0f, -1f), Distance = 0.3885902f }, new() { Normal = new (0f, -1f, 0f), Distance = 0.2392914f }, new() { Normal = new (-0.9379954f, -0.1147756f, 0.3270951f), Distance = 0.2195036f }, new() { Normal = new (0.6328022f, -0.1846452f, 0.7519757f), Distance = 0.2637728f }, new() { Normal = new (-0.9973147f, -0.07323541f, 0f), Distance = 0.2226646f }, new() { Normal = new (0.9573124f, -0.1342237f, 0.2560019f), Distance = 0.2149236f }, new() { Normal = new (0.9379954f, -0.1147757f, 0.327095f), Distance = 0.2195036f }, new() { Normal = new (0.9958532f, -0.08934596f, 0.01713406f), Distance = 0.219561f }, new() { Normal = new (0.9973147f, -0.07323541f, 0f), Distance = 0.2226646f }, new() { Normal = new (-0.9573124f, -0.1342237f, 0.2560019f), Distance = 0.2149236f }, new() { Normal = new (-0.9958532f, -0.08934595f, 0.01713406f), Distance = 0.219561f }, new() { Normal = new (-0.5759401f, -0.1617043f, 0.8013393f), Distance = 0.2726981f }, new() { Normal = new (-0.6328021f, -0.1846451f, 0.7519758f), Distance = 0.2637728f } }, Allocator.Persistent), FaceMaxVertexCount = 7, LocalBoundingBoxSize = new (0.4836719f, 0.4805828f, 0.7791803f) },
#else
				new HullShapeData { VertexCount = 14, Vertices = new Vector3[] { new (-0.1705244f, 0.2392914f, 0.2660305f), new (0f, 0.2392914f, 0.3885902f), new (0.1705245f, 0.2392914f, 0.2660306f), new (-3.106324E-10f, -0.2392914f, 0.2920158f), new (0.2056924f, -0.2392914f, -0.3885902f), new (-0.2408359f, 0.2392914f, -0.3885902f), new (0.2408359f, 0.2392914f, -0.3885902f), new (0.145641f, -0.2392914f, 0.1694561f), new (-0.145641f, -0.2392914f, 0.1694561f), new (0.19956f, -0.2392914f, -0.03217292f), new (-0.2408359f, 0.2392914f, 0.06440151f), new (-0.2056924f, -0.2392914f, -0.3885902f), new (0.2408359f, 0.2392914f, 0.06440157f), new (-0.19956f, -0.2392914f, -0.0321729f) }, EdgeCount = 54, Edges = new HullHalfEdgeData[] { new() { FacePtr = 0, NextPtr = 2, OriginVertexPtr = 0, PrevPtr = 19, TwinPtr = 1 }, new() { FacePtr = 13, NextPtr = 52, OriginVertexPtr = 1, PrevPtr = 7, TwinPtr = 0 }, new() { FacePtr = 0, NextPtr = 36, OriginVertexPtr = 1, PrevPtr = 0, TwinPtr = 3 }, new() { FacePtr = 1, NextPtr = 6, OriginVertexPtr = 2, PrevPtr = 4, TwinPtr = 2 }, new() { FacePtr = 1, NextPtr = 3, OriginVertexPtr = 3, PrevPtr = 6, TwinPtr = 5 }, new() { FacePtr = 5, NextPtr = 20, OriginVertexPtr = 2, PrevPtr = 22, TwinPtr = 4 }, new() { FacePtr = 1, NextPtr = 4, OriginVertexPtr = 1, PrevPtr = 3, TwinPtr = 7 }, new() { FacePtr = 13, NextPtr = 1, OriginVertexPtr = 3, PrevPtr = 52, TwinPtr = 6 }, new() { FacePtr = 2, NextPtr = 10, OriginVertexPtr = 5, PrevPtr = 29, TwinPtr = 9 }, new() { FacePtr = 0, NextPtr = 27, OriginVertexPtr = 6, PrevPtr = 34, TwinPtr = 8 }, new() { FacePtr = 2, NextPtr = 48, OriginVertexPtr = 6, PrevPtr = 8, TwinPtr = 11 }, new() { FacePtr = 10, NextPtr = 35, OriginVertexPtr = 4, PrevPtr = 43, TwinPtr = 10 }, new() { FacePtr = 3, NextPtr = 21, OriginVertexPtr = 9, PrevPtr = 38, TwinPtr = 13 }, new() { FacePtr = 7, NextPtr = 32, OriginVertexPtr = 7, PrevPtr = 30, TwinPtr = 12 }, new() { FacePtr = 4, NextPtr = 16, OriginVertexPtr = 10, PrevPtr = 18, TwinPtr = 15 }, new() { FacePtr = 11, NextPtr = 46, OriginVertexPtr = 8, PrevPtr = 44, TwinPtr = 14 }, new() { FacePtr = 4, NextPtr = 18, OriginVertexPtr = 8, PrevPtr = 14, TwinPtr = 17 }, new() { FacePtr = 14, NextPtr = 41, OriginVertexPtr = 0, PrevPtr = 53, TwinPtr = 16 }, new() { FacePtr = 4, NextPtr = 14, OriginVertexPtr = 0, PrevPtr = 16, TwinPtr = 19 }, new() { FacePtr = 0, NextPtr = 0, OriginVertexPtr = 10, PrevPtr = 27, TwinPtr = 18 }, new() { FacePtr = 5, NextPtr = 22, OriginVertexPtr = 3, PrevPtr = 5, TwinPtr = 21 }, new() { FacePtr = 3, NextPtr = 40, OriginVertexPtr = 7, PrevPtr = 12, TwinPtr = 20 }, new() { FacePtr = 5, NextPtr = 5, OriginVertexPtr = 7, PrevPtr = 20, TwinPtr = 23 }, new() { FacePtr = 8, NextPtr = 31, OriginVertexPtr = 2, PrevPtr = 37, TwinPtr = 22 }, new() { FacePtr = 6, NextPtr = 26, OriginVertexPtr = 11, PrevPtr = 28, TwinPtr = 25 }, new() { FacePtr = 12, NextPtr = 50, OriginVertexPtr = 10, PrevPtr = 47, TwinPtr = 24 }, new() { FacePtr = 6, NextPtr = 28, OriginVertexPtr = 10, PrevPtr = 24, TwinPtr = 27 }, new() { FacePtr = 0, NextPtr = 19, OriginVertexPtr = 5, PrevPtr = 9, TwinPtr = 26 }, new() { FacePtr = 6, NextPtr = 24, OriginVertexPtr = 5, PrevPtr = 26, TwinPtr = 29 }, new() { FacePtr = 2, NextPtr = 8, OriginVertexPtr = 11, PrevPtr = 48, TwinPtr = 28 }, new() { FacePtr = 7, NextPtr = 13, OriginVertexPtr = 12, PrevPtr = 32, TwinPtr = 31 }, new() { FacePtr = 8, NextPtr = 37, OriginVertexPtr = 7, PrevPtr = 23, TwinPtr = 30 }, new() { FacePtr = 7, NextPtr = 30, OriginVertexPtr = 9, PrevPtr = 13, TwinPtr = 33 }, new() { FacePtr = 9, NextPtr = 39, OriginVertexPtr = 12, PrevPtr = 42, TwinPtr = 32 }, new() { FacePtr = 0, NextPtr = 9, OriginVertexPtr = 12, PrevPtr = 36, TwinPtr = 35 }, new() { FacePtr = 10, NextPtr = 43, OriginVertexPtr = 6, PrevPtr = 11, TwinPtr = 34 }, new() { FacePtr = 0, NextPtr = 34, OriginVertexPtr = 2, PrevPtr = 2, TwinPtr = 37 }, new() { FacePtr = 8, NextPtr = 23, OriginVertexPtr = 12, PrevPtr = 31, TwinPtr = 36 }, new() { FacePtr = 3, NextPtr = 12, OriginVertexPtr = 4, PrevPtr = 49, TwinPtr = 39 }, new() { FacePtr = 9, NextPtr = 42, OriginVertexPtr = 9, PrevPtr = 33, TwinPtr = 38 }, new() { FacePtr = 3, NextPtr = 45, OriginVertexPtr = 3, PrevPtr = 21, TwinPtr = 41 }, new() { FacePtr = 14, NextPtr = 53, OriginVertexPtr = 8, PrevPtr = 17, TwinPtr = 40 }, new() { FacePtr = 9, NextPtr = 33, OriginVertexPtr = 4, PrevPtr = 39, TwinPtr = 43 }, new() { FacePtr = 10, NextPtr = 11, OriginVertexPtr = 12, PrevPtr = 35, TwinPtr = 42 }, new() { FacePtr = 11, NextPtr = 15, OriginVertexPtr = 13, PrevPtr = 46, TwinPtr = 45 }, new() { FacePtr = 3, NextPtr = 51, OriginVertexPtr = 8, PrevPtr = 40, TwinPtr = 44 }, new() { FacePtr = 11, NextPtr = 44, OriginVertexPtr = 10, PrevPtr = 15, TwinPtr = 47 }, new() { FacePtr = 12, NextPtr = 25, OriginVertexPtr = 13, PrevPtr = 50, TwinPtr = 46 }, new() { FacePtr = 2, NextPtr = 29, OriginVertexPtr = 4, PrevPtr = 10, TwinPtr = 49 }, new() { FacePtr = 3, NextPtr = 38, OriginVertexPtr = 11, PrevPtr = 51, TwinPtr = 48 }, new() { FacePtr = 12, NextPtr = 47, OriginVertexPtr = 11, PrevPtr = 25, TwinPtr = 51 }, new() { FacePtr = 3, NextPtr = 49, OriginVertexPtr = 13, PrevPtr = 45, TwinPtr = 50 }, new() { FacePtr = 13, NextPtr = 7, OriginVertexPtr = 0, PrevPtr = 1, TwinPtr = 53 }, new() { FacePtr = 14, NextPtr = 17, OriginVertexPtr = 3, PrevPtr = 41, TwinPtr = 52 } }, FaceCount = 15, Faces = new HullFaceData[] { new() { EdgesPtr = 9, VertexCount = 7 }, new() { EdgesPtr = 4, VertexCount = 3 }, new() { EdgesPtr = 10, VertexCount = 4 }, new() { EdgesPtr = 40, VertexCount = 7 }, new() { EdgesPtr = 14, VertexCount = 3 }, new() { EdgesPtr = 20, VertexCount = 3 }, new() { EdgesPtr = 24, VertexCount = 3 }, new() { EdgesPtr = 30, VertexCount = 3 }, new() { EdgesPtr = 37, VertexCount = 3 }, new() { EdgesPtr = 33, VertexCount = 3 }, new() { EdgesPtr = 43, VertexCount = 3 }, new() { EdgesPtr = 44, VertexCount = 3 }, new() { EdgesPtr = 47, VertexCount = 3 }, new() { EdgesPtr = 7, VertexCount = 3 }, new() { EdgesPtr = 53, VertexCount = 3 } }, Planes = new PlaneData[] { new() { Normal = new (0f, 1f, 0f), Distance = 0.2392914f }, new() { Normal = new (0.57594f, -0.1617043f, 0.8013394f), Distance = 0.2726982f }, new() { Normal = new (0f, 0f, -1f), Distance = 0.3885902f }, new() { Normal = new (0f, -1f, 0f), Distance = 0.2392914f }, new() { Normal = new (-0.9379954f, -0.1147756f, 0.3270951f), Distance = 0.2195036f }, new() { Normal = new (0.6328022f, -0.1846452f, 0.7519757f), Distance = 0.2637728f }, new() { Normal = new (-0.9973147f, -0.07323541f, 0f), Distance = 0.2226646f }, new() { Normal = new (0.9573124f, -0.1342237f, 0.2560019f), Distance = 0.2149236f }, new() { Normal = new (0.9379954f, -0.1147757f, 0.327095f), Distance = 0.2195036f }, new() { Normal = new (0.9958532f, -0.08934596f, 0.01713406f), Distance = 0.219561f }, new() { Normal = new (0.9973147f, -0.07323541f, 0f), Distance = 0.2226646f }, new() { Normal = new (-0.9573124f, -0.1342237f, 0.2560019f), Distance = 0.2149236f }, new() { Normal = new (-0.9958532f, -0.08934595f, 0.01713406f), Distance = 0.219561f }, new() { Normal = new (-0.5759401f, -0.1617043f, 0.8013393f), Distance = 0.2726981f }, new() { Normal = new (-0.6328021f, -0.1846451f, 0.7519758f), Distance = 0.2637728f } }, FaceMaxVertexCount = 7, LocalBoundingBoxSize = new (0.4836719f, 0.4805828f, 0.7791803f) },
#endif
			});
	}
}
