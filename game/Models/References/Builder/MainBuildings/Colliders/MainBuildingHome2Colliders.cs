using System;
using System.Numerics;
using Models.Physics.Types;
using Models.HitBox.Types;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.References.Colliders;

#if UNITY_MATH
using Unity.Collections;
using Unity.Mathematics;
#endif

namespace Models.References.Builder.Colliders
{
	/// <auto-generated>This is autogenerated class from <see cref="CollidersFile"/></auto-generated>
	public static class MainBuildingHome2Colliders
	{
		public static readonly CollidersDescription Main = new CollidersDescription(
			boxes: new BoxColliderDescription[] {
				new BoxColliderDescription(new Vector3(-12.059f, 3.303f, 2.9475f), new Quaternion(3.793217E-07f, 0.7071068f, 0.7071068f, -4.003952E-07f), new Vector3(0.25f, 9.8996f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.059f, 1.6383f, -1.8773f), new Quaternion(0f, 1f, 0f, -5.960465E-07f), new Vector3(0.25f, 3.0794f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.059f, 1.6383f, 7.7726f), new Quaternion(0f, 1f, 0f, -5.960465E-07f), new Vector3(0.25f, 3.0794f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.0589f, 3.303f, 12.147f), new Quaternion(3.793217E-07f, 0.7071068f, 0.7071068f, -4.003951E-07f), new Vector3(0.25f, 6.0015f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.059f, 1.6383f, 9.2712f), new Quaternion(0f, 1f, 0f, -5.960465E-07f), new Vector3(0.25f, 3.0794f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.059f, 1.6383f, 15.0227f), new Quaternion(0f, 1f, 0f, -5.960465E-07f), new Vector3(0.25f, 3.0794f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.2505f, 1.1176f, -11.733f), new Quaternion(-0.499999f, 0.5000009f, 0.5000007f, 0.4999993f), new Vector3(0.4536f, 0.3657f, 0.028f), RigidBodyType.DisabledShots, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.4821f, 1.276f, -11.733f), new Quaternion(-0.1830131f, 0.6830136f, 0.1830137f, 0.6830115f), new Vector3(0.4536f, 0.3436f, 0.0209f), RigidBodyType.DisabledShots, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.8191f, 1.4368f, -17.7518f), new Quaternion(-0.478475f, 0.5206359f, -0.5206359f, -0.478475f), new Vector3(0.2254f, 2.78f, 2.3923f), RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact | RigidBodyType.DisabledGrenadeCollisions, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4573f, 0.2151f, -17.656f), new Quaternion(-0.478475f, 0.5206359f, -0.5206359f, -0.4784749f), new Vector3(0.8599f, 0.0908f, 0.0511f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1776f, 0.2151f, -17.886f), new Quaternion(-0.478475f, 0.5206359f, -0.5206359f, -0.4784749f), new Vector3(0.8599f, 0.0908f, 0.0511f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4573f, 1.0475f, -17.6564f), new Quaternion(0f, -0.04216092f, 0f, 0.9991109f), new Vector3(0.08f, 1.6263f, 0.08f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1776f, 1.0475f, -17.8864f), new Quaternion(0f, -0.04216092f, 0f, 0.9991109f), new Vector3(0.08f, 1.6263f, 0.08f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.8174f, 0.7611f, -17.7711f), new Quaternion(0f, -0.04216092f, 0f, 0.9991109f), new Vector3(2.74f, 0.0623f, 0.0384f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.8174f, 0.3017f, -17.7711f), new Quaternion(0f, -0.04216092f, 0f, 0.9991109f), new Vector3(2.74f, 0.0623f, 0.0384f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.8206f, 0.5362f, -17.7713f), new Quaternion(0f, -0.04216092f, 0f, 0.9991109f), new Vector3(2.6583f, 0.33f, 0.0384f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.8167f, 1.723f, -17.771f), new Quaternion(0.03103423f, -0.04214058f, 0.001309596f, 0.9986288f), new Vector3(2.6029f, 1.7548f, 0.0384f), RigidBodyType.DisabledCollisions | RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.6842f, 1.6706f, 10.4957f), new Quaternion(0f, 0.7071069f, 0f, 0.7071067f), new Vector3(0.18f, 3.1485f, 0.3181f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.6842f, 1.6706f, 14.0299f), new Quaternion(0f, 0.7071069f, 0f, 0.7071067f), new Vector3(0.18f, 3.1485f, 0.3181f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.6689f, 1.6706f, 14.0301f), new Quaternion(0f, 0.7071069f, 0f, 0.7071067f), new Vector3(0.18f, 3.1485f, 0.3181f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.6689f, 1.6706f, 10.4957f), new Quaternion(0f, 0.7071069f, 0f, 0.7071067f), new Vector3(0.18f, 3.1485f, 0.3181f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.6696f, 1.0556f, 10.3077f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(0.1848f, 0.5319f, 0.2931f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.1949f, 0.1658f, 11.3879f), new Quaternion(0.09229591f, 0.7010575f, -0.09229595f, 0.7010574f), new Vector3(0.6445f, 0.096f, 0.8934f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.1949f, 0.1658f, 13.1379f), new Quaternion(0.09229591f, 0.7010575f, -0.09229595f, 0.7010574f), new Vector3(0.6445f, 0.096f, 0.8934f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1672f, 0.1156f, 12.2629f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(3.1816f, 0.0357f, 5.201f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.174f, 0.976f, -2.6248f), new Quaternion(0f, 0.847073f, 0f, 0.5314766f), new Vector3(0.4879f, 0.2658f, 0.4612f), 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.79f, 0.9761f, -2.5946f), new Quaternion(0f, -0.1097618f, 0f, 0.993958f), new Vector3(0.4879f, 0.2658f, 0.4612f), 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4061f, 0.4637f, -2.2941f), new Quaternion(0f, -0.7031789f, 0f, 0.711013f), new Vector3(0.3085f, 0.7393f, 0.6602f), RigidBodyType.DisabledCollisions, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.1084f, 0.4637f, -2.7054f), new Quaternion(0f, -0.829596f, 0f, 0.5583643f), new Vector3(0.3085f, 0.7393f, 0.6602f), RigidBodyType.DisabledCollisions, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.8165f, 0.4637f, -2.759f), new Quaternion(0f, -0.7323837f, 0f, 0.6808922f), new Vector3(0.3085f, 0.7393f, 0.6602f), RigidBodyType.DisabledCollisions, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.849f, 0.9016f, -17.5956f), new Quaternion(0f, 1f, 0f, -3.218651E-06f), new Vector3(4.5312f, 0.1106f, 1.6172f), 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.849f, 0.4752f, -17.6613f), new Quaternion(0f, 1f, 0f, -3.218651E-06f), new Vector3(3.2586f, 0.7422f, 1.1077f), RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.3049f, 0.5567f, -18.0705f), new Quaternion(0f, -0.006022622f, 0f, 0.9999819f), new Vector3(0.5847f, 0.1375f, 0.6328f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.2863f, 0.9894f, -18.3302f), new Quaternion(0.6552448f, -0.004549527f, 0.003946363f, 0.7553926f), new Vector3(0.5847f, 0.1659f, 0.8135f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.5344f, 0.5567f, -17.7846f), new Quaternion(0f, -0.8555973f, 0f, 0.5176421f), new Vector3(0.5847f, 0.1375f, 0.6328f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.3113f, 0.9894f, -17.6502f), new Quaternion(-0.3391885f, 0.6463236f, -0.5606359f, -0.3910302f), new Vector3(0.5847f, 0.1659f, 0.8135f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.4491f, 0.4715f, -9.6088f), new Quaternion(0f, -0.8660247f, 0f, 0.5000013f), new Vector3(0.545f, 0.8142f, 0.5121f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.2443f, 1.022f, -9.4906f), new Quaternion(0.05255898f, 0.8612267f, 0.09103449f, -0.4972312f), new Vector3(0.545f, 0.4883f, 0.039f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.3016f, 4.7038f, -17.5884f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(1.2317f, 1.1795f, 1.2174f), 0, SurfaceType.Concrete, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.3016f, 4.7038f, -9.8384f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(1.2317f, 1.1795f, 1.2174f), 0, SurfaceType.Concrete, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9354f, 0.5986f, 15.9979f), new Quaternion(0f, 1f, 0f, 1.221895E-06f), new Vector3(2.9969f, 1f, 0.8f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9354f, 2.369f, 15.9979f), new Quaternion(0f, 1f, 0f, 1.221895E-06f), new Vector3(2.9969f, 0.4753f, 0.8f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9354f, 1.585f, 16.3799f), new Quaternion(0f, 1f, 0f, 1.221895E-06f), new Vector3(2.9969f, 1.1487f, 0.036f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.31f, 0.6002f, 15.9947f), new Quaternion(0f, 1f, 0f, 1.311302E-06f), new Vector3(2.2423f, 0.9852f, 0.7936f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.56f, 0.6002f, 15.9947f), new Quaternion(0f, 1f, 0f, 1.311302E-06f), new Vector3(2.2423f, 0.9852f, 0.7936f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.6331f, 0.8358f, 11.7212f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(1.1218f, 1.3085f, 0.4061f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.582f, 0.8358f, 13.3443f), new Quaternion(0f, 0.7295256f, 0f, 0.6839535f), new Vector3(1.1218f, 1.3085f, 0.4061f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.542f, 4.841f, 6.6481f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(0.3369f, 0.8833f, 0.3262f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.309f, 4.841f, -2.3189f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(0.3369f, 0.8833f, 0.3262f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.3981f, 1.8986f, -2.7821f), new Quaternion(0f, 0.9703456f, 0f, -0.2417218f), new Vector3(0.1326f, 4.2387f, 0.1265f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.3982f, 1.8986f, 9.742f), new Quaternion(0f, 0.8466097f, 0f, 0.5322142f), new Vector3(0.1326f, 4.2387f, 0.1265f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.8442f, 1.8986f, 8.7844f), new Quaternion(0f, 0.9926891f, 0f, -0.1206999f), new Vector3(0.1326f, 4.2387f, 0.1265f), RigidBodyType.DisabledShots, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(18.3412f, 2.2913f, 7.987f), new Quaternion(0f, 0.7071064f, 0f, 0.7071072f), new Vector3(0.1015f, 4.5803f, 0.1024f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(40.2832f, 2.2913f, 8.811f), new Quaternion(0f, 0.7071064f, 0f, 0.7071072f), new Vector3(0.1015f, 4.5803f, 0.1024f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.2432f, 2.2913f, -3.329f), new Quaternion(0f, 0.7071064f, 0f, 0.7071072f), new Vector3(0.1015f, 4.5803f, 0.1024f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(11.0132f, 2.2913f, 9.9769f), new Quaternion(0f, 0.7071064f, 0f, 0.7071072f), new Vector3(0.1015f, 4.5803f, 0.1024f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(40.5204f, 2.2913f, -7.569f), new Quaternion(0f, 0.7071064f, 0f, 0.7071072f), new Vector3(0.1015f, 4.5803f, 0.1024f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.1118f, 1.1891f, -10.3365f), new Quaternion(0f, -0.4869621f, 0f, 0.8734232f), new Vector3(0.3f, 0.17f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.15f, 1.3203f, -10.3129f), new Quaternion(0.1901736f, -0.475279f, 0.1060281f, 0.8524682f), new Vector3(0.33f, 0.01f, 0.02f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 1.3986f, -5.2522f), new Quaternion(-0.5f, 0.5000009f, 0.5000004f, 0.4999987f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 1.3986f, -6.8523f), new Quaternion(-0.5f, 0.5000009f, 0.5000004f, 0.4999987f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 2.7486f, -6.0523f), new Quaternion(0.7071069f, -0.7071066f, -6.322026E-07f, 9.377673E-07f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.034f, 1.3986f, -5.2522f), new Quaternion(-0.5f, 0.5000009f, 0.5000004f, 0.4999987f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.034f, 1.3986f, -6.8522f), new Quaternion(-0.5f, 0.5000009f, 0.5000004f, 0.4999987f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.034f, 2.7486f, -6.0523f), new Quaternion(0.7071069f, -0.7071066f, -6.322026E-07f, 9.377673E-07f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.184f, 1.3986f, -3.1522f), new Quaternion(-0.707107f, 1.612941E-06f, 2.420344E-07f, 0.7071066f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.584f, 1.3986f, -3.1521f), new Quaternion(-0.707107f, 1.612941E-06f, 2.420344E-07f, 0.7071066f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.3839f, 2.7486f, -3.1522f), new Quaternion(-0.5000007f, 0.5000006f, -0.4999996f, 0.4999993f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.084f, 1.3986f, 2.2487f), new Quaternion(-0.5000007f, -0.4999984f, -0.4999996f, 0.5000013f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.084f, 1.3986f, 3.8487f), new Quaternion(-0.5000007f, -0.4999984f, -0.4999996f, 0.5000013f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.084f, 2.7486f, 3.0488f), new Quaternion(-1.148502E-06f, 1.454067E-06f, -0.707107f, 0.7071066f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.5839f, 1.3986f, -3.1522f), new Quaternion(1.148192E-06f, 0.7071066f, 0.707107f, -2.519098E-06f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.1839f, 1.3986f, -3.1522f), new Quaternion(1.148192E-06f, 0.7071066f, 0.707107f, -2.519098E-06f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3839f, 2.7486f, -3.1522f), new Quaternion(0.499999f, -0.4999986f, -0.5000013f, 0.5000011f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.2479f, 5.699f, -3.1522f), new Quaternion(1.148192E-06f, 0.7071066f, 0.707107f, -2.519098E-06f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.8479f, 5.699f, -3.1523f), new Quaternion(1.148192E-06f, 0.7071066f, 0.707107f, -2.519098E-06f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0479f, 7.049f, -3.1522f), new Quaternion(0.499999f, -0.4999986f, -0.5000013f, 0.5000011f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.5202f, 5.6988f, 7.2036f), new Quaternion(1.148192E-06f, 0.7071066f, 0.707107f, -2.519098E-06f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.1202f, 5.6988f, 7.2035f), new Quaternion(1.148192E-06f, 0.7071066f, 0.707107f, -2.519098E-06f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.3202f, 7.0488f, 7.2036f), new Quaternion(0.499999f, -0.4999986f, -0.5000013f, 0.5000011f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.2478f, 5.6986f, -8.9524f), new Quaternion(1.148192E-06f, 0.7071066f, 0.707107f, -2.519098E-06f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.8478f, 5.6986f, -8.9524f), new Quaternion(1.148192E-06f, 0.7071066f, 0.707107f, -2.519098E-06f), new Vector3(0.1f, 0.3f, 2.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0478f, 7.0486f, -8.9524f), new Quaternion(0.499999f, -0.4999986f, -0.5000013f, 0.5000011f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 2.0986f, -8.0773f), new Quaternion(-7.375699E-08f, -1.256074E-14f, -0.7071068f, 0.7071068f), new Vector3(4f, 0.3f, 0.85f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 2.0986f, -4.0273f), new Quaternion(-7.375699E-08f, -1.256074E-14f, -0.7071068f, 0.7071068f), new Vector3(4f, 0.3f, 0.85f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.359f, 2.1951f, -8.4973f), new Quaternion(0f, 1f, 0f, 2.980233E-07f), new Vector3(2f, 2.15f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.034f, 2.0986f, -7.7523f), new Quaternion(-3.161013E-07f, -1.053671E-07f, -0.7071066f, 0.7071069f), new Vector3(4f, 0.3f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.0341f, 2.0986f, -4.3523f), new Quaternion(-2.001975E-07f, -2.95028E-07f, -0.7071068f, 0.7071067f), new Vector3(4f, 0.3f, 1.4999f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.359f, 1.0486f, -8.2511f), new Quaternion(-7.375699E-08f, -5.024295E-15f, -0.7071068f, 0.7071068f), new Vector3(0.1f, 4.7f, 0.6025f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.059f, 0.5486f, -8.2511f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.1f, 0.9f, 0.6025f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.159f, 0.5486f, -8.2511f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.1f, 0.9f, 0.6025f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.909f, 0.464f, -8.2543f), new Quaternion(0f, 1f, 0f, 2.980233E-08f), new Vector3(1.3125f, 0.3f, 0.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7341f, 1.9735f, -5.1523f), new Quaternion(0.5000006f, -0.4999994f, 0.4999997f, 0.5000004f), new Vector3(0.1f, 0.3f, 3.5498f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.984f, 5.4735f, -5.1522f), new Quaternion(0.5000007f, -0.4999994f, 0.4999996f, 0.5000004f), new Vector3(0.1f, 0.3f, 5.3498f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 2.8486f, -6.0523f), new Quaternion(0.7071069f, -0.7071066f, -6.743495E-07f, 9.693774E-07f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.984f, 2.8486f, -6.0522f), new Quaternion(0.7071069f, -0.7071066f, -6.743495E-07f, 9.693774E-07f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.984f, 8.0983f, -6.0522f), new Quaternion(0.7071069f, -0.7071066f, -6.743495E-07f, 9.693774E-07f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 1.9735f, -4.5021f), new Quaternion(0.5000006f, -0.4999994f, 0.4999997f, 0.5000004f), new Vector3(0.1f, 0.3f, 3.5498f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2092f, 8.099f, 2.0966f), new Quaternion(-2.022982E-06f, -3.750942E-06f, -0.7070833f, 0.7071303f), new Vector3(0.1f, 0.15f, 5.5002f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.1089f, 8.0983f, -6.0528f), new Quaternion(-0.7070833f, 0.7071303f, 1.517236E-06f, 3.96167E-06f), new Vector3(0.1f, 0.15f, 5.5002f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.0206f, 5.3115f, 10.9804f), new Quaternion(-2.698594E-06f, -3.576279E-06f, 3.232354E-05f, 1f), new Vector3(0.4f, 0.1746f, 0.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.3206f, 5.301f, 10.9692f), new Quaternion(-2.698594E-06f, -3.576279E-06f, 3.232354E-05f, 1f), new Vector3(1f, 0.0044f, 0.6358f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.11f, 4.7489f, 9.2779f), new Quaternion(-3.232352E-05f, 1f, -2.698572E-06f, 3.933907E-06f), new Vector3(0.07f, 0.7f, 2.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.3394f, 4.749f, 7.7284f), new Quaternion(-3.232352E-05f, 1f, -2.698572E-06f, 3.933907E-06f), new Vector3(1.6115f, 0.7f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.384f, 2.0986f, -8.6523f), new Quaternion(-0.4999999f, 0.5000002f, -0.4999998f, 0.5000001f), new Vector3(4f, 0.3f, 5.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3841f, 2.0986f, -3.4523f), new Quaternion(-0.4999999f, 0.5000002f, -0.4999998f, 0.5000001f), new Vector3(4f, 0.3f, 5.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.359f, 0.5486f, -8.2773f), new Quaternion(0f, 1f, 0f, -2.980232E-08f), new Vector3(4.7f, 0.9f, 0.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3589f, 2.1951f, -8.4813f), new Quaternion(0f, 1f, 0f, 2.980232E-07f), new Vector3(1.8f, 1.95f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.809f, 0.8033f, -8.2543f), new Quaternion(0f, 1f, 0f, 2.980234E-08f), new Vector3(1.3125f, 0.3f, 0.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.659f, 0.5486f, -8.2511f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.1f, 0.9f, 0.6025f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.809f, 0.464f, -8.2543f), new Quaternion(0f, 1f, 0f, 2.980234E-08f), new Vector3(1.3125f, 0.3f, 0.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.359f, 2.1951f, -8.4923f), new Quaternion(0f, 1f, 0f, -2.980232E-08f), new Vector3(1.894f, 2.036f, 0.0473f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 0.1486f, -7.2773f), new Quaternion(0.7071069f, -0.7071067f, -6.743495E-07f, 9.377673E-07f), new Vector3(0.1f, 0.3f, 0.75f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7341f, 1.9735f, -6.9523f), new Quaternion(0.5000006f, -0.4999994f, 0.4999997f, 0.5000004f), new Vector3(0.1f, 0.3f, 3.5498f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.984f, 5.4735f, -6.9523f), new Quaternion(0.5000006f, -0.4999994f, 0.4999997f, 0.5000004f), new Vector3(0.1f, 0.3f, 5.3498f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 0.1486f, -4.8272f), new Quaternion(0.7071069f, -0.7071066f, -6.743496E-07f, 9.693774E-07f), new Vector3(0.1f, 0.3f, 0.7501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 2.0986f, -7.2851f), new Quaternion(0.7071071f, -0.7071066f, -5.900558E-07f, 8.850838E-07f), new Vector3(4f, 0.1f, 0.7344f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7341f, 1.9735f, -7.6023f), new Quaternion(0.5000006f, -0.4999994f, 0.4999997f, 0.5000004f), new Vector3(0.1f, 0.3f, 3.5498f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 2.0986f, -4.7922f), new Quaternion(0.7071071f, -0.7071066f, -5.900558E-07f, 8.850838E-07f), new Vector3(4f, 0.1f, 0.7344f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.3205f, 4.8488f, 11.0302f), new Quaternion(-3.232352E-05f, 1f, -2.698594E-06f, 3.33786E-06f), new Vector3(1.3738f, 0.9f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.3206f, 5.3115f, 10.5908f), new Quaternion(-2.698594E-06f, -3.576279E-06f, 3.232354E-05f, 1f), new Vector3(1f, 0.1746f, 0.121f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.3206f, 5.5988f, 11.2851f), new Quaternion(-2.698578E-06f, -3.695489E-06f, 3.232354E-05f, 1f), new Vector3(0.05f, 0.4f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.3207f, 5.7738f, 11.1601f), new Quaternion(-0.7071086f, -2.545677E-05f, 2.034644E-05f, 0.707105f), new Vector3(0.05f, 0.2f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5752f, 4.7252f, 8.4616f), new Quaternion(-2.845809E-05f, 0.9238786f, -0.3826859f, 1.602547E-05f), new Vector3(1f, 0.8945f, 0.0211f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5648f, 4.4512f, 9.5015f), new Quaternion(-3.232353E-05f, 1f, -2.698573E-06f, 3.933907E-06f), new Vector3(1.0397f, 0.0044f, 1.8223f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.7693f, 4.749f, 9.2756f), new Quaternion(-3.232352E-05f, 1f, -2.698572E-06f, 3.933907E-06f), new Vector3(0.4714f, 0.7f, 2.3946f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.3394f, 4.749f, 10.9605f), new Quaternion(-3.232353E-05f, 1f, -2.698571E-06f, 3.933907E-06f), new Vector3(1.6115f, 0.7f, 0.9751f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.4247f, 4.9026f, 8.3913f), new Quaternion(2.067536E-05f, -0.7071081f, 2.473647E-05f, 0.7071055f), new Vector3(0.5146f, 0.2085f, 0.121f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.8091f, -0.0514f, -6.0523f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(5.5f, 0.3f, 12.4501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.9591f, 4.2486f, -6.0523f), new Quaternion(0f, 0.7071069f, 0f, 0.7071067f), new Vector3(5.5f, 0.3f, 12.7501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0662f, 8.5485f, -5.9025f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(5.7996f, 0.3f, 14.9647f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.5097f, 9.5485f, -6.0522f), new Quaternion(0f, 0.7071069f, 0f, 0.7071067f), new Vector3(6.0999f, 0.9f, 11.2516f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.342f, 8.5485f, -6.0521f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(6.1f, 0.3f, 3.5869f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.4272f, 8.5485f, -13.7023f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(9.2004f, 0.3f, 1.7573f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0662f, 8.5485f, -13.5525f), new Quaternion(0f, 0.7071069f, 0f, 0.7071067f), new Vector3(9.5f, 0.3f, 14.9647f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.9449f, 8.5485f, -18.8025f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(1f, 0.3f, 16.7219f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.0839f, 8.5485f, -11.1525f), new Quaternion(0f, 6.556511E-07f, 0f, 1f), new Vector3(1f, 0.3f, 16.3001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.9449f, 8.8985f, -10.6525f), new Quaternion(0f, 5.960464E-07f, 0f, 1f), new Vector3(18.2217f, 0.4f, 16.8002f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-7.5659f, 3.8819f, -13.8024f), new Quaternion(-5.348739E-08f, 5.677597E-07f, -0.08211614f, 0.9966228f), new Vector3(3.3055f, 0.1092f, 10.0003f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.209f, 9.5985f, -15.8026f), new Quaternion(0f, 6.556511E-07f, 0f, 1f), new Vector3(3.25f, 1f, 2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.2934f, 9.5435f, 6.0003f), new Quaternion(0f, -0.7071052f, 0f, 0.7071084f), new Vector3(1.6984f, 0.8901f, 1.2632f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.209f, 10.2677f, -15.8026f), new Quaternion(0f, 5.960465E-07f, 0f, 1f), new Vector3(2.8164f, 0.3384f, 1.5981f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.2934f, 10.1392f, 6.0003f), new Quaternion(0f, -0.7071053f, 0f, 0.7071083f), new Vector3(1.4718f, 0.3012f, 1.0094f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.084f, 9.4735f, -13.8026f), new Quaternion(0f, 6.556511E-07f, 0f, 1f), new Vector3(1.4999f, 0.75f, 2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.5566f, 9.4323f, 6.0873f), new Quaternion(0f, -0.7071052f, 0f, 0.7071084f), new Vector3(0.7838f, 0.6676f, 1.2632f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6439f, 6.6618f, 12.351f), new Quaternion(0f, 1.579523E-06f, 0f, 1f), new Vector3(1.2499f, 0.75f, 1.3873f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.5817f, 6.6618f, 2.5903f), new Quaternion(0f, 0.7071071f, 0f, 0.7071065f), new Vector3(1.2499f, 0.75f, 1.3873f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.433f, 7.205f, 4.5216f), new Quaternion(0f, -0.7071066f, 0f, 0.7071069f), new Vector3(1f, 1.8808f, 1.8872f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.433f, 7.205f, 5.7196f), new Quaternion(0f, 0.7071072f, 0f, 0.7071064f), new Vector3(1f, 1.8808f, 1.8872f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.0817f, 6.6618f, 2.5903f), new Quaternion(0f, 0.7071071f, 0f, 0.7071065f), new Vector3(1.2499f, 0.75f, 1.3873f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6439f, 6.8237f, 12.351f), new Quaternion(0f, 1.758337E-06f, 0f, 1f), new Vector3(0.9737f, 0.5843f, 1.0807f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.5817f, 6.8237f, 2.5903f), new Quaternion(0f, 0.7071071f, 0f, 0.7071065f), new Vector3(0.9737f, 0.5843f, 1.0807f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.0817f, 6.8237f, 2.5903f), new Quaternion(0f, 0.7071071f, 0f, 0.7071065f), new Vector3(0.9737f, 0.5843f, 1.0807f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9379f, 6.5368f, 12.3509f), new Quaternion(0f, 1.579523E-06f, 0f, 1f), new Vector3(1.4999f, 0.5f, 0.6936f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.582f, 6.5368f, 1.9136f), new Quaternion(0f, 0.7071071f, 0f, 0.7071065f), new Vector3(1.4999f, 0.5f, 0.6936f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.5038f, 6.7656f, 3.6166f), new Quaternion(-0.5000001f, 0.5000002f, -0.4999999f, 0.4999999f), new Vector3(1f, 0.5f, 0.6936f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.3622f, 6.7656f, 6.6246f), new Quaternion(0.4999995f, -0.4999999f, -0.5000005f, 0.5000002f), new Vector3(1f, 0.5f, 0.6936f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.507f, 7.5156f, 3.8666f), new Quaternion(-1.220761E-07f, 0.707107f, -1.518784E-07f, 0.7071066f), new Vector3(1f, 0.5f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.359f, 7.5156f, 6.3746f), new Quaternion(1.518784E-07f, -0.7071064f, -1.220762E-07f, 0.7071072f), new Vector3(1f, 0.5f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.082f, 6.5368f, 1.9136f), new Quaternion(0f, 0.7071071f, 0f, 0.7071065f), new Vector3(1.4999f, 0.5f, 0.6936f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.9706f, 8.8985f, -6.0273f), new Quaternion(0f, 5.960465E-07f, 0f, 1f), new Vector3(1.8298f, 0.4f, 7.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9089f, 8.5485f, -2.5024f), new Quaternion(0f, -0.7071053f, 0f, 0.7071084f), new Vector3(1f, 0.3f, 10.6502f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.734f, 8.5485f, 4.8477f), new Quaternion(0f, 1f, 0f, -3.695488E-06f), new Vector3(1f, 0.3f, 13.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.9338f, 8.5485f, 4.8479f), new Quaternion(0f, 1f, 0f, -3.695488E-06f), new Vector3(1f, 0.3f, 13.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.8339f, 8.5485f, 12.1977f), new Quaternion(0f, 0.7071102f, 0f, 0.7071034f), new Vector3(1f, 0.3f, 9.1998f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.8339f, 8.8985f, 5.0978f), new Quaternion(0f, 0.7071103f, 0f, 0.7071033f), new Vector3(14.7003f, 0.4f, 8.6999f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.7342f, 5.8368f, 6.6974f), new Quaternion(0f, 1f, 0f, -3.516674E-06f), new Vector3(1f, 0.3f, 19.4004f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.7339f, 5.8368f, 16.8977f), new Quaternion(0f, 0.7071104f, 0f, 0.7071032f), new Vector3(1f, 0.3f, 10.9995f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.7336f, 5.8368f, 14.5478f), new Quaternion(0f, 6.645919E-06f, 0f, 1f), new Vector3(1f, 0.3f, 5.6999f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.6086f, 6.1368f, 14.4226f), new Quaternion(0f, 6.645919E-06f, 0f, 1f), new Vector3(0.75f, 0.3f, 5.4495f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.5591f, 5.8368f, -3.2777f), new Quaternion(0f, 0.7071103f, 0f, 0.7071033f), new Vector3(0.55f, 0.3f, 8.6499f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.8589f, 6.1368f, 6.9475f), new Quaternion(0f, 0.7071104f, 0f, 0.7071032f), new Vector3(20.3993f, 0.3f, 10.7497f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.7849f, 8.5484f, -2.5021f), new Quaternion(0f, 0.7071103f, 0f, 0.7071033f), new Vector3(1f, 0.3f, 2.7011f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.5096f, 9.8484f, -2.5022f), new Quaternion(0f, 0.7071103f, 0f, 0.7071033f), new Vector3(1f, 0.3f, 13.2517f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.5099f, 9.8484f, -9.6022f), new Quaternion(0f, 0.7071103f, 0f, 0.7071033f), new Vector3(1f, 0.3f, 13.2517f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.5097f, 10.1484f, -6.0523f), new Quaternion(0f, 0.7071103f, 0f, 0.7071033f), new Vector3(7.6001f, 0.3f, 12.7522f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-31.2207f, 8.5485f, -9.6021f), new Quaternion(0f, 0.7071103f, 0f, 0.7071033f), new Vector3(1f, 0.3f, 1.8297f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-31.6355f, 8.5485f, -6.0521f), new Quaternion(0f, 1f, 0f, -3.516674E-06f), new Vector3(1f, 0.3f, 6.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-31.6355f, 9.8485f, -6.0522f), new Quaternion(0f, 1f, 0f, -3.516674E-06f), new Vector3(1f, 0.3f, 6.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.3839f, 9.8485f, -6.0522f), new Quaternion(0f, 1f, 0f, -3.516674E-06f), new Vector3(1f, 0.3f, 6.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.8059f, 8.5485f, -14.2023f), new Quaternion(0f, 1f, 0f, -3.516674E-06f), new Vector3(1f, 0.3f, 10.2004f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2339f, 5.8368f, 6.6975f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(19.4004f, 0.3f, 9.9995f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.2556f, 4.2486f, -13.8025f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(10f, 0.3f, 20.0998f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.0847f, -0.0514f, -6.2022f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(5.8001f, 0.3f, 6.1012f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3589f, 0.464f, -8.2543f), new Quaternion(0f, 1f, 0f, 2.980233E-08f), new Vector3(1.4063f, 0.3f, 0.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.0658f, 4.9025f, 8.3914f), new Quaternion(2.067535E-05f, -0.7071081f, 2.473646E-05f, 0.7071055f), new Vector3(0.5146f, 0.2085f, 0.1531f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.8008f, 4.8005f, 8.3913f), new Quaternion(2.067534E-05f, -0.7071081f, 2.473645E-05f, 0.7071055f), new Vector3(0.5146f, 0.0044f, 0.6313f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.7532f, 4.9025f, 8.1122f), new Quaternion(2.067535E-05f, -0.7071081f, 2.473646E-05f, 0.7071055f), new Vector3(0.0434f, 0.2085f, 0.778f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.4847f, 4.7985f, 8.3914f), new Quaternion(-0.353535f, -0.6123859f, -0.3535328f, 0.6123816f), new Vector3(0.6015f, 0.2085f, 0.121f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.1874f, 5.6525f, 8.3913f), new Quaternion(2.067535E-05f, -0.7071081f, 2.473646E-05f, 0.7071055f), new Vector3(0.5146f, 0.2085f, 0.1531f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.9838f, 7.7488f, 7.2035f), new Quaternion(0.5000162f, -0.499986f, -0.4999816f, 0.5000161f), new Vector3(1.3f, 0.3f, 6.9003f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.766f, 7.799f, -3.1523f), new Quaternion(-0.4999831f, 0.5000176f, -0.5000149f, 0.4999846f), new Vector3(1.1998f, 0.3f, 2.4634f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.3022f, 5.7486f, 7.2035f), new Quaternion(0.5000162f, -0.4999861f, -0.4999816f, 0.5000161f), new Vector3(2.7f, 0.3f, 2.2631f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.8161f, 5.7989f, -3.1522f), new Quaternion(-0.4999831f, 0.5000176f, -0.5000149f, 0.4999846f), new Vector3(2.8002f, 0.3f, 0.5633f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.0591f, 7.0485f, -3.1523f), new Quaternion(-0.4999831f, 0.5000176f, -0.5000149f, 0.4999846f), new Vector3(2.7f, 0.3f, 2.3502f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.559f, 0.5486f, -8.2511f), new Quaternion(0f, -2.384186E-07f, 0f, 1f), new Vector3(0.1f, 0.9f, 0.6025f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.3842f, 6.3987f, 1.8756f), new Quaternion(0.7071074f, -0.7071062f, 1.79124E-06f, -1.685873E-06f), new Vector3(4.0002f, 0.3f, 10.3558f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5846f, 2.1684f, -3.17f), new Quaternion(0.5000024f, -0.5000021f, -0.4999982f, 0.4999973f), new Vector3(4.4614f, 0.2647f, 4.5014f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.2847f, 2.0983f, -2.87f), new Quaternion(0.5000024f, -0.5000021f, -0.4999982f, 0.4999973f), new Vector3(3.9995f, 0.2647f, 1.7014f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3839f, 3.6488f, -3.17f), new Quaternion(0.5000024f, -0.5000021f, -0.4999982f, 0.4999973f), new Vector3(1.5004f, 0.2647f, 1.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.3339f, 2.2488f, -3.1698f), new Quaternion(0.5000024f, -0.5000021f, -0.4999982f, 0.4999973f), new Vector3(4.3004f, 0.2647f, 2.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.9166f, 6.5493f, -3.1524f), new Quaternion(0.5000024f, -0.5000021f, -0.4999982f, 0.4999973f), new Vector3(4.3005f, 0.3f, 5.8374f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.0671f, 6.2486f, -8.9521f), new Quaternion(0.5000024f, -0.5000021f, -0.4999982f, 0.4999973f), new Vector3(4.2999f, 0.3f, 1.5368f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.1516f, 6.3986f, -8.9524f), new Quaternion(0.5000024f, -0.5000021f, -0.4999982f, 0.4999973f), new Vector3(3.9999f, 0.3f, 4.3076f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.5091f, 2.0986f, -8.952f), new Quaternion(0.5000024f, -0.5000021f, -0.4999982f, 0.4999973f), new Vector3(4f, 0.3f, 1.85f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.8434f, 4.7461f, 8.3914f), new Quaternion(2.067535E-05f, -0.7071081f, 2.473646E-05f, 0.7071055f), new Vector3(0.6015f, 0.1043f, 0.5975f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.9478f, 5.749f, -3.1523f), new Quaternion(0.707109f, -2.191643E-05f, -2.410807E-05f, 0.7071046f), new Vector3(0.1f, 0.3f, 2.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2474f, 6.3863f, 11.4491f), new Quaternion(-2.698589E-06f, -3.635884E-06f, 3.232354E-05f, 1f), new Vector3(2.5779f, 1.8181f, 0.0473f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.7532f, 4.9025f, 8.6704f), new Quaternion(2.067535E-05f, -0.7071081f, 2.473646E-05f, 0.7071055f), new Vector3(0.0434f, 0.2085f, 0.778f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.3206f, 5.3488f, 11.3129f), new Quaternion(-2.698589E-06f, -3.576279E-06f, 3.232353E-05f, 1f), new Vector3(1f, 0.1f, 0.2347f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.034f, 2.8486f, -6.0523f), new Quaternion(0.7071069f, -0.7071066f, -6.743496E-07f, 9.693774E-07f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.3839f, 2.8486f, -3.1522f), new Quaternion(-0.5000013f, 0.5000013f, -0.499999f, 0.4999985f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3839f, 2.8486f, -3.1522f), new Quaternion(-0.5000013f, 0.5000013f, -0.499999f, 0.4999985f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0479f, 7.149f, -3.1523f), new Quaternion(-0.5000013f, 0.5000013f, -0.499999f, 0.4999985f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.084f, 2.8486f, 3.0488f), new Quaternion(-2.697401E-06f, 2.107344E-06f, -0.7071074f, 0.7071062f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.034f, 1.4986f, -6.9523f), new Quaternion(0.5000006f, -0.4999994f, 0.4999997f, 0.5000004f), new Vector3(0.1f, 0.3f, 2.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.4839f, 1.4986f, -3.1522f), new Quaternion(1.685873E-06f, -0.7071066f, 0.707107f, 1.896608E-06f), new Vector3(0.1f, 0.3f, 2.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.4839f, 1.4986f, -3.1522f), new Quaternion(1.685873E-06f, -0.7071066f, 0.707107f, 1.896608E-06f), new Vector3(0.1f, 0.3f, 2.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.1478f, 5.799f, -3.1523f), new Quaternion(1.685873E-06f, -0.7071066f, 0.707107f, 1.896608E-06f), new Vector3(0.1f, 0.3f, 2.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.6206f, 5.3116f, 10.9803f), new Quaternion(-2.698594E-06f, -3.576279E-06f, 3.232354E-05f, 1f), new Vector3(0.4f, 0.1746f, 0.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5751f, 4.749f, 10.4378f), new Quaternion(-2.476435E-05f, 0.7071036f, 2.094802E-05f, 0.70711f), new Vector3(0.07f, 0.7f, 1.14f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.034f, 3.4986f, -6.0522f), new Quaternion(-8.429368E-08f, -8.429376E-08f, -0.7071069f, 0.7071066f), new Vector3(1.2f, 0.3f, 1.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.0401f, 4.749f, 9.2778f), new Quaternion(-3.232352E-05f, 1f, -2.698572E-06f, 3.933907E-06f), new Vector3(0.07f, 0.7f, 2.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.002f, 5.7489f, 7.2036f), new Quaternion(0.5000162f, -0.4999861f, -0.4999816f, 0.5000161f), new Vector3(2.7f, 0.3f, 2.9367f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3589f, 0.8033f, -8.2543f), new Quaternion(0f, 1f, 0f, 2.980233E-08f), new Vector3(1.4063f, 0.3f, 0.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.909f, 0.8033f, -8.2543f), new Quaternion(0f, 1f, 0f, 2.980233E-08f), new Vector3(1.3125f, 0.3f, 0.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.034f, 1.4986f, -5.1522f), new Quaternion(0.5000006f, -0.4999994f, 0.4999997f, 0.5000004f), new Vector3(0.1f, 0.3f, 2.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.284f, 1.4986f, -3.1522f), new Quaternion(1.685873E-06f, -0.7071066f, 0.707107f, 1.896608E-06f), new Vector3(0.1f, 0.3f, 2.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.284f, 1.4986f, -3.1522f), new Quaternion(1.685873E-06f, -0.7071066f, 0.707107f, 1.896608E-06f), new Vector3(0.1f, 0.3f, 2.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.9479f, 5.799f, -3.1523f), new Quaternion(1.685873E-06f, -0.7071066f, 0.707107f, 1.896608E-06f), new Vector3(0.1f, 0.3f, 2.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.734f, 3.4579f, -6.0531f), new Quaternion(0.7071071f, -0.7071066f, -7.586431E-07f, 1.137965E-06f), new Vector3(1.2166f, 0.1f, 1.7888f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-31.0581f, 5.4735f, -6.0534f), new Quaternion(0.707107f, -0.7071066f, -7.58643E-07f, 1.137965E-06f), new Vector3(5.3498f, 0.0479f, 1.7889f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7341f, 3.7984f, -6.0522f), new Quaternion(0.707107f, -0.7071066f, -6.743495E-07f, 1.043134E-06f), new Vector3(0.1f, 0.3f, 3.2002f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5751f, 4.7489f, 8.1178f), new Quaternion(-2.476435E-05f, 0.7071036f, 2.094802E-05f, 0.70711f), new Vector3(0.07f, 0.7f, 1.14f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2474f, 6.3863f, 11.4383f), new Quaternion(-2.698577E-06f, -3.695488E-06f, 3.232354E-05f, 1f), new Vector3(2.45f, 1.7414f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.641f, 6.3984f, -8.9525f), new Quaternion(0.499998f, -0.4999987f, -0.5000015f, 0.5000018f), new Vector3(4.0003f, 0.3f, 2.9138f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.9478f, 5.7986f, -8.9524f), new Quaternion(3.076717E-06f, -0.7071062f, 0.7071074f, 3.171547E-06f), new Vector3(0.1f, 0.3f, 2.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.1478f, 5.7986f, -8.9524f), new Quaternion(3.076717E-06f, -0.7071062f, 0.7071074f, 3.171547E-06f), new Vector3(0.1f, 0.3f, 2.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0478f, 7.1486f, -8.9524f), new Quaternion(0.5000026f, -0.500002f, 0.4999984f, -0.499997f), new Vector3(0.1f, 0.3f, 1.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0478f, 7.796f, -8.9525f), new Quaternion(0.4999979f, -0.4999987f, -0.5000014f, 0.5000019f), new Vector3(1.1948f, 0.3f, 1.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.6884f, 6.3979f, -18.6528f), new Quaternion(0.4999979f, -0.4999987f, -0.5000015f, 0.5000018f), new Vector3(4.0003f, 0.3f, 1.0081f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0344f, 6.3987f, -18.0526f), new Quaternion(-0.7071063f, 0.7071073f, 3.561406E-06f, -3.350672E-06f), new Vector3(4.0003f, 0.3f, 1.5002f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0342f, 6.3984f, -9.3523f), new Quaternion(-0.7071063f, 0.7071073f, 3.561406E-06f, -3.350672E-06f), new Vector3(4.0003f, 0.3f, 1.0997f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.0302f, 6.3983f, -18.6526f), new Quaternion(0.4999979f, -0.4999988f, -0.5000015f, 0.5000018f), new Vector3(4.0003f, 0.3f, 0.3919f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4547f, 6.3354f, -16.4983f), new Quaternion(0.05779731f, 0.9983283f, -4.54027E-07f, -4.089554E-06f), new Vector3(0.031f, 0.4587f, 0.7649f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3798f, 6.3354f, -17.7467f), new Quaternion(0.0445308f, 0.7691889f, 0.0368438f, 0.6364024f), new Vector3(0.031f, 0.4587f, 0.7649f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3971f, 5.9247f, -17.8369f), new Quaternion(-4.008764E-07f, 0.7704769f, -1.957606E-07f, 0.637468f), new Vector3(0.034f, 1.042f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3971f, 5.4253f, -17.8369f), new Quaternion(-4.008762E-07f, 0.7704769f, -1.957605E-07f, 0.637468f), new Vector3(0.1391f, 0.0431f, 0.6202f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.4767f, 6.377f, -17.83f), new Quaternion(0.47825f, 0.4782545f, 0.5208405f, 0.5208405f), new Vector3(0.1374f, 0.0811f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.1937f, 5.8028f, -17.3215f), new Quaternion(0.6727884f, 0.6727911f, 0.2176107f, 0.2176086f), new Vector3(0.1374f, 0.0811f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.4767f, 5.8028f, -17.83f), new Quaternion(0.47825f, 0.4782545f, 0.5208405f, 0.5208405f), new Vector3(0.1374f, 0.0811f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2639f, 5.4941f, -15.8388f), new Quaternion(2.689462E-07f, 1f, -4.765984E-07f, -3.993512E-06f), new Vector3(0.4361f, 0.0262f, 0.2286f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4893f, 6.377f, -16.4983f), new Quaternion(0.7071055f, 0.7071081f, -1.264403E-07f, -1.980899E-06f), new Vector3(0.1374f, 0.0811f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3805f, 5.7878f, -17.7507f), new Quaternion(-4.008762E-07f, 0.7704769f, -1.957606E-07f, 0.637468f), new Vector3(0.034f, 0.5032f, 0.8391f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.4793f, 6.3354f, -17.7958f), new Quaternion(0.0390905f, 0.6752219f, 0.04257207f, 0.7353469f), new Vector3(0.031f, 0.4587f, 0.7649f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.4714f, 5.9247f, -17.8875f), new Quaternion(-5.207324E-07f, 0.6763527f, -1.242458E-07f, 0.736578f), new Vector3(0.034f, 1.042f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5738f, 5.7878f, -17.7238f), new Quaternion(-5.207324E-07f, 0.6763527f, -1.242456E-07f, 0.736578f), new Vector3(0.034f, 0.5032f, 0.8391f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4893f, 5.8028f, -16.4983f), new Quaternion(0.7071055f, 0.7071081f, -1.264403E-07f, -1.980899E-06f), new Vector3(0.1374f, 0.0811f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3798f, 5.7878f, -17.7467f), new Quaternion(-4.008764E-07f, 0.7704769f, -1.957607E-07f, 0.637468f), new Vector3(0.031f, 0.4587f, 0.7649f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3866f, 6.377f, -17.7802f), new Quaternion(0.5448066f, 0.5448104f, 0.4507594f, 0.4507589f), new Vector3(0.1374f, 0.0811f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5743f, 5.7878f, -17.7199f), new Quaternion(-5.207323E-07f, 0.6763527f, -1.242458E-07f, 0.736578f), new Vector3(0.031f, 0.4587f, 0.7649f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.4714f, 5.4253f, -17.8875f), new Quaternion(-5.207324E-07f, 0.6763527f, -1.242457E-07f, 0.736578f), new Vector3(0.1391f, 0.0431f, 0.6202f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.1691f, 5.7878f, -17.3041f), new Quaternion(-3.76716E-08f, 0.9514692f, -3.706998E-07f, 0.3077442f), new Vector3(0.034f, 0.5032f, 0.8391f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4788f, 4.7449f, -16.2839f), new Quaternion(2.689463E-07f, 1f, -4.765986E-07f, -3.814698E-06f), new Vector3(0.9076f, 0.6825f, 0.6202f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4866f, 5.6014f, -15.8387f), new Quaternion(2.689463E-07f, 1f, -4.765981E-07f, -4.231931E-06f), new Vector3(0.8491f, 0.1318f, 0.3016f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5716f, 5.8028f, -17.754f), new Quaternion(0.47825f, 0.4782545f, 0.5208405f, 0.5208405f), new Vector3(0.1374f, 0.0811f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4866f, 5.4696f, -15.8387f), new Quaternion(2.689463E-07f, 1f, -4.765981E-07f, -4.231931E-06f), new Vector3(0.8491f, 0.1318f, 0.3016f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.5469f, 5.4253f, -16.4984f), new Quaternion(2.689462E-07f, 1f, -4.765982E-07f, -3.993512E-06f), new Vector3(0.1391f, 0.0431f, 0.6202f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.1658f, 5.7878f, -17.3017f), new Quaternion(-3.767164E-08f, 0.9514691f, -3.706997E-07f, 0.3077444f), new Vector3(0.031f, 0.4587f, 0.7649f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4589f, 5.7878f, -16.4983f), new Quaternion(2.689463E-07f, 1f, -4.765981E-07f, -4.23193E-06f), new Vector3(0.034f, 0.5032f, 0.8391f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.1937f, 6.377f, -17.3215f), new Quaternion(0.6727884f, 0.6727911f, 0.2176107f, 0.2176086f), new Vector3(0.1374f, 0.0811f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3805f, 6.3358f, -17.7507f), new Quaternion(0.0445308f, 0.7691889f, 0.03684381f, 0.6364024f), new Vector3(0.034f, 0.5032f, 0.8391f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.4789f, 6.3358f, -17.7998f), new Quaternion(0.03909048f, 0.6752219f, 0.04257204f, 0.7353469f), new Vector3(0.034f, 0.5032f, 0.8391f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5664f, 5.9247f, -17.8114f), new Quaternion(-5.207324E-07f, 0.6763527f, -1.242458E-07f, 0.736578f), new Vector3(0.034f, 1.042f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2408f, 4.5572f, -16.2838f), new Quaternion(2.689461E-07f, 1f, -4.765981E-07f, -3.993512E-06f), new Vector3(0.4661f, 0.1358f, 0.4701f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4548f, 5.7878f, -16.4983f), new Quaternion(2.689461E-07f, 1f, -4.765981E-07f, -4.172325E-06f), new Vector3(0.031f, 0.4587f, 0.7649f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4866f, 5.8649f, -15.8387f), new Quaternion(2.689463E-07f, 1f, -4.765981E-07f, -4.231931E-06f), new Vector3(0.8491f, 0.1318f, 0.3016f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2403f, 5.9247f, -17.3555f), new Quaternion(-3.767154E-08f, 0.9514691f, -3.706997E-07f, 0.3077444f), new Vector3(0.034f, 1.042f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.4789f, 5.7878f, -17.7997f), new Quaternion(-5.207324E-07f, 0.6763527f, -1.242456E-07f, 0.736578f), new Vector3(0.034f, 0.5032f, 0.8391f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.4793f, 5.7878f, -17.7958f), new Quaternion(-5.207323E-07f, 0.6763527f, -1.242458E-07f, 0.736578f), new Vector3(0.031f, 0.4587f, 0.7649f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.0329f, 4.3961f, -13.4877f), new Quaternion(-4.843444E-07f, 0.7071106f, -1.468326E-07f, 0.707103f), new Vector3(4.1991f, 0.0363f, 3.6238f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.169f, 6.3358f, -17.3041f), new Quaternion(0.05499202f, 0.9498786f, 0.01778662f, 0.3072299f), new Vector3(0.034f, 0.5032f, 0.8391f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5743f, 6.3354f, -17.7199f), new Quaternion(0.0390905f, 0.6752219f, 0.04257207f, 0.7353469f), new Vector3(0.031f, 0.4587f, 0.7649f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5716f, 6.377f, -17.754f), new Quaternion(0.47825f, 0.4782545f, 0.5208405f, 0.5208405f), new Vector3(0.1374f, 0.0811f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3866f, 5.8028f, -17.7802f), new Quaternion(0.5448066f, 0.5448104f, 0.4507594f, 0.4507589f), new Vector3(0.1374f, 0.0811f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2639f, 5.6259f, -15.8388f), new Quaternion(2.689462E-07f, 1f, -4.765984E-07f, -3.993512E-06f), new Vector3(0.4361f, 0.0262f, 0.2286f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2639f, 5.7576f, -15.8388f), new Quaternion(2.689462E-07f, 1f, -4.765984E-07f, -3.993512E-06f), new Vector3(0.4361f, 0.0262f, 0.2286f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.1658f, 6.3354f, -17.3018f), new Quaternion(0.05499204f, 0.9498787f, 0.01778663f, 0.3072298f), new Vector3(0.031f, 0.4587f, 0.7649f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2408f, 4.9168f, -16.2838f), new Quaternion(2.689461E-07f, 1f, -4.765981E-07f, -3.993512E-06f), new Vector3(0.4661f, 0.1358f, 0.4701f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.5469f, 5.9247f, -16.4984f), new Quaternion(2.689461E-07f, 1f, -4.765982E-07f, -4.172326E-06f), new Vector3(0.034f, 1.042f, 0.142f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5738f, 6.3358f, -17.7238f), new Quaternion(0.03909048f, 0.6752219f, 0.04257204f, 0.7353469f), new Vector3(0.034f, 0.5032f, 0.8391f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.5664f, 5.4253f, -17.8114f), new Quaternion(-5.207324E-07f, 0.6763527f, -1.242457E-07f, 0.736578f), new Vector3(0.1391f, 0.0431f, 0.6202f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2408f, 4.7378f, -16.2838f), new Quaternion(2.689461E-07f, 1f, -4.765981E-07f, -3.993512E-06f), new Vector3(0.4661f, 0.1358f, 0.4701f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2403f, 5.4253f, -17.3555f), new Quaternion(-3.767157E-08f, 0.9514691f, -3.706998E-07f, 0.3077444f), new Vector3(0.1391f, 0.0431f, 0.6202f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2639f, 5.8894f, -15.8388f), new Quaternion(2.689462E-07f, 1f, -4.765984E-07f, -3.993512E-06f), new Vector3(0.4361f, 0.0262f, 0.2286f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.2829f, 5.4253f, -17.2918f), new Quaternion(-3.842818E-07f, 0.7819725f, -2.050461E-07f, 0.623313f), new Vector3(0.2204f, 0.0431f, 0.7521f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4866f, 5.7331f, -15.8387f), new Quaternion(2.689463E-07f, 1f, -4.765981E-07f, -4.231931E-06f), new Vector3(0.8491f, 0.1318f, 0.3016f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.4589f, 6.3358f, -16.4983f), new Quaternion(0.05779735f, 0.9983284f, -4.642887E-07f, -4.208964E-06f), new Vector3(0.034f, 0.5032f, 0.8391f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.4317f, 6.3983f, -11.4473f), new Quaternion(0.4999905f, -0.4999909f, -0.5000094f, 0.5000092f), new Vector3(4.0003f, 4.6897f, 1.7478f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.2659f, 6.3983f, -16.2973f), new Quaternion(0.4999905f, -0.4999909f, -0.5000094f, 0.5000092f), new Vector3(4.0003f, 5.0103f, 2.0797f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0343f, 8.2734f, -13.6024f), new Quaternion(0.4999904f, -0.4999911f, -0.5000092f, 0.5000094f), new Vector3(0.2502f, 7.4003f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.2179f, 5.0736f, -6.0453f), new Quaternion(0f, 0.7071186f, 0f, 0.707095f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.704f, 5.0736f, -3.5454f), new Quaternion(0f, 1.305342E-05f, 0f, 1f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.5928f, 4.6486f, -8.0452f), new Quaternion(0f, 0.7071186f, 0f, 0.707095f), new Vector3(1f, 0.5f, 0.9996f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.704f, 4.6486f, -4.1704f), new Quaternion(0f, 1.254678E-05f, 0f, 1f), new Vector3(1f, 0.5f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.2178f, 5.0736f, -8.0454f), new Quaternion(0f, 0.7071186f, 0f, 0.707095f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.704f, 5.0736f, -3.5454f), new Quaternion(0f, 1.305342E-05f, 0f, 1f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.5929f, 4.6486f, -6.0453f), new Quaternion(0f, 0.7071186f, 0f, 0.707095f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.704f, 4.6486f, -3.9204f), new Quaternion(0f, 1.254678E-05f, 0f, 1f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.5926f, 4.6486f, -7.0452f), new Quaternion(0f, 0.7071186f, 0f, 0.707095f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.704f, 4.6486f, -3.9204f), new Quaternion(0f, 1.254678E-05f, 0f, 1f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.2177f, 5.0736f, -7.0454f), new Quaternion(0f, 0.7071186f, 0f, 0.707095f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.704f, 5.0736f, -3.5454f), new Quaternion(0f, 1.305342E-05f, 0f, 1f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.7178f, 5.0736f, -8.4205f), new Quaternion(0f, 1.692772E-05f, 0f, 1f), new Vector3(0.75f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.7179f, 5.0736f, -5.6703f), new Quaternion(0f, 1.692772E-05f, 0f, 1f), new Vector3(0.75f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.079f, 5.0736f, -4.0454f), new Quaternion(0f, -0.7070978f, 0f, 0.7071158f), new Vector3(0.75f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.9681f, 4.6486f, -7.9662f), new Quaternion(0f, 0.9765332f, 0f, 0.2153672f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.6167f, 4.6486f, -17.5919f), new Quaternion(0f, 0.9962134f, 0f, -0.08694215f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.8147f, 4.6486f, -17.7027f), new Quaternion(0f, 0.9828941f, 0f, 0.1841716f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.8863f, 4.6486f, -3.9544f), new Quaternion(0f, 0.06143758f, 0f, 0.9981109f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.0877f, 4.6486f, -0.2397f), new Quaternion(0f, 0.7478845f, 0f, 0.663829f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.2063f, 4.6486f, -1.2326f), new Quaternion(0f, 0.7478845f, 0f, 0.663829f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.2684f, 4.6486f, 5.6709f), new Quaternion(0f, 0.5522624f, 0f, 0.8336704f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.8106f, 5.0736f, -8.306f), new Quaternion(0f, 0.9765332f, 0f, 0.2153672f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.6817f, 5.0736f, -17.9607f), new Quaternion(0f, 0.9962134f, 0f, -0.08694179f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.6793f, 5.0736f, -18.0518f), new Quaternion(0f, 0.9828941f, 0f, 0.1841716f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.8404f, 5.0736f, -3.5827f), new Quaternion(0f, 0.06143758f, 0f, 0.9981109f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.7159f, 5.0736f, -0.2841f), new Quaternion(0f, 0.7478845f, 0f, 0.663829f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.8339f, 5.0736f, -1.2771f), new Quaternion(0f, 0.7478845f, 0f, 0.663829f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.9235f, 5.0736f, 5.817f), new Quaternion(0f, 0.5522624f, 0f, 0.8336704f), new Vector3(1f, 0.35f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.3537f, 4.9736f, -8.1447f), new Quaternion(0f, 0.5382265f, 0f, 0.8428003f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.0352f, 4.9736f, -17.5181f), new Quaternion(0f, 0.7659073f, 0f, 0.6429511f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.2109f, 4.9736f, -17.8564f), new Quaternion(0f, 0.5647829f, 0f, 0.8252395f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.4647f, 4.9736f, -4.0067f), new Quaternion(0f, -0.6623271f, 0f, 0.7492149f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.2567f, 4.9736f, -1.6546f), new Quaternion(0f, 0.05943728f, 0f, 0.9982321f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.1029f, 4.9736f, 5.2796f), new Quaternion(0f, -0.1989846f, 0f, 0.9800026f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.5824f, 4.9736f, -7.7878f), new Quaternion(0f, 0.5382265f, 0f, 0.8428003f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1983f, 4.9736f, -17.6659f), new Quaternion(0f, 0.7659073f, 0f, 0.6429511f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.4184f, 4.9736f, -17.5493f), new Quaternion(0f, 0.5647829f, 0f, 0.8252395f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.3079f, 4.9736f, -3.9019f), new Quaternion(0f, -0.6623271f, 0f, 0.7492149f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.0368f, 4.9736f, 0.1821f), new Quaternion(0f, 0.05943728f, 0f, 0.9982321f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.4337f, 4.9736f, 6.0623f), new Quaternion(0f, -0.1989846f, 0f, 0.9800026f), new Vector3(0.5f, 0.15f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7659f, 4.6486f, -6.6878f), new Quaternion(0f, -0.7044881f, 0f, 0.7097159f), new Vector3(1.8f, 0.05f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.766f, 4.9235f, -6.6878f), new Quaternion(0f, -0.704488f, 0f, 0.7097159f), new Vector3(2f, 0.05f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.1842f, 4.6486f, -5.7595f), new Quaternion(0f, -0.704488f, 0f, 0.7097159f), new Vector3(0.05f, 0.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3343f, 4.6486f, -5.7659f), new Quaternion(0f, -0.704488f, 0f, 0.7097159f), new Vector3(0.05f, 0.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3479f, 4.6486f, -7.6157f), new Quaternion(0f, 0.7097161f, 0f, 0.7044879f), new Vector3(0.05f, 0.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.1978f, 4.6486f, -7.6094f), new Quaternion(0f, 0.7097162f, 0f, 0.7044878f), new Vector3(0.05f, 0.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5606f, 4.8986f, -15.7494f), new Quaternion(0f, 1f, 0f, -9.298325E-06f), new Vector3(3f, 1f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9356f, 4.8986f, -17.1245f), new Quaternion(0f, 0.7071137f, 0f, 0.7070999f), new Vector3(2.5f, 1f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.1856f, 4.8986f, -17.1244f), new Quaternion(0f, 0.7071137f, 0f, 0.7070999f), new Vector3(2.5f, 1f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5607f, 4.8986f, -18.4994f), new Quaternion(0f, 1f, 0f, -9.298325E-06f), new Vector3(3f, 1f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3194f, 4.8833f, -17.1244f), new Quaternion(0.09229482f, 0.7010643f, -0.09229662f, 0.7010506f), new Vector3(2.5f, 1f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.802f, 4.8833f, -17.1244f), new Quaternion(-0.09229482f, 0.7010643f, 0.09229662f, 0.7010506f), new Vector3(2.5f, 1f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5606f, 4.8833f, -15.8831f), new Quaternion(0.1305265f, 9.963695E-06f, -1.277793E-06f, 0.9914449f), new Vector3(2.5f, 1f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5607f, 4.8833f, -18.3657f), new Quaternion(-0.1305265f, 9.828428E-06f, 1.352942E-06f, 0.9914449f), new Vector3(2.5f, 1f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5606f, 4.4284f, -17.1245f), new Quaternion(0f, 9.775161E-06f, 0f, 1f), new Vector3(2f, 0.1516f, 1.9999f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5606f, 4.8061f, -17.1245f), new Quaternion(0f, 9.924175E-06f, 0f, 1f), new Vector3(2.6894f, 0.815f, 2.6893f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.8954f, 5.0864f, -2.6825f), new Quaternion(0.7071089f, -0.7071047f, -2.461369E-05f, -2.017774E-05f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5977f, 0.7901f, -3.9406f), new Quaternion(2.056763E-05f, 2.452944E-05f, -0.707106f, 0.7071076f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.1454f, 5.0864f, -2.6825f), new Quaternion(0.7071089f, -0.7071047f, -2.461369E-05f, -2.017774E-05f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3476f, 0.7901f, -3.9406f), new Quaternion(2.056763E-05f, 2.452944E-05f, -0.707106f, 0.7071076f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.3954f, 5.0864f, -2.6825f), new Quaternion(0.7071089f, -0.7071047f, -2.461369E-05f, -2.017774E-05f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.0977f, 0.7902f, -3.9406f), new Quaternion(2.056763E-05f, 2.452944E-05f, -0.707106f, 0.7071076f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.9688f, 4.5008f, 2.0368f), new Quaternion(-3.275494E-05f, 6.496907E-06f, -2.449628E-06f, 1f), new Vector3(2.8707f, 0.2001f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.1953f, 6.0227f, -2.7115f), new Quaternion(-2.117572E-06f, 1f, 3.244124E-05f, -3.814697E-06f), new Vector3(0.1f, 3.2474f, 0.5813f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2977f, 1.7264f, -3.9113f), new Quaternion(3.24413E-05f, 3.784895E-06f, 2.117616E-06f, 1f), new Vector3(0.1f, 3.2474f, 0.5813f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.0954f, 6.0252f, -2.7115f), new Quaternion(3.244129E-05f, 4.053116E-06f, 2.117587E-06f, 1f), new Vector3(0.1f, 3.2524f, 0.5813f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.3977f, 1.7289f, -3.9115f), new Quaternion(-2.117634E-06f, 1f, 3.24413E-05f, -4.410744E-06f), new Vector3(0.1f, 3.2524f, 0.5813f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.1453f, 7.6964f, -2.7114f), new Quaternion(0.7071087f, -0.7071049f, -2.49298E-05f, -2.059921E-05f), new Vector3(0.1f, 3.9999f, 0.5813f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3477f, 3.4001f, -3.9113f), new Quaternion(2.064139E-05f, 2.480339E-05f, -0.7071056f, 0.707108f), new Vector3(0.1f, 3.9999f, 0.5813f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.9954f, 6.9264f, -2.7114f), new Quaternion(-2.117555E-06f, 1f, 3.244128E-05f, -2.384186E-06f), new Vector3(0.8609f, 0.0508f, 0.4972f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4976f, 2.6301f, -3.9114f), new Quaternion(3.24413E-05f, 2.592802E-06f, 2.117552E-06f, 1f), new Vector3(0.8609f, 0.0508f, 0.4972f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.4688f, 6.7709f, 2.8708f), new Quaternion(-3.275493E-05f, 6.496907E-06f, -2.449636E-06f, 1f), new Vector3(0.034f, 0.5032f, 1.08f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.6829f, 4.3896f, 1.9522f), new Quaternion(-2.142912E-05f, -0.7071013f, -2.489336E-05f, 0.7071123f), new Vector3(4.1991f, 0.0363f, 3.6238f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0688f, 4.8309f, 2.0367f), new Quaternion(-3.275493E-05f, 6.496907E-06f, -2.449634E-06f, 1f), new Vector3(3.1f, 0.45f, 2.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.5688f, 5.5308f, 2.0366f), new Quaternion(-3.275492E-05f, 6.645919E-06f, -2.449637E-06f, 1f), new Vector3(0.1001f, 0.95f, 2.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.8954f, 4.6864f, -2.6826f), new Quaternion(0.7071089f, -0.7071047f, -2.461369E-05f, -2.017774E-05f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5976f, 0.3901f, -3.9405f), new Quaternion(2.056763E-05f, 2.452944E-05f, -0.707106f, 0.7071076f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.1454f, 4.6864f, -2.6825f), new Quaternion(0.7071089f, -0.7071047f, -2.461369E-05f, -2.017774E-05f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3476f, 0.3901f, -3.9405f), new Quaternion(2.056763E-05f, 2.452944E-05f, -0.707106f, 0.7071076f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.3954f, 4.6864f, -2.6825f), new Quaternion(0.7071089f, -0.7071047f, -2.461369E-05f, -2.017774E-05f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.0977f, 0.3901f, -3.9406f), new Quaternion(2.056763E-05f, 2.452944E-05f, -0.707106f, 0.7071076f), new Vector3(0.35f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.9953f, 6.4264f, -2.7115f), new Quaternion(-2.117555E-06f, 1f, 3.244128E-05f, -2.384186E-06f), new Vector3(0.8609f, 0.0508f, 0.4972f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4977f, 2.1301f, -3.9114f), new Quaternion(3.24413E-05f, 2.592802E-06f, 2.117552E-06f, 1f), new Vector3(0.8609f, 0.0508f, 0.4972f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.4688f, 6.7708f, 1.3707f), new Quaternion(-3.275493E-05f, 6.645919E-06f, -2.449648E-06f, 1f), new Vector3(0.034f, 0.95f, 1.3761f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.8954f, 6.5264f, -2.6894f), new Quaternion(0.7071082f, -0.7071054f, -2.47823E-05f, -2.055708E-05f), new Vector3(2.1443f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5978f, 2.2301f, -3.9335f), new Quaternion(2.106284E-05f, 2.467693E-05f, -0.7071054f, 0.7071082f), new Vector3(2.1443f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.1453f, 6.5264f, -2.6894f), new Quaternion(0.7071082f, -0.7071054f, -2.47823E-05f, -2.055708E-05f), new Vector3(2.1443f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3478f, 2.2301f, -3.9335f), new Quaternion(2.106284E-05f, 2.467693E-05f, -0.7071054f, 0.7071082f), new Vector3(2.1443f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.3953f, 6.5264f, -2.6894f), new Quaternion(0.7071082f, -0.7071054f, -2.47823E-05f, -2.055708E-05f), new Vector3(2.1443f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.0977f, 2.2301f, -3.9335f), new Quaternion(2.106284E-05f, 2.467693E-05f, -0.7071054f, 0.7071082f), new Vector3(2.1443f, 1.0889f, 0.553f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.8339f, 4.2486f, 4.2978f), new Quaternion(-1.369729E-05f, 9.229869E-06f, -0.7070842f, 0.7071294f), new Vector3(0.3001f, 7.2001f, 14.6001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.934f, 4.2482f, 2.0266f), new Quaternion(-1.380265E-05f, 8.829484E-06f, -0.707084f, 0.7071296f), new Vector3(0.3001f, 3f, 10.2574f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.834f, 8.5486f, 4.3478f), new Quaternion(-1.365514E-05f, 9.019139E-06f, -0.7070842f, 0.7071294f), new Vector3(0.3001f, 7.2001f, 14.7001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.1454f, 6.0227f, -2.7115f), new Quaternion(-2.117535E-06f, 1f, 3.244129E-05f, -2.324581E-06f), new Vector3(3.8f, 3.2425f, 0.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.3477f, 1.7264f, -3.9114f), new Quaternion(3.244131E-05f, 2.682209E-06f, 2.117567E-06f, 1f), new Vector3(3.8f, 3.2425f, 0.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.9954f, 5.9264f, -2.7115f), new Quaternion(-2.117555E-06f, 1f, 3.244128E-05f, -2.384186E-06f), new Vector3(0.8609f, 0.0508f, 0.4972f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4976f, 1.6301f, -3.9114f), new Quaternion(3.24413E-05f, 2.592802E-06f, 2.117552E-06f, 1f), new Vector3(0.8609f, 0.0508f, 0.4972f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.9953f, 6.5464f, -2.9115f), new Quaternion(-2.117534E-06f, 1f, 3.244128E-05f, -2.324581E-06f), new Vector3(1.3975f, 2.1951f, 0.1236f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4977f, 2.2501f, -3.7114f), new Quaternion(3.244131E-05f, 2.682209E-06f, 2.117575E-06f, 1f), new Vector3(1.3975f, 2.1951f, 0.1236f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.8688f, 5.1508f, 1.4367f), new Quaternion(-3.275494E-05f, 6.645919E-06f, -2.449643E-06f, 1f), new Vector3(0.5f, 0.15f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0688f, 4.9209f, 2.0367f), new Quaternion(-3.275495E-05f, 6.496907E-06f, -2.449646E-06f, 1f), new Vector3(2.9012f, 0.3f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.8688f, 5.1509f, 2.6367f), new Quaternion(-3.275494E-05f, 6.645919E-06f, -2.449643E-06f, 1f), new Vector3(0.5f, 0.15f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2842f, 6.3994f, -1.8279f), new Quaternion(-1.540418E-05f, 1.076818E-05f, -0.707084f, 0.7071296f), new Vector3(3.9997f, 0.3f, 2.3486f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.284f, 6.3992f, 5.95f), new Quaternion(-1.517238E-05f, 1.078925E-05f, -0.707084f, 0.7071297f), new Vector3(4f, 0.3f, 2.2071f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.5108f, 6.8648f, 2.0366f), new Quaternion(-3.275493E-05f, 6.645919E-06f, -2.449641E-06f, 1f), new Vector3(0.1001f, 0.95f, 3.483f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.5708f, 6.8648f, 2.0365f), new Quaternion(-3.275494E-05f, 6.496907E-06f, -2.449639E-06f, 1f), new Vector3(0.0692f, 0.6561f, 1.8801f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.7341f, 1.971f, -8.9523f), new Quaternion(0.5000083f, -0.5000081f, 0.4999918f, -0.4999917f), new Vector3(3.7449f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.4839f, 1.9735f, -8.9523f), new Quaternion(0.5000083f, -0.5000081f, 0.4999918f, -0.4999917f), new Vector3(3.7498f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.6092f, 3.7984f, -8.9523f), new Quaternion(-0.7071072f, 1.076853E-05f, 1.212777E-05f, 0.7071064f), new Vector3(4.1504f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.3841f, 0.9236f, -7.5522f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.2841f, 3.1236f, -4.5524f), new Quaternion(-0.4999983f, -0.4999986f, 0.5000014f, 0.5000016f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.6841f, 1.1236f, -7.5522f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.9841f, 3.3236f, -4.5525f), new Quaternion(-0.4999983f, -0.4999986f, 0.5000014f, 0.5000016f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.9841f, 1.3236f, -7.5522f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.6841f, 3.5236f, -4.5525f), new Quaternion(-0.4999983f, -0.4999986f, 0.5000014f, 0.5000016f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.0841f, 0.7236f, -7.5522f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.0881f, -0.7596f, -12.6829f), new Quaternion(3.055648E-06f, 2.381297E-06f, 0.707107f, 0.7071066f), new Vector3(1.3058f, 0.2772f, 3.642f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.7377f, -0.7596f, -14.6425f), new Quaternion(0.5000015f, 0.5000013f, 0.4999987f, 0.4999985f), new Vector3(1.3058f, 0.2772f, 0.978f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.5995f, -0.7596f, -13.0333f), new Quaternion(0.5000025f, 0.5000025f, 0.4999976f, 0.4999976f), new Vector3(1.3058f, 2.9413f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.0881f, -0.7596f, -15.1312f), new Quaternion(0.7071069f, 0.7071067f, -2.497201E-06f, -2.465591E-06f), new Vector3(1.3058f, 0.2772f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.7745f, -0.7596f, -11.3877f), new Quaternion(0.2706015f, 0.2706012f, 0.6532803f, 0.65328f), new Vector3(1.3058f, 0.9899f, 0.4949f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.7745f, -0.7596f, -14.9562f), new Quaternion(0.6532828f, 0.6532824f, 0.2705957f, 0.2705952f), new Vector3(1.3058f, 0.9899f, 0.4949f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.5841f, 2.9236f, -4.5524f), new Quaternion(-0.4999983f, -0.4999986f, 0.5000014f, 0.5000016f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.5415f, 2.0986f, -8.9522f), new Quaternion(0.5000083f, -0.5000081f, 0.4999918f, -0.4999918f), new Vector3(4f, 0.3f, 5.5148f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.067f, 2.0986f, -8.9521f), new Quaternion(0.5000084f, -0.500008f, 0.4999918f, -0.4999918f), new Vector3(4f, 0.3f, 1.5366f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.9854f, 1.4486f, -6.0521f), new Quaternion(-1.159039E-05f, 1.049457E-05f, -0.7071069f, 0.7071066f), new Vector3(2.7f, 0.3f, 6.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.9854f, 5.5985f, -4.0522f), new Quaternion(-1.1622E-05f, 1.076852E-05f, -0.7071068f, 0.7071068f), new Vector3(5.6f, 0.3f, 2.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.9854f, 5.5985f, -8.0521f), new Quaternion(-1.1622E-05f, 1.076852E-05f, -0.7071068f, 0.7071068f), new Vector3(5.6f, 0.3f, 2.1001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.2841f, 1.5236f, -7.5522f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.3841f, 3.7236f, -4.5524f), new Quaternion(-0.4999983f, -0.4999986f, 0.5000014f, 0.5000016f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.4841f, 0.3236f, -7.5523f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.9764f, -1.1596f, -10.3899f), new Quaternion(3.013501E-06f, 2.592032E-06f, 0.7071069f, 0.7071066f), new Vector3(1.3058f, 0.5004f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.7767f, -1.1596f, -14.7542f), new Quaternion(0.5000015f, 0.5000013f, 0.4999987f, 0.4999985f), new Vector3(1.3058f, 0.5004f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.7767f, -1.1596f, -12.9192f), new Quaternion(0.5000027f, 0.5000021f, 0.4999978f, 0.4999973f), new Vector3(1.3058f, 3.1696f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.974f, -1.1596f, -15.9539f), new Quaternion(0.7071068f, 0.7071068f, -2.486664E-06f, -2.465591E-06f), new Vector3(1.3058f, 0.5055f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.2832f, -1.1596f, -10.8926f), new Quaternion(0.2706015f, 0.2706012f, 0.6532803f, 0.65328f), new Vector3(1.3058f, 1.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.2794f, -1.1596f, -15.4474f), new Quaternion(0.6532829f, 0.6532823f, 0.2705957f, 0.2705952f), new Vector3(1.3058f, 1.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.1841f, 2.5236f, -4.5523f), new Quaternion(-0.4999983f, -0.4999986f, 0.5000014f, 0.5000016f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.1841f, 0.1236f, -7.5523f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.911f, -1.3596f, -10.0899f), new Quaternion(3.034574E-06f, 2.360224E-06f, 0.7071071f, 0.7071065f), new Vector3(1.3058f, 0.6312f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.4767f, -1.3596f, -14.8196f), new Quaternion(0.5000015f, 0.5000013f, 0.4999987f, 0.4999984f), new Vector3(1.3058f, 0.6312f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.4767f, -1.3596f, -12.8578f), new Quaternion(0.5000028f, 0.5000022f, 0.4999979f, 0.4999973f), new Vector3(1.3058f, 3.2925f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.9125f, -1.3596f, -16.254f), new Quaternion(0.7071068f, 0.7071068f, -2.486664E-06f, -2.465591E-06f), new Vector3(1.3058f, 0.6284f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.0684f, -1.3596f, -10.6831f), new Quaternion(0.2706015f, 0.2706012f, 0.6532804f, 0.6532798f), new Vector3(1.3058f, 1.7963f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.0699f, -1.3596f, -15.6622f), new Quaternion(0.6532829f, 0.6532824f, 0.2705957f, 0.2705951f), new Vector3(1.3058f, 1.7963f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.8512f, -1.5596f, -9.7898f), new Quaternion(3.055647E-06f, 2.381297E-06f, 0.707107f, 0.7071066f), new Vector3(1.3058f, 0.751f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.1766f, -1.5596f, -14.8795f), new Quaternion(0.5000015f, 0.5000013f, 0.4999987f, 0.4999985f), new Vector3(1.3058f, 0.751f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.1766f, -1.5596f, -12.7973f), new Quaternion(0.5000027f, 0.5000021f, 0.4999978f, 0.4999973f), new Vector3(1.3058f, 3.4134f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.8521f, -1.5596f, -16.554f), new Quaternion(0.7071068f, 0.7071068f, -2.486664E-06f, -2.465591E-06f), new Vector3(1.3058f, 0.7493f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.856f, -1.5596f, -10.4711f), new Quaternion(0.2706015f, 0.2706012f, 0.6532803f, 0.65328f), new Vector3(1.3058f, 2.0521f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.8579f, -1.5596f, -15.8746f), new Quaternion(0.6532826f, 0.6532826f, 0.2705956f, 0.2705952f), new Vector3(1.3058f, 2.0521f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.7899f, -1.7596f, -9.4897f), new Quaternion(3.055647E-06f, 2.381297E-06f, 0.707107f, 0.7071066f), new Vector3(1.3058f, 0.8736f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.8765f, -1.7596f, -14.9408f), new Quaternion(0.5000015f, 0.5000013f, 0.4999987f, 0.4999985f), new Vector3(1.3058f, 0.8736f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.8766f, -1.7596f, -12.736f), new Quaternion(0.5000024f, 0.5000024f, 0.4999976f, 0.4999975f), new Vector3(1.3058f, 3.536f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.7908f, -1.7596f, -16.8541f), new Quaternion(0.7071068f, 0.7071068f, -2.676325E-06f, -2.634178E-06f), new Vector3(1.3058f, 0.872f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.6449f, -1.7596f, -10.2578f), new Quaternion(0.2706016f, 0.2706011f, 0.6532804f, 0.65328f), new Vector3(1.3058f, 2.3028f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.6447f, -1.7596f, -16.0857f), new Quaternion(0.6532826f, 0.6532826f, 0.2705956f, 0.2705953f), new Vector3(1.3058f, 2.3028f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.7267f, -1.9596f, -9.1897f), new Quaternion(3.055646E-06f, 2.549884E-06f, 0.7071068f, 0.7071068f), new Vector3(1.3058f, 1f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.5765f, -1.9596f, -15.004f), new Quaternion(0.5000014f, 0.5000014f, 0.4999987f, 0.4999984f), new Vector3(1.3058f, 1f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.5765f, -1.9596f, -12.672f), new Quaternion(0.5000027f, 0.5000021f, 0.4999978f, 0.4999973f), new Vector3(1.3058f, 3.6641f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.7267f, -1.9596f, -17.1541f), new Quaternion(0.7071068f, 0.7071068f, -2.676325E-06f, -2.634177E-06f), new Vector3(1.3058f, 1f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.4318f, -1.9596f, -10.0468f), new Quaternion(0.2706015f, 0.2706012f, 0.6532803f, 0.65328f), new Vector3(1.3058f, 2.5434f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.4336f, -1.9596f, -16.2988f), new Quaternion(0.6532828f, 0.6532823f, 0.2705956f, 0.2705953f), new Vector3(1.3058f, 2.5434f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.0591f, 0.6486f, -6.3273f), new Quaternion(0.5000015f, 0.5000014f, 0.4999986f, 0.4999986f), new Vector3(1f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.3591f, 2.8486f, -6.3273f), new Quaternion(0.5000015f, 0.5000014f, 0.4999986f, 0.4999986f), new Vector3(1f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.3592f, 3.3236f, -6.0523f), new Quaternion(1.490254E-08f, 0.7071088f, -1.489949E-08f, 0.7071047f), new Vector3(0.4999f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.3591f, 2.8486f, -5.7774f), new Quaternion(0.5000015f, 0.5000014f, 0.4999986f, 0.4999986f), new Vector3(1f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3592f, 4.8486f, -5.7774f), new Quaternion(0.5000015f, 0.5000014f, 0.4999986f, 0.4999986f), new Vector3(1f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3592f, 4.8486f, -8.7774f), new Quaternion(0.5000015f, 0.5000014f, 0.4999986f, 0.4999986f), new Vector3(1f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3592f, 5.3236f, -7.3024f), new Quaternion(3.052984E-06f, 0.7071069f, -1.029935E-06f, 0.7071067f), new Vector3(3f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.8841f, 1.9236f, -7.5522f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.7841f, 4.1236f, -4.5525f), new Quaternion(-0.4999983f, -0.4999986f, 0.5000014f, 0.5000016f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.1841f, 2.1236f, -7.5522f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.4841f, 4.3236f, -4.5524f), new Quaternion(-0.4999983f, -0.4999986f, 0.5000014f, 0.5000016f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.5853f, 1.2231f, -6.0523f), new Quaternion(0.4999986f, -0.4999986f, -0.5000015f, 0.5000014f), new Vector3(2.251f, 5.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.7841f, 0.5236f, -7.5523f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.0359f, -0.9596f, -10.7009f), new Quaternion(2.865987E-06f, 2.613106E-06f, 0.7071069f, 0.7071066f), new Vector3(1.3058f, 0.3815f, 0.3221f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.0877f, -0.9596f, -14.6948f), new Quaternion(0.5000017f, 0.5000012f, 0.4999988f, 0.4999982f), new Vector3(1.3058f, 0.3815f, 0.3221f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.0881f, -0.9596f, -12.9791f), new Quaternion(0.5000024f, 0.5000024f, 0.4999976f, 0.4999975f), new Vector3(1.3058f, 3.0496f, 0.3228f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.0339f, -0.9596f, -15.6425f), new Quaternion(0.7071068f, 0.7071068f, -2.486664E-06f, -2.465591E-06f), new Vector3(1.3058f, 0.3856f, 0.3228f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.4944f, -0.9596f, -11.1056f), new Quaternion(0.2706017f, 0.2706013f, 0.6532803f, 0.6532798f), new Vector3(1.3058f, 1.296f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.4924f, -0.9596f, -15.2363f), new Quaternion(0.6532828f, 0.6532825f, 0.2705957f, 0.2705951f), new Vector3(1.3058f, 1.296f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.8841f, 2.7236f, -4.5524f), new Quaternion(-0.4999983f, -0.4999986f, 0.5000014f, 0.5000016f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.5841f, 1.7236f, -7.5522f), new Quaternion(0.5000015f, 0.5000015f, 0.4999986f, 0.4999985f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.0841f, 3.9236f, -4.5525f), new Quaternion(-0.4999983f, -0.4999986f, 0.5000014f, 0.5000016f), new Vector3(0.05f, 2.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.3175f, 0.3443f, -7.5522f), new Quaternion(0.6764617f, -0.2059071f, -0.2059109f, 0.6764633f), new Vector3(3.9163f, 2.5f, 1.7807f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.7014f, 2.2229f, -6.3273f), new Quaternion(0.6764618f, -0.2059072f, -0.205911f, 0.6764632f), new Vector3(3.9661f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.8632f, 3.1855f, -4.5525f), new Quaternion(-0.2059112f, 0.6764629f, -0.6764622f, 0.2059065f), new Vector3(3.7798f, 2.5f, 0.1448f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.8672f, 4.3234f, -5.7774f), new Quaternion(-0.2059112f, 0.6764629f, -0.6764622f, 0.2059065f), new Vector3(3.6055f, 0.05f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.832f, 2.444f, -18.5074f), new Quaternion(-1.07896E-05f, 1.003095E-05f, -0.7071071f, 0.7071065f), new Vector3(1.4495f, 2.4044f, 0.1011f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.832f, 2.444f, -18.4574f), new Quaternion(-1.081067E-05f, 1.022062E-05f, -0.7071071f, 0.7071065f), new Vector3(1.3495f, 2.3044f, 0.0474f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.3918f, 7.4182f, -11.4474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(4.6896f, 0.05f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9923f, 0.348f, -12.2749f), new Quaternion(0f, 0.7720386f, 0f, 0.6355757f), new Vector3(1.7749f, 0.05f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.0717f, 0.798f, -12.2587f), new Quaternion(0f, 0.70711f, 0f, 0.7071036f), new Vector3(1f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.0717f, 0.798f, -13.2587f), new Quaternion(0f, 0.70711f, 0f, 0.7071036f), new Vector3(1f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.1128f, 0.798f, -13.3864f), new Quaternion(0f, -0.7071047f, 0f, 0.7071089f), new Vector3(1f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.0717f, 0.798f, -14.1337f), new Quaternion(0f, 0.7071093f, 0f, 0.7071043f), new Vector3(0.75f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.449f, 0.3464f, -11.4514f), new Quaternion(0f, -0.6355758f, 0f, 0.7720385f), new Vector3(0.05f, 0.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.7505f, 0.3464f, -13.252f), new Quaternion(0f, -0.6355758f, 0f, 0.7720385f), new Vector3(0.05f, 0.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5842f, 2.07f, -14.128f), new Quaternion(-0.7071066f, -1.053513E-05f, 0.707107f, -1.245738E-05f), new Vector3(9.2588f, 3.9541f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2568f, 2.0738f, -18.6527f), new Quaternion(1f, 1.625816E-05f, -1.937151E-07f, 1.359231E-06f), new Vector3(3.5551f, 3.9541f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0343f, 6.2618f, -13.6134f), new Quaternion(-0.7071065f, -1.053512E-05f, 0.7071071f, -1.245737E-05f), new Vector3(7.4399f, 3.7378f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.231f, 6.25f, 3.9316f), new Quaternion(0.7071077f, 1.245737E-05f, 0.7071059f, -1.05351E-05f), new Vector3(1.8481f, 3.7023f, 0.05f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0872f, 6.2493f, -7.888f), new Quaternion(-0.7071063f, 1.05351E-05f, 0.7071073f, 1.245737E-05f), new Vector3(1.8481f, 3.7023f, 0.05f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2309f, 6.25f, 0.2606f), new Quaternion(-0.707106f, -1.05351E-05f, 0.7071076f, -1.245737E-05f), new Vector3(1.8481f, 3.7023f, 0.05f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0872f, 6.2493f, -4.2169f), new Quaternion(0.7071073f, -1.245737E-05f, 0.7071063f, 1.05351E-05f), new Vector3(1.8481f, 3.7023f, 0.05f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0179f, 6.3953f, -18.6525f), new Quaternion(-9.238721E-07f, 1.697339E-05f, 1f, 1.359269E-06f), new Vector3(5.6502f, 4.0054f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2838f, 6.3459f, 9.8984f), new Quaternion(0.7071075f, 1.245737E-05f, 0.7071061f, -1.05351E-05f), new Vector3(1f, 4.1053f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.3839f, 7.6487f, 9.4008f), new Quaternion(-0.4999939f, 0.49999f, -0.5000091f, 0.500007f), new Vector3(1f, 3.7496f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.8807f, 4.8987f, 16.5475f), new Quaternion(9.778094E-06f, -1.10425E-05f, -0.7071086f, 0.7071049f), new Vector3(1f, 3.7496f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.5863f, 4.8987f, 16.5481f), new Quaternion(0.7071093f, -0.7071042f, 7.92358E-06f, -8.387193E-06f), new Vector3(1f, 3.7496f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.9587f, 2.3236f, -9.2519f), new Quaternion(0f, 1f, 0f, -3.218651E-06f), new Vector3(0.7568f, 0.05f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4467f, 0.348f, -12.7587f), new Quaternion(0f, 0.7071093f, 0f, 0.7071043f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9923f, 0.618f, -12.2749f), new Quaternion(0f, 0.7720386f, 0f, 0.6355757f), new Vector3(2f, 0.05f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2838f, 6.2736f, 9.4484f), new Quaternion(0.7071075f, 1.245737E-05f, 0.7071061f, -1.05351E-05f), new Vector3(0.1f, 3.7496f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.3839f, 8.0987f, 9.4008f), new Quaternion(-0.4999939f, 0.49999f, -0.5000091f, 0.5000071f), new Vector3(0.1f, 3.2496f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.7309f, 5.3487f, 16.5475f), new Quaternion(9.693799E-06f, -1.129538E-05f, -0.7071086f, 0.7071049f), new Vector3(0.1f, 3.5492f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.5864f, 5.3487f, 16.548f), new Quaternion(0.7071093f, -0.7071042f, 7.92358E-06f, -8.387193E-06f), new Vector3(0.1f, 3.2496f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5842f, 1.9737f, -9.5524f), new Quaternion(-0.7071066f, -1.053513E-05f, 0.707107f, -1.245737E-05f), new Vector3(0.1f, 3.5502f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0342f, 6.2734f, -11.7023f), new Quaternion(-0.7071066f, -1.053512E-05f, 0.707107f, -1.245737E-05f), new Vector3(0.1f, 3.5498f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2092f, 6.2741f, -0.6034f), new Quaternion(0.7071077f, 1.245738E-05f, 0.7071059f, -1.053511E-05f), new Vector3(0.1f, 3.5497f, 0.1501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.1089f, 6.2735f, -3.3529f), new Quaternion(-0.7071063f, 1.053511E-05f, 0.7071073f, 1.245739E-05f), new Vector3(0.1f, 3.5497f, 0.1501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.2428f, 6.273f, -18.6526f), new Quaternion(-9.238721E-07f, 1.697339E-05f, 1f, 1.35927E-06f), new Vector3(0.1f, 3.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7339f, 7.0486f, -3.1524f), new Quaternion(-9.238721E-07f, 1.697339E-05f, 1f, 1.35927E-06f), new Vector3(0.3f, 2.6999f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7339f, 6.3985f, -18.1524f), new Quaternion(-9.238721E-07f, 1.697339E-05f, 1f, 1.35927E-06f), new Vector3(0.3f, 3.9999f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7341f, 6.3986f, -8.9523f), new Quaternion(-9.238721E-07f, 1.697339E-05f, 1f, 1.35927E-06f), new Vector3(0.3f, 3.9999f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5844f, 1.9737f, -15.2527f), new Quaternion(-0.7071066f, -1.053513E-05f, 0.707107f, -1.245738E-05f), new Vector3(0.1f, 3.5502f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5844f, 1.9737f, -17.0027f), new Quaternion(-0.7071066f, -1.053513E-05f, 0.707107f, -1.245738E-05f), new Vector3(0.1f, 3.5502f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2344f, 1.9737f, -18.6527f), new Quaternion(1f, 1.625816E-05f, -1.937151E-07f, 1.359217E-06f), new Vector3(0.1f, 3.5502f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9844f, 1.9735f, -18.6525f), new Quaternion(1f, 1.625815E-05f, -2.682209E-07f, 1.359229E-06f), new Vector3(0.1f, 3.5502f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0345f, 6.2734f, -15.5025f), new Quaternion(-0.7071065f, -1.053512E-05f, 0.7071071f, -1.245737E-05f), new Vector3(0.1f, 3.5498f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2091f, 6.2742f, 2.9965f), new Quaternion(0.7071077f, 1.245737E-05f, 0.7071059f, -1.053511E-05f), new Vector3(0.1f, 3.5497f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.109f, 6.2735f, -6.9528f), new Quaternion(-0.7071063f, 1.053511E-05f, 0.7071073f, 1.245738E-05f), new Vector3(0.1f, 3.5497f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2091f, 6.2741f, 1.1965f), new Quaternion(-0.707106f, -1.053509E-05f, 0.7071076f, -1.245735E-05f), new Vector3(0.1f, 3.5497f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.109f, 6.2735f, -5.1528f), new Quaternion(0.7071073f, -1.245736E-05f, 0.7071063f, 1.05351E-05f), new Vector3(0.1f, 3.5497f, 0.15f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.043f, 6.273f, -18.6524f), new Quaternion(-9.238721E-07f, 1.697339E-05f, 1f, 1.35927E-06f), new Vector3(0.1f, 3.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5842f, 1.9737f, -11.4525f), new Quaternion(-0.7071066f, -1.053513E-05f, 0.707107f, -1.245738E-05f), new Vector3(0.1f, 3.5502f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.1428f, 6.273f, -18.6527f), new Quaternion(-1.087785E-06f, 1.697338E-05f, 1f, 1.359274E-06f), new Vector3(0.1f, 3.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5844f, 1.9737f, -18.7601f), new Quaternion(-0.7071066f, -1.053513E-05f, 0.707107f, -1.245737E-05f), new Vector3(0.0853f, 3.5502f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.4786f, 1.9737f, -18.6525f), new Quaternion(1f, 1.625815E-05f, -2.68221E-07f, 1.359229E-06f), new Vector3(0.0885f, 3.5502f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.6901f, 1.9737f, -18.5527f), new Quaternion(1f, 1.625815E-05f, -2.68221E-07f, 1.359229E-06f), new Vector3(0.0885f, 3.5502f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2344f, 1.9736f, -18.6525f), new Quaternion(1f, 1.625815E-05f, -2.682209E-07f, 1.359229E-06f), new Vector3(0.1f, 3.5502f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0345f, 6.2734f, -17.2525f), new Quaternion(-0.7071066f, -1.053512E-05f, 0.707107f, -1.245737E-05f), new Vector3(0.1f, 3.5498f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2091f, 6.2742f, 4.7965f), new Quaternion(0.7071077f, 1.245737E-05f, 0.7071059f, -1.053511E-05f), new Vector3(0.1f, 3.5497f, 0.1499f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.109f, 6.2735f, -8.7529f), new Quaternion(-0.7071063f, 1.05351E-05f, 0.7071073f, 1.245738E-05f), new Vector3(0.1f, 3.5497f, 0.1499f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.7843f, 6.273f, -18.6526f), new Quaternion(-9.238721E-07f, 1.697339E-05f, 1f, 1.359272E-06f), new Vector3(0.1f, 3.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.4877f, 0.348f, -12.3864f), new Quaternion(0f, -0.707105f, 0f, 0.7071086f), new Vector3(1f, 0.5f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.1128f, 0.798f, -12.3864f), new Quaternion(0f, -0.707105f, 0f, 0.7071086f), new Vector3(1f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0708f, 1.7536f, -9.1669f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(2.7496f, 1.3321f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2341f, 0.3464f, -11.2978f), new Quaternion(0f, -0.6355758f, 0f, 0.7720385f), new Vector3(0.05f, 0.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5356f, 0.3464f, -13.0983f), new Quaternion(0f, -0.6355758f, 0f, 0.7720385f), new Vector3(0.05f, 0.5f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0707f, 0.3333f, -9.4657f), new Quaternion(0f, 1f, 0f, -3.218651E-06f), new Vector3(2.5001f, 0.4696f, 0.573f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.6127f, 0.798f, -12.0114f), new Quaternion(0f, 1f, 0f, -2.980233E-06f), new Vector3(0.75f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.6127f, 0.798f, -15.7614f), new Quaternion(0f, 1f, 0f, -3.576279E-06f), new Vector3(0.75f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.9587f, 1.8236f, -9.2519f), new Quaternion(0f, 1f, 0f, -3.218651E-06f), new Vector3(0.7568f, 0.05f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0342f, 6.2734f, -9.9523f), new Quaternion(-0.7071065f, -1.053512E-05f, 0.7071071f, -1.245737E-05f), new Vector3(0.1f, 3.5498f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2837f, 6.2736f, 10.3485f), new Quaternion(-0.7071058f, -1.053511E-05f, 0.7071078f, -1.245738E-05f), new Vector3(0.1f, 3.7496f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.3838f, 7.1986f, 9.4008f), new Quaternion(0.5000087f, 0.5000075f, -0.499994f, -0.4999898f), new Vector3(0.1f, 3.2496f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.7309f, 4.4486f, 16.5476f), new Quaternion(0.7071092f, 0.7071044f, 9.440861E-06f, 1.068419E-05f), new Vector3(0.1f, 3.5492f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.5863f, 4.4486f, 16.5479f), new Quaternion(-7.660214E-06f, -9.525219E-06f, 0.707109f, 0.7071046f), new Vector3(0.1f, 3.2496f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2838f, 8.0984f, 9.8984f), new Quaternion(-0.4999921f, -0.5000063f, 0.5000097f, 0.4999919f), new Vector3(0.1f, 0.8f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.3839f, 7.6486f, 10.9756f), new Quaternion(-1.228359E-06f, -0.7071181f, 3.471018E-06f, 0.7070955f), new Vector3(0.1f, 0.8f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4555f, 4.8987f, 16.5475f), new Quaternion(3.322958E-06f, 1f, -1.585805E-06f, 1.472235E-05f), new Vector3(0.1f, 0.8f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.0115f, 4.8987f, 16.5481f), new Quaternion(1.585813E-06f, -1.239777E-05f, 3.322954E-06f, 1f), new Vector3(0.1f, 0.8f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2838f, 4.4488f, 9.8984f), new Quaternion(-0.4999921f, -0.5000063f, 0.5000098f, 0.4999919f), new Vector3(0.1f, 0.8002f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.3839f, 7.6487f, 7.826f), new Quaternion(-1.228358E-06f, -0.7071182f, 3.471017E-06f, 0.7070954f), new Vector3(0.1f, 0.8002f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.0063f, 4.8987f, 16.5474f), new Quaternion(3.322958E-06f, 1f, -1.585803E-06f, 1.472235E-05f), new Vector3(0.1f, 0.8002f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.1612f, 4.8987f, 16.5481f), new Quaternion(1.585813E-06f, -1.245737E-05f, 3.322953E-06f, 1f), new Vector3(0.1f, 0.8002f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.3458f, 0.3486f, -9.4519f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(0.05f, 0.5f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.5958f, 0.3236f, -9.4519f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(0.05f, 0.45f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9708f, 0.3236f, -9.4518f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(0.6206f, 0.2789f, 0.6432f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.1708f, 0.3236f, -9.4519f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(0.6206f, 0.2789f, 0.6432f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0708f, 0.3236f, -9.4518f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(0.8866f, 0.2789f, 0.6433f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.7958f, 0.3486f, -9.4519f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(0.05f, 0.5f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.5458f, 0.3236f, -9.4519f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(0.05f, 0.45f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.73f, 2.13f, -13.7354f), new Quaternion(0.0553871f, 8.94561E-08f, 0.998465f, -3.357271E-08f), new Vector3(0.0628f, 4.1971f, 0.0628f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.73f, 0.122f, -13.7354f), new Quaternion(0.05538709f, 8.945609E-08f, 0.998465f, -3.357271E-08f), new Vector3(0.6741f, 0.0737f, 2.4514f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.73f, 0.122f, -13.7354f), new Quaternion(-0.2144625f, 7.717233E-08f, 0.9767323f, -5.633642E-08f), new Vector3(0.6741f, 0.0737f, 2.4514f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.73f, 0.122f, -13.7354f), new Quaternion(-0.4640683f, 5.961599E-08f, 0.8857995f, -7.466715E-08f), new Vector3(0.6741f, 0.0737f, 2.4514f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.73f, 0.122f, -13.7354f), new Quaternion(-0.6831703f, 3.75828E-08f, 0.7302591f, -8.784327E-08f), new Vector3(0.6741f, 0.0737f, 2.4514f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.73f, 0.122f, -13.7354f), new Quaternion(0.8546536f, -1.252943E-08f, -0.5191988f, 9.472448E-08f), new Vector3(0.6741f, 0.0737f, 2.4514f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.73f, 0.122f, -13.7354f), new Quaternion(0.956858f, 1.139986E-08f, -0.2905559f, 9.486544E-08f), new Vector3(0.4894f, 0.0737f, 2.4514f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.2789f, 0.6036f, -9.3519f), new Quaternion(0f, 0.9984649f, 0f, -0.05538794f), new Vector3(0.6f, 1f, 0.45f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.4467f, 0.348f, -14.2587f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.7377f, 0.348f, -14.3864f), new Quaternion(0f, -0.7071047f, 0f, 0.7071089f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.7377f, 0.348f, -15.3864f), new Quaternion(0f, -0.7071047f, 0f, 0.7071089f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1708f, 0.6636f, -9.4519f), new Quaternion(0f, 1f, 0f, -3.159047E-06f), new Vector3(0.4158f, 0.0618f, 0.2705f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9708f, 0.6636f, -9.4519f), new Quaternion(0.9873103f, 2.912994E-06f, 0.1588033f, 4.685049E-07f), new Vector3(0.2613f, 0.0618f, 0.2705f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.9587f, 1.3236f, -9.2519f), new Quaternion(0f, 1f, 0f, -3.218651E-06f), new Vector3(0.7568f, 0.05f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2708f, 0.7536f, -9.4519f), new Quaternion(0.7071058f, 0.7071078f, 6.079674E-06f, -2.507734E-06f), new Vector3(0.2982f, 0.1204f, 0.3087f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5842f, 0.1486f, -14.1526f), new Quaternion(0.4999919f, -0.4999916f, -0.5000085f, 0.500008f), new Vector3(0.1f, 9.3003f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.3843f, 0.1486f, -18.6527f), new Quaternion(-0.7071066f, 0.707107f, 1.166414E-05f, -1.125321E-05f), new Vector3(0.1f, 3.3f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.3845f, 3.7986f, -18.6525f), new Quaternion(-0.7071066f, 0.707107f, 1.166414E-05f, -1.125321E-05f), new Vector3(0.1f, 3.3f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0343f, 4.4485f, -13.6024f), new Quaternion(0.4999919f, -0.4999917f, -0.5000084f, 0.5000079f), new Vector3(0.1f, 7.4002f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2091f, 4.4493f, 2.0966f), new Quaternion(-0.5000264f, 0.4999907f, -0.4999768f, 0.5000061f), new Vector3(0.1f, 5.5002f, 0.1499f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.109f, 4.4487f, -6.0529f), new Quaternion(-0.4999768f, 0.5000066f, 0.5000262f, -0.4999905f), new Vector3(0.1f, 5.5002f, 0.1499f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0135f, 4.4482f, -18.6526f), new Quaternion(-3.429709E-05f, -1.306556E-05f, -0.7071087f, 0.7071049f), new Vector3(0.1f, 5.6414f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1708f, 0.6636f, -9.4519f), new Quaternion(0f, 1f, 0f, -3.159047E-06f), new Vector3(0.4745f, 0.1204f, 0.3087f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.9708f, 0.6636f, -9.4519f), new Quaternion(0.9873103f, 2.912994E-06f, 0.1588034f, 4.685041E-07f), new Vector3(0.2982f, 0.1204f, 0.3087f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.179f, 2.3236f, -9.2519f), new Quaternion(0f, 1f, 0f, -3.218651E-06f), new Vector3(0.7568f, 0.05f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.1967f, 0.798f, -14.6337f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(0.5f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.1127f, 0.798f, -14.3864f), new Quaternion(0f, -0.7071047f, 0f, 0.7071089f), new Vector3(1f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.1127f, 0.798f, -15.3864f), new Quaternion(0f, -0.7071047f, 0f, 0.7071089f), new Vector3(1f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.179f, 1.8236f, -9.2519f), new Quaternion(0f, 1f, 0f, -3.218651E-06f), new Vector3(0.7568f, 0.05f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.3918f, 5.9182f, -11.4474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(4.6896f, 0.05f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.3918f, 4.8593f, -11.4473f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(4.6898f, 0.9223f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.343f, 4.6122f, -11.4474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(1.3875f, 0.428f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.343f, 4.6122f, -12.9474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(1.3875f, 0.428f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.343f, 4.6122f, -9.9474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(1.3875f, 0.428f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.343f, 5.0619f, -11.4474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(1.3875f, 0.3903f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.343f, 5.0619f, -12.9474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(1.3875f, 0.3903f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.343f, 5.0619f, -9.9474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(1.3875f, 0.3903f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5843f, 3.7987f, -14.1526f), new Quaternion(0.4999919f, -0.4999916f, -0.5000085f, 0.500008f), new Vector3(0.1f, 9.3003f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.0344f, 8.0983f, -13.6024f), new Quaternion(0.4999919f, -0.4999917f, -0.5000084f, 0.5000079f), new Vector3(0.1f, 7.4003f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0135f, 8.0978f, -18.6528f), new Quaternion(-3.41285E-05f, -1.298127E-05f, -0.7071087f, 0.7071049f), new Vector3(0.1f, 5.6415f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.7757f, 0.078f, -13.0053f), new Quaternion(0f, 0.9967833f, 0f, -0.08014502f), new Vector3(2.8798f, 0.0722f, 4.2092f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.179f, 1.3236f, -9.2519f), new Quaternion(0f, 1f, 0f, -3.218651E-06f), new Vector3(0.7568f, 0.05f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.9587f, 0.6036f, -9.4519f), new Quaternion(0f, 0.997889f, 0f, 0.06494217f), new Vector3(0.6f, 1f, 0.45f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.6092f, 2.0986f, -18.6523f), new Quaternion(-1.047349E-05f, 1.005203E-05f, -0.7071069f, 0.7071066f), new Vector3(4f, 3.1497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.934f, 2.0986f, -8.9524f), new Quaternion(-1.069477E-05f, 1.017847E-05f, -0.7071069f, 0.7071066f), new Vector3(4f, 4.9999f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.3918f, 6.4182f, -11.4474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(4.6896f, 0.05f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4467f, 0.348f, -14.0087f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(1f, 0.5f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0708f, 0.5736f, -9.4519f), new Quaternion(0f, 1f, 0f, -3.218651E-06f), new Vector3(2.5f, 0.05f, 0.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.584f, 2.0988f, -9.3024f), new Quaternion(0.499992f, -0.4999916f, -0.5000085f, 0.500008f), new Vector3(4.0003f, 0.3999f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5842f, 3.9738f, -14.1526f), new Quaternion(0.4999919f, -0.4999917f, -0.5000084f, 0.500008f), new Vector3(0.2502f, 9.3003f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.3845f, 3.9737f, -18.6525f), new Quaternion(-0.7071066f, 0.7071069f, 1.192756E-05f, -1.167468E-05f), new Vector3(0.2502f, 3.3f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0091f, 2.0986f, -18.6523f), new Quaternion(-1.069477E-05f, 9.67271E-06f, -0.7071074f, 0.7071062f), new Vector3(4f, 5.6503f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.061f, 2.099f, -18.5094f), new Quaternion(-1.069477E-05f, 9.67271E-06f, -0.7071074f, 0.7071062f), new Vector3(4f, 1.5f, 0.0793f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.561f, 3.4323f, -18.5095f), new Quaternion(-1.069477E-05f, 9.67271E-06f, -0.7071074f, 0.7071062f), new Vector3(1.3334f, 1.5f, 0.0793f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.061f, 3.4323f, -18.5095f), new Quaternion(-1.069477E-05f, 9.67271E-06f, -0.7071074f, 0.7071062f), new Vector3(1.3334f, 1.5f, 0.0793f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.561f, 2.099f, -18.5095f), new Quaternion(-1.069477E-05f, 9.67271E-06f, -0.7071074f, 0.7071062f), new Vector3(1.3334f, 1.5f, 0.0793f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.061f, 2.099f, -18.5095f), new Quaternion(-1.069477E-05f, 9.67271E-06f, -0.7071074f, 0.7071062f), new Vector3(1.3334f, 1.5f, 0.0793f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.561f, 0.7657f, -18.5095f), new Quaternion(-1.069477E-05f, 9.67271E-06f, -0.7071074f, 0.7071062f), new Vector3(1.3334f, 1.5f, 0.0793f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.061f, 0.7656f, -18.5095f), new Quaternion(-1.069477E-05f, 9.67271E-06f, -0.7071074f, 0.7071062f), new Vector3(1.3334f, 1.5f, 0.0793f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.561f, 2.099f, -18.5095f), new Quaternion(-1.069477E-05f, 9.67271E-06f, -0.7071074f, 0.7071062f), new Vector3(4f, 1.5f, 0.0793f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2843f, 8.2741f, 2.0965f), new Quaternion(0.4999919f, -0.4999917f, -0.5000083f, 0.5000079f), new Vector3(0.2502f, 5.5001f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.034f, 8.2734f, -6.0526f), new Quaternion(0.500007f, -0.5000066f, 0.4999933f, -0.4999931f), new Vector3(0.2502f, 5.4997f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0134f, 8.2729f, -18.6527f), new Quaternion(0.7071071f, -0.7071065f, -9.567331E-06f, 1.052617E-05f), new Vector3(0.2502f, 5.6418f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.9855f, 8.2734f, -6.0521f), new Quaternion(0.4999923f, -0.4999928f, -0.5000075f, 0.5000074f), new Vector3(0.2502f, 1.8998f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2838f, 8.2735f, 9.8984f), new Quaternion(0.4999919f, -0.4999917f, -0.5000084f, 0.5000079f), new Vector3(0.2502f, 1f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.7341f, 3.9735f, -6.0522f), new Quaternion(0.4999923f, -0.4999928f, -0.5000075f, 0.5000074f), new Vector3(0.2502f, 3.2001f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.609f, 3.9735f, -8.9523f), new Quaternion(-1.092658E-05f, 9.778075E-06f, -0.7071072f, 0.7071064f), new Vector3(0.2502f, 4.3502f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.2708f, 0.7536f, -9.4519f), new Quaternion(0.707106f, 0.7071076f, 6.300946E-06f, -2.802763E-06f), new Vector3(0.2613f, 0.0618f, 0.2705f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4467f, 0.348f, -11.7587f), new Quaternion(0f, 0.70711f, 0f, 0.7071036f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.7377f, 0.348f, -13.3864f), new Quaternion(0f, -0.7071047f, 0f, 0.7071089f), new Vector3(1f, 0.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.149f, 2.0988f, -17.5967f), new Quaternion(0.4999918f, -0.4999917f, -0.5000084f, 0.500008f), new Vector3(3.9995f, 1.8108f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.3557f, 2.0986f, -18.0526f), new Quaternion(0.4999919f, -0.4999916f, -0.5000085f, 0.500008f), new Vector3(4f, 1.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.3554f, 2.0986f, -9.5523f), new Quaternion(0.4999919f, -0.4999916f, -0.5000085f, 0.500008f), new Vector3(4f, 1.5f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.3918f, 6.9182f, -11.4474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(4.6896f, 0.05f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.0698f, 2.1006f, -18.6522f), new Quaternion(-1.074745E-05f, 9.988809E-06f, -0.7071071f, 0.7071065f), new Vector3(3.9961f, 2.4712f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.97f, 2.0986f, -18.4026f), new Quaternion(-1.047349E-05f, 1.005203E-05f, -0.7071069f, 0.7071066f), new Vector3(4f, 0.9286f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.9697f, 2.0986f, -9.2023f), new Quaternion(-1.046296E-05f, 1.011525E-05f, -0.7071069f, 0.7071066f), new Vector3(4f, 0.9286f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.3918f, 8.1459f, -11.4474f), new Quaternion(0f, 0.7071099f, 0f, 0.7071037f), new Vector3(4.6897f, 0.5052f, 0.332f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.0708f, 1.7536f, -9.1519f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(2.8305f, 1.4105f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.4467f, 0.798f, -14.6337f), new Quaternion(0f, 1f, 0f, -3.099442E-06f), new Vector3(1f, 0.4f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.2559f, 5.4974f, -13.8025f), new Quaternion(0.5000095f, -0.5000091f, 0.499991f, -0.4999906f), new Vector3(0.05f, 9.8f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5451f, 5.4974f, -18.7521f), new Quaternion(-1.435101E-05f, 1.376095E-05f, -0.7071071f, 0.7071065f), new Vector3(0.05f, 10.4788f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.9449f, 5.4974f, -8.8523f), new Quaternion(-1.424564E-05f, 1.373988E-05f, -0.707107f, 0.7071066f), new Vector3(0.05f, 3.2785f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-31.7348f, 5.497f, -3.0522f), new Quaternion(0.7071068f, -0.7071068f, -1.449851E-05f, 1.449851E-05f), new Vector3(0.05f, 1.1987f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.9338f, 5.4971f, 7.1052f), new Quaternion(0.7071068f, -0.7071068f, -1.449851E-05f, 1.449852E-05f), new Vector3(0.05f, 2.8f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-32.3841f, 5.497f, 2.0266f), new Quaternion(0.5000106f, -0.5000102f, 0.4999897f, -0.4999894f), new Vector3(0.05f, 10.0574f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.634f, 5.4974f, -6.0522f), new Quaternion(0.4999893f, -0.4999894f, -0.5000107f, 0.5000106f), new Vector3(0.05f, 5.4998f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.2559f, 4.9374f, -13.8025f), new Quaternion(0.5000092f, -0.5000092f, 0.4999908f, -0.4999908f), new Vector3(1.0777f, 9.8001f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.5451f, 4.9374f, -18.7523f), new Quaternion(-1.426672E-05f, 1.359237E-05f, -0.7071072f, 0.7071064f), new Vector3(1.0777f, 10.4788f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.9448f, 4.9374f, -8.8524f), new Quaternion(-1.425618E-05f, 1.373988E-05f, -0.7071073f, 0.7071063f), new Vector3(1.0777f, 3.2785f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-31.7348f, 4.937f, -3.0521f), new Quaternion(-0.7071066f, 0.7071069f, 1.463549E-05f, -1.451959E-05f), new Vector3(1.0777f, 1.1988f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.9339f, 4.9371f, 7.1053f), new Quaternion(-0.7071066f, 0.7071069f, 1.463549E-05f, -1.451959E-05f), new Vector3(1.0777f, 2.8001f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-32.3842f, 4.937f, 2.0266f), new Quaternion(0.5000109f, -0.5000101f, 0.4999899f, -0.4999892f), new Vector3(1.0777f, 10.0575f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.634f, 4.9374f, -6.0522f), new Quaternion(0.4999893f, -0.4999894f, -0.5000107f, 0.5000106f), new Vector3(1.0777f, 5.4999f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.2557f, 4.9605f, -18.7525f), new Quaternion(0.5000095f, -0.5000091f, 0.499991f, -0.4999906f), new Vector3(1.1238f, 0.1f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.8344f, 4.9604f, -18.7527f), new Quaternion(0.5000095f, -0.5000091f, 0.499991f, -0.4999906f), new Vector3(1.1238f, 0.1f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.2556f, 4.9605f, -8.8525f), new Quaternion(0.5000095f, -0.5000091f, 0.499991f, -0.4999906f), new Vector3(1.1238f, 0.1f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-32.3842f, 4.9601f, -3.052f), new Quaternion(0.4999905f, -0.4999906f, -0.5000095f, 0.5000095f), new Vector3(1.1238f, 0.1f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-32.3839f, 4.9601f, 7.1054f), new Quaternion(0.4999905f, -0.4999906f, -0.5000095f, 0.5000095f), new Vector3(1.1238f, 0.1f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5489f, 0.5541f, -12.9164f), new Quaternion(-7.414798E-08f, 0.7071081f, -3.216264E-07f, 0.7071055f), new Vector3(5.05f, 0.9f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5489f, 1.7241f, -9.7874f), new Quaternion(-3.216264E-07f, -0.7071058f, 7.414788E-08f, 0.7071078f), new Vector3(1.1079f, 3.25f, 0.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5989f, 1.7291f, -16.0953f), new Quaternion(-3.216264E-07f, -0.7071058f, 7.414788E-08f, 0.7071078f), new Vector3(1.1079f, 3.25f, 0.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5989f, 1.7291f, -17.5703f), new Quaternion(-3.216264E-07f, -0.7071058f, 7.414787E-08f, 0.7071078f), new Vector3(1.6422f, 3.25f, 0.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.8598f, 1.0641f, -9.4952f), new Quaternion(2.79855E-07f, 1f, 1.749933E-07f, -2.980233E-06f), new Vector3(1f, 0.1f, 0.7158f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.8597f, 2.6841f, -9.5344f), new Quaternion(2.79855E-07f, 1f, 1.749934E-07f, -2.980232E-06f), new Vector3(1f, 0.1416f, 0.7158f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5489f, 1.0541f, -11.4164f), new Quaternion(-3.216263E-07f, -0.7071058f, 7.414794E-08f, 0.7071078f), new Vector3(1.9501f, 0.1f, 0.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.1489f, 1.0541f, -12.8914f), new Quaternion(-3.216262E-07f, -0.7071058f, 7.414793E-08f, 0.7071078f), new Vector3(1f, 0.1f, 0.121f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.8881f, 1.0541f, -12.8914f), new Quaternion(-3.216264E-07f, -0.7071058f, 7.41479E-08f, 0.7071078f), new Vector3(1f, 0.1f, 0.2216f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5489f, 1.0141f, -12.8914f), new Quaternion(-3.216264E-07f, -0.7071056f, 7.414784E-08f, 0.707108f), new Vector3(1f, 0.0044f, 0.6358f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.8489f, 1.3041f, -12.8914f), new Quaternion(-3.216263E-07f, -0.7071058f, 7.414793E-08f, 0.7071078f), new Vector3(0.05f, 0.4f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.8489f, 1.2041f, -12.8914f), new Quaternion(0.499999f, -0.4999995f, -0.5000008f, 0.5000008f), new Vector3(0.06f, 0.1891f, 0.06f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.0971f, 1.0541f, -9.5522f), new Quaternion(-1.749928E-07f, 4.112721E-06f, 2.798555E-07f, 1f), new Vector3(2.2603f, 0.1f, 0.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3652f, 1.0541f, -10.8752f), new Quaternion(7.414857E-08f, 0.7071087f, 3.216264E-07f, 0.7071049f), new Vector3(3.5458f, 0.1f, 1.2036f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5489f, 3.3991f, -9.8374f), new Quaternion(-3.216264E-07f, -0.7071058f, 7.414786E-08f, 0.7071078f), new Vector3(1.2079f, 0.1f, 0.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5989f, 3.4041f, -16.9664f), new Quaternion(-3.216264E-07f, -0.7071058f, 7.414789E-08f, 0.7071078f), new Vector3(3.0501f, 0.1f, 0.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5489f, 1.7241f, -10.3914f), new Quaternion(-0.4999988f, -0.5000001f, 0.5000001f, 0.5000011f), new Vector3(3.25f, 0.1f, 0.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5989f, 1.7291f, -16.6993f), new Quaternion(-0.4999988f, -0.5000001f, 0.5000001f, 0.5000011f), new Vector3(3.25f, 0.1f, 0.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5739f, 1.7291f, -18.4415f), new Quaternion(-0.4999988f, -0.5000001f, 0.5000001f, 0.5000011f), new Vector3(3.25f, 0.1f, 0.8501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5489f, 1.7741f, -9.1835f), new Quaternion(-0.4999988f, -0.4999999f, 0.5000002f, 0.5000011f), new Vector3(3.35f, 0.1f, 0.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5989f, 1.7291f, -15.4914f), new Quaternion(-0.4999988f, -0.4999999f, 0.5000002f, 0.5000011f), new Vector3(3.25f, 0.1f, 0.9001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.1489f, 0.6041f, -12.8967f), new Quaternion(-1.875536E-06f, 1.622654E-06f, -0.7071071f, 0.7071065f), new Vector3(1f, 0.3f, 7.5893f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.1489f, 2.6041f, -12.9414f), new Quaternion(-2.054661E-06f, 1.369774E-06f, -0.7071074f, 0.7071062f), new Vector3(0.1f, 0.3f, 4.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.1489f, 1.8741f, -10.4914f), new Quaternion(-0.4999987f, -0.5000013f, 0.5000016f, -0.4999983f), new Vector3(0.1f, 0.3f, 1.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.1489f, 1.8741f, -15.3914f), new Quaternion(-0.4999987f, -0.5000013f, 0.5000016f, -0.4999983f), new Vector3(0.1f, 0.3f, 1.55f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.1489f, 1.8741f, -9.7717f), new Quaternion(-1.875536E-06f, 1.622654E-06f, -0.7071072f, 0.7071064f), new Vector3(1.55f, 0.3f, 1.3393f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.1489f, 1.8741f, -16.0664f), new Quaternion(-1.875536E-06f, 1.622654E-06f, -0.7071072f, 0.7071064f), new Vector3(1.55f, 0.3f, 1.2499f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.1489f, 3.3738f, -12.8967f), new Quaternion(-2.117881E-06f, 1.432994E-06f, -0.7071074f, 0.7071062f), new Vector3(1.4495f, 0.3f, 7.5893f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.1489f, 1.8741f, -12.8914f), new Quaternion(-0.4999987f, -0.5000014f, 0.5000015f, -0.4999984f), new Vector3(5f, 0.1f, 1.55f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.5489f, 1.0541f, -14.4164f), new Quaternion(-3.216263E-07f, -0.7071057f, 7.414793E-08f, 0.7071079f), new Vector3(2.05f, 0.1f, 0.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.8597f, 3.4298f, -9.5344f), new Quaternion(2.798549E-07f, 1f, 1.749932E-07f, -3.159047E-06f), new Vector3(0.6f, 1.3375f, 0.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.1489f, 1.1541f, -12.9414f), new Quaternion(-2.033587E-06f, 1.222259E-06f, -0.7071072f, 0.7071064f), new Vector3(0.1f, 0.3f, 4.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.7489f, 1.4741f, -12.8914f), new Quaternion(-0.500001f, -0.4999995f, -0.4999993f, 0.5000004f), new Vector3(0.05f, 0.2f, 0.05f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.9739f, 0.5491f, -9.5335f), new Quaternion(-2.798554E-07f, 1f, -1.749929E-07f, -4.649163E-06f), new Vector3(2.25f, 0.9f, 0.8f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.4023f, 0.5541f, -10.8523f), new Quaternion(3.216265E-07f, -0.7071046f, -7.414846E-08f, 0.707109f), new Vector3(3.5f, 0.9f, 0.8932f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.8666f, -0.0514f, -13.8022f), new Quaternion(-5.373727E-07f, 1f, 5.268354E-08f, -1.376867E-05f), new Vector3(14.8646f, 0.3f, 10.0006f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(1.3009f, -0.6f, -18.0954f), new Quaternion(0.5000073f, 0.5000079f, 0.4999922f, 0.4999927f), new Vector3(1.4475f, 1.1621f, 8.6537f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(1.5262f, -1.5467f, -12.3797f), new Quaternion(0.7071177f, 7.246439E-07f, 0.7070959f, 6.128798E-07f), new Vector3(14.7112f, 0.3f, 17.8535f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.54f, -0.6f, -12.7054f), new Quaternion(1.011525E-05f, 1.053672E-05f, 0.707107f, 0.7071066f), new Vector3(1.4475f, 1.1621f, 4.6331f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.33f, -0.6f, -17.9254f), new Quaternion(-0.4002922f, -0.4002914f, 0.582895f, 0.5828949f), new Vector3(1.4475f, 1.1621f, 1.5977f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.88f, -0.6f, -15.9354f), new Quaternion(0.7061359f, 0.7061359f, -0.03704179f, -0.03704266f), new Vector3(1.4475f, 1.1621f, 1.2473f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.79f, -0.6f, -14.9254f), new Quaternion(0.7005346f, 0.7005343f, 0.09618558f, 0.09618422f), new Vector3(1.4475f, 1.1621f, 1.2473f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.13f, -0.6f, -17.5354f), new Quaternion(-0.3380794f, -0.3380784f, 0.6210496f, 0.6210496f), new Vector3(1.4475f, 1.1621f, 1.4119f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.58f, -0.6f, -16.8154f), new Quaternion(0.6782064f, 0.6782057f, -0.2000912f, -0.2000917f), new Vector3(1.4475f, 1.1621f, 1.1023f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.74f, -0.6f, -17.0154f), new Quaternion(-0.2396728f, -0.2396721f, 0.6652499f, 0.6652496f), new Vector3(1.4475f, 1.1621f, 1.0914f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.07f, -0.6f, -17.4254f), new Quaternion(0.6398765f, 0.6398758f, -0.3009293f, -0.3009295f), new Vector3(1.4475f, 1.1621f, 0.8521f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.11f, -0.6f, -16.4254f), new Quaternion(-0.1507898f, -0.1507887f, 0.690842f, 0.6908419f), new Vector3(1.4475f, 1.1621f, 1.0914f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(6.47f, -0.6f, -17.7954f), new Quaternion(0.5951228f, 0.5951225f, -0.3818754f, -0.3818759f), new Vector3(1.4475f, 1.1621f, 0.8521f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.4f, -0.6f, -15.6254f), new Quaternion(-0.1011703f, -0.1011695f, 0.6998318f, 0.699832f), new Vector3(1.4475f, 1.1621f, 1.4939f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(5.82f, -0.6f, -18.0411f), new Quaternion(0.5663936f, 0.5663931f, -0.4233184f, -0.4233189f), new Vector3(1.4475f, 1.1621f, 0.8536f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.4553f, -0.6f, -9.7486f), new Quaternion(0.0640564f, 0.0640567f, 0.7041996f, 0.7041993f), new Vector3(1.4475f, 1.1621f, 1.6367f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.0573f, -0.6f, -8.7291f), new Quaternion(0.221864f, 0.2223518f, 0.6705799f, 0.6720557f), new Vector3(1.4475f, 1.1621f, 1.1194f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.4586f, -0.6f, -8.0359f), new Quaternion(0.3035084f, 0.3025618f, 0.6398786f, 0.6378827f), new Vector3(1.4475f, 1.1621f, 1.2677f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.71f, -0.65f, -7.5454f), new Quaternion(0.3776425f, 0.3625877f, 0.6010104f, 0.6039064f), new Vector3(1.4475f, 1.1621f, 1.5904f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.7f, -0.6f, -7.5354f), new Quaternion(0.3715779f, 0.3687414f, 0.604782f, 0.6001654f), new Vector3(1.4475f, 1.1621f, 1.0132f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-0.9876f, -0.6002f, -6.5279f), new Quaternion(0.4056478f, 0.4027365f, 0.582468f, 0.5779138f), new Vector3(1.4475f, 1.1621f, 4.8722f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(1.66f, -0.6f, -5.7154f), new Quaternion(0.4674998f, 0.4641337f, 0.5339073f, 0.5300631f), new Vector3(1.4475f, 1.1621f, 1.1077f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(2.43f, -0.6f, -5.7554f), new Quaternion(0.5627449f, 0.5586929f, 0.4323556f, 0.4292423f), new Vector3(1.4475f, 1.1621f, 0.931f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(3.09f, -0.6f, -6.0354f), new Quaternion(0.6196577f, 0.6151957f, 0.3458871f, 0.343396f), new Vector3(1.4475f, 1.1621f, 0.931f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(3.7f, -0.6f, -6.4754f), new Quaternion(0.6434835f, 0.6388495f, 0.2992372f, 0.2970817f), new Vector3(1.4475f, 1.1621f, 0.931f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(4.09f, -0.6f, -6.8354f), new Quaternion(0.6686454f, 0.6638302f, 0.2377544f, 0.2360417f), new Vector3(1.4475f, 1.1621f, 0.398f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(5.9169f, -0.6f, -10.6585f), new Quaternion(-0.1535979f, -0.152492f, 0.692835f, 0.6878471f), new Vector3(1.4475f, 1.1621f, 8.3762f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2839f, 6.3986f, 8.3761f), new Quaternion(-3.266276E-07f, -4.467423E-06f, -0.7070844f, 0.7071292f), new Vector3(3.9998f, 0.3f, 2.0445f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.3838f, 8.2738f, 9.2488f), new Quaternion(-1.264362E-07f, -4.76244E-06f, -0.7070843f, 0.7071293f), new Vector3(0.2504f, 0.3f, 4.3987f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.3838f, 7.6486f, 11.2369f), new Quaternion(-3.266274E-07f, -4.804586E-06f, -0.7070844f, 0.7071292f), new Vector3(1.0001f, 0.3f, 0.4225f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2335f, 4.8987f, 16.5476f), new Quaternion(0.499983f, -0.5000182f, -0.4999849f, 0.5000138f), new Vector3(1.0001f, 0.3f, 1.456f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.3838f, 7.6486f, 7.4146f), new Quaternion(-3.371638E-07f, -4.846732E-06f, -0.7070844f, 0.7071292f), new Vector3(1.0001f, 0.3f, 0.7227f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.5725f, 4.8987f, 16.5479f), new Quaternion(-0.4999862f, 0.5000153f, -0.4999818f, 0.5000167f), new Vector3(1.0001f, 0.3f, 0.7227f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.4451f, 4.8987f, 16.5477f), new Quaternion(-0.4999862f, 0.5000153f, -0.4999818f, 0.5000167f), new Vector3(1.0001f, 0.3f, 1.0223f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9339f, 5.5487f, 16.5479f), new Quaternion(-0.4999862f, 0.5000152f, -0.4999818f, 0.5000167f), new Vector3(0.2999f, 0.3f, 9.9999f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.3837f, 5.7736f, 9.2507f), new Quaternion(-3.266272E-07f, -4.804586E-06f, -0.7070844f, 0.7071292f), new Vector3(2.7498f, 0.3f, 4.3948f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2838f, 6.3987f, 10.9234f), new Quaternion(-3.266274E-07f, -4.804586E-06f, -0.7070844f, 0.7071292f), new Vector3(3.9997f, 0.3f, 1.0499f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.2817f, 5.3704f, -17.6931f), new Quaternion(-5.057634E-07f, 0.707109f, -5.057619E-07f, 0.7071046f), new Vector3(1.3f, 0.1f, 2.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.4818f, 4.8704f, -17.1931f), new Quaternion(-5.057631E-07f, 0.7071089f, -5.057615E-07f, 0.7071047f), new Vector3(0.1f, 0.9f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.7717f, 4.8704f, -18.2028f), new Quaternion(-1.463718E-12f, 1f, -7.152562E-07f, -2.205372E-06f), new Vector3(0.1f, 0.9f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.0817f, 4.8704f, -18.1931f), new Quaternion(-5.057631E-07f, 0.7071089f, -5.057615E-07f, 0.7071047f), new Vector3(0.1f, 0.9f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.7717f, 4.8704f, -15.8028f), new Quaternion(-1.463718E-12f, 1f, -7.152562E-07f, -2.205372E-06f), new Vector3(0.1f, 0.9f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.7717f, 4.8704f, -18.2028f), new Quaternion(2.056361E-06f, 3.57628E-07f, 1f, -1.200817E-12f), new Vector3(0.1f, 0.9f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.0817f, 4.8704f, -17.1931f), new Quaternion(-0.7071047f, 2.528798E-07f, 0.7071089f, -7.586435E-07f), new Vector3(0.1f, 0.9f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.4818f, 4.8704f, -18.1931f), new Quaternion(-0.7071047f, 2.528798E-07f, 0.7071089f, -7.586435E-07f), new Vector3(0.1f, 0.9f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.7717f, 4.8704f, -15.8028f), new Quaternion(2.056361E-06f, 3.57628E-07f, 1f, -1.200817E-12f), new Vector3(0.1f, 0.9f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.2717f, 5.3704f, -17.0028f), new Quaternion(-1.406875E-12f, 1f, -7.152564E-07f, -2.205372E-06f), new Vector3(1.3f, 0.1f, 2.7f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.6839f, -0.1764f, 7.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.1841f, -0.1764f, 7.9478f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.184f, -0.1764f, 10.3978f), new Quaternion(0.5000058f, 0.500006f, 0.4999942f, 0.4999941f), new Vector3(0.55f, 2.4f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.6841f, -0.1764f, 7.9479f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.684f, -0.1764f, 10.3978f), new Quaternion(0.5000058f, 0.500006f, 0.4999942f, 0.4999941f), new Vector3(0.55f, 2.4f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.1839f, -0.1764f, 7.9479f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.1839f, -0.1764f, 10.3979f), new Quaternion(0.5000058f, 0.500006f, 0.4999942f, 0.4999941f), new Vector3(0.55f, 2.4f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.6839f, -0.1764f, 2.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.184f, -0.1764f, 2.9478f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.684f, -0.1764f, 2.9478f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.1839f, -0.1764f, 2.9479f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.6839f, -0.1764f, 15.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.6839f, -0.1764f, 5.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.184f, -0.1764f, 5.4478f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.684f, -0.1764f, 5.4478f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.1839f, -0.1764f, 5.4479f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.6839f, -0.1764f, -2.0523f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.184f, -0.1764f, -2.0522f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.684f, -0.1764f, -2.0522f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.1839f, -0.1764f, -2.0521f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.6839f, -0.1764f, 10.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.6839f, -0.1764f, 0.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.184f, -0.1764f, 0.4478f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.684f, -0.1764f, 0.4478f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.1839f, -0.1764f, 0.4479f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.6839f, -0.1764f, 12.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1839f, -0.1764f, 7.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1839f, -0.1764f, 2.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1839f, -0.1764f, 15.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1839f, -0.1764f, 5.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1839f, -0.1764f, -2.0523f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1839f, -0.1764f, 10.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1839f, -0.1764f, 0.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1839f, -0.1764f, 12.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6839f, -0.1764f, 7.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6839f, -0.1764f, 2.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6839f, -0.1764f, 15.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6839f, -0.1764f, 5.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6839f, -0.1764f, -2.0523f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6839f, -0.1764f, 10.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6839f, -0.1764f, 0.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6839f, -0.1764f, 12.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1839f, -0.1764f, 7.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1839f, -0.1764f, 2.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1839f, -0.1764f, 15.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1839f, -0.1764f, 5.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1839f, -0.1764f, -2.0523f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1839f, -0.1764f, 10.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1839f, -0.1764f, 0.4477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1839f, -0.1764f, 12.9477f), new Quaternion(0.5000058f, 0.500006f, 0.499994f, 0.4999942f), new Vector3(0.55f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.9662f, 0.6733f, 16.9184f), new Quaternion(0.5000083f, -0.4999924f, 0.4999921f, 0.5000073f), new Vector3(0.5001f, 0.9999f, 1.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.1417f, 0.7983f, 17.0484f), new Quaternion(0.500008f, -0.4999923f, 0.4999923f, 0.5000074f), new Vector3(0.75f, 2.5f, 1.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2917f, 3.0822f, 16.8684f), new Quaternion(1f, 5.267987E-08f, -1.643598E-05f, -7.442976E-08f), new Vector3(1f, 7.0675f, 0.3564f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-17.2917f, 6.4378f, 14.2968f), new Quaternion(-0.7071078f, -1.154826E-05f, 1.129537E-05f, 0.7071058f), new Vector3(1f, 4.7868f, 0.3564f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.0839f, 1.7633f, -2.5022f), new Quaternion(0.7071071f, -0.7071066f, -1.171682E-05f, 1.15377E-05f), new Vector3(3.3294f, 0.3f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.084f, 1.7633f, 15.7727f), new Quaternion(0.7071068f, -0.7071067f, -1.154824E-05f, 1.163253E-05f), new Vector3(3.3294f, 0.3f, 1.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.0839f, 1.7633f, 8.522f), new Quaternion(0.707107f, -0.7071066f, -1.137965E-05f, 1.159038E-05f), new Vector3(3.3294f, 0.3f, 1.2485f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.0839f, 4.5633f, 6.6977f), new Quaternion(0.7071069f, -0.7071066f, -1.13375E-05f, 1.157984E-05f), new Vector3(2.2706f, 0.3f, 19.3998f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.0839f, 1.6984f, 12.2983f), new Quaternion(0.499992f, -0.4999926f, -0.5000077f, 0.5000076f), new Vector3(4.2996f, 8.7985f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.0839f, 1.9734f, -2.5524f), new Quaternion(0.4999922f, -0.4999925f, -0.5000079f, 0.5000075f), new Vector3(3.7496f, 1.5001f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9339f, 2.2486f, 16.5477f), new Quaternion(0.4999923f, -0.4999919f, -0.500009f, 0.5000068f), new Vector3(4.3f, 0.3f, 9.9999f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6839f, 1.4986f, -3.1523f), new Quaternion(0.4999917f, -0.4999927f, -0.5000084f, 0.5000073f), new Vector3(2.8f, 0.3f, 7.5001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-27.884f, 2.0986f, -3.0271f), new Quaternion(0.499992f, -0.4999924f, -0.5000086f, 0.5000071f), new Vector3(4f, 0.0502f, 3.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.3839f, 3.4986f, -3.0274f), new Quaternion(0.4999919f, -0.4999924f, -0.5000086f, 0.500007f), new Vector3(1.2f, 0.0502f, 1.9f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.3339f, 2.0986f, -3.0273f), new Quaternion(0.4999919f, -0.4999924f, -0.5000086f, 0.500007f), new Vector3(4f, 0.0502f, 2.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.8334f, 2.1985f, 11.6979f), new Quaternion(0.7071066f, -8.850838E-07f, 9.588407E-07f, 0.7071069f), new Vector3(7.2001f, 0.2f, 4.1999f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.349f, -0.6754f, -12.9414f), new Quaternion(0.4999945f, -0.5000083f, 0.4999898f, 0.5000074f), new Vector3(5.0001f, 0.1f, 3.549f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.0093f, 2.0985f, -18.8526f), new Quaternion(-4.046035E-06f, 0.7071178f, -0.7070958f, -8.85071E-07f), new Vector3(5.6499f, 0.1f, 4.6f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.3489f, 5.5238f, -12.9414f), new Quaternion(0.4999945f, -0.5000083f, 0.4999898f, 0.5000074f), new Vector3(5.0001f, 0.1f, 5.7494f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.484f, 6.3991f, 6.0008f), new Quaternion(0.4999945f, -0.5000083f, 0.4999898f, 0.5000074f), new Vector3(2.3086f, 0.1f, 3.9994f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.484f, 8.2739f, 2.0965f), new Quaternion(0.4999945f, -0.5000084f, 0.4999898f, 0.5000073f), new Vector3(5.5001f, 0.1f, 0.2497f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.484f, 6.3992f, -1.828f), new Quaternion(0.4999945f, -0.5000083f, 0.4999898f, 0.5000074f), new Vector3(2.3488f, 0.1f, 3.9994f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.484f, 1.9854f, 2.1015f), new Quaternion(0.4999944f, -0.5000084f, 0.4999898f, 0.5000073f), new Vector3(10.1074f, 0.1f, 4.2259f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.8334f, 6.3983f, 11.6977f), new Quaternion(0.7071068f, -1.011524E-06f, 9.272306E-07f, 0.7071068f), new Vector3(7.2001f, 0.2f, 4.1998f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.7836f, 2.0986f, 11.4978f), new Quaternion(0.4999919f, -0.4999924f, -0.5000086f, 0.500007f), new Vector3(4f, 0.2f, 7.0997f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.8337f, 6.3984f, 11.523f), new Quaternion(0.4999919f, -0.4999924f, -0.5000086f, 0.500007f), new Vector3(4f, 0.1496f, 7.2001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.3837f, 2.0986f, 4.3229f), new Quaternion(0.7071072f, -0.7071064f, -1.283371E-05f, 1.149555E-05f), new Vector3(4f, 0.1f, 14.5501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-29.2337f, 2.0986f, 4.3229f), new Quaternion(0.7071072f, -0.7071064f, -1.283371E-05f, 1.149555E-05f), new Vector3(4f, 0.2f, 14.5501f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.6339f, 1.4986f, -3.1524f), new Quaternion(0.4999919f, -0.4999925f, -0.5000086f, 0.500007f), new Vector3(2.8f, 0.3f, 0.5999f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.9341f, 4.2986f, -3.1526f), new Quaternion(0.4999919f, -0.4999925f, -0.5000087f, 0.500007f), new Vector3(2.8f, 0.3f, 9.9997f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.084f, 3.345f, 3.0604f), new Quaternion(0.4999926f, -0.4999924f, -0.5000076f, 0.5000075f), new Vector3(1.0064f, 1.8176f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.084f, 1.9234f, 3.9488f), new Quaternion(0.4999927f, -0.499992f, -0.500008f, 0.5000073f), new Vector3(3.6497f, 0.1f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.0839f, 4.7734f, 6.6977f), new Quaternion(-1.088443E-05f, 9.398754E-06f, -0.7071072f, 0.7071064f), new Vector3(1.8503f, 0.3f, 20.0001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.084f, 0.1486f, 0.1478f), new Quaternion(-0.4999943f, 0.5000061f, 0.5000064f, 0.499993f), new Vector3(3.9001f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.084f, 1.9234f, 2.1478f), new Quaternion(0.7071074f, -0.7071062f, -8.176479E-06f, 8.492581E-06f), new Vector3(3.6497f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.4839f, 1.4486f, -3.1522f), new Quaternion(0.500007f, -0.5000061f, 0.4999941f, -0.4999928f), new Vector3(2.7f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.0838f, 1.9706f, 5.9353f), new Quaternion(0.7071076f, 1.245737E-05f, 0.707106f, -1.05351E-05f), new Vector3(3.9002f, 3.7552f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.086f, 1.9706f, 0.1449f), new Quaternion(0.7071076f, 1.245737E-05f, 0.707106f, -1.05351E-05f), new Vector3(3.9001f, 3.7552f, 0.1f), 0, SurfaceType.ArmoredGlass, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.0839f, 1.9734f, 4.0488f), new Quaternion(0.7071076f, 1.245737E-05f, 0.707106f, -1.053511E-05f), new Vector3(0.1f, 3.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.0861f, 1.9734f, 0.1449f), new Quaternion(0.7071076f, 1.245737E-05f, 0.707106f, -1.053511E-05f), new Vector3(0.1f, 3.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.086f, 1.9734f, -1.7552f), new Quaternion(0.7071076f, 1.245737E-05f, 0.707106f, -1.053511E-05f), new Vector3(0.1f, 3.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.0836f, 1.9734f, 7.849f), new Quaternion(0.7071076f, 1.245737E-05f, 0.707106f, -1.053511E-05f), new Vector3(0.1f, 3.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.084f, 1.9734f, 5.9489f), new Quaternion(0.7071076f, 1.245737E-05f, 0.707106f, -1.053511E-05f), new Vector3(0.1f, 3.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.0861f, 1.9734f, 2.045f), new Quaternion(0.7071076f, 1.245737E-05f, 0.707106f, -1.053511E-05f), new Vector3(0.1f, 3.5497f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.0839f, 0.1486f, 5.9489f), new Quaternion(-0.5000265f, 0.4999907f, -0.4999768f, 0.5000061f), new Vector3(0.1f, 3.9002f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.0863f, 3.7982f, 3.0469f), new Quaternion(-0.5000265f, 0.4999907f, -0.4999768f, 0.5000061f), new Vector3(0.1f, 9.7042f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.898f, 4.879f, 0.0516f), new Quaternion(-0.4999994f, -0.5000006f, 0.5000005f, -0.4999996f), new Vector3(0.9508f, 0.6852f, 0.4629f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.898f, 4.879f, 4.3016f), new Quaternion(-0.4999994f, -0.5000006f, 0.5000005f, -0.4999996f), new Vector3(0.9508f, 0.6852f, 0.4629f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.943f, 4.879f, 0.0516f), new Quaternion(-0.4999994f, -0.5000006f, 0.5000005f, -0.4999996f), new Vector3(0.7858f, 0.619f, 0.2774f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.9162f, 0.2683f, 16.8224f), new Quaternion(-0.4147126f, -0.4147118f, 0.5727248f, 0.572725f), new Vector3(0.85f, 2.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.8362f, 0.2783f, 17.5524f), new Quaternion(-0.4737966f, -0.4737953f, 0.5248975f, 0.5248976f), new Vector3(0.85f, 2.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(0.5538f, 0.2736f, 15.5724f), new Quaternion(-0.3961866f, -0.3961856f, 0.5856932f, 0.5856932f), new Vector3(0.85f, 2.5f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(0.4767f, 0.9743f, 15.38f), new Quaternion(-0.3961866f, -0.3961856f, 0.5856933f, 0.5856932f), new Vector3(0.5513f, 1.2391f, 0.6565f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.9939f, 0.9683f, 16.6264f), new Quaternion(-0.4171952f, -0.4171942f, 0.5709191f, 0.570919f), new Vector3(0.5513f, 1.2391f, 0.6565f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.8578f, 0.9783f, 17.3491f), new Quaternion(-0.4730727f, -0.4730717f, 0.52555f, 0.5255497f), new Vector3(0.5513f, 1.2391f, 0.6565f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(0.4818f, 1.3153f, 15.3944f), new Quaternion(-6.780029E-07f, -0.5602918f, -2.23518E-08f, 0.8282954f), new Vector3(0.5513f, 1.2391f, 0.8141f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.9846f, 1.3083f, 16.637f), new Quaternion(-7.253097E-07f, -0.5900027f, -2.093299E-08f, 0.8074014f), new Vector3(0.5513f, 1.2391f, 0.8141f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.851f, 1.3183f, 17.3607f), new Quaternion(-8.068195E-07f, -0.6690253f, 1.839481E-07f, 0.7432397f), new Vector3(0.5513f, 1.2391f, 0.8141f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(0.2768f, 1.3663f, 14.8824f), new Quaternion(-0.3961866f, -0.3961857f, 0.5856932f, 0.5856932f), new Vector3(0.5513f, 0.6246f, 0.4954f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.1571f, 1.3583f, 16.1132f), new Quaternion(-0.4171952f, -0.4171942f, 0.5709191f, 0.570919f), new Vector3(0.5513f, 0.6246f, 0.4954f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.9137f, 1.3683f, 16.8137f), new Quaternion(-0.4730727f, -0.4730717f, 0.52555f, 0.5255497f), new Vector3(0.5513f, 0.6246f, 0.4954f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.943f, 4.879f, 4.3016f), new Quaternion(-0.4999994f, -0.5000006f, 0.5000005f, -0.4999996f), new Vector3(0.7858f, 0.619f, 0.2774f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.215f, 4.523f, 0.5016f), new Quaternion(0.4999995f, 0.5000005f, -0.5000004f, 0.4999997f), new Vector3(0.05f, 0.05f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.215f, 4.523f, 4.7516f), new Quaternion(0.4999995f, 0.5000005f, -0.5000004f, 0.4999997f), new Vector3(0.05f, 0.05f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.215f, 4.523f, -0.3984f), new Quaternion(0.4999995f, 0.5000005f, -0.5000004f, 0.4999997f), new Vector3(0.05f, 0.05f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.215f, 4.523f, 3.8516f), new Quaternion(0.4999995f, 0.5000005f, -0.5000004f, 0.4999997f), new Vector3(0.05f, 0.05f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.58f, 4.523f, 0.5016f), new Quaternion(0.4999995f, 0.5000005f, -0.5000004f, 0.4999997f), new Vector3(0.05f, 0.05f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.58f, 4.523f, 4.7516f), new Quaternion(0.4999995f, 0.5000005f, -0.5000004f, 0.4999997f), new Vector3(0.05f, 0.05f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.58f, 4.523f, -0.3984f), new Quaternion(0.4999995f, 0.5000005f, -0.5000004f, 0.4999997f), new Vector3(0.05f, 0.05f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-22.58f, 4.523f, 3.8516f), new Quaternion(0.4999995f, 0.5000005f, -0.5000004f, 0.4999997f), new Vector3(0.05f, 0.05f, 0.25f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.3739f, 1.8474f, -7.2416f), new Quaternion(0.7071075f, 0f, 0.7071061f, 0f), new Vector3(0.3f, 4.0496f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.1039f, 2.2224f, -7.2416f), new Quaternion(0.7071075f, 0f, 0.7071061f, 0f), new Vector3(0.3f, 4.7996f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.0939f, 2.2729f, -16.0716f), new Quaternion(0.7071075f, 0f, 0.7071061f, 0f), new Vector3(0.3f, 4.6553f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.1039f, 1.7729f, -13.0716f), new Quaternion(0.7071075f, 0f, 0.7071061f, 0f), new Vector3(0.3f, 3.6554f, 0.3252f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.8439f, 0.8118f, -8.4616f), new Quaternion(0f, -0.3826836f, 0f, 0.9238795f), new Vector3(1f, 0.6401f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.4239f, 1.3864f, -10.3016f), new Quaternion(0f, -5.364419E-07f, 0f, 1f), new Vector3(1f, 0.1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.7639f, 2.236f, -8.5416f), new Quaternion(0f, 0.9238794f, 0f, -0.3826838f), new Vector3(1.4848f, 0.4009f, 0.741f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.1139f, 1.9364f, -8.1816f), new Quaternion(0f, 0.9238794f, 0f, -0.3826838f), new Vector3(1.4848f, 1f, 0.2589f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.4239f, 0.8364f, -10.3016f), new Quaternion(0f, -5.364419E-07f, 0f, 1f), new Vector3(1f, 1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.8539f, 3.4753f, -8.4416f), new Quaternion(0f, -0.3826836f, 0f, 0.9238795f), new Vector3(0.6445f, 0.1868f, 0.5863f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.2339f, 1.9364f, -7.8316f), new Quaternion(0f, -0.3826837f, 0f, 0.9238795f), new Vector3(1f, 1f, 0.2589f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.3739f, 2.8253f, -7.9616f), new Quaternion(0.3535534f, -0.3535538f, 0.1464467f, 0.8535532f), new Vector3(1.1138f, 0.1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.2139f, 0.8364f, -8.7916f), new Quaternion(0f, -0.3826837f, 0f, 0.9238795f), new Vector3(1f, 1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.8539f, 3.527f, -8.4416f), new Quaternion(0f, 0.9238794f, 0f, -0.3826838f), new Vector3(0.4576f, 0.6256f, 0.5217f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.0039f, 1.3864f, -7.8816f), new Quaternion(0f, -0.7071071f, 0f, 0.7071065f), new Vector3(1f, 0.1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.4139f, 0.8364f, -9.2916f), new Quaternion(0f, -5.364419E-07f, 0f, 1f), new Vector3(1f, 1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.4139f, 1.3864f, -9.2916f), new Quaternion(0f, -5.364419E-07f, 0f, 1f), new Vector3(1f, 0.1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.4939f, 0.8364f, -8.0816f), new Quaternion(0f, -0.3826837f, 0f, 0.9238795f), new Vector3(1f, 1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.4939f, 1.3864f, -8.0816f), new Quaternion(0f, -0.3826836f, 0f, 0.9238795f), new Vector3(1f, 0.1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.3339f, 2.8253f, -8.9316f), new Quaternion(-0.3535534f, -0.3535537f, -0.1464467f, 0.8535532f), new Vector3(1.1138f, 0.1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.4639f, 1.9364f, -9.0616f), new Quaternion(0f, -0.3826837f, 0f, 0.9238795f), new Vector3(1f, 1f, 0.2589f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.0039f, 0.8364f, -7.8816f), new Quaternion(0f, -0.7071071f, 0f, 0.7071065f), new Vector3(1f, 1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.8539f, 2.7753f, -8.4416f), new Quaternion(0f, 0.9238794f, 0f, -0.3826838f), new Vector3(0.7298f, 0.6778f, 1.0095f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.2539f, 1.3864f, -7.8916f), new Quaternion(0f, -0.7071071f, 0f, 0.7071065f), new Vector3(1f, 0.1f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.5339f, 2.6016f, -8.1216f), new Quaternion(-0.353554f, 0.8535531f, 0.1464468f, -0.3535535f), new Vector3(0.9993f, 0.5335f, 0.9998f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.8539f, 3.1642f, -8.4416f), new Quaternion(0f, -0.3826836f, 0f, 0.9238795f), new Vector3(1.1138f, 0.1f, 0.7304f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.1839f, 2.6016f, -8.7616f), new Quaternion(0.3535534f, 0.8535532f, -0.1464469f, -0.3535536f), new Vector3(0.9993f, 0.5335f, 0.9998f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.2539f, 0.8364f, -7.8916f), new Quaternion(0f, -0.7071071f, 0f, 0.7071066f), new Vector3(1f, 1f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.2139f, 1.3864f, -8.7916f), new Quaternion(0f, -0.3826836f, 0f, 0.9238795f), new Vector3(1f, 0.1f, 1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.1039f, 1.7729f, -10.3216f), new Quaternion(0.7071075f, 0f, 0.7071061f, 0f), new Vector3(0.3f, 3.6554f, 0.3252f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.0173f, 0.7743f, -15.3079f), new Quaternion(0f, -5.364419E-07f, 0f, 1f), new Vector3(2.9332f, 0.1924f, 0.8346f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.0174f, 1.0931f, -15.673f), new Quaternion(0.6273286f, -4.177566E-07f, 3.365253E-07f, 0.7787547f), new Vector3(2.9332f, 0.1757f, 0.6598f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(14.8518f, 0.8867f, -13.7867f), new Quaternion(0f, 0.7071069f, 0f, 0.7071066f), new Vector3(0.8903f, 0.0831f, 1.6965f), 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.7036f, 0.7781f, -13.7618f), new Quaternion(0f, 0.7071065f, 0f, 0.7071072f), new Vector3(0.3369f, 0.8833f, 0.3262f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.4343f, 0.7781f, -7.8714f), new Quaternion(0f, -0.7071066f, 0f, 0.7071069f), new Vector3(0.3369f, 0.8833f, 0.3262f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.0939f, 0.1864f, -11.8016f), new Quaternion(0.5000013f, 0.4999983f, 0.5000002f, 0.5000002f), new Vector3(0.3f, 8.832f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.0839f, 0.1864f, -7.2416f), new Quaternion(1.70695E-06f, -4.425417E-07f, 0.7071078f, 0.7071058f), new Vector3(0.3f, 7.7151f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.144f, 0.1864f, -16.0716f), new Quaternion(1.70695E-06f, -4.425417E-07f, 0.7071078f, 0.7071058f), new Vector3(0.3f, 3.5951f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.394f, 0.8364f, -16.1716f), new Quaternion(1.70695E-06f, -4.425417E-07f, 0.7071078f, 0.7071058f), new Vector3(1f, 0.1f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.194f, 1.2864f, -16.1716f), new Quaternion(1.490944E-06f, 8.940696E-07f, 1.111624E-06f, 1f), new Vector3(3.4951f, 0.1f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.2239f, 1.2864f, -7.1716f), new Quaternion(1.490944E-06f, 8.940697E-07f, 1.111624E-06f, 1f), new Vector3(7.4432f, 0.1f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.1939f, 1.2864f, -11.6516f), new Quaternion(1.840294E-06f, -0.7071061f, -2.682196E-07f, 0.7071075f), new Vector3(8.5296f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.1939f, 2.2864f, -11.6516f), new Quaternion(1.840294E-06f, -0.7071061f, -2.682196E-07f, 0.7071075f), new Vector3(8.5296f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.1939f, 3.2864f, -11.6516f), new Quaternion(1.840294E-06f, -0.7071061f, -2.682196E-07f, 0.7071075f), new Vector3(8.5296f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.1939f, 0.7864f, -11.6516f), new Quaternion(1.840294E-06f, -0.7071061f, -2.682196E-07f, 0.7071075f), new Vector3(8.5296f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.1939f, 1.7864f, -11.6516f), new Quaternion(1.840294E-06f, -0.7071061f, -2.682196E-07f, 0.7071075f), new Vector3(8.5296f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.1939f, 2.7864f, -11.6516f), new Quaternion(1.840294E-06f, -0.7071061f, -2.682196E-07f, 0.7071075f), new Vector3(8.5296f, 0.3f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.1139f, 0.1864f, -11.6516f), new Quaternion(1.70695E-06f, -4.425417E-07f, 0.7071078f, 0.7071058f), new Vector3(0.3f, 3.646f, 8.532f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(11.4239f, 0.1654f, -11.7416f), new Quaternion(-0.6903458f, -0.1530452f, -0.1530452f, 0.6903457f), new Vector3(0.3f, 9.7487f, 0.3f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(11.1539f, 0.1154f, -11.8616f), new Quaternion(-0.6903458f, -0.1530452f, -0.1530452f, 0.6903457f), new Vector3(0.3f, 9.7457f, 0.2f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.8739f, 0.0654f, -11.9916f), new Quaternion(-0.6903458f, -0.1530452f, -0.1530452f, 0.6903457f), new Vector3(0.3f, 9.7457f, 0.1f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.3691f, -0.3456f, 14.2148f), new Quaternion(0.7010564f, 0.7010562f, -0.09230454f, -0.09230453f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.0162f, -0.3456f, 11.8f), new Quaternion(0.7010564f, 0.7010562f, -0.09230454f, -0.09230453f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.6633f, -0.3456f, 9.3852f), new Quaternion(0.7010564f, 0.7010562f, -0.09230454f, -0.09230453f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.3104f, -0.3456f, 6.9704f), new Quaternion(0.7010564f, 0.7010562f, -0.09230454f, -0.09230453f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(3.2343f, -0.3299f, 11.4795f), new Quaternion(0.6830106f, 0.6830105f, -0.183021f, -0.1830208f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(0.7342f, -0.3299f, 7.1494f), new Quaternion(0.6830106f, 0.6830105f, -0.183021f, -0.1830208f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(1.9843f, -0.3299f, 9.3144f), new Quaternion(0.6830106f, 0.6830105f, -0.183021f, -0.1830208f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-0.5158f, -0.3299f, 4.9844f), new Quaternion(0.6830106f, 0.6830105f, -0.183021f, -0.1830208f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.2634f, -0.3456f, 8.9126f), new Quaternion(0.1830229f, 0.1830229f, 0.6830103f, 0.6830097f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(5.301f, -0.3456f, 10.2863f), new Quaternion(0.1830229f, 0.1830229f, 0.6830103f, 0.6830097f), new Vector3(0.7998f, 2.2727f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(6.1077f, -0.3456f, 6.6931f), new Quaternion(0.1830229f, 0.1830229f, 0.6830103f, 0.6830097f), new Vector3(0.7998f, 2.718f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.8668f, -0.3456f, 6.1772f), new Quaternion(0.09230656f, 0.09230671f, 0.7010564f, 0.7010556f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.1955f, -0.3456f, 15.0546f), new Quaternion(-0.4777149f, -0.4777149f, 0.5213335f, 0.5213334f), new Vector3(0.7998f, 2.5f, 2.7537f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.287f, -0.3456f, 12.553f), new Quaternion(-0.4777148f, -0.4777149f, 0.5213335f, 0.5213335f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.5049f, -0.3456f, 10.0626f), new Quaternion(-0.4777148f, -0.4777149f, 0.5213335f, 0.5213335f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.7228f, -0.3456f, 7.5721f), new Quaternion(-0.4777148f, -0.4777149f, 0.5213335f, 0.5213335f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-1.0911f, -0.3539f, 13.4965f), new Quaternion(-0.4055804f, -0.4055803f, 0.5792276f, 0.5792276f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-1.9461f, -0.3539f, 11.1472f), new Quaternion(-0.4055804f, -0.4055803f, 0.5792276f, 0.5792276f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.8011f, -0.3539f, 8.798f), new Quaternion(-0.4055804f, -0.4055803f, 0.5792276f, 0.5792276f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.6562f, -0.3539f, 6.4488f), new Quaternion(-0.4055804f, -0.4055803f, 0.5792276f, 0.5792276f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(3.9733f, -0.3456f, 8.1845f), new Quaternion(-0.3265063f, -0.3265063f, 0.627211f, 0.627211f), new Vector3(0.7998f, 2.7413f, 2.7178f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(2.4701f, -0.3456f, 6.0378f), new Quaternion(-0.3265063f, -0.3265063f, 0.627211f, 0.627211f), new Vector3(0.7998f, 2.5f, 2.7178f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(11.5888f, -0.3456f, 6.8955f), new Quaternion(-0.4055803f, -0.4055803f, 0.5792276f, 0.5792276f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.701f, -0.3379f, 14.7318f), new Quaternion(-0.4545199f, -0.4545199f, 0.541675f, 0.5416748f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.1351f, -0.3379f, 12.2697f), new Quaternion(-0.4545199f, -0.4545199f, 0.541675f, 0.5416748f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.5692f, -0.3379f, 9.8077f), new Quaternion(-0.4545199f, -0.4545199f, 0.541675f, 0.5416748f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-7.0033f, -0.3379f, 7.3457f), new Quaternion(-0.4545199f, -0.4545199f, 0.541675f, 0.5416748f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(1.1157f, -0.3456f, 12.5824f), new Quaternion(-0.379929f, -0.379929f, 0.5963673f, 0.5963672f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-0.9974f, -0.3456f, 8.0509f), new Quaternion(-0.379929f, -0.379929f, 0.5963673f, 0.5963672f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(0.0591f, -0.3456f, 10.3166f), new Quaternion(-0.379929f, -0.379929f, 0.5963673f, 0.5963672f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.054f, -0.3456f, 5.7851f), new Quaternion(-0.379929f, -0.379929f, 0.5963673f, 0.5963672f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.382f, -0.3456f, 7.8096f), new Quaternion(0.5963684f, 0.5963684f, 0.3799274f, 0.3799274f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(8.5022f, -0.3456f, 11.0745f), new Quaternion(0.6112134f, 0.6112134f, 0.3555533f, 0.3555533f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.9751f, -0.3456f, 15.4219f), new Quaternion(0.6112134f, 0.6112134f, 0.3555533f, 0.3555533f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.9068f, -0.3409f, 20.5868f), new Quaternion(0.6112134f, 0.6112134f, 0.3555534f, 0.3555534f), new Vector3(0.7998f, 3.3076f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.7046f, -0.3456f, -14.3452f), new Quaternion(-0.3799277f, 0.379926f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 3.7403f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.7612f, -0.3456f, -16.6109f), new Quaternion(-0.3799277f, 0.379926f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 3.7403f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(11.8177f, -0.3456f, -18.8767f), new Quaternion(-0.3799277f, 0.379926f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 3.7403f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.2559f, -0.3456f, -12.6892f), new Quaternion(-0.3799277f, 0.3799261f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 4.0962f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(14.3125f, -0.3456f, -14.9549f), new Quaternion(-0.3799277f, 0.3799261f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 4.0962f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.369f, -0.3456f, -17.2207f), new Quaternion(-0.3799277f, 0.3799261f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 4.0962f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(6.535f, -0.3456f, -7.5478f), new Quaternion(-0.3799277f, 0.379926f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 3.7403f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.0862f, -0.3456f, -5.8918f), new Quaternion(-0.3799277f, 0.3799261f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 4.0962f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.5915f, -0.3456f, -9.8136f), new Quaternion(-0.3799277f, 0.379926f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 3.7403f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(11.1427f, -0.3456f, -8.1576f), new Quaternion(-0.3799277f, 0.3799261f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 4.0962f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(8.6481f, -0.3456f, -12.0794f), new Quaternion(-0.3799277f, 0.379926f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 3.7403f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.1994f, -0.3456f, -10.4235f), new Quaternion(-0.3799277f, 0.3799261f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 4.0962f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.8397f, -0.3456f, -4.1809f), new Quaternion(-0.3799277f, 0.3799261f, -0.5963692f, 0.5963681f), new Vector3(0.7998f, 2.5f, 5.651f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.7355f, -0.3456f, 13.252f), new Quaternion(0.6112134f, 0.6112134f, 0.3555533f, 0.3555533f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.3347f, -0.3456f, 17.8285f), new Quaternion(0.6112134f, 0.6112134f, 0.3555534f, 0.3555533f), new Vector3(0.7998f, 3.0421f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(8.3255f, -0.3455f, 5.5439f), new Quaternion(0.5963684f, 0.5963684f, 0.3799274f, 0.3799274f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.0915f, -0.3456f, 5.6791f), new Quaternion(0.5416759f, 0.541676f, 0.4545186f, 0.4545186f), new Vector3(0.7998f, 2.5f, 2.282f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-0.4558f, -0.3462f, 0.1475f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.5442f, -0.3462f, 0.1472f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.5442f, -0.3462f, 0.147f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(29.5442f, -0.3462f, 0.1467f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.2522f, -0.3462f, -7.351f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.2522f, -0.3462f, -9.8542f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.2522f, -0.3462f, -12.3573f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.249f, -0.3462f, -19.8496f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.2451f, -0.3462f, -14.8534f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.2419f, -0.3462f, -22.3457f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.2366f, -0.3509f, 24.5734f), new Quaternion(0.7071069f, 0.7071066f, -9.052671E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.1725f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.245f, -0.3462f, -17.3534f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.2419f, -0.3462f, -24.6323f), new Quaternion(0.7071069f, 0.7071066f, -9.048516E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.2366f, -0.3509f, 22.4521f), new Quaternion(0.7071069f, 0.7071066f, -9.048516E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(2.0442f, -0.3462f, 0.1474f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.0442f, -0.3462f, 0.1472f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(22.0442f, -0.3462f, 0.1469f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.7304f, -0.3462f, -7.351f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(21.391f, -0.3462f, 17.6469f), new Quaternion(0.7071069f, 0.7071067f, -9.078002E-06f, -8.988731E-06f), new Vector3(0.7998f, 3.8071f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.7304f, -0.3462f, -9.8542f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.7304f, -0.3462f, -17.3495f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.7303f, -0.3462f, -22.3495f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.725f, -0.3509f, 24.5701f), new Quaternion(0.7071069f, 0.7071066f, -9.052671E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.1725f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.7304f, -0.3462f, -12.3573f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.7233f, -0.3462f, -14.8534f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.7304f, -0.3462f, -19.8527f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.7303f, -0.3462f, -24.6379f), new Quaternion(0.7071069f, 0.7071066f, -9.048516E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.725f, -0.3509f, 22.4464f), new Quaternion(0.7071069f, 0.7071066f, -9.048516E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(18.2273f, -0.3462f, -19.8527f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(32.0442f, -0.3462f, 0.1466f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(34.618f, -0.8179f, -5.6822f), new Quaternion(0.7010574f, 0.7010574f, 0.09229612f, 0.09229623f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.9189f, -0.8256f, 20.5729f), new Quaternion(0.5609859f, 0.5609859f, -0.430459f, -0.4304588f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.4706f, -0.8109f, 24.6314f), new Quaternion(0.7071065f, 0.7071071f, 4.596475E-07f, 1.374662E-06f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.3645f, -0.7909f, -21.6921f), new Quaternion(0.6532826f, 0.653282f, -0.270596f, -0.2705963f), new Vector3(1.7298f, 2.2287f, 2.1017f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.4298f, -0.7729f, -22.3323f), new Quaternion(0.6830133f, 0.6830131f, -0.1830105f, -0.1830111f), new Vector3(1.7298f, 2.2927f, 2.1017f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.9705f, -0.7809f, -23.45f), new Quaternion(0.7010579f, 0.7010575f, -0.09229352f, -0.09229404f), new Vector3(1.7298f, 2.2928f, 1.0024f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.0256f, -0.7909f, -24.6879f), new Quaternion(0.707107f, 0.7071066f, 1.888734E-06f, 1.50861E-06f), new Vector3(1.7298f, 2.3633f, 2.1018f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.3341f, -0.8021f, -22.5114f), new Quaternion(0.2705977f, -0.2705942f, -0.6532825f, 0.6532823f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(14.0978f, -0.8021f, -21.7477f), new Quaternion(-0.6532825f, 0.6532826f, -0.270594f, 0.2705973f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.896f, -0.8179f, -3.2074f), new Quaternion(0.5609863f, 0.5609863f, -0.4304585f, -0.4304582f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.3797f, -0.8226f, 6.2874f), new Quaternion(-0.09229816f, -0.09229824f, 0.7010573f, 0.701057f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(18.2162f, -0.3463f, -18.2351f), new Quaternion(-0.1289027f, 0.1289019f, -0.6952579f, 0.6952589f), new Vector3(0.8012f, 1.9471f, 1.5755f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.5473f, -0.3464f, -20.68f), new Quaternion(-0.09229732f, 0.09229622f, -0.7010567f, 0.7010579f), new Vector3(0.8012f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.8793f, -0.8209f, 11.7861f), new Quaternion(0.5577953f, 0.5577952f, 0.4345858f, 0.434585f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(4.9053f, -0.8209f, 11.4721f), new Quaternion(0.08712258f, 0.08712164f, 0.7017195f, 0.7017187f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(5.2879f, -0.8209f, -6.5531f), new Quaternion(0.6252637f, 0.6252624f, -0.330222f, -0.3302208f), new Vector3(1.7298f, 1.5157f, 2.3728f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.7691f, -0.9809f, -18.1203f), new Quaternion(-0.2434749f, -0.2434748f, 0.6638685f, 0.6638665f), new Vector3(2.0356f, 1.2726f, 1.2726f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.8486f, -0.8209f, -7.3652f), new Quaternion(0.6913147f, 0.6913134f, 0.1486103f, 0.1486094f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.8307f, -0.8209f, -16.1334f), new Quaternion(0.4579893f, 0.457989f, 0.5387457f, 0.538744f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(32.0977f, -0.8179f, 8.5298f), new Quaternion(0.4304566f, 0.4304579f, 0.5609875f, 0.5609868f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.0391f, -0.8256f, 19.6466f), new Quaternion(0.5000005f, 0.5000004f, -0.4999996f, -0.4999995f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.5656f, -0.8129f, -19.1385f), new Quaternion(-2.133508E-06f, -2.555945E-06f, 0.7071067f, 0.7071069f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.0162f, -0.8179f, -4.1337f), new Quaternion(0.5000008f, 0.5000011f, -0.4999991f, -0.499999f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.4534f, -0.8226f, 6.1672f), new Quaternion(-2.068249E-06f, -2.173087E-06f, 0.7071066f, 0.7071069f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.1272f, -0.3558f, -19.1385f), new Quaternion(-1.6614E-06f, 9.033545E-08f, -0.7071073f, 0.7071063f), new Vector3(0.8012f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.219f, -0.8209f, 12.6525f), new Quaternion(0.6097476f, 0.6097483f, 0.358061f, 0.3580605f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(32.9209f, -0.8069f, -6.5166f), new Quaternion(0.6532814f, 0.6532815f, 0.2705982f, 0.2705984f), new Vector3(1.7298f, 1.0814f, 1.3491f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.0845f, -0.8099f, 22.27f), new Quaternion(0.653282f, 0.6532815f, -0.2705977f, -0.2705973f), new Vector3(1.7298f, 1.0814f, 1.3491f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.4241f, -0.7999f, 22.8354f), new Quaternion(0.6830127f, 0.6830122f, 0.1830133f, 0.183014f), new Vector3(1.7298f, 1.1506f, 1.3491f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.7665f, -0.8109f, 24.0162f), new Quaternion(0.6830128f, 0.6830122f, 0.1830133f, 0.1830139f), new Vector3(1.7298f, 1.0814f, 1.3865f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.6378f, -0.7899f, 17.2655f), new Quaternion(-0.4048959f, 0.4048969f, -0.579706f, 0.5797058f), new Vector3(1.7298f, 1.0814f, 1.3865f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.5814f, -0.7909f, 18.8472f), new Quaternion(0.123611f, -0.1236082f, -0.6962174f, 0.6962203f), new Vector3(1.7298f, 1.0814f, 1.3865f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.0551f, -0.8009f, -23.1478f), new Quaternion(0.6123729f, 0.6123746f, -0.3535506f, -0.3535519f), new Vector3(1.7298f, 1.0814f, 1.3491f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.1704f, -0.8009f, -21.0326f), new Quaternion(0.6830127f, 0.683014f, -0.1830095f, -0.1830112f), new Vector3(1.7298f, 1.0814f, 1.3491f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(11.5979f, -0.8021f, -22.9679f), new Quaternion(0.09229483f, -0.09229114f, -0.7010584f, 0.7010571f), new Vector3(1.7298f, 1.0814f, 2.0327f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.9748f, -0.8021f, -23.4444f), new Quaternion(0.7010587f, 0.7010555f, 0.09229695f, 0.09229918f), new Vector3(1.7298f, 1.24f, 1.5913f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.0788f, -0.8019f, -24.946f), new Quaternion(0.6123718f, 0.6123712f, 0.3535526f, 0.3535576f), new Vector3(1.7298f, 1.0814f, 1.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.9587f, -0.8021f, -20.7618f), new Quaternion(0.7010593f, -0.7010563f, 0.09229156f, -0.09229474f), new Vector3(1.7298f, 1.5964f, 1.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.0616f, -0.8179f, -1.5103f), new Quaternion(0.653282f, 0.6532821f, -0.2705967f, -0.2705967f), new Vector3(1.7298f, 1.0814f, 1.3491f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.1192f, -0.8226f, 7.0793f), new Quaternion(-0.2706004f, -0.2706004f, 0.6532805f, 0.6532806f), new Vector3(1.7298f, 1.4502f, 1.4692f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.6309f, -0.8079f, 9.8552f), new Quaternion(0.4263094f, 0.42631f, 0.5641459f, 0.564145f), new Vector3(1.7298f, 1.4502f, 1.6619f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(6.8357f, -0.8209f, 11.2247f), new Quaternion(-0.09746475f, -0.09746531f, 0.7003577f, 0.7003573f), new Vector3(1.7298f, 1.4502f, 1.6619f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(3.7269f, -0.8209f, -4.8035f), new Quaternion(0.6894255f, 0.6894251f, -0.1571395f, -0.1571396f), new Vector3(1.7298f, 1.9349f, 3.2578f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.5793f, -0.9809f, -16.5364f), new Quaternion(-0.407001f, -0.4070004f, 0.5782309f, 0.5782301f), new Vector3(2.0356f, 1.7065f, 2.5005f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.5174f, -0.8209f, -8.3834f), new Quaternion(0.6292952f, 0.629295f, 0.3224719f, 0.3224705f), new Vector3(1.7298f, 1.4502f, 1.6507f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.2792f, -0.8209f, -18.0143f), new Quaternion(0.3029459f, 0.3029464f, 0.6389247f, 0.638923f), new Vector3(1.7298f, 1.4502f, 1.6507f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(11.0255f, -0.8106f, -1.9801f), new Quaternion(0.5986109f, 0.5986125f, 0.3763836f, 0.3763821f), new Vector3(1.7298f, 1.4502f, 2.3129f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(32.8897f, -0.8179f, 6.7903f), new Quaternion(0.270595f, 0.2705962f, 0.6532834f, 0.6532816f), new Vector3(1.7298f, 1.4502f, 1.4692f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(33.7624f, -0.8179f, -6.0428f), new Quaternion(0.6830128f, 0.6830126f, 0.1830127f, 0.1830128f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.5583f, -0.8256f, 21.4285f), new Quaternion(0.6123729f, 0.6123725f, -0.3535532f, -0.3535529f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.3928f, -0.8109f, 23.097f), new Quaternion(0.7010579f, 0.7010567f, 0.09229663f, 0.09229734f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.615f, -0.8009f, 24.2708f), new Quaternion(0.701058f, 0.7010566f, 0.09229661f, 0.09229733f), new Vector3(1.7298f, 1.0814f, 1.5772f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.2412f, -0.8109f, 16.4611f), new Quaternion(-0.3257663f, 0.3257645f, -0.6275954f, 0.627597f), new Vector3(1.7298f, 1.0814f, 1.3367f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.7753f, -0.8109f, 19.2432f), new Quaternion(0.2134267f, -0.2134272f, -0.6741272f, 0.6741295f), new Vector3(1.7298f, 1.0814f, 1.3367f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.3795f, -0.8109f, 15.5438f), new Quaternion(-0.2343427f, 0.2343411f, -0.6671452f, 0.667147f), new Vector3(1.7298f, 1.0814f, 1.3367f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.9587f, -0.8109f, 19.9784f), new Quaternion(0.3060395f, -0.3060393f, -0.6374469f, 0.6374491f), new Vector3(1.7298f, 1.4092f, 1.3367f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.1677f, -0.8083f, -18.0213f), new Quaternion(-0.1469762f, -0.1469762f, 0.6916633f, 0.6916632f), new Vector3(1.7298f, 1.0814f, 1.5094f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.0728f, -0.8109f, -21.8396f), new Quaternion(0.5541852f, 0.5541868f, -0.4391792f, -0.439178f), new Vector3(1.7298f, 1.0814f, 1.5094f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(18.6324f, -0.8079f, -18.2058f), new Quaternion(0.0002038362f, 0.000203843f, 0.7071068f, 0.7071068f), new Vector3(1.7298f, 1.0814f, 1.5094f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-12.8147f, -0.8009f, -24.0812f), new Quaternion(0.5609865f, 0.5609873f, -0.4304571f, -0.430458f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.4266f, -0.8009f, -21.6435f), new Quaternion(0.6532825f, 0.653282f, -0.2705954f, -0.2705971f), new Vector3(1.7298f, 2.4911f, 2.7056f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.2826f, -0.8021f, -22.5347f), new Quaternion(0.1830125f, -0.183008f, -0.6830146f, 0.6830122f), new Vector3(1.7298f, 1.0814f, 2.3175f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.8604f, -0.8021f, -23.5508f), new Quaternion(0.7071096f, 0.707104f, 6.797049E-08f, 2.782693E-06f), new Vector3(1.7298f, 1.1563f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.6406f, -0.8019f, -24.011f), new Quaternion(0.6532831f, 0.6532792f, 0.2705968f, 0.270601f), new Vector3(1.7298f, 1.1563f, 1.54f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(14.8402f, -0.8021f, -21.1891f), new Quaternion(0.683014f, -0.6830127f, 0.1830082f, -0.1830126f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.5354f, -0.8179f, -2.3518f), new Quaternion(0.6123731f, 0.6123729f, -0.3535526f, -0.3535522f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(18.2353f, -0.8226f, 6.648f), new Quaternion(-0.1830146f, -0.1830147f, 0.6830124f, 0.683012f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.4826f, -0.3558f, -20.0577f), new Quaternion(-0.1830144f, 0.1830125f, -0.6830124f, 0.6830127f), new Vector3(0.8012f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.7694f, -0.8209f, 10.8656f), new Quaternion(0.4962987f, 0.4962983f, 0.5036755f, 0.5036732f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(5.8246f, -0.8209f, 11.359f), new Quaternion(-0.005215832f, -0.005215957f, 0.7070887f, 0.7070864f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(4.7209f, -0.806f, -5.6128f), new Quaternion(0.6630164f, 0.6630164f, -0.2457834f, -0.2457825f), new Vector3(1.7597f, 1.4254f, 2.8899f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.6519f, -0.9609f, -18.1262f), new Quaternion(-0.3280443f, -0.3280451f, 0.6264082f, 0.6264074f), new Vector3(2.0356f, 2.942f, 4.0305f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.6265f, -0.8209f, -7.8602f), new Quaternion(0.6660025f, 0.6660021f, 0.2375731f, 0.2375731f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.555f, -0.8209f, -17.0173f), new Quaternion(0.3837506f, 0.3837503f, 0.5939158f, 0.5939153f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.3087f, -0.8109f, -1.2911f), new Quaternion(0.6677542f, 0.6677558f, 0.2326012f, 0.2326013f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(32.4583f, -0.8179f, 7.6742f), new Quaternion(0.353551f, 0.3535518f, 0.6123748f, 0.6123724f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(32.4542f, -0.8179f, -7.351f), new Quaternion(0.6123725f, 0.6123722f, 0.3535535f, 0.3535537f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(18.2501f, -0.8256f, 22.7366f), new Quaternion(0.683013f, 0.6830129f, -0.1830122f, -0.1830119f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.6958f, -0.8109f, 22.2626f), new Quaternion(0.6532815f, 0.6532807f, 0.2705987f, 0.2705995f), new Vector3(1.7298f, 1.0814f, 1.3421f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.0311f, -0.8059f, 23.493f), new Quaternion(0.6532815f, 0.6532807f, 0.2705986f, 0.2705995f), new Vector3(1.7298f, 1.0814f, 1.3282f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.8711f, -0.8009f, 18.1352f), new Quaternion(-0.4771f, 0.4771005f, -0.5218972f, 0.5218947f), new Vector3(1.7298f, 1.0814f, 1.3282f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.4512f, -0.8009f, 18.6139f), new Quaternion(0.03167772f, -0.03167454f, -0.7063964f, 0.7063975f), new Vector3(1.7298f, 1.0814f, 1.3282f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.5283f, -0.8019f, -17.3637f), new Quaternion(-0.3535555f, -0.3535556f, 0.6123716f, 0.6123708f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.493f, -0.8009f, -21.3588f), new Quaternion(0.6627901f, 0.6627916f, -0.2463908f, -0.2463902f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.0896f, -0.8009f, -20.7922f), new Quaternion(0.7010587f, 0.7010569f, -0.09229273f, -0.0922937f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.8097f, -0.8021f, -23.0174f), new Quaternion(3.425041E-07f, 4.434986E-06f, -0.7071066f, 0.7071069f), new Vector3(1.7298f, 1.0814f, 2.1482f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-7.7415f, -0.8021f, -23.5508f), new Quaternion(-2.616609E-06f, -2.108909E-06f, 0.7071065f, 0.7071071f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(18.2272f, -0.8179f, -1.0436f), new Quaternion(0.6830133f, 0.6830128f, -0.1830119f, -0.183011f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.5434f, -0.8226f, 7.9562f), new Quaternion(-0.3535551f, -0.3535561f, 0.6123717f, 0.6123707f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.2451f, -0.3558f, -18.7072f), new Quaternion(-0.3535556f, 0.3535536f, -0.6123713f, 0.6123722f), new Vector3(0.8012f, 1.0814f, 1.243f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.2775f, -0.8209f, 9.0793f), new Quaternion(0.3490269f, 0.3490266f, 0.6149637f, 0.6149637f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.6135f, -0.8209f, 11.8752f), new Quaternion(-0.1880452f, -0.1880473f, 0.6816446f, 0.6816437f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(2.6285f, -0.8209f, -5.4984f), new Quaternion(0.7040384f, 0.7040376f, -0.06580803f, -0.06580561f), new Vector3(1.7298f, 1.0814f, 1.3088f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.3601f, -0.9809f, -15.2989f), new Quaternion(-0.4789924f, -0.4789931f, 0.5201606f, 0.5201586f), new Vector3(2.0356f, 1.2726f, 1.5402f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(5.8105f, -0.9809f, -19.1739f), new Quaternion(-0.06335731f, -0.06335925f, 0.7042633f, 0.7042619f), new Vector3(2.0356f, 1.2726f, 1.5402f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.8144f, -0.8209f, -9.3168f), new Quaternion(0.5818212f, 0.5818194f, 0.4018522f, 0.4018523f), new Vector3(1.7298f, 1.0814f, 1.3088f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.4519f, -0.8209f, -18.5446f), new Quaternion(0.2169585f, 0.2169572f, 0.6730009f, 0.6729996f), new Vector3(1.7298f, 1.0814f, 1.3088f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.8097f, -0.7909f, -3.766f), new Quaternion(0.5218477f, 0.5218474f, 0.4771532f, 0.4771532f), new Vector3(1.7298f, 2.4299f, 2.0431f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(33.7665f, -0.8179f, 6.3661f), new Quaternion(0.1830099f, 0.1830098f, 0.6830144f, 0.6830126f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(32.0936f, -0.8179f, -8.2066f), new Quaternion(0.5609854f, 0.5609853f, 0.4304593f, 0.4304597f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.4016f, -0.8256f, 23.0902f), new Quaternion(0.7010576f, 0.7010574f, -0.09229545f, -0.09229518f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.222f, -0.8109f, 21.4353f), new Quaternion(0.6123722f, 0.6123714f, 0.3535542f, 0.3535548f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(14.5328f, -0.8109f, 22.7552f), new Quaternion(0.6123722f, 0.6123714f, 0.3535541f, 0.3535548f), new Vector3(1.7298f, 1.0814f, 1.4322f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.889f, -0.8129f, -16.5081f), new Quaternion(-0.4304612f, -0.430461f, 0.5609844f, 0.5609841f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(18.7222f, -0.8209f, -20.8426f), new Quaternion(0.68928f, 0.6892822f, -0.1577709f, -0.1577706f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.3787f, -0.8179f, -0.6901f), new Quaternion(0.7010574f, 0.7010577f, -0.09229475f, -0.09229469f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.897f, -0.8226f, 8.8047f), new Quaternion(-0.4304612f, -0.4304613f, 0.560984f, 0.5609842f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.8442f, -0.8209f, 8.3666f), new Quaternion(0.2657718f, 0.2657721f, 0.6552609f, 0.6552585f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(8.3277f, -0.8209f, 12.4338f), new Quaternion(-0.2754099f, -0.2754098f, 0.6512682f, 0.6512669f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(1.6809f, -0.8209f, -5.555f), new Quaternion(0.7066044f, 0.7066044f, 0.02665146f, 0.02665147f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.1834f, -0.9809f, -14.1888f), new Quaternion(0.5427896f, 0.5427896f, -0.4531881f, -0.4531881f), new Vector3(2.0356f, 1.2726f, 1.2726f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(6.8358f, -0.9609f, -18.7142f), new Quaternion(-0.1547415f, -0.1547414f, 0.6899675f, 0.6899675f), new Vector3(2.0356f, 1.2726f, 1.2726f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.9275f, -0.8209f, -10.2502f), new Quaternion(0.5243904f, 0.5243908f, 0.4743574f, 0.4743565f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.4407f, -0.8209f, -18.9477f), new Quaternion(0.1272573f, 0.1272584f, 0.6955615f, 0.6955611f), new Vector3(1.7298f, 1.3841f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(34.6221f, -0.8179f, 6.0054f), new Quaternion(0.09229289f, 0.09229391f, 0.7010585f, 0.701057f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.9734f, -0.8179f, -9.1329f), new Quaternion(0.4999999f, 0.4999999f, 0.4999999f, 0.5000002f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.7825f, -0.8209f, 23.198f), new Quaternion(0.7071069f, 0.7071066f, 2.845636E-07f, 6.736682E-07f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.0092f, -0.8129f, -15.5817f), new Quaternion(0.5000011f, 0.500002f, -0.4999983f, -0.4999986f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.4453f, -0.8179f, -0.5628f), new Quaternion(0.7071066f, 0.707107f, 7.662313E-07f, 1.388998E-06f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.0243f, -0.8226f, 9.7381f), new Quaternion(0.5000009f, 0.5000021f, -0.4999984f, -0.4999987f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(11.6067f, -0.8209f, 7.7942f), new Quaternion(0.1779699f, 0.1779705f, 0.6843438f, 0.6843441f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(8.8934f, -0.8209f, 13.1975f), new Quaternion(-0.3580597f, -0.3580616f, 0.6097477f, 0.6097483f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(0.7546f, -0.8209f, -5.7459f), new Quaternion(0.6970801f, 0.6970811f, 0.1186531f, 0.1186545f), new Vector3(1.7298f, 1.0814f, 1.0814f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.4558f, -0.3462f, 0.1476f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.0701f, -0.3462f, 0.1477f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -9.000659E-06f), new Vector3(0.7998f, 2.7285f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(4.5442f, -0.3462f, 0.1474f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(14.5442f, -0.3462f, 0.1471f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(24.5442f, -0.3462f, 0.1468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.499f, -0.3462f, -7.3581f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.5075f, -0.3462f, -9.8542f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.4991f, -0.3462f, -17.3566f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.4989f, -0.3462f, -22.3566f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.4936f, -0.3509f, 24.564f), new Quaternion(0.7071069f, 0.7071066f, -9.052671E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.1725f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.4991f, -0.3462f, -12.3503f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.4991f, -0.3462f, -14.8534f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.5075f, -0.3462f, -19.8527f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.5075f, -0.3462f, -24.638f), new Quaternion(0.7071069f, 0.7071066f, -9.048516E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.5022f, -0.3509f, 22.4463f), new Quaternion(0.7071069f, 0.7071066f, -9.048516E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(34.1464f, -0.3462f, -4.855f), new Quaternion(0.7071069f, 0.7071066f, -9.077907E-06f, -9.001514E-06f), new Vector3(0.7998f, 3.2954f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(37.0441f, -0.3462f, -4.8535f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(40.044f, -0.3462f, -4.8536f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.98179E-06f), new Vector3(0.7998f, 3.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(34.5443f, -0.3462f, 0.1466f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(37.0443f, -0.3462f, 0.1465f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(40.0443f, -0.3462f, 0.1464f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.98179E-06f), new Vector3(0.7998f, 3.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(34.5441f, -0.3462f, -2.3534f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(37.0441f, -0.3462f, -2.3535f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(40.0441f, -0.3462f, -2.3536f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.98179E-06f), new Vector3(0.7998f, 3.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.9558f, -0.3462f, 0.1476f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.0442f, -0.3462f, 0.1473f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.0442f, -0.3462f, 0.147f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(27.0442f, -0.3462f, 0.1468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2529f, -0.3462f, -7.3581f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2458f, -0.3462f, -9.8542f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2529f, -0.3462f, -17.3566f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2528f, -0.3462f, -22.3566f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2475f, -0.3509f, 24.564f), new Quaternion(0.7071069f, 0.7071066f, -9.052671E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.1725f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2529f, -0.3462f, -12.3503f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2529f, -0.3462f, -14.8534f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2459f, -0.3462f, -19.8527f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2458f, -0.3462f, -24.638f), new Quaternion(0.7071069f, 0.7071066f, -9.048516E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2405f, -0.3509f, 22.4463f), new Quaternion(0.7071069f, 0.7071066f, -9.048516E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.749f, -0.3462f, -7.351f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.749f, -0.3462f, -9.8542f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.749f, -0.3462f, -17.3495f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.7458f, -0.3462f, -24.629f), new Quaternion(0.7071069f, 0.7071066f, -9.048516E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.7405f, -0.3509f, 22.4554f), new Quaternion(0.7071069f, 0.7071066f, -9.048516E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.0745f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.749f, -0.3462f, -19.8495f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.7489f, -0.3462f, -22.3495f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.7436f, -0.3509f, 24.5701f), new Quaternion(0.7071069f, 0.7071066f, -9.052671E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.1725f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.749f, -0.3462f, -12.3573f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.7561f, -0.3462f, -14.8534f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.3696f, -0.3456f, -6.4194f), new Quaternion(0.7010587f, 0.7010585f, 0.09228717f, 0.09228725f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.6482f, -0.3451f, -19.9027f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(5.8518f, -0.3363f, -19.9031f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(1.5492f, -0.3371f, -22.5799f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 3.0238f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.1482f, -0.3451f, -19.9028f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(3.3518f, -0.3363f, -19.903f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-0.9508f, -0.3371f, -22.5798f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 3.0238f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-3.4508f, -0.3371f, -22.5797f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 3.0238f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.1482f, -0.3539f, -19.9026f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(0.8518f, -0.3451f, -19.9029f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.9508f, -0.3371f, -22.5797f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 3.0238f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.4486f, -0.3421f, -22.08f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(10.8518f, -0.3363f, -19.9032f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(6.5491f, -0.3371f, -22.58f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 3.0238f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.0492f, -0.3371f, -22.5801f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 3.0238f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.098f, -0.3409f, -21.2831f), new Quaternion(0.5000068f, 0.5000067f, 0.499993f, 0.4999936f), new Vector3(0.7998f, 2.4196f, 3.8885f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(13.4345f, -0.3363f, -19.881f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.6661f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-16.6482f, -0.3539f, -19.9024f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-24.1482f, -0.3539f, -19.9022f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.6482f, -0.3539f, -19.9026f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-1.6482f, -0.3451f, -19.9029f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(8.3518f, -0.3363f, -19.9031f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(4.0491f, -0.3371f, -22.5799f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 3.0238f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-19.1482f, -0.3539f, -19.9024f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-26.6482f, -0.3539f, -19.9022f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-14.1482f, -0.3539f, -19.9025f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-21.6482f, -0.3539f, -19.9023f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.6021f, -0.3539f, -19.9022f), new Quaternion(0.5000067f, 0.5000067f, 0.4999929f, 0.4999936f), new Vector3(0.7998f, 2.5f, 1.4076f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.5561f, -0.3412f, -10.7241f), new Quaternion(9.188007E-06f, 9.942386E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 3.2438f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.5559f, -0.3412f, -16.0993f), new Quaternion(9.188007E-06f, 9.96966E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.5558f, -0.3412f, -13.5993f), new Quaternion(9.188007E-06f, 9.96966E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-32.3854f, -0.3451f, -7.8521f), new Quaternion(9.188007E-06f, 9.96966E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-32.3854f, -0.3451f, -3.5522f), new Quaternion(9.188007E-06f, 9.967465E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 1.0998f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-32.3854f, -0.3451f, -5.3521f), new Quaternion(9.188007E-06f, 9.96966E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-32.7846f, -0.3451f, -1.7523f), new Quaternion(9.186987E-06f, 9.969661E-06f, 0.7071066f, 0.7071071f), new Vector3(0.7998f, 1.7015f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.6835f, -0.3451f, 10.5478f), new Quaternion(9.188007E-06f, 9.96966E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.6835f, -0.3451f, 13.0478f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.6836f, -0.3451f, 5.5478f), new Quaternion(9.188007E-06f, 9.96966E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-25.6769f, -0.3451f, 13.0479f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.6838f, -0.3451f, 0.5478f), new Quaternion(9.188007E-06f, 9.96966E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.6836f, -0.3451f, 8.0478f), new Quaternion(9.188007E-06f, 9.96966E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-28.1801f, -0.3451f, 13.0479f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.6837f, -0.3451f, 3.0478f), new Quaternion(9.188007E-06f, 9.96966E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.1879f, -0.3451f, 13.0479f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.6839f, -0.3451f, 17.9477f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.218f, -0.3267f, 19.6638f), new Quaternion(-0.4159085f, -0.4159076f, 0.5718571f, 0.571857f), new Vector3(0.7998f, 3.1218f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.1975f, -0.3409f, 21.9032f), new Quaternion(-0.3377083f, -0.3377073f, 0.6212518f, 0.6212513f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.3445f, -0.3289f, 23.6256f), new Quaternion(-0.4159087f, -0.4159079f, 0.5718569f, 0.571857f), new Vector3(0.7998f, 2.0353f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.0918f, -0.3409f, 25.021f), new Quaternion(-2.929206E-07f, -4.140159E-07f, 0.7071069f, 0.7071066f), new Vector3(0.7998f, 2.5f, 1.6248f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.684f, -0.3451f, 12.9477f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.6841f, -0.3451f, 7.9476f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.6842f, -0.3451f, 2.9476f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.6844f, -0.3451f, -2.0524f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.9346f, -0.3451f, -10.0525f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 4f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.6846f, -0.3451f, -10.0525f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.9347f, -0.3451f, -12.5525f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 4.0001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.6847f, -0.3451f, -12.5525f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.9347f, -0.3451f, -15.0525f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 4.0001f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.6847f, -0.3451f, -15.0525f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-9.9165f, -0.3451f, -17.4776f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000066f, 0.5000069f), new Vector3(0.7998f, 2.3501f, 4.0366f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.6848f, -0.3451f, -17.4776f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000066f, 0.5000069f), new Vector3(0.7998f, 2.3501f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1846f, -0.3451f, -10.0524f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1847f, -0.3451f, -12.5524f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1847f, -0.3451f, -15.0524f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1848f, -0.3451f, -17.4775f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.3501f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.6839f, -0.3451f, 15.4477f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.6841f, -0.3451f, 10.4476f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.6842f, -0.3451f, 5.4476f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.6843f, -0.3451f, 0.4476f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-11.0093f, -0.3451f, -4.6774f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000066f, 0.5000069f), new Vector3(0.7998f, 2.75f, 3.1497f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-10.2594f, -0.3451f, -7.4275f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000066f, 0.5000069f), new Vector3(0.7998f, 2.75f, 4.6498f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-20.8339f, -0.3451f, 17.9479f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.7999f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.4838f, -0.3451f, 17.948f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-23.4839f, -0.3451f, 15.498f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000066f, 0.5000069f), new Vector3(0.7998f, 2.4001f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-15.6839f, -0.3451f, 17.9478f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-13.1839f, -0.3451f, 17.9477f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-18.1839f, -0.3451f, 17.9479f), new Quaternion(-0.4999937f, -0.4999928f, 0.5000067f, 0.5000069f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.6839f, -0.3451f, -1.8523f), new Quaternion(9.188007E-06f, 9.972489E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 2.3002f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-32.7209f, -0.3412f, -10.3522f), new Quaternion(9.178354E-06f, 9.96966E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 1.8293f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-30.5559f, -0.3412f, -19.2453f), new Quaternion(9.188007E-06f, 9.959333E-06f, 0.7071066f, 0.707107f), new Vector3(0.7998f, 2.5f, 3.8139f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.0166f, -0.3456f, -4.0046f), new Quaternion(0.7010587f, 0.7010585f, 0.09228717f, 0.09228725f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-4.6636f, -0.3455f, -1.5897f), new Quaternion(0.7010587f, 0.7010585f, 0.09228717f, 0.09228725f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(3.2339f, -0.3456f, -3.6842f), new Quaternion(0.6830151f, 0.6830148f, 0.1830043f, 0.1830043f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(2.0782f, -0.3319f, -1.4647f), new Quaternion(0.6830151f, 0.6830148f, 0.1830043f, 0.1830044f), new Vector3(0.7998f, 2.7177f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.8324f, -0.3456f, -0.9507f), new Quaternion(0.7010586f, 0.7010586f, 0.09228711f, 0.09228712f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.691f, -0.3456f, -7.4341f), new Quaternion(0.7064337f, 0.7064338f, 0.03084393f, 0.03084394f), new Vector3(0.7998f, 2.7738f, 2.849f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.2819f, -0.3456f, -4.8229f), new Quaternion(0.7064337f, 0.7064338f, 0.03084394f, 0.03084395f), new Vector3(0.7998f, 2.5f, 2.6309f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.5055f, -0.3456f, -2.2671f), new Quaternion(0.7064337f, 0.7064338f, 0.03084394f, 0.03084395f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-1.0916f, -0.3619f, -5.7011f), new Quaternion(0.6963642f, 0.6963642f, 0.1227883f, 0.1227884f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-1.9467f, -0.3619f, -3.3518f), new Quaternion(0.6963642f, 0.6963642f, 0.1227883f, 0.1227884f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.8017f, -0.3619f, -1.0026f), new Quaternion(0.6963642f, 0.6963642f, 0.1227883f, 0.1227884f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.5543f, -0.3456f, -1.6689f), new Quaternion(-0.1227881f, -0.1227882f, 0.696364f, 0.6963644f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(15.2608f, -0.3456f, -0.0582f), new Quaternion(-0.03084372f, -0.0308439f, 0.7064338f, 0.7064338f), new Vector3(0.7998f, 3.973f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.7016f, -0.3529f, -6.9363f), new Quaternion(0.704416f, 0.704416f, 0.06162873f, 0.06162873f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.1357f, -0.3529f, -4.4743f), new Quaternion(0.704416f, 0.704416f, 0.06162873f, 0.06162873f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-6.5698f, -0.3529f, -2.0123f), new Quaternion(0.704416f, 0.704416f, 0.06162873f, 0.06162873f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(1.1151f, -0.3456f, -4.7871f), new Quaternion(0.6903454f, 0.6903455f, 0.1530463f, 0.1530464f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(0.0586f, -0.3456f, -2.5213f), new Quaternion(0.6903454f, 0.6903455f, 0.1530463f, 0.1530464f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-0.998f, -0.3456f, -0.2556f), new Quaternion(0.6903454f, 0.6903455f, 0.1530463f, 0.1530464f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(5.3552f, -0.3456f, -2.581f), new Quaternion(-0.1530466f, -0.1530462f, 0.690345f, 0.6903459f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(4.296f, -0.3456f, -0.3155f), new Quaternion(-0.1530466f, -0.1530462f, 0.690345f, 0.6903459f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.1669f, -0.3456f, -0.4342f), new Quaternion(-0.061629f, -0.06162845f, 0.7044158f, 0.7044163f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-0.4557f, -0.3462f, 5.1475f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.5443f, -0.3462f, 5.1472f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(18.2371f, -0.3509f, 5.1594f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(29.5443f, -0.3462f, 5.1467f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(2.0443f, -0.3462f, 5.1474f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.0443f, -0.3462f, 5.1472f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(21.3908f, -0.3509f, 5.1594f), new Quaternion(0.7071069f, 0.7071066f, -9.061137E-06f, -8.999829E-06f), new Vector3(0.7998f, 3.8071f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(32.0443f, -0.3462f, 5.1466f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.4557f, -0.3462f, 5.1476f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.07f, -0.3462f, 5.1477f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -9.000779E-06f), new Vector3(0.7998f, 2.7285f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(4.5443f, -0.3462f, 5.1474f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(14.5443f, -0.3462f, 5.1471f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(24.5443f, -0.3462f, 5.1468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(34.5443f, -0.3462f, 5.1466f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(37.0443f, -0.3462f, 5.1465f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(40.0444f, -0.3462f, 5.1465f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.98179E-06f), new Vector3(0.7998f, 3.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.9557f, -0.3462f, 5.1476f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.0443f, -0.3462f, 5.1473f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.0443f, -0.3462f, 5.147f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(27.0443f, -0.3462f, 5.1468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-0.4558f, -0.3462f, 2.6475f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(9.5442f, -0.3462f, 2.6472f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(19.5442f, -0.3462f, 2.647f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(29.5443f, -0.3462f, 2.6467f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(28.7561f, -0.3462f, -4.8549f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.2522f, -0.3462f, -4.8478f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.6487f, -0.3462f, 7.6459f), new Quaternion(0.7071069f, 0.7071066f, -9.077943E-06f, -8.989656E-06f), new Vector3(0.7998f, 1.7086f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(32.0441f, -0.3462f, -2.3534f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.6487f, -0.3462f, 10.146f), new Quaternion(0.7071069f, 0.7071066f, -9.077943E-06f, -9.002064E-06f), new Vector3(0.7998f, 1.7086f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(2.0442f, -0.3462f, 2.6474f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(12.0442f, -0.3462f, 2.6472f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(22.0442f, -0.3462f, 2.6469f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(20.7233f, -0.3462f, -4.8549f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(21.3909f, -0.3509f, 7.6594f), new Quaternion(0.7071069f, 0.7071066f, -9.061069E-06f, -9.000092E-06f), new Vector3(0.7998f, 3.807f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(21.4191f, -0.3462f, -2.3531f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -9.00186E-06f), new Vector3(0.7998f, 3.75f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(21.3909f, -0.3509f, 10.1595f), new Quaternion(0.7071069f, 0.7071066f, -9.061114E-06f, -8.988744E-06f), new Vector3(0.7998f, 3.8071f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(32.0443f, -0.3462f, 2.6466f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-5.4558f, -0.3462f, 2.6476f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-8.07f, -0.3462f, 2.6477f), new Quaternion(0.7071069f, 0.7071067f, -9.078048E-06f, -9.000802E-06f), new Vector3(0.7998f, 2.7284f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(4.5442f, -0.3462f, 2.6474f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(14.5442f, -0.3462f, 2.6471f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(24.5443f, -0.3462f, 2.6468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(23.499f, -0.3462f, -4.8549f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.992302E-06f), new Vector3(0.7998f, 2.9985f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(24.5444f, -0.3462f, 7.6468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(24.5441f, -0.3462f, -2.3532f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(24.5444f, -0.3462f, 10.1468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(34.5443f, -0.3462f, 2.6466f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(37.0443f, -0.3462f, 2.6465f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(40.0443f, -0.3462f, 2.6465f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.98179E-06f), new Vector3(0.7998f, 3.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(-2.9558f, -0.3462f, 2.6476f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(7.0442f, -0.3462f, 2.6473f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(17.0442f, -0.3462f, 2.647f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(27.0443f, -0.3462f, 2.6468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(26.2529f, -0.3462f, -4.8549f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(27.0444f, -0.3462f, 7.6468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(29.5444f, -0.3462f, 7.6467f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(27.0441f, -0.3462f, -2.3532f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(29.5441f, -0.3462f, -2.3533f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(21.391f, -0.3509f, 15.1594f), new Quaternion(0.7071069f, 0.7071066f, -9.061128E-06f, -8.999882E-06f), new Vector3(0.7998f, 3.8071f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(21.3911f, -0.3509f, 20.1594f), new Quaternion(0.7071069f, 0.7071066f, -9.061128E-06f, -8.999878E-06f), new Vector3(0.7998f, 3.8071f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(27.0445f, -0.3462f, 15.1468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(27.0447f, -0.3462f, 20.1468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(21.391f, -0.3509f, 12.6595f), new Quaternion(0.7071069f, 0.7071067f, -9.061068E-06f, -8.98898E-06f), new Vector3(0.7998f, 3.807f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(27.0445f, -0.3462f, 12.6468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(27.0446f, -0.3462f, 17.6468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(24.5445f, -0.3462f, 15.1468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(24.5447f, -0.3462f, 20.1468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(29.5445f, -0.3462f, 15.1467f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(29.5447f, -0.3462f, 20.1467f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.6489f, -0.3462f, 15.146f), new Quaternion(0.7071069f, 0.7071066f, -9.077943E-06f, -9.014198E-06f), new Vector3(0.7998f, 1.7086f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(24.5445f, -0.3462f, 12.6468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(24.5446f, -0.3462f, 17.6468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(29.5445f, -0.3462f, 12.6467f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(29.5446f, -0.3462f, 17.6467f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.6488f, -0.3462f, 12.646f), new Quaternion(0.7071069f, 0.7071066f, -9.077943E-06f, -9.014364E-06f), new Vector3(0.7998f, 1.7086f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.6488f, -0.3462f, 17.6459f), new Quaternion(0.7071069f, 0.7071067f, -9.061127E-06f, -9.014974E-06f), new Vector3(0.7998f, 1.7084f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(31.6489f, -0.3462f, 20.1459f), new Quaternion(0.7071069f, 0.7071067f, -9.061127E-06f, -9.014974E-06f), new Vector3(0.7998f, 1.7084f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(18.225f, -0.3509f, 23.9092f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(16.5306f, -0.3332f, -19.3922f), new Quaternion(8.551592E-06f, -1.061603E-05f, -0.707107f, 0.7071066f), new Vector3(0.7998f, 3.5256f, 3.4208f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(27.0444f, -0.3462f, 10.1468f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new BoxColliderDescription(new Vector3(29.5444f, -0.3462f, 10.1467f), new Quaternion(0.7071069f, 0.7071066f, -9.078049E-06f, -8.99624E-06f), new Vector3(0.7998f, 2.5f, 2.5f), 0, SurfaceType.Default, HitType.Default, 1f, 1f)
			}, 
			triangles: new TriangleColliderDescription[] {
				new TriangleColliderDescription(new Vector3(9.3361f, 3.6157f, -7.2209f), new Vector3(17.0938f, 4.3877f, -7.1527f), new Vector3(17.0938f, 4.3877f, -16.0545f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new TriangleColliderDescription(new Vector3(9.4122f, 0.3364f, -7.3704f), new Vector3(13.298f, 0.3364f, -7.3896f), new Vector3(13.2979f, 0.3364f, -15.9216f), 0, SurfaceType.Default, HitType.Default, 1f, 1f),
				new TriangleColliderDescription(new Vector3(17.0866f, 4.3507f, -7.1527f), new Vector3(9.3264f, 3.6005f, -7.1957f), new Vector3(17.0866f, 4.3507f, -16.0546f), 0, SurfaceType.Default, HitType.Default, 1f, 1f)
			}, 
			capsules: new CapsuleColliderDescription[] {
				new CapsuleColliderDescription(new Vector3(-15.4571f, 0.1707f, -17.656f), new Quaternion(0.7064782f, -0.02981228f, 0.02981228f, 0.706478f), 0.74f, 0.08f, RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact | RigidBodyType.DisabledGrenadeCollisions, SurfaceType.Default, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(-18.1778f, 0.1707f, -17.886f), new Quaternion(0.7064782f, -0.02981228f, 0.02981228f, 0.706478f), 0.74f, 0.08f, RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact | RigidBodyType.DisabledGrenadeCollisions, SurfaceType.Default, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(17.0341f, 6.4175f, 17.4245f), new Quaternion(-0.06147749f, 0.9978436f, -0.02299288f, -0.0002026856f), 13f, 0.45f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(13.6434f, 2.4583f, -5.9501f), new Quaternion(-0.1386873f, 0.8702621f, 0.07976675f, -0.4658829f), 13f, 0.45f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(-36.8145f, 4.5389f, -22.6117f), new Quaternion(-0.0216941f, 0.5394062f, -0.06118373f, 0.8395396f), 13f, 0.45f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(-19.1955f, 4.5723f, -25f), new Quaternion(0.01788208f, -0.5723221f, -0.02209203f, 0.8195362f), 13f, 0.45f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(-37.4416f, 1.9532f, -18.0138f), new Quaternion(-0.02364674f, 0.9400242f, 0.0008754015f, 0.3402861f), 13f, 0.45f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(40.5633f, 2.3881f, -21.476f), new Quaternion(-0.03538171f, -0.8503262f, -0.1254528f, 0.509858f), 13f, 0.45f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(-13.8781f, 4.705f, 25.0449f), new Quaternion(-0.06530462f, -0.8112373f, -0.08692371f, 0.5745205f), 13f, 0.45f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(18.5854f, 6.1963f, 7.7213f), new Quaternion(-0.1232105f, -0.2122124f, 0.04427457f, 0.9684136f), 13f, 0.3f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(35.6939f, 4.0056f, 24.9738f), new Quaternion(0.06936152f, 0.9608027f, 0.01926576f, -0.2677235f), 13f, 0.3f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(-17.0622f, 5.865f, 24.604f), new Quaternion(0.03095846f, -0.5015045f, 0.07294624f, 0.8615183f), 13f, 0.3f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(-32.3921f, 5.475f, 25.0172f), new Quaternion(0.05109752f, -0.2250755f, 0.06056906f, 0.9711136f), 13f, 0.3f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f),
				new CapsuleColliderDescription(new Vector3(-37.5591f, 3.299f, 5.2451f), new Quaternion(-0.07745949f, -0.04439897f, 0.1073274f, 0.9902068f), 13f, 0.3f, 0, SurfaceType.Wood, HitType.Default, 1f, 1f)
			}, 
			hulls: Array.Empty<HullColliderDescription>(), 
			hullData: Array.Empty<HullShapeData>());
	}
}
