using System;
using System.Numerics;
using System.Collections.Generic;
using Framework.Replication.Enum;
using Models.MathUtils;
using Models.Physics.Types;
using Models.References.Builder;
using Models.References.CargoWorkbench;
using Models.References.Collection;
using Models.References.Door;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.KinematicObject;
using Models.References.Location;
using Models.References.Location.LocationTraders;
using Models.References.Location.LocationVehiclesSpawnPoints;
using Models.References.MoneyWorkbench;
using Models.References.Physics;
using Models.References.Plot.Colliders;
using Models.References.Switch;
using Models.References.Trader;
using Models.References.Trigger;
using Models.References.Vehicle;
using Models.References.Builder.Colliders;
using Models.References.Inventory;
using Models.References.Plot;
using Models.HitBox.Types;
using Models.References.PlotContainers;
using Models.References.WindowBlinds;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.References.Colliders;
#if UNITY_MATH
using Unity.Collections;
using Unity.Mathematics;
#endif

namespace Models.References.Builder
{
/// <auto-generated>This is autogenerated class from <see cref="PlotMainBuildingSchemeDescriptionFile"/></auto-generated>
public partial class MainBuildingDescription
{
    public static MainBuildingDescription Home1 { get; } = new MainBuildingDescription(
        id: "home_1",
        colliders: MainBuildingHome1Colliders.Main,
        obbs: new OBB[]{ new OBB(new Vector3(-15.8054f, 4.8264f, 1.3633f), new Vector3(12.7962f, 5.8486f, 13.6926f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(13.0753f, 4.8264f, 13.3555f), new Vector3(4.5728f, 5.8486f, 5.0277f), new Quaternion(0f, 0f, 0f, 1f)) },
        inventoryItem: () => InventoryItemDescription.Home1,
        furnitureSlotsInteractPosition: new Vector3(-15.25f, 0.1125f, -8.68f),
        copFurnitureSlot: new PlotConstructionSlotDescription(new Vector3(-8.812f, 0.115f, -8.415f), new Quaternion(0f, -0.0168337f, 0f, 0.9998583f)),
        collectionTableSlot: new PlotConstructionSlotDescription(new Vector3(-18.221f, 0.1156f, -1.513f), new Quaternion(0f, -1f, 0f, 4.917383E-07f)),
        cargoWorkbenchSlot: new PlotConstructionSlotDescription(new Vector3(-22.208f, 0.115f, -0.993f), new Quaternion(0f, 0.7071058f, 0f, 0.7071078f)),
        storageSlots: new Dictionary<StorageSlotDescription, PlotConstructionSlotDescription>()
        {
            {StorageSlotDescription.Slot1, new PlotConstructionSlotDescription(new Vector3(-19.32f, 0.1272f, -8.6323f), new Quaternion(0f, 1.460314E-06f, 0f, 1f))},
            {StorageSlotDescription.Slot2, new PlotConstructionSlotDescription(new Vector3(-20.48f, 0.1272f, -8.632f), new Quaternion(0f, 1.460314E-06f, 0f, 1f))},
            {StorageSlotDescription.Slot3, new PlotConstructionSlotDescription(new Vector3(-21.658f, 0.1272f, -8.632f), new Quaternion(0f, 1.460314E-06f, 0f, 1f))},
        },
        shelvingSlots: new Dictionary<ShelvingSlotDescription, PlotConstructionSlotDescription>()
        {
            {ShelvingSlotDescription.Slot0, new PlotConstructionSlotDescription(new Vector3(-12.8f, 0.117f, -8.2836f), new Quaternion(0f, 1.937151E-07f, 0f, 1f))},
            {ShelvingSlotDescription.Slot1, new PlotConstructionSlotDescription(new Vector3(-17.7001f, 0.117f, -8.2836f), new Quaternion(0f, -2.05636E-06f, 0f, -1f))},
            {ShelvingSlotDescription.Slot2, new PlotConstructionSlotDescription(new Vector3(-10.6493f, 0.117f, -8.2997f), new Quaternion(0f, 1.639128E-06f, 0f, 1f))},
        },
        npcPosition: new Vector3(-12.68f, 1.213f, 3.278f),
        planningBoardPosition: new Vector3(-18.673f, 0.115f, 3.707f),
        carGarage: new PlotGarageDescription<PlotCarSlotDescription>(new Vector3(-14.386f, 1.505f, -0.9212f), new Dictionary<PlotCarSlotDescription, PlotGarageSlotDescription>{ { PlotCarSlotDescription.Slot0, new PlotGarageSlotDescription(new Vector3(-12.439f, 0.105f, -5.148f), new Quaternion(0f, 0.7071069f, 0f, 0.7071066f), 0f) } }),
        doors: new Dictionary<MainBuildingDoorSlotDescription, PlotDoorDescription>()
        {
            {MainBuildingDoorSlotDescription.Slot0, new PlotDoorDescription(DoorDescription.DoorSingleMetal_01, new Vector3(-10.2649f, 0.115f, 5.7754f), new Vector3(-10.2649f, 1.465f, 5.7754f), new Quaternion(0f, -0.7071071f, 0f, 0.7071066f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot1, new PlotDoorDescription(DoorDescription.DoorSingleMetal_01, new Vector3(-22.4363f, 0.115f, -5.6746f), new Vector3(-22.4363f, 1.465f, -5.6746f), new Quaternion(0f, 0.7071064f, 0f, 0.7071072f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot2, new PlotDoorDescription(DoorDescription.DoorSingleWooden_01, new Vector3(-15.9506f, 0.115f, -0.7889f), new Vector3(-15.9506f, 1.465f, -0.7889f), new Quaternion(0f, 1f, 0f, -1.013279E-06f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot3, new PlotDoorDescription(DoorDescription.DoorSingleWooden_01, new Vector3(-19.1648f, 0.115f, 1.2755f), new Vector3(-19.1648f, 1.465f, 1.2755f), new Quaternion(0f, 0.7071085f, 0f, 0.7071051f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot4, new PlotDoorDescription(DoorDescription.DoorSingleWooden_01, new Vector3(-19.1649f, 0.115f, 6.1754f), new Vector3(-19.1649f, 1.465f, 6.1754f), new Quaternion(0f, 0.7071085f, 0f, 0.7071051f), new OBB[] {  }, 3)},
            {MainBuildingDoorSlotDescription.Slot5, new PlotDoorDescription(DoorDescription.WindowDoubleGlass_01, new Vector3(-10.2745f, 1.0758f, 9.2021f), new Vector3(-10.3029f, 2.1143f, 9.1897f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new OBB[] { new OBB(new Vector3(-11.063f, 1.0758f, 9.2004f), new Vector3(1.36f, 2.5f, 0.7915f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)), new OBB(new Vector3(-8.9994f, 1.0758f, 9.2021f), new Vector3(2.5f, 2.5f, 1.2788f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 1)},
            {MainBuildingDoorSlotDescription.Slot6, new PlotDoorDescription(DoorDescription.WindowDoubleGlass_01, new Vector3(-16.4295f, 1.0758f, 12.2014f), new Vector3(-16.4277f, 2.165f, 12.1731f), new Quaternion(0f, 4.768371E-07f, 0f, 1f), new OBB[] { new OBB(new Vector3(-16.3957f, 1.0758f, 11.4129f), new Vector3(1.4903f, 2.5f, 0.7915f), new Quaternion(0f, 4.768371E-07f, 0f, 1f)), new OBB(new Vector3(-16.4294f, 1.0758f, 13.4766f), new Vector3(2.5f, 2.5f, 1.2788f), new Quaternion(0f, 4.768371E-07f, 0f, 1f)) }, 1)},
            {MainBuildingDoorSlotDescription.Slot7, new PlotDoorDescription(DoorDescription.WindowDoubleGlass_01, new Vector3(-23.9265f, 1.0757f, 6.3237f), new Vector3(-23.8982f, 2.165f, 6.3255f), new Quaternion(0f, -0.7071065f, 0f, 0.7071071f), new OBB[] { new OBB(new Vector3(-23.138f, 1.0757f, 6.7017f), new Vector3(1.3762f, 2.5f, 0.7915f), new Quaternion(0f, -0.7071065f, 0f, 0.7071071f)), new OBB(new Vector3(-25.2016f, 1.0757f, 6.3237f), new Vector3(2.5f, 2.5f, 1.2788f), new Quaternion(0f, -0.7071065f, 0f, 0.7071071f)) }, 1)},
        },
        windowBlinds: new Dictionary<MainBuildingWindowBlindsSlotDescription, PlotWindowBlindsDescription>()
        {
            {MainBuildingWindowBlindsSlotDescription.Slot0, new PlotWindowBlindsDescription(new Vector3(-10.4095f, 1.9848f, 9.2021f), new OBB[] { new OBB(new Vector3(-11.063f, 1.0758f, 9.2004f), new Vector3(1.36f, 2.5f, 0.7915f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)), new OBB(new Vector3(-8.9994f, 1.0758f, 9.2021f), new Vector3(2.5f, 2.5f, 1.2788f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) })},
            {MainBuildingWindowBlindsSlotDescription.Slot1, new PlotWindowBlindsDescription(new Vector3(-16.4295f, 1.9778f, 12.0214f), new OBB[] { new OBB(new Vector3(-16.3957f, 1.0758f, 11.4129f), new Vector3(1.4903f, 2.5f, 0.7915f), new Quaternion(0f, 4.768371E-07f, 0f, 1f)), new OBB(new Vector3(-16.4294f, 1.0758f, 13.4766f), new Vector3(2.5f, 2.5f, 1.2788f), new Quaternion(0f, 4.768371E-07f, 0f, 1f)) })},
            {MainBuildingWindowBlindsSlotDescription.Slot2, new PlotWindowBlindsDescription(new Vector3(-23.6615f, 1.9857f, 6.3237f), new OBB[] { new OBB(new Vector3(-23.138f, 1.0757f, 6.7017f), new Vector3(1.3762f, 2.5f, 0.7915f), new Quaternion(0f, -0.7071065f, 0f, 0.7071071f)), new OBB(new Vector3(-25.2016f, 1.0757f, 6.3237f), new Vector3(2.5f, 2.5f, 1.2788f), new Quaternion(0f, -0.7071065f, 0f, 0.7071071f)) })},
            {MainBuildingWindowBlindsSlotDescription.Slot3, new PlotWindowBlindsDescription(new Vector3(-10.3606f, 1.481f, 1.2477f), new OBB[] { new OBB(new Vector3(-11.2106f, 1.589f, 1.4959f), new Vector3(2.1553f, 1.9792f, 0.7932f), new Quaternion(0f, -0.7071104f, 0f, -0.7071033f)) })},
        },
        plotContainers: new Dictionary<MainBuildingPlotContainerSlotDescription, PlotContainerLocationDescription>()
        {
            {MainBuildingPlotContainerSlotDescription.Slot0, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_01, new Vector3(-19.969f, 0.487f, 7.81f), new Quaternion(0f, 0.7071065f, 0f, 0.7071072f))},
            {MainBuildingPlotContainerSlotDescription.Slot1, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_01, new Vector3(-21.282f, 0.487f, 9.87f), new Quaternion(0f, -1.490116E-08f, 0f, 1f))},
            {MainBuildingPlotContainerSlotDescription.Slot2, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_01, new Vector3(-18.037f, 0.75f, -0.234f), new Quaternion(0f, 1f, 0f, 7.748603E-07f))},
            {MainBuildingPlotContainerSlotDescription.Slot3, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_02, new Vector3(-11.6956f, 0.5526f, 2.885f), new Quaternion(0f, 0f, 0f, 1f))},
            {MainBuildingPlotContainerSlotDescription.Slot4, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_02, new Vector3(-23.351f, 0.7059f, 4.387f), new Quaternion(0f, 1f, 0f, 5.811452E-07f))},
            {MainBuildingPlotContainerSlotDescription.Slot5, new PlotContainerLocationDescription(PlotContainerDescription.PlotContainer_02, new Vector3(-8.644f, 0.5956f, -1.732f), new Quaternion(0f, 1.117587E-06f, 0f, -1f))},
        },
        moneyWorkbench: new PlotMoneyWorkbenchDescription(new Vector3(-18.646f, 1.0202f, 9.666f), new Quaternion(0f, 0.7071058f, 0f, 0.7071078f), new Vector3(0.066f, 0.455f, -0.178f)),
        kinematicObjectsControllers: new KinematicObjectsControllerDescription[]
        {
            new KinematicObjectsControllerDescription(
				new KinematicObjectDescription[] {
						new KinematicObjectDescription(
						new CollidersDescription(
							boxes: new BoxColliderDescription[] {
								new BoxColliderDescription(new Vector3(0f, 0.1453f, -1.4411f), new Quaternion(0f, 2.442558E-14f, 0f, 1f), new Vector3(5.5f, 3.1094f, 0.1f), 0, SurfaceType.Metal, HitType.Default, 1f, 1f)
							}, 
							triangles: Array.Empty<TriangleColliderDescription>(), 
							capsules: Array.Empty<CapsuleColliderDescription>(), 
							hulls: Array.Empty<HullColliderDescription>(), 
							hullData: Array.Empty<HullShapeData>()))
					}, 
				new KinematicObjectMovementDescription[] {
						new KinematicObjectMovementDescription(new Vector3(-8.6916f, 1.5244f, -4.7994f), new Quaternion(0f, -0.7071044f, 0f, 0.7071092f), new Vector3(-8.9916f, 2.0244f, -4.7994f), new Quaternion(0.4449982f, -0.5495234f, 0.4449953f, 0.549527f))
					}, 
				3000, 500, KinematicObjectMovementStateDescription.Point1, false, 
				new OBB[] { new OBB(new Vector3(-7.2216f, 0.9878f, -4.7996f), new Vector3(3.875f, 2.7272f, 2.5f), new Quaternion(0f, 0.7071065f, 0f, -0.7071071f)) }, 
				new OBB[] { new OBB(new Vector3(-1.3839f, 1.5244f, -4.7996f), new Vector3(3.875f, 2.5f, 20.8524f), new Quaternion(0f, 0.7071065f, 0f, -0.7071071f)), new OBB(new Vector3(6.209f, 1.5244f, -7.1517f), new Vector3(6.2271f, 2.5f, 13.2595f), new Quaternion(0f, 0.7071065f, 0f, -0.7071071f)), new OBB(new Vector3(7.3225f, 1.5244f, -3.9168f), new Vector3(9.4621f, 2.5f, 12.1459f), new Quaternion(0f, 0.7071065f, 0f, -0.7071071f)) }, 
				new HashSet<KinematicObjectMovementStateDescription> { KinematicObjectMovementStateDescription.Point1 }),
        },
        terrainCarveZones: new TerrainCarveZoneDescription[]
        {
        },
        spawnPoint: new OrientedPoint(new Vector3(-22f, 0.1746f, 6.25f), new Quaternion(0f, -0.7021211f, 0f, -0.7120575f)),
        defenseSettings: PlotBuildingDefenseSettingsDescription.Home1,
        defenseInteractionPoint: new Vector3(-8.582f, 1.57f, -0.583f)
    );
}
}
