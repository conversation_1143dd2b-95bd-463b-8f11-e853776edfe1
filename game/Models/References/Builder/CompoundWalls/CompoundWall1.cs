using Models.References.Builder.Colliders;
using System.Numerics;
using System.Collections.Generic;
using Models.References.Inventory;
using Models.References.Door;
using Models.References.KinematicObject;
using Models.References.Plot;
using Models.MathUtils;
using Models.Physics.Types;
using Models.HitBox.Types;
using System;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.References.Colliders;

namespace Models.References.Builder
{
/// <auto-generated>This is autogenerated class from <see cref="PlotCompoundWallSchemeDescriptionFile"/></auto-generated>
public partial class CompoundWallDescription
{
    public static CompoundWallDescription CompoundWall1 { get; } = new CompoundWallDescription(
        id: "compound_wall_1",
        colliders: CompoundWall1Colliders.Main,
        obbs: new OBB[]{ new OBB(new Vector3(2f, 1.3075f, 26.0001f), new Vector3(40.295f, 1.4425f, 0.295f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(2f, 1.3075f, -26f), new Vector3(40.295f, 1.4425f, 0.295f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(-38.0099f, 1.3075f, 0.0225f), new Vector3(0.2851f, 1.4425f, 26.2725f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(41.991f, 1.3075f, 0f), new Vector3(0.286f, 1.4425f, 26.295f), new Quaternion(0f, 0f, 0f, 1f)) },
        inventoryItem: () => InventoryItemDescription.CompoundWall1,
        doors: new Dictionary<CompoundWallDoorSlotDescription, PlotDoorDescription>()
        {
        },
        kinematicObjectsControllers: new KinematicObjectsControllerDescription[]
        {
            new KinematicObjectsControllerDescription(
				new KinematicObjectDescription[] {
						new KinematicObjectDescription(
						new CollidersDescription(
							boxes: new BoxColliderDescription[] {
								new BoxColliderDescription(new Vector3(1.2734f, 1.4103f, 0.166f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(9.3089f, 2.3065f, 0.1219f), RigidBodyType.DisabledImpact, SurfaceType.Metal, HitType.Default, 1f, 1f),
								new BoxColliderDescription(new Vector3(-4.0173f, 1.4103f, 0.166f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(1.2743f, 2.3065f, 0.1219f), RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact, SurfaceType.Default, HitType.Transparent, 1f, 1f)
							}, 
							triangles: Array.Empty<TriangleColliderDescription>(), 
							capsules: Array.Empty<CapsuleColliderDescription>(), 
							hulls: Array.Empty<HullColliderDescription>(), 
							hullData: Array.Empty<HullShapeData>()))
					}, 
				new KinematicObjectMovementDescription[] {
						new KinematicObjectMovementDescription(new Vector3(41.38f, 0f, 2.902f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(41.38f, 0f, 11.502f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f))
					}, 
				3000, 500, KinematicObjectMovementStateDescription.Point1, false, 
				new OBB[] { new OBB(new Vector3(41.38f, 0f, 2.6195f), new Vector3(6.2825f, 4f, 2.5f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 
				new OBB[] { new OBB(new Vector3(26.1505f, 4f, -0.6818f), new Vector3(15f, 5f, 15.5513f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)), new OBB(new Vector3(68.7303f, 0.8268f, 0.009f), new Vector3(26.286f, 8.1732f, 27.0919f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 
				new HashSet<KinematicObjectMovementStateDescription> { KinematicObjectMovementStateDescription.Point1, KinematicObjectMovementStateDescription.Point2 }),
        },
        defenseSettings: PlotBuildingDefenseSettingsDescription.CompoundWall1,
        defenseInteractionPoint: new Vector3(42.123f, 1.22f, 10.975f)
    );
}
}
