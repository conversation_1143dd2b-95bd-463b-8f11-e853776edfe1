using Models.References.Builder.Colliders;
using System.Numerics;
using System.Collections.Generic;
using Models.References.Inventory;
using Models.References.Door;
using Models.References.KinematicObject;
using Models.References.Plot;
using Models.MathUtils;
using Models.Physics.Types;
using Models.HitBox.Types;
using System;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.References.Colliders;

namespace Models.References.Builder
{
/// <auto-generated>This is autogenerated class from <see cref="PlotCompoundWallSchemeDescriptionFile"/></auto-generated>
public partial class CompoundWallDescription
{
    public static CompoundWallDescription CompoundWall0 { get; } = new CompoundWallDescription(
        id: "compound_wall_0",
        colliders: CompoundWall0Colliders.Main,
        obbs: new OBB[]{ new OBB(new Vector3(1.8657f, 1.24f, 26.0987f), new Vector3(40.3429f, 1.375f, 0.1959f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(1.9513f, 1.24f, -26.0637f), new Vector3(40.4293f, 1.375f, 0.2476f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(-38.1739f, 1.24f, 0.0063f), new Vector3(0.3103f, 1.375f, 26.2985f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(42.0754f, 1.3075f, -0.0436f), new Vector3(0.3168f, 1.3075f, 26.3333f), new Quaternion(0f, 0f, 0f, 1f)) },
        inventoryItem: () => InventoryItemDescription.CompoundWall0,
        doors: new Dictionary<CompoundWallDoorSlotDescription, PlotDoorDescription>()
        {
            {CompoundWallDoorSlotDescription.Slot0, new PlotDoorDescription(DoorDescription.DoorSingleGrid_01, new Vector3(42f, 0f, -4f), new Vector3(42.004f, 1.2882f, -3.9973f), new Quaternion(0f, -0.7071068f, 0f, 0.7071068f), new OBB[] {  }, 3)},
        },
        kinematicObjectsControllers: new KinematicObjectsControllerDescription[]
        {
            new KinematicObjectsControllerDescription(
				new KinematicObjectDescription[] {
						new KinematicObjectDescription(
						new CollidersDescription(
							boxes: new BoxColliderDescription[] {
								new BoxColliderDescription(new Vector3(0.59f, 1.4125f, 0.18f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(10.24f, 2.3002f, 0.18f), RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact, SurfaceType.Default, HitType.Transparent, 1f, 1f)
							}, 
							triangles: Array.Empty<TriangleColliderDescription>(), 
							capsules: Array.Empty<CapsuleColliderDescription>(), 
							hulls: Array.Empty<HullColliderDescription>(), 
							hullData: Array.Empty<HullShapeData>()))
					}, 
				new KinematicObjectMovementDescription[] {
						new KinematicObjectMovementDescription(new Vector3(41.575f, 0f, 3.65f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(41.575f, 0f, 12.25f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f))
					}, 
				3000, 500, KinematicObjectMovementStateDescription.Point1, false, 
				new OBB[] { new OBB(new Vector3(41.575f, 0f, 3.2212f), new Vector3(6.4288f, 4f, 2.5f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 
				new OBB[] { new OBB(new Vector3(26.3455f, 4f, 0.0662f), new Vector3(15f, 5f, 15.5513f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)), new OBB(new Vector3(68.9253f, 0.8268f, 0.0663f), new Vector3(26f, 8.1732f, 27.0919f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 
				new HashSet<KinematicObjectMovementStateDescription> { KinematicObjectMovementStateDescription.Point1, KinematicObjectMovementStateDescription.Point2 }),
        },
        defenseSettings: PlotBuildingDefenseSettingsDescription.CompoundWall0,
        defenseInteractionPoint: new Vector3(42.067f, 1.43f, 10.682f)
    );
}
}
