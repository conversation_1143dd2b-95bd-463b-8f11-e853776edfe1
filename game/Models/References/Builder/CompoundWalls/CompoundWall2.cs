using Models.References.Builder.Colliders;
using System.Numerics;
using System.Collections.Generic;
using Models.References.Inventory;
using Models.References.Door;
using Models.References.KinematicObject;
using Models.References.Plot;
using Models.MathUtils;
using Models.Physics.Types;
using Models.HitBox.Types;
using System;
using Models.Physics.Shapes.Data.Common;
using Models.Physics.Shapes.Data.Hull;
using Models.References.Colliders;

namespace Models.References.Builder
{
/// <auto-generated>This is autogenerated class from <see cref="PlotCompoundWallSchemeDescriptionFile"/></auto-generated>
public partial class CompoundWallDescription
{
    public static CompoundWallDescription CompoundWall2 { get; } = new CompoundWallDescription(
        id: "compound_wall_2",
        colliders: CompoundWall2Colliders.Main,
        obbs: new OBB[]{ new OBB(new Vector3(1.9927f, 1.24f, 25.743f), new Vector3(40.2923f, 1.375f, 0.6336f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(2.0279f, 1.24f, -26.0539f), new Vector3(40.2576f, 1.375f, 0.5603f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(-37.459f, 1.24f, -0.1223f), new Vector3(0.772f, 1.375f, 26.4995f), new Quaternion(0f, 0f, 0f, 1f)), new OBB(new Vector3(41.5731f, 1.24f, -0.119f), new Vector3(0.7124f, 1.375f, 26.4957f), new Quaternion(0f, 0f, 0f, 1f)) },
        inventoryItem: () => InventoryItemDescription.CompoundWall2,
        doors: new Dictionary<CompoundWallDoorSlotDescription, PlotDoorDescription>()
        {
            {CompoundWallDoorSlotDescription.Slot0, new PlotDoorDescription(DoorDescription.CompoundWallDoorSingleStone_01, new Vector3(-8.0503f, 0f, 26.0218f), new Vector3(-8.0503f, 1.3f, 26.0218f), new Quaternion(0f, -1f, 0f, 4.217028E-06f), new OBB[] {  }, 3)},
            {CompoundWallDoorSlotDescription.Slot1, new PlotDoorDescription(DoorDescription.CompoundWallDoorSingleStone_01, new Vector3(-12.0503f, 0f, -25.9782f), new Vector3(-12.0503f, 1.3f, -25.9782f), new Quaternion(0f, -1f, 0f, 4.217028E-06f), new OBB[] {  }, 3)},
            {CompoundWallDoorSlotDescription.Slot2, new PlotDoorDescription(DoorDescription.CompoundWallDoorSingleStone_01, new Vector3(41.994f, 0f, -4.7845f), new Vector3(41.994f, 1.3f, -4.7845f), new Quaternion(0f, -0.7071098f, 0f, -0.7071038f), new OBB[] {  }, 3)},
        },
        kinematicObjectsControllers: new KinematicObjectsControllerDescription[]
        {
            new KinematicObjectsControllerDescription(
				new KinematicObjectDescription[] {
						new KinematicObjectDescription(
						new CollidersDescription(
							boxes: new BoxColliderDescription[] {
								new BoxColliderDescription(new Vector3(1.2835f, 1.6745f, 0.166f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(9.2886f, 2.8349f, 0.1219f), RigidBodyType.DisabledImpact, SurfaceType.Concrete, HitType.Default, 1f, 1f),
								new BoxColliderDescription(new Vector3(-4.308f, 1.6702f, 0.166f), new Quaternion(0f, 0f, 0f, 1f), new Vector3(1.9004f, 2.8263f, 0.1219f), RigidBodyType.DisabledShots | RigidBodyType.DisabledImpact, SurfaceType.Default, HitType.Transparent, 1f, 1f)
							}, 
							triangles: Array.Empty<TriangleColliderDescription>(), 
							capsules: Array.Empty<CapsuleColliderDescription>(), 
							hulls: Array.Empty<HullColliderDescription>(), 
							hullData: Array.Empty<HullShapeData>()))
					}, 
				new KinematicObjectMovementDescription[] {
						new KinematicObjectMovementDescription(new Vector3(41.362f, 0f, 2.47f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f), new Vector3(41.362f, 0f, 11.07f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f))
					}, 
				3000, 500, KinematicObjectMovementStateDescription.Point1, false, 
				new OBB[] { new OBB(new Vector3(41.362f, 0f, 2.3306f), new Vector3(6.1394f, 4f, 2.5f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 
				new OBB[] { new OBB(new Vector3(26.1325f, 4f, -1.1138f), new Vector3(15f, 5f, 15.5513f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)), new OBB(new Vector3(68.7123f, 0.8268f, -0.423f), new Vector3(26.286f, 8.1732f, 27.0919f), new Quaternion(0f, 0.7071068f, 0f, 0.7071068f)) }, 
				new HashSet<KinematicObjectMovementStateDescription> { KinematicObjectMovementStateDescription.Point1, KinematicObjectMovementStateDescription.Point2 }),
        },
        defenseSettings: PlotBuildingDefenseSettingsDescription.CompoundWall2,
        defenseInteractionPoint: new Vector3(42.222f, 1.211f, 10.543f)
    );
}
}
