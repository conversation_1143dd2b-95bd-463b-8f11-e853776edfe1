using System.Collections.Generic;
using Models.MathUtils;
using Models.References.Door;
using Models.References.KinematicObject;
using Models.References.PlotBuildingDefenseBlock;

namespace Models.References.Builder
{
    public record LocationCompoundWallDescription(
        IReadOnlyDictionary<CompoundWallDoorSlotDescription, LocationBuilderDoorDescription> Doors,
        IReadOnlyList<OBB> <PERSON><PERSON>,
        AABB Aabb,
        LocationPlotBuildingDefenseBlockDescription DefenseBlockDescription
    );
}