using System;
using System.Collections.Generic;
using Models.References.Inventory;
using Models.Utils.Extensions;

namespace Models.References.Loot
{
    public class LootItemsListDescription
    {
        private LootItemsListDescription(IReadOnlyList<LootItemDescription> items)
        {
            Items = items;
        }
    
        public IReadOnlyList<LootItemDescription> Items { get; }

        public void GetItem(Random random, out InventoryItemDescription item, out int count, out int ammo, out int cumulativePoints)
        {
            int itemsCount = Items.Count;
            LootItemDescription lootItem = itemsCount == 1 ? Items[0] : Items[random.Next(itemsCount)];

            item = lootItem.Item;
            ammo = lootItem.Ammo;

            count = random.NextInclusive(lootItem.CountMin, lootItem.CountMax);
            cumulativePoints = random.NextInclusive(lootItem.CumulativePointMin, lootItem.CumulativePointMax);
        }
    
        public static LootItemsListDescription SafeAll { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.SyringeUncommon),
        }        );

        public static LootItemsListDescription Cash { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.CashCommon, 4, InventoryItemDescription.CashCommon.StackSize),
        }        );
        public static LootItemsListDescription CashUncommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.CashUncommon, 4, 15),
        }        );
        public static LootItemsListDescription CashRare { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.CashRare, 10, 50),
        }        );
    
        public static LootItemsListDescription CashRareCargo { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.CashRare, 30, 50),
        }        );

        public static LootItemsListDescription KeysT1 { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey0),
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey1),
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey2),
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey3),
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey4),
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey5),
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey6),
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey7),
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey8),
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey9),
            LootItemDescription.BuildItem(InventoryItemDescription.SafeKey10),
        }        );
        public static LootItemsListDescription KeysT2 { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.RoomKey0),
            LootItemDescription.BuildItem(InventoryItemDescription.RoomKey1),
            LootItemDescription.BuildItem(InventoryItemDescription.RoomKey2),
            LootItemDescription.BuildItem(InventoryItemDescription.RoomKey4),
            LootItemDescription.BuildItem(InventoryItemDescription.RoomKey5),
            LootItemDescription.BuildItem(InventoryItemDescription.RoomKey6),
        }        );
        public static LootItemsListDescription KeysT3 { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.EpicKey0),
        }        );
    
        public static LootItemsListDescription AlcoholCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.TrashAlcoholBox, 1, 5),
            LootItemDescription.BuildItem(InventoryItemDescription.Alcohol0),
            LootItemDescription.BuildItem(InventoryItemDescription.Alcohol1),
            LootItemDescription.BuildItem(InventoryItemDescription.Alcohol2),
        }        );
        public static LootItemsListDescription AlcoholEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Alcohol3),
            LootItemDescription.BuildItem(InventoryItemDescription.Alcohol5),
        }        );
        public static LootItemsListDescription AlcoholLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Alcohol4),
        }        );
        public static LootItemsListDescription TrashAlcohol { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.TrashAlcoholBox, 1, 5),
        }        );
        public static LootItemsListDescription Drugs { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Drugs),
        }        );
        public static LootItemsListDescription Gold { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GoldBar),
        }        );
        public static LootItemsListDescription GenericElectronics { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Toaster),
            LootItemDescription.BuildItem(InventoryItemDescription.Hairdryer),
        }        );
        public static LootItemsListDescription HeadphonesCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Headphones1),
            LootItemDescription.BuildItem(InventoryItemDescription.Headphones2),
        }        );
        public static LootItemsListDescription HeadphonesEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Headphones3),
            LootItemDescription.BuildItem(InventoryItemDescription.Headphones4),
        }        );
        public static LootItemsListDescription PhonesCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Phone1),
            LootItemDescription.BuildItem(InventoryItemDescription.Phone2),
        }        );
        public static LootItemsListDescription PhonesEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Phone3),
            LootItemDescription.BuildItem(InventoryItemDescription.Phone4),
        }        );
        public static LootItemsListDescription GameConsoleCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GameConsole0),
        }        );
        public static LootItemsListDescription GameConsoleEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GameConsole1),
            LootItemDescription.BuildItem(InventoryItemDescription.GameConsole2),
            LootItemDescription.BuildItem(InventoryItemDescription.GameConsole3),
        }        );
        public static LootItemsListDescription LaptopCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Laptop1),
        }        );
        public static LootItemsListDescription LaptopEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Laptop2),
            LootItemDescription.BuildItem(InventoryItemDescription.Laptop3),
            LootItemDescription.BuildItem(InventoryItemDescription.Laptop4),
            LootItemDescription.BuildItem(InventoryItemDescription.Laptop5),
        }        );
        public static LootItemsListDescription LaptopLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Laptop6),
        }        );
        public static LootItemsListDescription GraphicsCardCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GraphicsCard1),
        }        );
        public static LootItemsListDescription GraphicsCardEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GraphicsCard1),
            LootItemDescription.BuildItem(InventoryItemDescription.GraphicsCard2),
        }        );
        public static LootItemsListDescription CameraCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Camera1),
            LootItemDescription.BuildItem(InventoryItemDescription.Camera2),
        }        );
        public static LootItemsListDescription CameraEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Camera3),
        }        );
        public static LootItemsListDescription TrashElectronics { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.TrashElectronicsBox, 1, 5),
        }        );

        public static LootItemsListDescription EarringsCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.SilverEarrings),
            LootItemDescription.BuildItem(InventoryItemDescription.Earrings),
            LootItemDescription.BuildItem(InventoryItemDescription.GoldenEarrings),
            LootItemDescription.BuildItem(InventoryItemDescription.GoldenEarrings2),
            LootItemDescription.BuildItem(InventoryItemDescription.GoldenEarrings3),
        }        );
        public static LootItemsListDescription EarringsEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GoldenEarrings4),
            LootItemDescription.BuildItem(InventoryItemDescription.GoldenEarrings5),
        }        );
        public static LootItemsListDescription RingsCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.SilverRing),
            LootItemDescription.BuildItem(InventoryItemDescription.GoldenRing),
            LootItemDescription.BuildItem(InventoryItemDescription.GoldenRing2),
            LootItemDescription.BuildItem(InventoryItemDescription.GoldenRing3),
        }        );
        public static LootItemsListDescription RingsEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GoldenRing4),
        }        );
        public static LootItemsListDescription NecklaceCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Necklace1),
            LootItemDescription.BuildItem(InventoryItemDescription.Necklace2),
            LootItemDescription.BuildItem(InventoryItemDescription.Necklace2),
            LootItemDescription.BuildItem(InventoryItemDescription.Necklace3),
        }        );
        public static LootItemsListDescription NecklaceEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Necklace4),
            LootItemDescription.BuildItem(InventoryItemDescription.Necklace5),
            LootItemDescription.BuildItem(InventoryItemDescription.Necklace6),
        }        );
        public static LootItemsListDescription NecklaceLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Necklace7),
        }        );
        public static LootItemsListDescription WatchCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Watch6),
            LootItemDescription.BuildItem(InventoryItemDescription.Watch2),
            LootItemDescription.BuildItem(InventoryItemDescription.Watch5),
        
        }        );
        public static LootItemsListDescription WatchEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Watch1),
            LootItemDescription.BuildItem(InventoryItemDescription.Watch3),
            LootItemDescription.BuildItem(InventoryItemDescription.Watch4),
        }        );
        public static LootItemsListDescription WatchLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Watch4),
        }        );
        public static LootItemsListDescription TrashJewelry { get; } = new (new LootItemDescription[]
        {       
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.TrashJewelryPouch, 1, 5),
        }        );
        public static LootItemsListDescription TrashJewelryCargo { get; } = new (new LootItemDescription[]
        {       
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.TrashJewelryPouch, 12, 25),
        }        );
        public static LootItemsListDescription OriginalPictureTube { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.OriginalPictureTube),
        }        );
        public static LootItemsListDescription TrashPictureTube { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.TrashPictureTube),
        }        );
        public static LootItemsListDescription TrashPictureTubeCargo { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.TrashPictureTube, 1, 3),
        }        );
        public static LootItemsListDescription VinylRecordEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.VinylRecord1),
        }        );
        public static LootItemsListDescription VinylRecordLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.VinylRecord2),
        }        );
        public static LootItemsListDescription GemsEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GemstoneSapphire),
            LootItemDescription.BuildItem(InventoryItemDescription.GemstoneEmerald),
            LootItemDescription.BuildItem(InventoryItemDescription.GemstoneRuby),
        }        );
        public static LootItemsListDescription GemsLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GemstoneDiamond),
        }        );
        public static LootItemsListDescription PerfumeCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Perfume3),
        }        );
        public static LootItemsListDescription PerfumeEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Perfume1),
            LootItemDescription.BuildItem(InventoryItemDescription.Perfume2),
        }        );
        public static LootItemsListDescription PokerCardsCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.PokerCards1),
            LootItemDescription.BuildItem(InventoryItemDescription.PokerCards3),
        }        );
        public static LootItemsListDescription PokerCardsEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.PokerCards2),
        }        );
        public static LootItemsListDescription ComicsCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Comics1),
            LootItemDescription.BuildItem(InventoryItemDescription.Comics3),
        }        );
        public static LootItemsListDescription ComicsEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Comics2),
            LootItemDescription.BuildItem(InventoryItemDescription.Comics4),
        }        );
        public static LootItemsListDescription ComicsLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Comics5),
        }        );
        public static LootItemsListDescription BaseballCardCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.BaseballCard2),
            LootItemDescription.BuildItem(InventoryItemDescription.BaseballCard3),
            LootItemDescription.BuildItem(InventoryItemDescription.BaseballCard4),
        }        );
        public static LootItemsListDescription BaseballCardEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.BaseballCard5),
        }        );
        public static LootItemsListDescription BaseballCardLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.BaseballCard1),
        }        );
        public static LootItemsListDescription MedicineCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.SyringeUncommon),
            LootItemDescription.BuildItem(InventoryItemDescription.PainkillerUncommon),
            LootItemDescription.BuildItem(InventoryItemDescription.AfakUncommon),
        
        }        );
        public static LootItemsListDescription MedicineEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.SyringeEpic),
            LootItemDescription.BuildItem(InventoryItemDescription.PainkillerEpic),
        }        );
        public static LootItemsListDescription MedicineLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.SyringeEpic),
            LootItemDescription.BuildItem(InventoryItemDescription.PainkillerEpic),
        }        );
        public static LootItemsListDescription TrashMedicine { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.TrashMedicineBox, 1, 5),
        }        );
        public static LootItemsListDescription BackpackUncommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.BackpackUncommon),
        }        );
        public static LootItemsListDescription BackpackEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.BackpackRare),
        }        );
        public static LootItemsListDescription BackpackLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.BackpackEpic),
        }        );
        public static LootItemsListDescription BeltCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.BeltUncommon),
        }        );
        public static LootItemsListDescription BeltEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.BeltRare),
        }        );
        public static LootItemsListDescription BeltLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.BeltEpic),
        }        );
        public static LootItemsListDescription VestUncommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Vest1Mk1),
        }        );
        public static LootItemsListDescription VestEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Vest3Mk1),
        }        );
        public static LootItemsListDescription VestLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Vest4Mk1),
        }        );
        public static LootItemsListDescription TrashAmmoBox { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.TrashAmmoBox, 1, 5),
        }        );
    
        public static LootItemsListDescription TrashAmmoBoxCargo { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.TrashAmmoBox, 5, 10),
        }        );
    
        public static LootItemsListDescription ArmorPlateCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.ArmorPlateUncommon, 1, 3),
        }        );
        public static LootItemsListDescription ArmorPlateEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.ArmorPlateUncommon, 1, 3),
        }        );
        public static LootItemsListDescription ArmorPlateLegendary { get; } = new (new LootItemDescription[]
        {

            LootItemDescription.BuildItem(InventoryItemDescription.ArmorPlateUncommon),
        }        );
        public static LootItemsListDescription WeaponAmmo { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.RifleAmmo, 10, 25),
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.MpAmmo, 10, 25),
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.ShotgunAmmo, 8, 15),
        }        );
        public static LootItemsListDescription WeaponCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Kl545M1),
            LootItemDescription.BuildItem(InventoryItemDescription.Kl545M2),
            LootItemDescription.BuildItem(InventoryItemDescription.Kl545M3),
            LootItemDescription.BuildItem(InventoryItemDescription.Jp7M1),
            LootItemDescription.BuildItem(InventoryItemDescription.Jp7M2),
            LootItemDescription.BuildItem(InventoryItemDescription.Jp7M3),
        }        );
        public static LootItemsListDescription WeaponEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Rc556M1),
            LootItemDescription.BuildItem(InventoryItemDescription.Rc556M2),
            LootItemDescription.BuildItem(InventoryItemDescription.Rc556M3),
            LootItemDescription.BuildItem(InventoryItemDescription.StarhM1),
            LootItemDescription.BuildItem(InventoryItemDescription.StarhM2),
            LootItemDescription.BuildItem(InventoryItemDescription.StarhM3),
            LootItemDescription.BuildItem(InventoryItemDescription.Pdw90M1),
            LootItemDescription.BuildItem(InventoryItemDescription.Pdw90M2),
            LootItemDescription.BuildItem(InventoryItemDescription.Pdw90M3),
            LootItemDescription.BuildItem(InventoryItemDescription.Ordr12M1),
            LootItemDescription.BuildItem(InventoryItemDescription.Ordr12M2),
            LootItemDescription.BuildItem(InventoryItemDescription.Ordr12M3),
            LootItemDescription.BuildItem(InventoryItemDescription.Lbr44M1),
            LootItemDescription.BuildItem(InventoryItemDescription.Lbr44M2),
        }        );
        public static LootItemsListDescription WeaponLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.ApScalarM1),
            LootItemDescription.BuildItem(InventoryItemDescription.ApScalarM2),
            LootItemDescription.BuildItem(InventoryItemDescription.ApScalarM3),
            LootItemDescription.BuildItem(InventoryItemDescription.SrReaperM1),
            LootItemDescription.BuildItem(InventoryItemDescription.SrReaperM2),
            LootItemDescription.BuildItem(InventoryItemDescription.SrReaperM3),
            LootItemDescription.BuildItem(InventoryItemDescription.Tag12Km1),
            LootItemDescription.BuildItem(InventoryItemDescription.Tag12Km2),
            LootItemDescription.BuildItem(InventoryItemDescription.Tag12Km3),
            LootItemDescription.BuildItem(InventoryItemDescription.Drm14M1),
            LootItemDescription.BuildItem(InventoryItemDescription.Drm14M2),
            LootItemDescription.BuildItem(InventoryItemDescription.Drm14M3),
        }        );
        public static LootItemsListDescription AntiqueStatuetteCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Antique_Statuette_3),
            LootItemDescription.BuildItem(InventoryItemDescription.Antique_Statuette_6),
        }        );
        public static LootItemsListDescription AntiqueStatuetteEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Antique_Statuette_1),
            LootItemDescription.BuildItem(InventoryItemDescription.Antique_Statuette_2),
            LootItemDescription.BuildItem(InventoryItemDescription.Antique_Statuette_5),
        }        );
        public static LootItemsListDescription AntiqueStatuetteLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Antique_Statuette_4),
        }        );
        public static LootItemsListDescription DecorativeToyCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.DecorativeToy1),
        }        );
        public static LootItemsListDescription DecorativeToyEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.DecorativeToy2),
            LootItemDescription.BuildItem(InventoryItemDescription.DecorativeToy3),
            LootItemDescription.BuildItem(InventoryItemDescription.DecorativeToy4),
        }        );
        public static LootItemsListDescription DecorativeToyLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.DecorativeToy5),
        }        );
        public static LootItemsListDescription MemoryDriveUncommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.MemoryDrive1),
        }        );
        public static LootItemsListDescription MemoryDriveEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.MemoryDrive2),
            LootItemDescription.BuildItem(InventoryItemDescription.MemoryDrive3),
        }        );
        public static LootItemsListDescription MemoryDriveLegendary { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.MemoryDrive4),
        }        );
        public static LootItemsListDescription LighterUncommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Lighter1),
        }        );
        public static LootItemsListDescription LighterEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Lighter2),
        }        );
        public static LootItemsListDescription DecorativeMedicineUncommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.InjectionFlacon1),
            LootItemDescription.BuildItem(InventoryItemDescription.InjectionFlacon2),
            LootItemDescription.BuildItem(InventoryItemDescription.Ampoule2),
        }        );
        public static LootItemsListDescription DecorativeMedicineEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.InjectionFlacon3),
            LootItemDescription.BuildItem(InventoryItemDescription.Ampoule1),
        }        );
        public static LootItemsListDescription DecorativeToolsCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Binoculars),
            LootItemDescription.BuildItem(InventoryItemDescription.Multitool),
        }        );
        public static LootItemsListDescription DecorativeToolsUncommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Binoculars),
            LootItemDescription.BuildItem(InventoryItemDescription.Multitool),
            LootItemDescription.BuildItem(InventoryItemDescription.LaserRangefinder),
        }        );
        public static LootItemsListDescription DecorativeToolsEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.ThermalImager),
        }        );
        public static LootItemsListDescription GrenadeUncommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.FragGrenadeUncommon, 1, 3),
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.GasGrenadeUncommon, 1, 3),
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.SmokeGrenadeUncommon, 1, 3),
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.FlashGrenadeUncommon, 1, 3),
        }        );
        public static LootItemsListDescription BuilderUncommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.CompoundWall0),
        }        );
        public static LootItemsListDescription BuilderRare { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.CarGarage0),
            LootItemDescription.BuildItem(InventoryItemDescription.CompoundWall1),
            LootItemDescription.BuildItem(InventoryItemDescription.StorageOutbuilding),
            LootItemDescription.BuildItem(InventoryItemDescription.Watchtower),
        }        );
        public static LootItemsListDescription BuilderEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.Home1),
            LootItemDescription.BuildItem(InventoryItemDescription.CompoundWall2),
            LootItemDescription.BuildItem(InventoryItemDescription.CarGarage1),
            LootItemDescription.BuildItem(InventoryItemDescription.CompoundWall1),
        }        );
        public static LootItemsListDescription CarsRare { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.FukurouMillennium),
            LootItemDescription.BuildItem(InventoryItemDescription.KronenE),
        }        );
        public static LootItemsListDescription CarModulesCommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.EcuCarUpgradeCommon),
            LootItemDescription.BuildItem(InventoryItemDescription.TurbochargerCarUpgradeCommon),
            LootItemDescription.BuildItem(InventoryItemDescription.EngineCoolingCarUpgradeCommon),
            LootItemDescription.BuildItem(InventoryItemDescription.SuspensionCarUpgradeCommon),
            LootItemDescription.BuildItem(InventoryItemDescription.SteeringRackCarUpgradeCommon),
        }        );
        public static LootItemsListDescription CarModulesRare { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.EcuCarUpgradeRare),
            LootItemDescription.BuildItem(InventoryItemDescription.TurbochargerCarUpgradeRare),
            LootItemDescription.BuildItem(InventoryItemDescription.EngineCoolingCarUpgradeRare),
            LootItemDescription.BuildItem(InventoryItemDescription.SuspensionCarUpgradeRare),
            LootItemDescription.BuildItem(InventoryItemDescription.SteeringRackCarUpgradeRare),
            LootItemDescription.BuildItem(InventoryItemDescription.PoliceRadioCarUpgradeCommon),
        }        );
        public static LootItemsListDescription BuilderModulesUncommon { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.CopFurnitureUpgradeEquip0),
            LootItemDescription.BuildItem(InventoryItemDescription.CopFurnitureUpgradeWeapon0),
            LootItemDescription.BuildItem(InventoryItemDescription.CopFurnitureUpgradeReward0),
            LootItemDescription.BuildItem(InventoryItemDescription.CargoWorkbenchUpgradeCircularSaw0),
            LootItemDescription.BuildItem(InventoryItemDescription.CargoWorkbenchUpgradePlasmaCutter0),
        }        );
        public static LootItemsListDescription BuilderModulesRare { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.CollectionTableUpgradeRare),
            LootItemDescription.BuildItem(InventoryItemDescription.MoneyWorkbenchUpgradeLaunderingRate0),
            LootItemDescription.BuildItem(InventoryItemDescription.UvLamp),
        }        );
        public static LootItemsListDescription BuilderModulesEpic { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.MoneyWorkbenchUpgradeSpeedup1),
            LootItemDescription.BuildItem(InventoryItemDescription.MoneyWorkbenchUpgradeLaunderingRate1),
            LootItemDescription.BuildItem(InventoryItemDescription.Lock0),
            LootItemDescription.BuildItem(InventoryItemDescription.CopFurnitureUpgradeWeapon1),
            LootItemDescription.BuildItem(InventoryItemDescription.CopFurnitureUpgradeReward1),
            LootItemDescription.BuildItem(InventoryItemDescription.CopFurnitureUpgradeEquip1),
            LootItemDescription.BuildItem(InventoryItemDescription.CopFurnitureUpgradeCar1),
            LootItemDescription.BuildItem(InventoryItemDescription.CopFurnitureUpgradeCar0),
            LootItemDescription.BuildItem(InventoryItemDescription.CollectionTableUpgradeLegendary),
            LootItemDescription.BuildItem(InventoryItemDescription.CargoWorkbenchUpgradeToolbox0),
            LootItemDescription.BuildItem(InventoryItemDescription.CargoWorkbenchUpgradeToolbox1),
            LootItemDescription.BuildItem(InventoryItemDescription.CargoWorkbenchUpgradeCircularSaw1),
            LootItemDescription.BuildItem(InventoryItemDescription.CargoWorkbenchUpgradePlasmaCutter1),
        }        );
                public static LootItemsListDescription Garbage { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItemWithStack(InventoryItemDescription.GarbageBag, 1, 5),
        }        );
        public static LootItemsListDescription GarbageSmall { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GarbageCamera),
            LootItemDescription.BuildItem(InventoryItemDescription.GarbageLighter),
        }        );
        public static LootItemsListDescription GarbageBig { get; } = new (new LootItemDescription[]
        {
            LootItemDescription.BuildItem(InventoryItemDescription.GarbageHeadphones),
            LootItemDescription.BuildItem(InventoryItemDescription.GarbageLaptop),
            LootItemDescription.BuildItem(InventoryItemDescription.GarbageToaster),
        }        );

    }
}