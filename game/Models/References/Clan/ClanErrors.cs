namespace Models.References.Clan
{
    public static class ClanErrors
    {
        public const string TagOccupied = "tag_occupied";
        public const string NameOccupied = "name_occupied";

        public const string ServiceError = "service_error";
        public const string UserBusy = "user_busy";
        public const string MembersLimit = "members_limit";
        public const string JoinError = "join_error";
        public const string ClanNotFound = "clan_not_found";
        public const string EnterTypeError = "enter_type_error";

        public const string UserNotFound = "user_not_found";
        public const string UserInClan = "user_in_clan";
        public const string UserInvitesLimit = "user_invites_limit";
        public const string UserJoinCooldown = "user_join_cooldown";
    
        public const string UserRequestsLimit = "user_requests_limit";
        public const string ClanRequestsLimit = "clan_requests_limit";
    }
}