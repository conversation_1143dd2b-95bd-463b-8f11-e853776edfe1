using System.Collections.Generic;
using Framework.Core.Identified;
using Models.References.Inventory;

namespace Models.References.SettlementBuilding
{
    public class SettlementBuildingDescription : Identified
    {
        private static readonly Dictionary<InventoryItemDescription, SettlementBuildingDescription> _buildingRequiredByInventoryItem = new();
    
        private SettlementBuildingDescription(string id, SettlementBuildingBonusDescription bonus) : base(id)
        {
            Bonus = bonus;
            if (bonus.HasTraderUnlockItems)
            {
                foreach (InventoryItemDescription unlockedItem in bonus.TraderUnlockItems)
                {
                    _buildingRequiredByInventoryItem.Add(unlockedItem, this);
                }
            }
        }

        public SettlementBuildingBonusDescription Bonus { get; }
        
        public static SettlementBuildingDescription BuildingCargoDiscount { get; } = new("cargo_discount", SettlementBuildingBonusDescription.CreateCargoDiscount(30, 300));
        public static SettlementBuildingDescription BuildingCargoDiscount_2 { get; } = new("cargo_discount_2", SettlementBuildingBonusDescription.CreateCargoDiscount(30, 750));
    
        public static SettlementBuildingDescription BuildingLaundering_1 { get; } = new("laundering_1", SettlementBuildingBonusDescription.CreateLaunderingRate(30, 100));
        public static SettlementBuildingDescription BuildingLaundering_2 { get; } = new("laundering_2", SettlementBuildingBonusDescription.CreateLaunderingRate(30, 150));
        public static SettlementBuildingDescription BuildingLaundering_3 { get; } = new("laundering_3", SettlementBuildingBonusDescription.CreateLaunderingRate(30, 250));

        public static SettlementBuildingDescription BuildingJp7Discount_1 { get; } = new("jp7_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Jp7M1, InventoryItemDescription.Jp7M2, InventoryItemDescription.Jp7M3 }, -0.1f));
        public static SettlementBuildingDescription BuildingJp7Discount_2 { get; } = new("jp7_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Jp7M1, InventoryItemDescription.Jp7M2, InventoryItemDescription.Jp7M3 }, -0.2f));
        public static SettlementBuildingDescription BuildingJp7Discount_3 { get; } = new("jp7_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Jp7M1, InventoryItemDescription.Jp7M2, InventoryItemDescription.Jp7M3 }, -0.5f));

        public static SettlementBuildingDescription BuildingKl545Discount_1 { get; } = new("kl_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Kl545M1, InventoryItemDescription.Kl545M2, InventoryItemDescription.Kl545M3 }, -0.1f));
        public static SettlementBuildingDescription BuildingKl545Discount_2 { get; } = new("kl_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Kl545M1, InventoryItemDescription.Kl545M2, InventoryItemDescription.Kl545M3 }, -0.2f));
        public static SettlementBuildingDescription BuildingKl545Discount_3 { get; } = new("kl_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Kl545M1, InventoryItemDescription.Kl545M2, InventoryItemDescription.Kl545M3 }, -0.5f));

        public static SettlementBuildingDescription BuildingRc556Discount_1 { get; } = new("rc556_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Rc556M1, InventoryItemDescription.Rc556M2, InventoryItemDescription.Rc556M3 }, -0.1f));
        public static SettlementBuildingDescription BuildingRc556Discount_2 { get; } = new("rc556_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Rc556M1, InventoryItemDescription.Rc556M2, InventoryItemDescription.Rc556M3 }, -0.2f));
        public static SettlementBuildingDescription BuildingRc556Discount_3 { get; } = new("rc556_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Rc556M1, InventoryItemDescription.Rc556M2, InventoryItemDescription.Rc556M3 }, -0.5f));

        public static SettlementBuildingDescription BuildingOrdrDiscount_1 { get; } = new("ordr_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Ordr12M1, InventoryItemDescription.Ordr12M2, InventoryItemDescription.Ordr12M3 }, -0.1f));
        public static SettlementBuildingDescription BuildingOrdrDiscount_2 { get; } = new("ordr_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Ordr12M1, InventoryItemDescription.Ordr12M2, InventoryItemDescription.Ordr12M3 }, -0.2f));
        public static SettlementBuildingDescription BuildingOrdrDiscount_3 { get; } = new("ordr_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Ordr12M1, InventoryItemDescription.Ordr12M2, InventoryItemDescription.Ordr12M3 }, -0.5f));

        public static SettlementBuildingDescription BuildingVest0Discount_1 { get; } = new("vest0_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest1Mk1 }, -0.1f));
        public static SettlementBuildingDescription BuildingVest0Discount_2 { get; } = new("vest0_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest1Mk1 }, -0.2f));
        public static SettlementBuildingDescription BuildingVest0Discount_3 { get; } = new("vest0_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest1Mk1 }, -0.5f));

        public static SettlementBuildingDescription BuildingVest1Discount_1 { get; } = new("vest1_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest2Mk1 }, -0.1f));
        public static SettlementBuildingDescription BuildingVest1Discount_2 { get; } = new("vest1_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest2Mk1 }, -0.2f));
        public static SettlementBuildingDescription BuildingVest1Discount_3 { get; } = new("vest1_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest2Mk1 }, -0.5f));

        public static SettlementBuildingDescription BuildingVest2Discount_1 { get; } = new("vest2_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest3Mk1 }, -0.1f));
        public static SettlementBuildingDescription BuildingVest2Discount_2 { get; } = new("vest2_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest3Mk1 }, -0.2f));
        public static SettlementBuildingDescription BuildingVest2Discount_3 { get; } = new("vest2_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest3Mk1 }, -0.5f));

        public static SettlementBuildingDescription BuildingVest3Discount_1 { get; } = new("vest3_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest4Mk1 }, -0.1f));
        public static SettlementBuildingDescription BuildingVest3Discount_2 { get; } = new("vest3_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest4Mk1 }, -0.2f));
        public static SettlementBuildingDescription BuildingVest3Discount_3 { get; } = new("vest3_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.Vest4Mk1 }, -0.5f));

        public static SettlementBuildingDescription BuildingSyringeDiscount_1 { get; } = new("syringe_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.SyringeUncommon, InventoryItemDescription.SyringeEpic }, -0.1f));
        public static SettlementBuildingDescription BuildingSyringeDiscount_2 { get; } = new("syringe_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.SyringeUncommon, InventoryItemDescription.SyringeEpic }, -0.2f));
        public static SettlementBuildingDescription BuildingSyringeDiscount_3 { get; } = new("syringe_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.SyringeUncommon, InventoryItemDescription.SyringeEpic }, -0.5f));
        
        public static SettlementBuildingDescription BuildingPainkillesDiscount_1 { get; } = new("painkiller_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.PainkillerUncommon, InventoryItemDescription.PainkillerEpic }, -0.1f));
        public static SettlementBuildingDescription BuildingPainkillesDiscount_2 { get; } = new("painkiller_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.PainkillerUncommon, InventoryItemDescription.PainkillerEpic }, -0.2f));
        public static SettlementBuildingDescription BuildingPainkillesDiscount_3 { get; } = new("painkiller_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.PainkillerUncommon, InventoryItemDescription.PainkillerEpic }, -0.5f));

        public static SettlementBuildingDescription BuildingAfakDiscount_1 { get; } = new("afak_discount_1", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.AfakUncommon }, -0.1f));
        public static SettlementBuildingDescription BuildingAfakDiscount_2 { get; } = new("afak_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.AfakUncommon }, -0.2f));
        public static SettlementBuildingDescription BuildingAfakDiscount_3 { get; } = new("afak_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.AfakUncommon }, -0.5f));

        public static SettlementBuildingDescription BuildingGasDiscount_2 { get; } = new("gas_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.GasGrenadeUncommon }, -0.2f));
        public static SettlementBuildingDescription BuildingGasDiscount_3 { get; } = new("gas_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.GasGrenadeUncommon }, -0.5f));

        public static SettlementBuildingDescription BuildingFragDiscount_2 { get; } = new("frag_discount_2", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.FragGrenadeUncommon }, -0.2f));
        public static SettlementBuildingDescription BuildingFragDiscount_3 { get; } = new("frag_discount_3", SettlementBuildingBonusDescription.CreateTraderItemsDiscount(30, new[] { InventoryItemDescription.FragGrenadeUncommon }, -0.5f));

        public static bool IsItemCouldBeUnlockedByBuilding(InventoryItemDescription item, out SettlementBuildingDescription settlementBuildingDescription) => _buildingRequiredByInventoryItem.TryGetValue(item, out settlementBuildingDescription);
    }
}