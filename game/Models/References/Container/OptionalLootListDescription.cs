using Models.References.Inventory;
using Models.References.Loot;
using Models.References.WeightItems;

namespace Models.References.Container
{
    public static class OptionalLootListDescription
    {
        public static SafeWeightItems<LootItemDescription> GearOptionalLoot { get; } = 
            new SafeWeightItems<LootItemDescription>(new WeightItem<LootItemDescription>[]
            {
                new(60, LootItemDescription.BuildItem(InventoryItemDescription.GrenadeLauncherShell)),
                new(15, LootItemDescription.BuildItem(InventoryItemDescription.Vest4Mk1)),
                new(15, LootItemDescription.BuildItem(InventoryItemDescription.SrReaperM3)),
                new(5, LootItemDescription.BuildItem(InventoryItemDescription.Drm14M3)),
            }            
        );
         public static SafeWeightItems<LootItemDescription> LuxOptionalLoot { get; } = 
            new SafeWeightItems<LootItemDescription>(new WeightItem<LootItemDescription>[]
            {
                new(60, LootItemDescription.BuildItem(InventoryItemDescription.AutoFlipper)),
                new(30, LootItemDescription.BuildItem(InventoryItemDescription.VinylRecord2)),
                new(5, LootItemDescription.BuildItem(InventoryItemDescription.YachtKey)),
                new(5, LootItemDescription.BuildItem(InventoryItemDescription.RomanovR8)),
            }            
        );
    }
}