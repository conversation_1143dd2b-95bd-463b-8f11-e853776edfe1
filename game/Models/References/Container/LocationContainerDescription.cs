using System;
using System.Collections.Generic;
using System.Numerics;
using Framework.Replication.EnumItem;
using Models.Physics.PhysicsBodies;
using Models.References.Inventory;
using Models.References.SecurityAlarm;

namespace Models.References.Container
{
    public class LocationContainerDescription : EnumItem
    {
        public LocationContainerDescription(string id, int index, int entityId, int inventoryEntityId, ContainerDescription containerDescription, IReadOnlyList<ContainerLootDescription> loot, Vector3 position, Quaternion orientation, Vector3 centerPoint, InventoryItemDescription requirementItem, LocationSecurityAlarmDescription securityAlarmDescription, HashSet<PhysicsBodyId> interactIgnoreStaticBodies, bool isQuestTarget, bool isEventContainer) : base(id, index)
        {
            if (isEventContainer && securityAlarmDescription != null)
                throw new Exception("Event container should not have a building target");
                
            EntityId = entityId;
            InventoryEntityId = inventoryEntityId;
            ContainerDescription = containerDescription;
            Loot = loot;
            Position = position;
            Orientation = orientation;
            CenterPoint = centerPoint;
            RequirementItem = requirementItem;
            SecurityAlarmDescription = securityAlarmDescription;
            InteractIgnoreStaticBodies = interactIgnoreStaticBodies;
            IsQuestTarget = isQuestTarget;
            IsEventContainer = isEventContainer;
        }

        public int EntityId { get; }
        public int InventoryEntityId { get; }
        public ContainerDescription ContainerDescription { get; }
        public IReadOnlyList<ContainerLootDescription> Loot { get; }
        public Vector3 Position { get; }
        public Quaternion Orientation { get; }
        public Vector3 CenterPoint { get; }
        public InventoryItemDescription RequirementItem { get; }
        public LocationSecurityAlarmDescription SecurityAlarmDescription { get; }
        public HashSet<PhysicsBodyId> InteractIgnoreStaticBodies { get; }
        public bool IsQuestTarget { get; }
        public bool IsEventContainer { get; }
    }
}