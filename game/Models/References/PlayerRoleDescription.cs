using System.Collections.Generic;
using Framework.Replication.Enum;
using Framework.Replication.EnumItem;
using Models.References.Vehicle;

namespace Models.References
{
    public class PlayerRoleDescription : EnumItem
    {
        private static readonly Enum<PlayerRoleDescription> _enum = new();
        public static IEnum<PlayerRoleDescription> Enum => _enum;

        private PlayerRoleDescription(string id, bool dropLootOnDeath, bool breakItemsOnKnockout, bool diesOnLeave, bool canTakeItemInHands, bool canLootEntities, bool canUseTrader, bool canUnlock, bool canArrestLoot, bool canRemoveDrill, float restoreHealthMax, long restoreHealthDelay, long restoreHealthPeriod, int restoreHealthAmount, bool canChangeInventory, bool canVisibleSecurityAlarmMarkers, bool isMaskByCustomizationVisible, bool takesDamageThroughArmor, bool canUseHeal, bool canUseEquippedItemsFromInventory, bool nicknameVisibleThroughWall, bool nicknameSameRoleVisibleThroughWall, bool useLootBagSkin, bool canHackSecurityPanel, bool canRepairSecurityPanel, bool canScannedMetalDetector, bool isSurrenderCommandAvailable, bool canRespawn, bool canReviveVigilantNpc, bool canRepairItems, bool canSeeCopCarsWithPoliceRadio, bool canBreakColliders, bool canTakeCargo, bool canMeleeAttack, bool useSpecialVehicle, VehicleGroupType specialVehicleGroupType,
            bool canExchangeCargo, bool canTakeCharacterLoot, bool canTakeVehicleLoot, bool removeVehicleOnDisconnect, bool spawnVehicleLootOnSuicide, long mainMenuExitDelay, IReadOnlyList<long> respawnCooldowns, long deathStreakTimerDuration, int killCumulativePoints, bool canChargeCumulativeItem) : base(id, _enum.Count)
        {
            DropLootOnDeath = dropLootOnDeath;
            BreakItemsOnKnockout = breakItemsOnKnockout;
            DiesOnLeave = diesOnLeave;
            CanTakeItemInHands = canTakeItemInHands;
            CanLootEntities = canLootEntities;
            CanUseTrader = canUseTrader;
            CanUnlock = canUnlock;
            CanArrestLoot = canArrestLoot;
            CanRemoveDrill = canRemoveDrill;
            RestoreHealthMax = restoreHealthMax;
            RestoreHealthDelay = restoreHealthDelay;
            RestoreHealthPeriod = restoreHealthPeriod;
            RestoreHealthAmount = restoreHealthAmount;
            CanChangeInventory = canChangeInventory;
            CanVisibleSecurityAlarmMarkers = canVisibleSecurityAlarmMarkers;
            IsMaskByCustomizationVisible = isMaskByCustomizationVisible;
            TakesDamageThroughArmor = takesDamageThroughArmor;
            CanUseHeal = canUseHeal;
            CanUseEquippedItemsFromInventory = canUseEquippedItemsFromInventory;
            NicknameVisibleThroughWall = nicknameVisibleThroughWall;
            NicknameSameRoleVisibleThroughWall = nicknameSameRoleVisibleThroughWall;
            UseLootBagSkin = useLootBagSkin;
            CanHackSecurityPanel = canHackSecurityPanel;
            CanRepairSecurityPanel = canRepairSecurityPanel;
            CanScannedMetalDetector = canScannedMetalDetector;
            IsSurrenderCommandAvailable = isSurrenderCommandAvailable;
            CanRespawn = canRespawn;
            CanReviveVigilantNpc = canReviveVigilantNpc;
            CanRepairItems = canRepairItems;
            CanSeeCopCarsWithPoliceRadio = canSeeCopCarsWithPoliceRadio;
            CanBreakColliders = canBreakColliders;
            CanTakeCargo = canTakeCargo;
            CanMeleeAttack = canMeleeAttack;
            UseSpecialVehicle = useSpecialVehicle;
            SpecialVehicleGroupType = specialVehicleGroupType;
            CanExchangeCargo = canExchangeCargo;
            CanTakeCharacterLoot = canTakeCharacterLoot;
            CanTakeVehicleLoot = canTakeVehicleLoot;
            RemoveVehicleOnDisconnect = removeVehicleOnDisconnect;
            SpawnVehicleLootOnSuicide = spawnVehicleLootOnSuicide;
            MainMenuExitDelay = mainMenuExitDelay;
            RespawnCooldowns = respawnCooldowns;
            DeathStreakTimerDuration = deathStreakTimerDuration;
            KillCumulativePoints = killCumulativePoints;
            CanChargeCumulativeItem = canChargeCumulativeItem;

            _enum.Add(this);
        }

        public bool DropLootOnDeath { get; }
        public bool BreakItemsOnKnockout { get; }
        public bool DiesOnLeave { get; }
        public bool CanTakeItemInHands { get; }
        public bool CanLootEntities { get; }
        public bool CanTakeCharacterLoot { get; }
        public bool CanTakeVehicleLoot { get; }
        public bool CanUseTrader { get; }
        public bool CanBreakColliders { get; }

        public bool CanUnlock { get; }

        public bool CanBecomeCop => this == Citizen;
        public bool CanBecomeCitizen => this == Cop;

        public bool IsCivilian => this == Citizen || this == Cop;
        public bool IsUnarmedCivilian => this == Citizen;

        public bool UseSpecialVehicle { get; }
        public VehicleGroupType SpecialVehicleGroupType { get; }

        public bool CanBecomeRobber => this == Citizen;

        public bool HasPoliceAttention => this == Robber;

        public bool CanArrestLoot { get; }

        public float RestoreHealthMax { get; }
        public long RestoreHealthDelay { get; }
        public long RestoreHealthPeriod { get; }
        public int RestoreHealthAmount { get; }

        public bool CanChangeInventory { get; }
        public bool CanVisibleSecurityAlarmMarkers { get; }

        public bool TakesDamageThroughArmor { get; }

        public bool CanUseHeal { get; }
        public bool CanUseEquippedItemsFromInventory { get; }

        public bool CanRespawn { get; }

        public bool IsCop => this == Cop;
        public bool IsPrisoner => this == Prisoner;
        public bool IsRobber => this == Robber;
        public bool IsSettler => this == Settler;
        public bool IsClanBattleMember => this == ClanBattleMember;

        public bool CanRefillCopAmmo => this == Cop;
        public bool NicknameVisibleThroughWall { get; }
        public bool NicknameSameRoleVisibleThroughWall { get; }

        public bool UseLootBagSkin { get; }
        public bool CanHackSecurityPanel { get; }
        public bool CanRepairSecurityPanel { get; }
        public bool CanScannedMetalDetector { get; }

        public bool CanSurrender => this == Robber;
        public bool CanArrest => this == Cop;
        public bool CanInterruptArresting => this == Robber;

        public bool IsSurrenderCommandAvailable { get; }

        public bool CanReviveVigilantNpc { get; }
        public bool CanRepairItems { get; }

        public bool CanSeeCopCarsWithPoliceRadio { get; }
        public bool CanHitSurrendered => this == Robber;
        public bool CanHitCamera => this == Robber;
        public bool CanHitNpc => this == Robber;
        public bool CanHearCopDangerLevelSound => this == Cop;
        public bool CanChangeCopEquip => this == Cop;
        public bool CanTakeCargo { get; }
        public bool CanMeleeAttack { get; }
        public bool CanExchangeCargo { get; }
        public bool CanRemoveDrill { get; }
        public bool CanCaptureBuilding => this == Robber;
        public bool IsBagVisible => this == Robber || this == Settler || this == ClanBattleMember;
        public bool IsMaskVisible => this == Robber || this == ClanBattleMember;
        public bool IsMaskByCustomizationVisible { get; }
        public bool CanUseBollard => this == Cop;
        public bool CanDistantUseKinematicObjectsControllers => this == Settler || this == Robber;
        public bool RemoveVehicleOnDisconnect { get; }
        public bool SpawnVehicleLootOnSuicide { get; }
        public bool CanJoinClanBattle => !IsRobber;
        public long MainMenuExitDelay { get; }
        public IReadOnlyList<long> RespawnCooldowns { get; }
        public long DeathStreakTimerDuration { get; }
        public int KillCumulativePoints { get; }
        public bool CanBeKnockedOut => !IsPrisoner;
        public bool CanUseMap => !IsPrisoner;
        public bool CanChargeCumulativeItem { get; }

        public static PlayerRoleDescription Settler { get; } = new("settler",
            dropLootOnDeath: true,
            breakItemsOnKnockout: true,
            diesOnLeave: false,
            canTakeItemInHands: true,
            canLootEntities: true,
            canUseTrader: true,
            canUnlock: true,
            useSpecialVehicle: false,
            specialVehicleGroupType: VehicleGroupType.Default,
            canArrestLoot: false,
            canRemoveDrill: false,
            restoreHealthMax: 0.2f,
            restoreHealthDelay: 5000,
            restoreHealthPeriod: 250,
            restoreHealthAmount: 5,
            canChangeInventory: true,
            canVisibleSecurityAlarmMarkers: false,
            isMaskByCustomizationVisible: true,
            takesDamageThroughArmor: false,
            canUseHeal: true,
            canUseEquippedItemsFromInventory: true,
            nicknameVisibleThroughWall: false,
            nicknameSameRoleVisibleThroughWall: false,
            useLootBagSkin: true,
            canHackSecurityPanel: false,
            canRepairSecurityPanel: false,
            canScannedMetalDetector: true,
            isSurrenderCommandAvailable: false,
            canRespawn: false,
            canReviveVigilantNpc: false,
            canRepairItems: true,
            canSeeCopCarsWithPoliceRadio: false,
            canBreakColliders: true,
            canTakeCargo: true,
            canMeleeAttack: true,
            canExchangeCargo: true,
            canTakeCharacterLoot: true,
            canTakeVehicleLoot: true,
            removeVehicleOnDisconnect: false,
            spawnVehicleLootOnSuicide: true,
            mainMenuExitDelay: 30 * TimeDescription.Second,
            respawnCooldowns: new [] { 0L, 10000L, 20000L, 30000L },
            deathStreakTimerDuration: 3 * TimeDescription.Minute,
            killCumulativePoints: 0,
            canChargeCumulativeItem: false
        );

        public static PlayerRoleDescription Citizen { get; } = new("citizen",
            dropLootOnDeath: false,
            breakItemsOnKnockout: false,
            diesOnLeave: false,
            canTakeItemInHands: false,
            canLootEntities: false,
            canUseTrader: true,
            canUnlock: false,
            useSpecialVehicle: false,
            specialVehicleGroupType: VehicleGroupType.Default,
            canArrestLoot: false,
            canRemoveDrill: false,
            restoreHealthMax: 1f,
            restoreHealthDelay: 5000,
            restoreHealthPeriod: 250,
            restoreHealthAmount: 5,
            canChangeInventory: true,
            canVisibleSecurityAlarmMarkers: false,
            isMaskByCustomizationVisible: false,
            takesDamageThroughArmor: true,
            canUseHeal: false,
            canUseEquippedItemsFromInventory: true,
            nicknameVisibleThroughWall: false,
            nicknameSameRoleVisibleThroughWall: false,
            useLootBagSkin: false,
            canHackSecurityPanel: false,
            canRepairSecurityPanel: false,
            canScannedMetalDetector: true,
            isSurrenderCommandAvailable: false,
            canRespawn: true,
            canReviveVigilantNpc: true,
            canRepairItems: false,
            canSeeCopCarsWithPoliceRadio: false,
            canBreakColliders: false,
            canTakeCargo: false,
            canMeleeAttack: false,
            canExchangeCargo: false,
            canTakeCharacterLoot: false,
            canTakeVehicleLoot: false,
            removeVehicleOnDisconnect: false,
            spawnVehicleLootOnSuicide: false,
            mainMenuExitDelay: 10 * TimeDescription.Second,
            respawnCooldowns: new [] { 0L },
            deathStreakTimerDuration: 3 * TimeDescription.Minute,
            killCumulativePoints: 1,
            canChargeCumulativeItem: false
        );

        public static PlayerRoleDescription Robber { get; } = new("robber",
            dropLootOnDeath: true,
            breakItemsOnKnockout: true,
            diesOnLeave: true,
            canTakeItemInHands: true,
            canLootEntities: true,
            canUseTrader: false,
            canUnlock: true,
            useSpecialVehicle: false,
            specialVehicleGroupType: VehicleGroupType.Default,
            canArrestLoot: false,
            canRemoveDrill: false,
            restoreHealthMax: 0.2f,
            restoreHealthDelay: 5000,
            restoreHealthPeriod: 250,
            restoreHealthAmount: 5,
            canChangeInventory: true,
            canVisibleSecurityAlarmMarkers: false,
            isMaskByCustomizationVisible: true,
            takesDamageThroughArmor: false,
            canUseHeal: true,
            canUseEquippedItemsFromInventory: false,
            nicknameVisibleThroughWall: false,
            nicknameSameRoleVisibleThroughWall: false,
            useLootBagSkin: true,
            canHackSecurityPanel: true,
            canRepairSecurityPanel: false,
            canScannedMetalDetector: true,
            isSurrenderCommandAvailable: false,
            canRespawn: false,
            canReviveVigilantNpc: false,
            canRepairItems: false,
            canSeeCopCarsWithPoliceRadio: true,
            canBreakColliders: true,
            canTakeCargo: true,
            canMeleeAttack: true,
            canExchangeCargo: true,
            canTakeCharacterLoot: true,
            canTakeVehicleLoot: true,
            removeVehicleOnDisconnect: true,
            spawnVehicleLootOnSuicide: true,
            mainMenuExitDelay: 0,
            respawnCooldowns: new [] { 0L },
            deathStreakTimerDuration: 3 * TimeDescription.Minute,
            killCumulativePoints: 1,
            canChargeCumulativeItem: true
        );

        public static PlayerRoleDescription Cop { get; } = new("cop",
            dropLootOnDeath: true,
            breakItemsOnKnockout: true,
            diesOnLeave: false,
            canTakeItemInHands: true,
            canLootEntities: false,
            canUseTrader: false,
            canUnlock: false,
            useSpecialVehicle: true,
            specialVehicleGroupType: VehicleGroupType.Police,
            canArrestLoot: true,
            canRemoveDrill: true,
            restoreHealthMax: 0.2f,
            restoreHealthDelay: 5000,
            restoreHealthPeriod: 250,
            restoreHealthAmount: 5,
            canChangeInventory: false,
            canVisibleSecurityAlarmMarkers: true,
            isMaskByCustomizationVisible: false,
            takesDamageThroughArmor: false,
            canUseHeal: true,
            canUseEquippedItemsFromInventory: false,
            nicknameVisibleThroughWall: false,
            nicknameSameRoleVisibleThroughWall: true,
            useLootBagSkin: false,
            canHackSecurityPanel: false,
            canRepairSecurityPanel: true,
            canScannedMetalDetector: true,
            isSurrenderCommandAvailable: true,
            canRespawn: true,
            canReviveVigilantNpc: true,
            canRepairItems: false,
            canSeeCopCarsWithPoliceRadio: true,
            canBreakColliders: false,
            canTakeCargo: false,
            canMeleeAttack: true,
            canExchangeCargo: false,
            canTakeCharacterLoot: false,
            canTakeVehicleLoot: false,
            removeVehicleOnDisconnect: true,
            spawnVehicleLootOnSuicide: false,
            mainMenuExitDelay: 10 * TimeDescription.Second,
            respawnCooldowns: new [] { 0L, 10000L, 20000L, 30000L },
            deathStreakTimerDuration: 3 * TimeDescription.Minute,
            killCumulativePoints: 1,
            canChargeCumulativeItem: false
        );

        public static PlayerRoleDescription ClanBattleMember { get; } = new("clan_battle_member",
            dropLootOnDeath: true,
            breakItemsOnKnockout: true,
            diesOnLeave: true,
            canTakeItemInHands: true,
            canLootEntities: false,
            canUseTrader: false,
            canUnlock: true,
            useSpecialVehicle: false,
            specialVehicleGroupType: VehicleGroupType.Default,
            canArrestLoot: false,
            canRemoveDrill: false,
            restoreHealthMax: 0.2f,
            restoreHealthDelay: 5000,
            restoreHealthPeriod: 250,
            restoreHealthAmount: 5,
            canChangeInventory: true,
            canVisibleSecurityAlarmMarkers: false,
            isMaskByCustomizationVisible: false,
            takesDamageThroughArmor: false,
            canUseHeal: true,
            canUseEquippedItemsFromInventory: true,
            nicknameVisibleThroughWall: false,
            nicknameSameRoleVisibleThroughWall: false,
            useLootBagSkin: true,
            canHackSecurityPanel: false,
            canRepairSecurityPanel: false,
            canScannedMetalDetector: true,
            isSurrenderCommandAvailable: false,
            canRespawn: false,
            canReviveVigilantNpc: false,
            canRepairItems: false,
            canSeeCopCarsWithPoliceRadio: false,
            canBreakColliders: true,
            canTakeCargo: true,
            canMeleeAttack: true,
            canExchangeCargo: false,
            canTakeCharacterLoot: true,
            canTakeVehicleLoot: true,
            removeVehicleOnDisconnect: true,
            spawnVehicleLootOnSuicide: true,
            mainMenuExitDelay: 0,
            respawnCooldowns: new [] { 0L },
            deathStreakTimerDuration: 3 * TimeDescription.Minute,
            killCumulativePoints: 0,
            canChargeCumulativeItem: false
        );
        
        public static PlayerRoleDescription Prisoner { get; } = new("prisoner",
            dropLootOnDeath: false,
            breakItemsOnKnockout: false,
            diesOnLeave: false,
            canTakeItemInHands: false,
            canLootEntities: false,
            canUseTrader: false,
            canUnlock: false,
            useSpecialVehicle: false,
            specialVehicleGroupType: VehicleGroupType.Default,
            canArrestLoot: false,
            canRemoveDrill: false,
            restoreHealthMax: 1f,
            restoreHealthDelay: 5000,
            restoreHealthPeriod: 250,
            restoreHealthAmount: 5,
            canChangeInventory: false,
            canVisibleSecurityAlarmMarkers: false,
            isMaskByCustomizationVisible: false,
            takesDamageThroughArmor: false,
            canUseHeal: false,
            canUseEquippedItemsFromInventory: false,
            nicknameVisibleThroughWall: false,
            nicknameSameRoleVisibleThroughWall: false,
            useLootBagSkin: false,
            canHackSecurityPanel: false,
            canRepairSecurityPanel: false,
            canScannedMetalDetector: true,
            isSurrenderCommandAvailable: false,
            canRespawn: true,
            canReviveVigilantNpc: false,
            canRepairItems: false,
            canSeeCopCarsWithPoliceRadio: false,
            canBreakColliders: false,
            canTakeCargo: false,
            canMeleeAttack: false,
            canExchangeCargo: false,
            canTakeCharacterLoot: false,
            canTakeVehicleLoot: false,
            removeVehicleOnDisconnect: false,
            spawnVehicleLootOnSuicide: false,
            mainMenuExitDelay: 0 * TimeDescription.Second,
            respawnCooldowns: new [] { 0L },
            deathStreakTimerDuration: 3 * TimeDescription.Minute,
            killCumulativePoints: 0,
            canChargeCumulativeItem: false
        );
    }
}