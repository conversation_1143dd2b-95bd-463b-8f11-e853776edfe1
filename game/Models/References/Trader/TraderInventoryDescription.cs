using System;
using System.Collections.Generic;
using System.Linq;
using Framework.Replication.Enum;
using Framework.Replication.EnumItem;
using Models.References.Inventory;

namespace Models.References.Trader
{
    public class TraderInventoryDescription : EnumItem
    {
        private static readonly Enum<TraderInventoryDescription> _enum = new();
        public static IEnum<TraderInventoryDescription> Enum => _enum;

        private TraderInventoryDescription(string id, float buyPriceModifier, float sellPriceModifier, InventoryItemDescription[] items, Dictionary<TraderInventoryItemCategoryDescription,IReadOnlyList<InventoryItemDescription>> categoryToItems, Dictionary<InventoryItemDescription, TraderInventoryItemCategoryDescription> itemToCategory, HashSet<InventoryItemDescription> lockedBySettlementBuildingItems, bool isPersonal, bool isSellAvailable, bool isSelfSettlementLocked, bool isTierBonusAvailable) : base(id, _enum.Count)
        {
            _enum.Add(this);
            
            Items = items;
            CategoryToItemsListDict = categoryToItems;
            ItemToCategoryDict = itemToCategory;

            BuyPriceModifier = buyPriceModifier;
            SellPriceModifier = sellPriceModifier;
            IsPersonal = isPersonal;
            IsSellAvailable = isSellAvailable;
            IsSelfSettlementLocked = isSelfSettlementLocked;
            IsTierBonusAvailable = isTierBonusAvailable;

            _lockedBySettlementBuildingItems = lockedBySettlementBuildingItems;
        }
        
        private static TraderInventoryDescription CreateCategoriesTraderInventory(string id, float buyPriceModifier, float sellPriceModifier, Dictionary<TraderInventoryItemCategoryDescription,IReadOnlyList<InventoryItemDescription>> categoryToItems, HashSet<InventoryItemDescription> lockedBySettlementBuildingItems, bool isPersonal, bool isSellAvailable, bool isSelfSettlementLocked, bool isTierBonusAvailable)
        {
            var items = categoryToItems.Values.SelectMany(array => array).ToArray();

            if (items.Length > TraderDescription.MaxItemsInTraderInventory)
                throw new ArgumentException($"Trader inventory items count must be less then {TraderDescription.MaxItemsInTraderInventory}. Or you should to increase {nameof(TraderDescription)}.{nameof(TraderDescription.MaxItemsInTraderInventory)} value");
           
            var itemsToCategory = new Dictionary<InventoryItemDescription, TraderInventoryItemCategoryDescription>();
            foreach (var keyValue in categoryToItems)
            {
                foreach (var item in keyValue.Value)
                {
                    if(!itemsToCategory.TryAdd(item, keyValue.Key))
                        throw new ArgumentException($"Trader inventory {id} already contains a item {item.Id} in tier {keyValue.Key.Id}.");
                }
            }
            return new TraderInventoryDescription(id,buyPriceModifier,sellPriceModifier,items,categoryToItems,itemsToCategory,lockedBySettlementBuildingItems,isPersonal,isSellAvailable,isSelfSettlementLocked, isTierBonusAvailable);
        }
        
        private static TraderInventoryDescription CreateItemsTraderInventory(string id, float buyPriceModifier, float sellPriceModifier, InventoryItemDescription[] items, HashSet<InventoryItemDescription> lockedBySettlementBuildingItems, bool isPersonal, bool isSellAvailable, bool isSelfSettlementLocked, bool isTierBonusAvailable)
        {
            if (items.Length > TraderDescription.MaxItemsInTraderInventory)
                throw new ArgumentException($"Trader inventory items count must be less then {TraderDescription.MaxItemsInTraderInventory}. Or you should to increase {nameof(TraderDescription)}.{nameof(TraderDescription.MaxItemsInTraderInventory)} value");
            
            return new TraderInventoryDescription(id,buyPriceModifier,sellPriceModifier,items,null,null,lockedBySettlementBuildingItems,isPersonal,isSellAvailable,isSelfSettlementLocked, isTierBonusAvailable);
        }

        public IReadOnlyList<InventoryItemDescription> Items { get; }
        public readonly Dictionary<InventoryItemDescription, TraderInventoryItemCategoryDescription> ItemToCategoryDict;
        public IReadOnlyDictionary<TraderInventoryItemCategoryDescription, IReadOnlyList<InventoryItemDescription>> CategoryToItemsListDict { get; }
        
        public float BuyPriceModifier { get; }
        public float SellPriceModifier { get; }
        public bool IsPersonal { get; }
        public bool IsSellAvailable { get; }
        public bool IsSelfSettlementLocked { get; }
        public bool IsTierBonusAvailable { get; }
        public bool IsCategoryTrader => ItemToCategoryDict != null && CategoryToItemsListDict != null;
    
        private readonly HashSet<InventoryItemDescription> _lockedBySettlementBuildingItems;


        public bool CheckItemLockedBySettlementBuilding(InventoryItemDescription item) => 
            _lockedBySettlementBuildingItems != null &&
            _lockedBySettlementBuildingItems.Contains(item);

        public static TraderInventoryDescription Laptop { get; } = CreateCategoriesTraderInventory(
            id: "laptop",
            buyPriceModifier: 1.2f,
            sellPriceModifier: 0.4f,
            new Dictionary<TraderInventoryItemCategoryDescription, IReadOnlyList<InventoryItemDescription>>()
            {
                {   TraderInventoryItemCategoryDescription.SettlementTier1, new InventoryItemDescription[]
                    {
                        InventoryItemDescription.BackpackRare,
                        InventoryItemDescription.Vest2Mk1,
                        InventoryItemDescription.BeltRare,
                        InventoryItemDescription.Vest1Mk1,
                        InventoryItemDescription.BeltUncommon,
                        
                        InventoryItemDescription.PistolAmmo,
                        InventoryItemDescription.ShotgunAmmo,
                        InventoryItemDescription.MpAmmo,
                        InventoryItemDescription.RifleAmmo,
                        InventoryItemDescription.AfakUncommon,
                        InventoryItemDescription.PainkillerUncommon,
                        InventoryItemDescription.ArmorPlateUncommon,
                        InventoryItemDescription.Lockpick1,
                        InventoryItemDescription.Drill,
                        
                        InventoryItemDescription.Jp7M1,
                        InventoryItemDescription.Kl545M1,
                        InventoryItemDescription.Dp9M2,
                        InventoryItemDescription.Lbr44M1,
                        InventoryItemDescription.Ordr12M1,

                        
                        InventoryItemDescription.FukurouMillennium,
                        InventoryItemDescription.LonghornVan,
                        InventoryItemDescription.KronenE,
                        
                        InventoryItemDescription.EngineCoolingCarUpgradeCommon,
                        InventoryItemDescription.TurbochargerCarUpgradeCommon,
                        InventoryItemDescription.EcuCarUpgradeCommon,
                        
                        InventoryItemDescription.Home3,
                        InventoryItemDescription.Home1,
                        InventoryItemDescription.CompoundWallWood,
                        InventoryItemDescription.CompoundWall0,
                        InventoryItemDescription.Watchtower,
                        InventoryItemDescription.CarGarage0,
                        
                        InventoryItemDescription.CopFurniture,
                        InventoryItemDescription.CollectionTable,
                        InventoryItemDescription.CargoWorkbench,
                        InventoryItemDescription.StorageSmall,
                        InventoryItemDescription.ShelvingSmall,
                        
                        InventoryItemDescription.MoneyWorkbenchUpgradeSpeedup0,
                        InventoryItemDescription.MoneyWorkbenchUpgradeLaunderingRate0,
                        InventoryItemDescription.CargoWorkbenchUpgradeAdditionalSlot,
                        InventoryItemDescription.CopFurnitureUpgradeWeapon0,
                        InventoryItemDescription.CopFurnitureUpgradeReward0,
                        InventoryItemDescription.CopFurnitureUpgradeEquip0,
                        
                        InventoryItemDescription.ActionCameraCumulative1,
                    }
                },
                {   TraderInventoryItemCategoryDescription.SettlementTier2, new InventoryItemDescription[]
                    {
                        InventoryItemDescription.BackpackEpic,
                        InventoryItemDescription.Vest3Mk1,
                        InventoryItemDescription.BeltEpic,
                        
                        InventoryItemDescription.SmokeGrenadeUncommon,
                        InventoryItemDescription.FlashGrenadeUncommon,
                        InventoryItemDescription.SyringeUncommon,
                        InventoryItemDescription.PainkillerEpic,

                        InventoryItemDescription.Jp7M2,
                        InventoryItemDescription.Kl545M2,
                        InventoryItemDescription.StarhM1,
                        InventoryItemDescription.Rc556M1,
                        InventoryItemDescription.Tag12Km1,
                        InventoryItemDescription.ApScalarM1,
                        
                        InventoryItemDescription.LonghornTitan,
                        InventoryItemDescription.KronenJ,
                        
                        InventoryItemDescription.SteeringRackCarUpgradeCommon,
                        InventoryItemDescription.SuspensionCarUpgradeCommon,
                        InventoryItemDescription.EngineCoolingCarUpgradeRare,
                        InventoryItemDescription.TurbochargerCarUpgradeRare,
                        InventoryItemDescription.TrunkInventoryCarUpgradeCommon,
                        InventoryItemDescription.TrunkCargoInventoryCarUpgradeCommon,
                        InventoryItemDescription.EcuCarUpgradeRare,
                        
                        InventoryItemDescription.CompoundWall1,
                        InventoryItemDescription.StorageOutbuilding,
                        InventoryItemDescription.HelicopterGarage,
                        
                        InventoryItemDescription.StorageMedium,
                        InventoryItemDescription.ShelvingMedium,
                        InventoryItemDescription.MoneyWorkbenchUpgradeLaunderingRate1,
                        InventoryItemDescription.CollectionTableUpgradeRare,
                        InventoryItemDescription.CargoWorkbenchUpgradeToolbox0,
                        InventoryItemDescription.CargoWorkbenchUpgradeCircularSaw0,
                        InventoryItemDescription.CargoWorkbenchUpgradePlasmaCutter0,
                        InventoryItemDescription.CopFurnitureUpgradeWeapon1,
                        InventoryItemDescription.CopFurnitureUpgradeCar0,
                        InventoryItemDescription.CopFurnitureUpgradeReward1,
                        InventoryItemDescription.CopFurnitureUpgradeEquip1,
                        
                        InventoryItemDescription.ActionCameraCumulative2,
                        InventoryItemDescription.LabFlaskCumulative2,
                        InventoryItemDescription.UsbRubberDuckyCumulative,
                        InventoryItemDescription.CargoManifestCumulative2,
                    }
                },
                {   TraderInventoryItemCategoryDescription.SettlementTier3, new InventoryItemDescription[]
                    {
                        InventoryItemDescription.BackpackLegend,
                        InventoryItemDescription.Vest4Mk1,
                        InventoryItemDescription.BeltLegend,
                        
                        InventoryItemDescription.Ordr12M2,
                        InventoryItemDescription.StarhM2,
                        InventoryItemDescription.Rc556M2,
                        InventoryItemDescription.Tag12Km2,
                        InventoryItemDescription.SrReaperM1,
                        InventoryItemDescription.ApScalarM2,
                        InventoryItemDescription.Drm14M1,
                        
                        InventoryItemDescription.FragGrenadeUncommon,
                        InventoryItemDescription.GasGrenadeUncommon,
                        InventoryItemDescription.SyringeEpic,

                        InventoryItemDescription.LonghornWild,
                        InventoryItemDescription.BulettiThunderX,
                        InventoryItemDescription.RomanovR8,
                        
                        InventoryItemDescription.SteeringRackCarUpgradeRare,
                        InventoryItemDescription.SuspensionCarUpgradeRare,
                        InventoryItemDescription.TrunkInventoryCarUpgradeRare,
                        InventoryItemDescription.TrunkCargoInventoryCarUpgradeRare,
                        InventoryItemDescription.PoliceRadioCarUpgradeCommon,
                        
                        InventoryItemDescription.Home2,
                        InventoryItemDescription.CompoundWall2,
                        InventoryItemDescription.CarGarage1,
                        
                        InventoryItemDescription.StorageBig,
                        InventoryItemDescription.ShelvingBig,
                        InventoryItemDescription.MoneyWorkbenchUpgradeLaunderingRate2,
                        InventoryItemDescription.CollectionTableUpgradeLegendary,
                        InventoryItemDescription.CargoWorkbenchUpgradeToolbox1,
                        InventoryItemDescription.CargoWorkbenchUpgradeCircularSaw1,
                        InventoryItemDescription.CargoWorkbenchUpgradePlasmaCutter1,
                        InventoryItemDescription.CopFurnitureUpgradeWeapon2,
                        InventoryItemDescription.CopFurnitureUpgradeCar1,
                        InventoryItemDescription.CopFurnitureUpgradeReward2,
                        InventoryItemDescription.CopFurnitureUpgradeEquip2,
                        
                        InventoryItemDescription.ActionCameraCumulative3,
                        InventoryItemDescription.LabFlaskCumulative3,
                        InventoryItemDescription.DepositBoxKeyCumulative,
                        InventoryItemDescription.CargoManifestCumulative3,
                        InventoryItemDescription.TabletCumulative,
                    }
                }
            },
            lockedBySettlementBuildingItems: null,
            isPersonal: true,
            isSellAvailable: true,
            isSelfSettlementLocked: false,
            isTierBonusAvailable: false);

        public static TraderInventoryDescription SettlerTrader { get; } = CreateItemsTraderInventory(
            id: "settler_trader",
            buyPriceModifier: 1.2f,
            sellPriceModifier: 0.55f,
            items: new InventoryItemDescription[]
            {
            }            ,
            lockedBySettlementBuildingItems: null,
            isPersonal: false,
            isSellAvailable: true,
            isSelfSettlementLocked: true,
            isTierBonusAvailable: true);

        public static TraderInventoryDescription SettlerTrader2 { get; } = CreateItemsTraderInventory(
            id: "settler_trader_2",
            buyPriceModifier: 1.2f,
            sellPriceModifier: 0.7f,
            items: new InventoryItemDescription[]
            {
            }            ,
            lockedBySettlementBuildingItems: null,
            isPersonal: false,
            isSellAvailable: true,
            isSelfSettlementLocked: false,
            isTierBonusAvailable: false);
            
        public static TraderInventoryDescription SettlerTrader3 { get; } = CreateItemsTraderInventory(
            id: "settler_trader_3",
            buyPriceModifier: 1.2f,
            sellPriceModifier: 0.85f,
            items: new InventoryItemDescription[]
            {
            }            ,
            lockedBySettlementBuildingItems: null,
            isPersonal: false,
            isSellAvailable: true,
            isSelfSettlementLocked: false,
            isTierBonusAvailable: false);

        public static TraderInventoryDescription CityTraderWeapon { get; } = CreateItemsTraderInventory(
            id: "city_trader_weapon",
            buyPriceModifier: 1.6f,
            sellPriceModifier: 0.4f,
            items: new InventoryItemDescription[]
            {
                InventoryItemDescription.PistolAmmo,
                InventoryItemDescription.ShotgunAmmo,
                InventoryItemDescription.MpAmmo,
                InventoryItemDescription.RifleAmmo,
                InventoryItemDescription.Jp7M2,
                InventoryItemDescription.Pdw90M1,
                InventoryItemDescription.Kl545M2,
            },
            lockedBySettlementBuildingItems: null,
            isPersonal: false,
            isSellAvailable: false,
            isSelfSettlementLocked: false,
            isTierBonusAvailable: false);

        public static TraderInventoryDescription CityTraderArmor { get; } = CreateItemsTraderInventory(
            id: "city_trader_armor",
            buyPriceModifier: 1.6f,
            sellPriceModifier: 0.4f,
            items: new InventoryItemDescription[]
            {
                InventoryItemDescription.BackpackUncommon,
                InventoryItemDescription.Vest1Mk1,
                InventoryItemDescription.BeltUncommon,
                InventoryItemDescription.ArmorPlateUncommon,
            }            ,
            lockedBySettlementBuildingItems: null,
            isPersonal: false,
            isSellAvailable: false,
            isSelfSettlementLocked: false,
            isTierBonusAvailable: false);

        public static TraderInventoryDescription CityTraderElectronics { get; } = CreateItemsTraderInventory(
            id: "city_trader_electronics",
            buyPriceModifier: 1.6f,
            sellPriceModifier: 0.4f,
            items: new InventoryItemDescription[]
            {
                InventoryItemDescription.Laptop1,
                InventoryItemDescription.GameConsole0,
                InventoryItemDescription.Phone1,
                InventoryItemDescription.Headphones1,
                InventoryItemDescription.Camera1,
                InventoryItemDescription.Phone2,
                InventoryItemDescription.GraphicsCard1,
                InventoryItemDescription.Phone4,
            }            ,
            lockedBySettlementBuildingItems: null,
            isPersonal: false,
            isSellAvailable: false,
            isSelfSettlementLocked: false,
            isTierBonusAvailable: false);

        public static TraderInventoryDescription CityTraderJewelry { get; } = CreateItemsTraderInventory(
            id: "city_trader_jewelry",
            buyPriceModifier: 1.6f,
            sellPriceModifier: 0.4f,
            items: new InventoryItemDescription[]
            {
                InventoryItemDescription.SilverEarrings,
                InventoryItemDescription.Earrings,
                InventoryItemDescription.GoldenRing2,
                InventoryItemDescription.GoldenEarrings2,
                InventoryItemDescription.SilverRing,
                InventoryItemDescription.GoldenRing,
                InventoryItemDescription.Watch3,
                InventoryItemDescription.Watch4,
                InventoryItemDescription.Necklace1,
            }            ,
            lockedBySettlementBuildingItems: null,
            isPersonal: false,
            isSellAvailable: false,
            isSelfSettlementLocked: false,
            isTierBonusAvailable: false);

        public static TraderInventoryDescription CityTraderAlcohol { get; } = CreateItemsTraderInventory(
            id: "city_trader_alcohol",
            buyPriceModifier: 1.6f,
            sellPriceModifier: 0.4f,
            items: new InventoryItemDescription[]
            {
                InventoryItemDescription.Alcohol0,
                InventoryItemDescription.Alcohol1,
                InventoryItemDescription.Alcohol3,
            }            ,
            lockedBySettlementBuildingItems: null,
            isPersonal: false,
            isSellAvailable: false,
            isSelfSettlementLocked: false,
            isTierBonusAvailable: false);

        public static TraderInventoryDescription CityTraderComicsAlcohol { get; } = CreateItemsTraderInventory(
            id: "city_trader_comics_alcohol",
            buyPriceModifier: 1.6f,
            sellPriceModifier: 0.4f,
            items: new InventoryItemDescription[]
            {
                InventoryItemDescription.Alcohol4,
                InventoryItemDescription.Alcohol2,
                InventoryItemDescription.Comics1,
            }            ,
            lockedBySettlementBuildingItems: null,
            isPersonal: false,
            isSellAvailable: false,
            isSelfSettlementLocked: false,
            isTierBonusAvailable: false);

        public static TraderInventoryDescription CityTraderMedicine { get; } = CreateItemsTraderInventory(
            id: "city_trader_medicine",
            buyPriceModifier: 1.6f,
            sellPriceModifier: 0.4f,
            items: new InventoryItemDescription[]
            {
                InventoryItemDescription.PainkillerUncommon,
                InventoryItemDescription.InjectionFlacon1,
                InventoryItemDescription.Ampoule1,
            }            ,
            lockedBySettlementBuildingItems: null,
            isPersonal: false,
            isSellAvailable: false,
            isSelfSettlementLocked: false,
            isTierBonusAvailable: false);

    }
}