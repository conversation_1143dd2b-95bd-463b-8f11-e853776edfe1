using System;

namespace Models.References.Inventory
{
    public class ArmorDescription
    {
        public const int SegmentAmount = 40;

        private ArmorDescription(int segmentsCount, float blockingRate)
        {
            MaxArmorAmount = segmentsCount * SegmentAmount;
            SegmentsCount = segmentsCount;
            BlockingRate = blockingRate;
        }

        public int MaxArmorAmount { get; }
        public int SegmentsCount { get; }
        public float BlockingRate { get; }

        public static int CalculateRestoredAmount(int segmentsCount, int currentAmount, int maxSegmentsCount)
        {
            var newAmount = currentAmount + SegmentAmount * segmentsCount;
            var maxAmount = SegmentAmount * maxSegmentsCount;
            return Math.Min(newAmount, maxAmount);
        }

        public static ArmorDescription Armor1Mk1 { get; } = new ArmorDescription
        (
            segmentsCount: 1,
            blockingRate: 0f
        );

        public static ArmorDescription Armor1Mk2 { get; } = new ArmorDescription
       (
           segmentsCount: 1,
           blockingRate: 0.1f
       );

        public static ArmorDescription Armor2Mk1 { get; } = new ArmorDescription
        (
            segmentsCount: 2,
            blockingRate: 0f
        );

        public static ArmorDescription Armor2Mk2 { get; } = new ArmorDescription
        (
            segmentsCount: 2,
            blockingRate: 0.1f
        );

        public static ArmorDescription Armor2Mk3 { get; } = new ArmorDescription
        (
            segmentsCount: 2,
            blockingRate: 0.25f
        );

        public static ArmorDescription Armor3Mk1 { get; } = new ArmorDescription
        (
            segmentsCount: 3,
            blockingRate: 0f
        );

        public static ArmorDescription Armor3Mk2 { get; } = new ArmorDescription
        (
            segmentsCount: 3,
            blockingRate: 0.1f
        );

        public static ArmorDescription Armor3Mk3 { get; } = new ArmorDescription
        (
            segmentsCount: 3,
            blockingRate: 0.25f
        );

        public static ArmorDescription Armor4Mk1 { get; } = new ArmorDescription
        (
            segmentsCount: 4,
            blockingRate: 0f
        );

        public static ArmorDescription Armor4Mk2 { get; } = new ArmorDescription
        (
            segmentsCount: 4,
            blockingRate: 0.1f
        );

        public static ArmorDescription Armor4Mk3 { get; } = new ArmorDescription
        (
            segmentsCount: 4,
            blockingRate: 0.25f
        );
    }
}