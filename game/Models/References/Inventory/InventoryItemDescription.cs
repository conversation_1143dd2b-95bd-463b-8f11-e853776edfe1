using System;
using Framework.Replication.Enum;
using Framework.Replication.EnumItem;
using Models.Models.CarUpgradesHolderModel;
using Models.References.Builder;
using Models.References.Builder.Breach;
using Models.References.Car;
using Models.References.Car.Upgrades;
using Models.References.CargoWorkbench;
using Models.References.Collection;
using Models.References.CopFurniture;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.Helicopter;
using Models.References.MoneyWorkbench;
using Models.References.PlotBuildingDefenseBlock;
using Models.References.Weapon;

namespace Models.References.Inventory
{
    public enum RarityItem
    {
        NoRare = 0,
        Common = 1,
        Uncommon = 2,
        Rare = 3,
        Epic = 4,
        Legendary = 5,
        Exotic = 6
    }

    public class InventoryItemDescription : EnumItem
    {
        private static readonly Enum<InventoryItemDescription> _enum = new Enum<InventoryItemDescription>();
        public static IEnum<InventoryItemDescription> Enum => _enum;

        private readonly Func<CollectionTableDescription> _collectionTableFunc;

        private static InventoryItemDescription CreateMainBuilding(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            MainBuildingDescription mainBuildingDescription) => new(id,
            type: InventoryItemTypeDescription.MainBuilding,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            mainBuildingDescription: mainBuildingDescription
        );

        private static InventoryItemDescription CreateCompoundWall(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            CompoundWallDescription compoundWallDescription) => new(id,
            type: InventoryItemTypeDescription.CompoundWall,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            compoundWallDescription: compoundWallDescription
        );

        private static InventoryItemDescription CreateOutbuilding(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            OutbuildingDescription outbuildingDescription) => new(id,
            type: InventoryItemTypeDescription.Outbuilding,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            outbuildingDescription: outbuildingDescription
        );

        private static InventoryItemDescription CreateCopFurniture(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            CopFurnitureDescription copFurnitureDescription) => new(id,
            type: InventoryItemTypeDescription.Furniture,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            copFurnitureDescription: copFurnitureDescription
        );

        private static InventoryItemDescription CreateCollectionTable(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            Func<CollectionTableDescription> collectionTableFunc) => new(id,
            type: InventoryItemTypeDescription.Furniture,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            collectionTableFunc: collectionTableFunc
        );

        private static InventoryItemDescription CreateCargoWorkbench(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            CargoWorkbenchDescription cargoWorkbenchDescription) => new(id,
            type: InventoryItemTypeDescription.Furniture,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            cargoWorkbenchDescription: cargoWorkbenchDescription
        );

        private static InventoryItemDescription CreateStorage(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            StorageDescription storageDescription) => new(
            id,
            type: InventoryItemTypeDescription.Furniture,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            storageDescription: storageDescription
        );

        private static InventoryItemDescription CreateShelving(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            ShelvingDescription shelvingDescription) =>
            new(id,
                type: InventoryItemTypeDescription.Furniture,
                cellsSize: cellsSize,
                stackSize: stackSize,
                rarity: (RarityItem)rarity,
                basePrice: basePrice,
                sellPriceModifier: sellPriceModifier,
                shelvingDescription: shelvingDescription
            );

        private static InventoryItemDescription CreateCargoWorkbenchUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int rarity, int stackSize, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            CargoWorkbenchUpgradeDescription cargoWorkbenchUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CargoWorkbenchUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            cargoWorkbenchUpgradeDescription: cargoWorkbenchUpgradeDescription
        );

        private static InventoryItemDescription CreateCollectionTableUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            CollectionTableUpgradeSlotDescription collectionTableUpgradeSlotDescription) => new(id,
            type: InventoryItemTypeDescription.CollectionTableUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            collectionTableUpgradeSlotDescription: collectionTableUpgradeSlotDescription
        );

        private static InventoryItemDescription CreateCopFurnitureUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            CopFurnitureUpgradeDescription copFurnitureUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CopFurnitureUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            copFurnitureUpgradeDescription: copFurnitureUpgradeDescription
        );

        private static InventoryItemDescription CreateMoneyWorkbenchUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            MoneyWorkbenchUpgradeDescription moneyWorkbenchUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.MoneyWorkbenchUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            moneyWorkbenchUpgradeDescription: moneyWorkbenchUpgradeDescription
        );

        private static InventoryItemDescription CreateBreachProtectionItem(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            BreachProtectionItemDescription breachProtectionItemDescription) => new(id,
            type: InventoryItemTypeDescription.Breach,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            breachProtectionItemDescription: breachProtectionItemDescription
        );

        private static InventoryItemDescription CreateCashItem(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, CashDescription cashDescription) => new(id,
            type: InventoryItemTypeDescription.Cash,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            isSellAvailable: false,
            cashDescription: cashDescription);

        private static InventoryItemDescription CreateTokenItem(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice) => CreateCashItem(id,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: rarity,
            basePrice: basePrice,
            cashDescription: CashDescription.Common
        );

        private static InventoryItemDescription CreateAlarmCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, AlarmCarUpgradeDescription alarmCarUpgradeDescription) => new(
            id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            alarmCarUpgradeDescription: alarmCarUpgradeDescription
        );

        private static InventoryItemDescription CreatePoliceRadioCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice,
            PoliceRadioCarUpgradeDescription policeRadioCarUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            policeRadioCarUpgradeDescription: policeRadioCarUpgradeDescription
        );

        private static InventoryItemDescription CreateHornCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, HornCarUpgradeDescription hornCarUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            hornCarUpgradeDescription: hornCarUpgradeDescription
        );

        private static InventoryItemDescription CreateEcuCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, EcuCarUpgradeDescription ecuCarUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            ecuCarUpgradeDescription: ecuCarUpgradeDescription
        );

        private static InventoryItemDescription CreateTurbochargerCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice,
            TurbochargerCarUpgradeDescription turbochargerCarUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            turbochargerCarUpgradeDescription: turbochargerCarUpgradeDescription
        );

        private static InventoryItemDescription CreateEngineCoolingCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice,
            EngineCoolingCarUpgradeDescription engineCoolingCarUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            engineCoolingCarUpgradeDescription: engineCoolingCarUpgradeDescription
        );

        private static InventoryItemDescription CreateNitroCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, NitroCarUpgradeDescription nitroCarUpgradeDescription) => new(
            id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            nitroCarUpgradeDescription: nitroCarUpgradeDescription
        );

        private static InventoryItemDescription CreateSuspensionCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice,
            SuspensionCarUpgradeDescription suspensionCarUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            suspensionCarUpgradeDescription: suspensionCarUpgradeDescription
        );

        private static InventoryItemDescription CreateSteeringRackCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice,
            SteeringRackCarUpgradeDescription steeringRackCarUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            steeringRackCarUpgradeDescription: steeringRackCarUpgradeDescription
        );

        private static InventoryItemDescription CreateTrunkInventoryCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice,
            TrunkInventoryCarUpgradeDescription trunkInventoryCarUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            trunkInventoryCarUpgradeDescription: trunkInventoryCarUpgradeDescription
        );

        private static InventoryItemDescription CreateTrunkCargoInventoryCarUpgrade(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice,
            TrunkCargoInventoryCarUpgradeDescription trunkCargoInventoryCarUpgradeDescription) => new(id,
            type: InventoryItemTypeDescription.CarUpgrade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            trunkCargoInventoryCarUpgradeDescription: trunkCargoInventoryCarUpgradeDescription
        );

        private static InventoryItemDescription CreateSmgWeapon(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, WeaponDescription weapon, int basePrice,
            InventoryItemSellPriceModifier sellPriceModifier, int durability,
            int repairPrice) => new(
            id: id,
            type: InventoryItemTypeDescription.Smg,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            durability: durability,
            repairPrice: repairPrice,
            weapon: weapon);

        private static InventoryItemDescription CreateBackpack(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            int inventoryLines) => new(
            id: id,
            type: InventoryItemTypeDescription.Backpack,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            inventoryLines: inventoryLines);

        private static InventoryItemDescription CreateArmor(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, ArmorDescription armor,
            int basePrice, InventoryItemSellPriceModifier sellPriceModifier, int durability, int repairPrice) => new(
            id: id,
            type: InventoryItemTypeDescription.Armor,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            durability: durability,
            repairPrice: repairPrice,
            armor: armor);

        private static InventoryItemDescription CreateBelt(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier, int pocketCount) =>
            new(
                id: id,
                type: InventoryItemTypeDescription.Belt,
                cellsSize: cellsSize,
                stackSize: stackSize,
                rarity: (RarityItem)rarity,
                basePrice: basePrice,
                sellPriceModifier: sellPriceModifier,
                pocketCount: pocketCount);

        private static InventoryItemDescription CreateMedicine(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            UsableItemDescription usableItem) => new(
            id: id,
            type: InventoryItemTypeDescription.Medicine,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            usableItem: usableItem);

        private static InventoryItemDescription CreateAmmo(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, AmmoDescription ammo) => new(
            id: id,
            type: InventoryItemTypeDescription.Ammo,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: null,
            ammo: ammo);

        private static InventoryItemDescription CreateArmorPlate(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier,
            UsableItemDescription usableItem) => new(
            id: id,
            type: InventoryItemTypeDescription.ArmorPlate,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            usableItem: usableItem);

        private static InventoryItemDescription CreateHandgun(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, WeaponDescription weapon, int basePrice,
            InventoryItemSellPriceModifier sellPriceModifier, int durability,
            int repairPrice) => new(
            id: id,
            type: InventoryItemTypeDescription.Handgun,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            durability: durability,
            repairPrice: repairPrice,
            weapon: weapon);

        private static InventoryItemDescription CreateAssaultRifle(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, WeaponDescription weapon,
            int basePrice, InventoryItemSellPriceModifier sellPriceModifier, int durability, int repairPrice) => new(
            id: id,
            type: InventoryItemTypeDescription.AssaultRifle,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            durability: durability,
            repairPrice: repairPrice,
            weapon: weapon);

        private static InventoryItemDescription CreateKeyWarehouse(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice,
            bool isKey) => new(
            id: id,
            type: InventoryItemTypeDescription.KeyWarehouse,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: null,
            isKey: isKey);

        private static InventoryItemDescription CreateKeyWarehouse(string id, InventoryItemCellsSizeDescription cellsSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier, int stackSize) => new(
            id: id,
            type: InventoryItemTypeDescription.KeyWarehouse,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier
        );

        private static InventoryItemDescription CreatePoliceRadio(string id, InventoryItemCellsSizeDescription cellsSize, int rarity, int basePrice,
            InventoryItemSellPriceModifier sellPriceModifier, int stackSize) => new(
            id: id,
            type: InventoryItemTypeDescription.PoliceRadio,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier
        );

        private static InventoryItemDescription CreateLockpick(string id, InventoryItemCellsSizeDescription cellsSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier, int stackSize) => new(
            id: id,
            type: InventoryItemTypeDescription.Lockpick,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier
        );

        private static InventoryItemDescription CreateSell(string id, InventoryItemCellsSizeDescription cellsSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier, int stackSize) => new(
            id: id,
            type: InventoryItemTypeDescription.Sell,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier
        );

        private static InventoryItemDescription CreateShotgun(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, WeaponDescription weapon, int basePrice,
            InventoryItemSellPriceModifier sellPriceModifier, int durability,
            int repairPrice) => new(
            id: id,
            type: InventoryItemTypeDescription.Shotgun,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            durability: durability,
            repairPrice: repairPrice,
            weapon: weapon);

        private static InventoryItemDescription CreateKeyContainer(string id, InventoryItemCellsSizeDescription cellsSize, int rarity, int basePrice, InventoryItemSellPriceModifier sellPriceModifier, int stackSize) => new(
            id: id,
            type: InventoryItemTypeDescription.KeyContainer,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier
        );

        private static InventoryItemDescription CreateGrenade(string id, InventoryItemCellsSizeDescription cellsSize, int rarity, int basePrice,
            ThrowableItemDescription throwableItem, InventoryItemSellPriceModifier sellPriceModifier, int stackSize) => new(
            id: id,
            type: InventoryItemTypeDescription.Grenade,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            throwableItem: throwableItem);

        private static InventoryItemDescription CreateCar(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, CarSlotDescription carSlotDescription,
            InventoryItemSellPriceModifier sellPriceModifier) => new(
            id: id,
            type: InventoryItemTypeDescription.Car,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            car: carSlotDescription);

        private static InventoryItemDescription CreateHelicopter(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, HelicopterSlotDescription helicopterSlotDescription,
            InventoryItemSellPriceModifier sellPriceModifier) => new(
            id: id,
            type: InventoryItemTypeDescription.Helicopter,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: sellPriceModifier,
            helicopter: helicopterSlotDescription);

        private static InventoryItemDescription CreateCumulativeItem(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, CumulativeItemDescription cumulativeItemDescription) => new(id,
            type: InventoryItemTypeDescription.Cumulative,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            cumulativeItemDescription: cumulativeItemDescription);

        private static InventoryItemDescription CreatePlotBuildingDefenseModule(string id, InventoryItemCellsSizeDescription cellsSize, int stackSize, int rarity, int basePrice, PlotBuildingDefenseModuleDescription plotBuildingDefenseModuleDescription) => new(id,
            type: InventoryItemTypeDescription.PlotBuildingDefenseModule,
            cellsSize: cellsSize,
            stackSize: stackSize,
            rarity: (RarityItem)rarity,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            plotBuildingDefenseModuleDescription: plotBuildingDefenseModuleDescription
        );

        private static InventoryItemDescription CreateDecoder(string id, int stackSize, int basePrice, RarityItem rarity, PlotBuildingDecoderDescription decoderDescription) => new(
            id,
            type: InventoryItemTypeDescription.PlotBuildingDecoder,
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: stackSize,
            basePrice: basePrice,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            rarity: rarity,
            decoderDescription: decoderDescription
        );

        private InventoryItemDescription(string id, InventoryItemTypeDescription type, InventoryItemCellsSizeDescription cellsSize, int basePrice, InventoryItemSellPriceModifier sellPriceModifier, int stackSize,
            RarityItem rarity, bool isSellAvailable = true, int inventoryLines = 0, int pocketCount = 0, bool isKey = false, int durability = 0, int repairPrice = 0, UsableItemDescription usableItem = null,
            ThrowableItemDescription throwableItem = null, ArmorDescription armor = null, WeaponDescription weapon = null, AmmoDescription ammo = null, CarSlotDescription car = null, HelicopterSlotDescription helicopter = null,
            MainBuildingDescription mainBuildingDescription = null, CompoundWallDescription compoundWallDescription = null, OutbuildingDescription outbuildingDescription = null,
            CopFurnitureDescription copFurnitureDescription = null, Func<CollectionTableDescription> collectionTableFunc = null, CargoWorkbenchDescription cargoWorkbenchDescription = null,
            StorageDescription storageDescription = null, ShelvingDescription shelvingDescription = null, CargoWorkbenchUpgradeDescription cargoWorkbenchUpgradeDescription = null,
            CollectionTableUpgradeSlotDescription collectionTableUpgradeSlotDescription = null, CopFurnitureUpgradeDescription copFurnitureUpgradeDescription = null,
            MoneyWorkbenchUpgradeDescription moneyWorkbenchUpgradeDescription = null, BreachProtectionItemDescription breachProtectionItemDescription = null, CashDescription cashDescription = null,
            AlarmCarUpgradeDescription alarmCarUpgradeDescription = null, PoliceRadioCarUpgradeDescription policeRadioCarUpgradeDescription = null, HornCarUpgradeDescription hornCarUpgradeDescription = null,
            EcuCarUpgradeDescription ecuCarUpgradeDescription = null, TurbochargerCarUpgradeDescription turbochargerCarUpgradeDescription = null, EngineCoolingCarUpgradeDescription engineCoolingCarUpgradeDescription = null,
            NitroCarUpgradeDescription nitroCarUpgradeDescription = null, SuspensionCarUpgradeDescription suspensionCarUpgradeDescription = null, SteeringRackCarUpgradeDescription steeringRackCarUpgradeDescription = null,
            TrunkInventoryCarUpgradeDescription trunkInventoryCarUpgradeDescription = null, TrunkCargoInventoryCarUpgradeDescription trunkCargoInventoryCarUpgradeDescription = null, CumulativeItemDescription cumulativeItemDescription = null,
            PlotBuildingDefenseModuleDescription plotBuildingDefenseModuleDescription = null, PlotBuildingDecoderDescription decoderDescription = null)
                : base(id, _enum.Count)
        {
            _enum.Add(this);

            Type = type;
            CellsSize = cellsSize;
            StackSize = stackSize;
            Rarity = rarity;
            IsStackable = stackSize > 1;
            UsableItem = usableItem;
            IsUsable = usableItem != null;
            ThrowableItem = throwableItem;
            IsThrowable = throwableItem != null;
            IsPocketItem = IsUsable || IsThrowable;
            Armor = armor;
            IsVest = armor != null;
            Weapon = weapon;
            IsWeapon = weapon != null;
            InventoryLines = inventoryLines;
            IsBackpack = inventoryLines > 0;
            PocketCount = pocketCount;
            IsBelt = pocketCount > 0;
            IsEquip = IsWeapon || IsVest || IsBackpack || IsBelt;
            Ammo = ammo;
            IsAmmo = ammo != null;
            HasArmorOrAmmo = IsWeapon || IsVest;
            IsKey = isKey;
            BasePrice = IsAmmo ? ammo.BasePrice : basePrice;
            SellPrice = IsAmmo
                ? ammo.SellPrice
                : basePrice == 0
                    ? 0
                    : sellPriceModifier.GetSellPrice(basePrice);
            Durability = durability;
            HasDurability = durability > 0;
            RepairPrice = repairPrice;
            IsSellAvailable = isSellAvailable;
            IsCar = car != null;
            IsHelicopter = helicopter != null;
            Car = car;
            Helicopter = helicopter;
            MainBuildingDescription = mainBuildingDescription;
            CompoundWallDescription = compoundWallDescription;
            OutbuildingDescription = outbuildingDescription;
            CopFurnitureDescription = copFurnitureDescription;
            _collectionTableFunc = collectionTableFunc;
            CargoWorkbenchDescription = cargoWorkbenchDescription;
            StorageDescription = storageDescription;
            ShelvingDescription = shelvingDescription;
            CargoWorkbenchUpgradeDescription = cargoWorkbenchUpgradeDescription;
            CollectionTableUpgradeSlotDescription = collectionTableUpgradeSlotDescription;
            CopFurnitureUpgradeDescription = copFurnitureUpgradeDescription;
            MoneyWorkbenchUpgradeDescription = moneyWorkbenchUpgradeDescription;
            BreachProtectionItemDescription = breachProtectionItemDescription;
            CashDescription = cashDescription;
            IsCash = cashDescription != null;
            AlarmCarUpgradeDescription = alarmCarUpgradeDescription;
            PoliceRadioCarUpgradeDescription = policeRadioCarUpgradeDescription;
            HornCarUpgradeDescription = hornCarUpgradeDescription;
            EcuCarUpgradeDescription = ecuCarUpgradeDescription;
            TurbochargerCarUpgradeDescription = turbochargerCarUpgradeDescription;
            EngineCoolingCarUpgradeDescription = engineCoolingCarUpgradeDescription;
            NitroCarUpgradeDescription = nitroCarUpgradeDescription;
            SuspensionCarUpgradeDescription = suspensionCarUpgradeDescription;
            SteeringRackCarUpgradeDescription = steeringRackCarUpgradeDescription;
            TrunkInventoryCarUpgradeDescription = trunkInventoryCarUpgradeDescription;
            TrunkCargoInventoryCarUpgradeDescription = trunkCargoInventoryCarUpgradeDescription;
            CumulativeItemDescription = cumulativeItemDescription;
            HasCumulativePoints = cumulativeItemDescription != null;

            ICarUpgradeDescription carUpgradeDescription =
                alarmCarUpgradeDescription != null ? alarmCarUpgradeDescription :
                policeRadioCarUpgradeDescription != null ? policeRadioCarUpgradeDescription :
                hornCarUpgradeDescription != null ? hornCarUpgradeDescription :
                ecuCarUpgradeDescription != null ? ecuCarUpgradeDescription :
                turbochargerCarUpgradeDescription != null ? turbochargerCarUpgradeDescription :
                engineCoolingCarUpgradeDescription != null ? engineCoolingCarUpgradeDescription :
                nitroCarUpgradeDescription != null ? nitroCarUpgradeDescription :
                suspensionCarUpgradeDescription != null ? suspensionCarUpgradeDescription :
                steeringRackCarUpgradeDescription != null ? steeringRackCarUpgradeDescription :
                trunkInventoryCarUpgradeDescription != null ? trunkInventoryCarUpgradeDescription :
                trunkCargoInventoryCarUpgradeDescription;

            CarUpgradesHolderTypeDescription = carUpgradeDescription?.HolderType;
            PlotBuildingDefenseModuleDescription = plotBuildingDefenseModuleDescription;
            PlotBuildingDecoderDescription = decoderDescription;
            IsDecoder = decoderDescription != null;
        }

        public InventoryItemTypeDescription Type { get; }
        public InventoryItemCellsSizeDescription CellsSize { get; }
        public int StackSize { get; }
        public RarityItem Rarity { get; }
        public bool IsStackable { get; }
        public UsableItemDescription UsableItem { get; }
        public bool IsUsable { get; }
        public ThrowableItemDescription ThrowableItem { get; }
        public bool IsThrowable { get; }
        public bool IsPocketItem { get; }
        public ArmorDescription Armor { get; }
        public bool IsVest { get; }
        public WeaponDescription Weapon { get; }
        public bool IsWeapon { get; }
        public int InventoryLines { get; }
        public bool IsBackpack { get; }
        public int PocketCount { get; }
        public bool IsBelt { get; }
        public bool IsEquip { get; }
        public AmmoDescription Ammo { get; }
        public bool IsAmmo { get; }
        public bool IsKey { get; }
        public int BasePrice { get; }
        public int SellPrice { get; }
        public int Durability { get; }
        public bool HasDurability { get; }
        public int RepairPrice { get; }
        public bool IsSellAvailable { get; }
        public bool IsCar { get; }
        public bool IsHelicopter { get; }
        public bool IsCash { get; }
        public CarSlotDescription Car { get; }
        public HelicopterSlotDescription Helicopter { get; }
        public MainBuildingDescription MainBuildingDescription { get; }
        public CompoundWallDescription CompoundWallDescription { get; }
        public OutbuildingDescription OutbuildingDescription { get; }
        public CopFurnitureDescription CopFurnitureDescription { get; }
        public CollectionTableDescription CollectionTableDescription => _collectionTableFunc?.Invoke();
        public CargoWorkbenchDescription CargoWorkbenchDescription { get; }
        public StorageDescription StorageDescription { get; }
        public ShelvingDescription ShelvingDescription { get; }
        public CargoWorkbenchUpgradeDescription CargoWorkbenchUpgradeDescription { get; }
        public CollectionTableUpgradeSlotDescription CollectionTableUpgradeSlotDescription { get; }
        public CopFurnitureUpgradeDescription CopFurnitureUpgradeDescription { get; }
        public MoneyWorkbenchUpgradeDescription MoneyWorkbenchUpgradeDescription { get; }
        public BreachProtectionItemDescription BreachProtectionItemDescription { get; }
        public CashDescription CashDescription { get; }
        public AlarmCarUpgradeDescription AlarmCarUpgradeDescription { get; }
        public PoliceRadioCarUpgradeDescription PoliceRadioCarUpgradeDescription { get; }
        public HornCarUpgradeDescription HornCarUpgradeDescription { get; }
        public EcuCarUpgradeDescription EcuCarUpgradeDescription { get; }
        public TurbochargerCarUpgradeDescription TurbochargerCarUpgradeDescription { get; }
        public EngineCoolingCarUpgradeDescription EngineCoolingCarUpgradeDescription { get; }
        public NitroCarUpgradeDescription NitroCarUpgradeDescription { get; }
        public SuspensionCarUpgradeDescription SuspensionCarUpgradeDescription { get; }
        public SteeringRackCarUpgradeDescription SteeringRackCarUpgradeDescription { get; }
        public TrunkInventoryCarUpgradeDescription TrunkInventoryCarUpgradeDescription { get; }
        public TrunkCargoInventoryCarUpgradeDescription TrunkCargoInventoryCarUpgradeDescription { get; }
        public bool HasArmorOrAmmo { get; }
        public CumulativeItemDescription CumulativeItemDescription { get; }
        public bool HasCumulativePoints { get; }
        public CarUpgradesHolderTypeDescription CarUpgradesHolderTypeDescription { get; }
        public PlotBuildingDefenseModuleDescription PlotBuildingDefenseModuleDescription { get; }
        public PlotBuildingDecoderDescription PlotBuildingDecoderDescription { get; }
        public bool IsDecoder { get; }

        public static InventoryItemDescription Jp7M1 { get; } = CreateSmgWeapon(
            id: "jp_7_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Jp7M1,
            basePrice: 2700,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 3,
            repairPrice: 130
        );

        public static InventoryItemDescription Jp7M2 { get; } = CreateSmgWeapon(
            id: "jp_7_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Jp7M2,
            basePrice: 3590,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 3,
            repairPrice: 130
        );

        public static InventoryItemDescription Jp7M3 { get; } = CreateSmgWeapon(
            id: "jp_7_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Jp7M3,
            basePrice: 4100,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 3,
            repairPrice: 130
        );

        public static InventoryItemDescription BackpackUncommon { get; } = CreateBackpack(
            id: "backpack_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            inventoryLines: 4,
            basePrice: 350,
            sellPriceModifier: InventoryItemSellPriceModifier.Free
        );

        public static InventoryItemDescription BackpackRare { get; } = CreateBackpack(
            id: "backpack_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            inventoryLines: 5,
            basePrice: 6200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BackpackEpic { get; } = CreateBackpack(
            id: "backpack_epic",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            inventoryLines: 6,
            basePrice: 19500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BackpackLegend { get; } = CreateBackpack(
            id: "backpack_legend",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            inventoryLines: 7,
            basePrice: 19500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Vest1Mk1 { get; } = CreateArmor(
            id: "vest_1_mk_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            armor: ArmorDescription.Armor1Mk1,
            basePrice: 1150,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 10
        );

        public static InventoryItemDescription Vest1Mk2 { get; } = CreateArmor(
            id: "vest_1_mk_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            armor: ArmorDescription.Armor1Mk2,
            basePrice: 1150,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 10
        );

        public static InventoryItemDescription BeltUncommon { get; } = CreateBelt(
            id: "belt_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 1,
            pocketCount: 1,
            basePrice: 170,
            sellPriceModifier: InventoryItemSellPriceModifier.Free
        );

        public static InventoryItemDescription BeltRare { get; } = CreateBelt(
            id: "belt_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            pocketCount: 2,
            basePrice: 3650,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BeltEpic { get; } = CreateBelt(
            id: "belt_epic",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            pocketCount: 3,
            basePrice: 6300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BeltLegend { get; } = CreateBelt(
            id: "belt_legend",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            pocketCount: 4,
            basePrice: 6300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SyringeUncommon { get; } = CreateMedicine(
            id: "syringe_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            usableItem: UsableItemDescription.SyringeUncommon,
            basePrice: 1150,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SyringeEpic { get; } = CreateMedicine(
            id: "syringe_epic",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 5,
            usableItem: UsableItemDescription.SyringeEpic,
            basePrice: 4250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription RifleAmmo { get; } = CreateAmmo(
            id: "rifle_ammo",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 120,
            rarity: 1,
            basePrice: 0,
            ammo: AmmoDescription.Rifle
        );

        public static InventoryItemDescription MpAmmo { get; } = CreateAmmo(
            id: "mp_ammo",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 120,
            rarity: 1,
            basePrice: 0,
            ammo: AmmoDescription.MP
        );

        public static InventoryItemDescription ShotgunAmmo { get; } = CreateAmmo(
            id: "shotgun_ammo",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 60,
            rarity: 1,
            basePrice: 0,
            ammo: AmmoDescription.Shotgun
        );

        public static InventoryItemDescription Vest2Mk1 { get; } = CreateArmor(
            id: "vest_2_mk_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            armor: ArmorDescription.Armor2Mk1,
            basePrice: 11600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 2150
        );

        public static InventoryItemDescription Vest2Mk2 { get; } = CreateArmor(
            id: "vest_2_mk_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            armor: ArmorDescription.Armor2Mk2,
            basePrice: 11600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 2150
        );

        public static InventoryItemDescription Vest2Mk3 { get; } = CreateArmor(
            id: "vest_2_mk_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            armor: ArmorDescription.Armor2Mk3,
            basePrice: 11600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 2150
        );

        public static InventoryItemDescription ArmorPlateUncommon { get; } = CreateArmorPlate(
            id: "armor_plate_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 1,
            basePrice: 750,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            usableItem: UsableItemDescription.ArmorPlateUncommon
        );

        public static InventoryItemDescription ArmorPlateEpic { get; } = CreateArmorPlate(
            id: "armor_plate_epic",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            usableItem: UsableItemDescription.ArmorPlateEpic
        );

        public static InventoryItemDescription CashCommon { get; } = CreateCashItem(
            id: "cash_common",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 200,
            rarity: 1,
            basePrice: 1,
            cashDescription: CashDescription.Common
        );

        public static InventoryItemDescription CashUncommon { get; } = CreateCashItem(
            id: "cash_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 200,
            rarity: 2,
            basePrice: 20,
            cashDescription: CashDescription.Uncommon
        );

        public static InventoryItemDescription CashRare { get; } = CreateCashItem(
            id: "cash_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 200,
            rarity: 4,
            basePrice: 100,
            cashDescription: CashDescription.Rare
        );

        public static InventoryItemDescription CashLegendary { get; } = CreateCashItem(
            id: "cash_legendary",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 200,
            rarity: 5,
            basePrice: 1000,
            cashDescription: CashDescription.Legendary
        );

        public static InventoryItemDescription Dp9M1 { get; } = CreateHandgun(
            id: "dp_9_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 1,
            weapon: WeaponDescription.Dp9M1,
            basePrice: 240,
            sellPriceModifier: InventoryItemSellPriceModifier.Free,
            durability: 3,
            repairPrice: 25
        );

        public static InventoryItemDescription Dp9M2 { get; } = CreateHandgun(
            id: "dp_9_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 1,
            weapon: WeaponDescription.Dp9M2,
            basePrice: 1040,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 3,
            repairPrice: 25
        );

        public static InventoryItemDescription PistolAmmo { get; } = CreateAmmo(
            id: "pistol_ammo",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 40,
            rarity: 1,
            basePrice: 0,
            ammo: AmmoDescription.Pistol
        );

        public static InventoryItemDescription Kl545M1 { get; } = CreateAssaultRifle(
            id: "kl_545_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Kl545M1,
            basePrice: 3890,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 5,
            repairPrice: 230
        );

        public static InventoryItemDescription Kl545M2 { get; } = CreateAssaultRifle(
            id: "kl_545_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Kl545M2,
            basePrice: 4300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 5,
            repairPrice: 230
        );

        public static InventoryItemDescription Kl545M3 { get; } = CreateAssaultRifle(
            id: "kl_545_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Kl545M3,
            basePrice: 5950,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 5,
            repairPrice: 230
        );

        public static InventoryItemDescription Cb45M1 { get; } = CreateSmgWeapon(
            id: "cb_45_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Cb45M1,
            basePrice: 3500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 145
        );

        public static InventoryItemDescription Cb45M2 { get; } = CreateSmgWeapon(
            id: "cb_45_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Cb45M2,
            basePrice: 3950,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 145
        );

        public static InventoryItemDescription Cb45M3 { get; } = CreateSmgWeapon(
            id: "cb_45_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Cb45M3,
            basePrice: 4200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 145
        );

        public static InventoryItemDescription Key0 { get; } = CreateKeyWarehouse(
            id: "key_0",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 0,
            isKey: true
        );

        public static InventoryItemDescription Key1 { get; } = CreateKeyWarehouse(
            id: "key_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 0,
            isKey: true
        );

        public static InventoryItemDescription Key2 { get; } = CreateKeyWarehouse(
            id: "key_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 0,
            isKey: true
        );

        public static InventoryItemDescription Key3 { get; } = CreateKeyWarehouse(
            id: "key_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 0,
            isKey: true
        );

        public static InventoryItemDescription Key4 { get; } = CreateKeyWarehouse(
            id: "key_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 0,
            isKey: true
        );

        public static InventoryItemDescription Key5 { get; } = CreateKeyWarehouse(
            id: "key_5",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 0,
            isKey: true
        );

        public static InventoryItemDescription PoliceRadio { get; } = CreatePoliceRadio(
            id: "police_radio",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 1500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Lockpick1 { get; } = CreateLockpick(
            id: "lockpick_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 1,
            basePrice: 360,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Vest3Mk1 { get; } = CreateArmor(
            id: "vest_3_mk_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            armor: ArmorDescription.Armor3Mk1,
            basePrice: 36500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 2700
        );

        public static InventoryItemDescription Vest3Mk2 { get; } = CreateArmor(
            id: "vest_3_mk_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            armor: ArmorDescription.Armor3Mk2,
            basePrice: 36500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 2700
        );

        public static InventoryItemDescription Vest3Mk3 { get; } = CreateArmor(
            id: "vest_3_mk_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            armor: ArmorDescription.Armor3Mk3,
            basePrice: 36500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 2700
        );

        public static InventoryItemDescription Phone1 { get; } = CreateSell(
            id: "phone_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 280,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Phone2 { get; } = CreateSell(
            id: "phone_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Phone3 { get; } = CreateSell(
            id: "phone_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Phone4 { get; } = CreateSell(
            id: "phone_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2900,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SilverEarrings { get; } = CreateSell(
            id: "silver_earrings",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );


        public static InventoryItemDescription Earrings { get; } = CreateSell(
            id: "earrings",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 1380,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GoldenEarrings { get; } = CreateSell(
            id: "golden_earrings",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GoldenEarrings2 { get; } = CreateSell(
            id: "golden_earrings_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 220,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GoldenEarrings3 { get; } = CreateSell(
            id: "golden_earrings_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 270,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GoldenEarrings4 { get; } = CreateSell(
            id: "golden_earrings_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GoldenEarrings5 { get; } = CreateSell(
            id: "golden_earrings_5",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Watch1 { get; } = CreateSell(
            id: "watch_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Watch2 { get; } = CreateSell(
            id: "watch_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 210,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Watch3 { get; } = CreateSell(
            id: "watch_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Watch4 { get; } = CreateSell(
            id: "watch_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Watch5 { get; } = CreateSell(
            id: "watch_5",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Watch6 { get; } = CreateSell(
            id: "watch_6",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 240,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SilverRing { get; } = CreateSell(
            id: "silver_ring",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 220,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GoldenRing { get; } = CreateSell(
            id: "golden_ring",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 190,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GoldenRing2 { get; } = CreateSell(
            id: "golden_ring_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2700,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GoldenRing3 { get; } = CreateSell(
            id: "golden_ring_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 240,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GoldenRing4 { get; } = CreateSell(
            id: "golden_ring_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Harris500M1 { get; } = CreateShotgun(
            id: "harris_500_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size4x1,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Harris500M1,
            basePrice: 3100,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 135
        );

        public static InventoryItemDescription Harris500M2 { get; } = CreateShotgun(
            id: "harris_500_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size4x1,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Harris500M2,
            basePrice: 3600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 135
        );

        public static InventoryItemDescription Harris500M3 { get; } = CreateShotgun(
            id: "harris_500_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size4x1,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Harris500M3,
            basePrice: 4500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 135
        );

        public static InventoryItemDescription Necklace1 { get; } = CreateSell(
            id: "necklace_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Necklace2 { get; } = CreateSell(
            id: "necklace_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 240,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Necklace3 { get; } = CreateSell(
            id: "necklace_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 220,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Necklace4 { get; } = CreateSell(
            id: "necklace_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Necklace5 { get; } = CreateSell(
            id: "necklace_5",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2530,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Necklace6 { get; } = CreateSell(
            id: "necklace_6",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Necklace7 { get; } = CreateSell(
            id: "necklace_7",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 5,
            basePrice: 59400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Laptop1 { get; } = CreateSell(
            id: "laptop_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 7120,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Laptop2 { get; } = CreateSell(
            id: "laptop_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 7200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Laptop3 { get; } = CreateSell(
            id: "laptop_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 7400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Laptop4 { get; } = CreateSell(
            id: "laptop_4",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 7200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Laptop5 { get; } = CreateSell(
            id: "laptop_5",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 7440,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Laptop6 { get; } = CreateSell(
            id: "laptop_6",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 77200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GameConsole0 { get; } = CreateSell(
            id: "game_console_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 3,
            rarity: 1,
            basePrice: 360,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GameConsole1 { get; } = CreateSell(
            id: "game_console_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 7400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GameConsole2 { get; } = CreateSell(
            id: "game_console_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 7000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GameConsole3 { get; } = CreateSell(
            id: "game_console_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 7050,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Alcohol0 { get; } = CreateSell(
            id: "alcohol_0",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 2,
            basePrice: 360,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Alcohol1 { get; } = CreateSell(
            id: "alcohol_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 2,
            basePrice: 360,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Alcohol2 { get; } = CreateSell(
            id: "alcohol_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 4,
            basePrice: 4200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Alcohol3 { get; } = CreateSell(
            id: "alcohol_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 4,
            basePrice: 4200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Alcohol4 { get; } = CreateSell(
            id: "alcohol_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 5,
            basePrice: 108900,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Alcohol5 { get; } = CreateSell(
            id: "alcohol_5",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 4,
            basePrice: 4200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Toaster { get; } = CreateSell(
            id: "toaster",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 190,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Comics1 { get; } = CreateSell(
            id: "comics_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Comics2 { get; } = CreateSell(
            id: "comics_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 4600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Comics3 { get; } = CreateSell(
            id: "comics_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 640,
            sellPriceModifier: InventoryItemSellPriceModifier.Common);

        public static InventoryItemDescription Comics4 { get; } = CreateSell(
            id: "comics_4",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 7200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Comics5 { get; } = CreateSell(
            id: "comics_5",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 55000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Hairdryer { get; } = CreateSell(
            id: "hairdryer",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 3,
            rarity: 4,
            basePrice: 3800,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GraphicsCard1 { get; } = CreateSell(
            id: "graphics_card_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 3,
            rarity: 4,
            basePrice: 4200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GraphicsCard2 { get; } = CreateSell(
            id: "graphics_card_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 3,
            rarity: 4,
            basePrice: 4200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Headphones1 { get; } = CreateSell(
            id: "headphones_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 240,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Headphones2 { get; } = CreateSell(
            id: "headphones_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 3,
            rarity: 2,
            basePrice: 330,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Headphones3 { get; } = CreateSell(
            id: "headphones_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 3,
            rarity: 4,
            basePrice: 4400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Headphones4 { get; } = CreateSell(
            id: "headphones_4",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 3,
            rarity: 4,
            basePrice: 4100,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey0 { get; } = CreateKeyContainer(
            id: "safe_key_0",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey1 { get; } = CreateKeyContainer(
            id: "safe_key_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey2 { get; } = CreateKeyContainer(
            id: "safe_key_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey3 { get; } = CreateKeyContainer(
            id: "safe_key_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey4 { get; } = CreateKeyContainer(
            id: "safe_key_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey5 { get; } = CreateKeyContainer(
            id: "safe_key_5",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey6 { get; } = CreateKeyContainer(
            id: "safe_key_6",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey7 { get; } = CreateKeyContainer(
            id: "safe_key_7",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey8 { get; } = CreateKeyContainer(
            id: "safe_key_8",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey9 { get; } = CreateKeyContainer(
            id: "safe_key_9",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SafeKey10 { get; } = CreateKeyContainer(
            id: "safe_key_10",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 1400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription RoomKey0 { get; } = CreateKeyWarehouse(
            id: "room_key_0",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 2300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription RoomKey1 { get; } = CreateKeyWarehouse(
            id: "room_key_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 2300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription RoomKey2 { get; } = CreateKeyWarehouse(
            id: "room_key_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 2300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription RoomKey3 { get; } = CreateKeyWarehouse(
            id: "room_key_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 2300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription RoomKey4 { get; } = CreateKeyWarehouse(
            id: "room_key_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 2300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription RoomKey5 { get; } = CreateKeyWarehouse(
            id: "room_key_5",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 2300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription RoomKey6 { get; } = CreateKeyWarehouse(
            id: "room_key_6",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 2300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription EpicKey0 { get; } = CreateKeyWarehouse(
            id: "epic_key_0",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 17800,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription EpicKey1 { get; } = CreateKeyWarehouse(
            id: "epic_key_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 17300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Camera1 { get; } = CreateSell(
            id: "camera_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 210,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Camera2 { get; } = CreateSell(
            id: "camera_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 170,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Camera3 { get; } = CreateSell(
            id: "camera_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription TrashJewelryPouch { get; } = CreateSell(
            id: "trash_jewelry_pouch",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 80,
            rarity: 1,
            basePrice: 20,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription TrashPictureTube { get; } = CreateSell(
            id: "trash_picture_tube",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 1,
            basePrice: 360,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription OriginalPictureTube { get; } = CreateSell(
            id: "original_picture_tube",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 4,
            basePrice: 4300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription TrashElectronicsBox { get; } = CreateSell(
            id: "trash_electronics_box",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 40,
            rarity: 1,
            basePrice: 50,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription TrashAlcoholBox { get; } = CreateSell(
            id: "trash_alcohol_box",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 40,
            rarity: 1,
            basePrice: 40,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription TrashMedicineBox { get; } = CreateSell(
            id: "trash_medicine_box",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 40,
            rarity: 1,
            basePrice: 30,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription TrashAmmoBox { get; } = CreateSell(
            id: "trash_ammo_box",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 40,
            rarity: 1,
            basePrice: 50,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Rc556M1 { get; } = CreateAssaultRifle(
            id: "rc_556_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.Rc556M1,
            basePrice: 17000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 1250
        );

        public static InventoryItemDescription Rc556M2 { get; } = CreateAssaultRifle(
            id: "rc_556_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.Rc556M2,
            basePrice: 18500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 1250
        );

        public static InventoryItemDescription Rc556M3 { get; } = CreateAssaultRifle(
            id: "rc_556_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.Rc556M3,
            basePrice: 20400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 1250
        );

        public static InventoryItemDescription StarhM1 { get; } = CreateAssaultRifle(
            id: "star_h_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.StarhM1,
            basePrice: 21500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 1400
        );

        public static InventoryItemDescription StarhM2 { get; } = CreateAssaultRifle(
            id: "star_h_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.StarhM2,
            basePrice: 22300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 1400
        );

        public static InventoryItemDescription StarhM3 { get; } = CreateAssaultRifle(
            id: "star_h_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.StarhM3,
            basePrice: 24200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 1400
        );

        public static InventoryItemDescription SrReaperM1 { get; } = CreateAssaultRifle(
            id: "sr_reaper_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.SrReaperM1,
            basePrice: 49850,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 9250
        );

        public static InventoryItemDescription SrReaperM2 { get; } = CreateAssaultRifle(
            id: "sr_reaper_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.SrReaperM2,
            basePrice: 53500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 9250
        );

        public static InventoryItemDescription SrReaperM3 { get; } = CreateAssaultRifle(
            id: "sr_reaper_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.SrReaperM3,
            basePrice: 57800,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 9250
        );

        public static InventoryItemDescription ApScalarM1 { get; } = CreateSmgWeapon(
            id: "ap_scalar_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.ApScalarM1,
            basePrice: 53500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 9250
        );

        public static InventoryItemDescription ApScalarM2 { get; } = CreateSmgWeapon(
            id: "ap_scalar_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.ApScalarM2,
            basePrice: 56200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 9250
        );

        public static InventoryItemDescription ApScalarM3 { get; } = CreateSmgWeapon(
            id: "ap_scalar_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.ApScalarM3,
            basePrice: 59650,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 9250
        );

        public static InventoryItemDescription Pdw90M1 { get; } = CreateSmgWeapon(
            id: "pdw_90_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Pdw90M1,
            basePrice: 7500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 990
        );

        public static InventoryItemDescription Pdw90M2 { get; } = CreateSmgWeapon(
            id: "pdw_90_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Pdw90M2,
            basePrice: 8250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 990
        );

        public static InventoryItemDescription Pdw90M3 { get; } = CreateSmgWeapon(
            id: "pdw_90_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size3x2,
            stackSize: 1,
            rarity: 2,
            weapon: WeaponDescription.Pdw90M3,
            basePrice: 8950,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 990
        );

        public static InventoryItemDescription Ordr12M1 { get; } = CreateShotgun(
            id: "ordr_12_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size4x1,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.Ordr12M1,
            basePrice: 9520,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 720
        );

        public static InventoryItemDescription Ordr12M2 { get; } = CreateShotgun(
            id: "ordr_12_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size4x1,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.Ordr12M2,
            basePrice: 9670,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 720
        );

        public static InventoryItemDescription Ordr12M3 { get; } = CreateShotgun(
            id: "ordr_12_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size4x1,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.Ordr12M3,
            basePrice: 10500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 720
        );

        public static InventoryItemDescription Lbr44M1 { get; } = CreateHandgun(
            id: "lbr_44_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.Lbr44M1,
            basePrice: 4500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 650
        );

        public static InventoryItemDescription Lbr44M2 { get; } = CreateHandgun(
            id: "lbr_44_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 4,
            weapon: WeaponDescription.Lbr44M2,
            basePrice: 5250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 650
        );

        public static InventoryItemDescription GoldBar { get; } = CreateSell(
            id: "gold_bar",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 3,
            rarity: 5,
            basePrice: 130000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GemstoneDiamond { get; } = CreateSell(
            id: "gemstone_diamond",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 5,
            basePrice: 240000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GemstoneSapphire { get; } = CreateSell(
            id: "gemstone_sapphire",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 12400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GemstoneRuby { get; } = CreateSell(
            id: "gemstone_ruby",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 13000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GemstoneEmerald { get; } = CreateSell(
            id: "gemstone_emerald",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 12600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Perfume3 { get; } = CreateSell(
            id: "perfume_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 240,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Perfume2 { get; } = CreateSell(
            id: "perfume_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Perfume1 { get; } = CreateSell(
            id: "perfume_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2520,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription VinylRecord1 { get; } = CreateSell(
            id: "vinyl_record_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 21000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription VinylRecord2 { get; } = CreateSell(
            id: "vinyl_record_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 118000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BaseballCard1 { get; } = CreateSell(
            id: "baseball_card_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 5,
            basePrice: 59400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BaseballCard2 { get; } = CreateSell(
            id: "baseball_card_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 190,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BaseballCard3 { get; } = CreateSell(
            id: "baseball_card_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 160,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BaseballCard4 { get; } = CreateSell(
            id: "baseball_card_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 230,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BaseballCard5 { get; } = CreateSell(
            id: "baseball_card_5",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription PokerCards1 { get; } = CreateSell(
            id: "poker_cards_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 180,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription PokerCards2 { get; } = CreateSell(
            id: "poker_cards_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription PokerCards3 { get; } = CreateSell(
            id: "poker_cards_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Drugs { get; } = CreateSell(
            id: "drugs",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 3,
            rarity: 4,
            basePrice: 9000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Drill { get; } = CreateSell(
            id: "drill",
            cellsSize: InventoryItemCellsSizeDescription.Size4x3,
            stackSize: 1,
            rarity: 4,
            basePrice: 1380,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Antique_Statuette_1 { get; } = CreateSell(
            id: "antique_statuette_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 4,
            basePrice: 4200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Antique_Statuette_2 { get; } = CreateSell(
            id: "antique_statuette_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 4,
            basePrice: 4500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Antique_Statuette_3 { get; } = CreateSell(
            id: "antique_statuette_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 2,
            basePrice: 360,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Antique_Statuette_4 { get; } = CreateSell(
            id: "antique_statuette_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 5,
            basePrice: 102800,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Antique_Statuette_5 { get; } = CreateSell(
            id: "antique_statuette_5",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 2,
            basePrice: 360,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Antique_Statuette_6 { get; } = CreateSell(
            id: "antique_statuette_6",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 2,
            basePrice: 420,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription DecorativeToy1 { get; } = CreateSell(
            id: "decorative_toy_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 2,
            basePrice: 380,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription DecorativeToy2 { get; } = CreateSell(
            id: "decorative_toy_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 2,
            basePrice: 300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription DecorativeToy3 { get; } = CreateSell(
            id: "decorative_toy_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 4,
            basePrice: 4300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription DecorativeToy4 { get; } = CreateSell(
            id: "decorative_toy_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 4,
            basePrice: 4000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription DecorativeToy5 { get; } = CreateSell(
            id: "decorative_toy_5",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 3,
            rarity: 5,
            basePrice: 105000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription MemoryDrive1 { get; } = CreateSell(
            id: "memory_drive_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 170,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription MemoryDrive2 { get; } = CreateSell(
            id: "memory_drive_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 240,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription MemoryDrive3 { get; } = CreateSell(
            id: "memory_drive_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription MemoryDrive4 { get; } = CreateSell(
            id: "memory_drive_4",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 5,
            basePrice: 59400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Lighter1 { get; } = CreateSell(
            id: "lighter_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Lighter2 { get; } = CreateSell(
            id: "lighter_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription InjectionFlacon1 { get; } = CreateSell(
            id: "injection_flacon_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 220,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription InjectionFlacon2 { get; } = CreateSell(
            id: "injection_flacon_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 170,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription InjectionFlacon3 { get; } = CreateSell(
            id: "injection_flacon_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Ampoule1 { get; } = CreateSell(
            id: "ampoule_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Ampoule2 { get; } = CreateSell(
            id: "ampoule_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 240,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Vest4Mk1 { get; } = CreateArmor(
            id: "vest_4_mk_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            armor: ArmorDescription.Armor4Mk1,
            basePrice: 113400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 5700
        );

        public static InventoryItemDescription Vest4Mk2 { get; } = CreateArmor(
            id: "vest_4_mk_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            armor: ArmorDescription.Armor4Mk2,
            basePrice: 113400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 5700
        );

        public static InventoryItemDescription Vest4Mk3 { get; } = CreateArmor(
            id: "vest_4_mk_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            armor: ArmorDescription.Armor4Mk3,
            basePrice: 113400,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 5700
        );

        public static InventoryItemDescription PainkillerUncommon { get; } = CreateMedicine(
            id: "painkiller_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 1,
            usableItem: UsableItemDescription.PainkillerUncommon,
            basePrice: 100,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription PainkillerEpic { get; } = CreateMedicine(
            id: "painkiller_epic",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            usableItem: UsableItemDescription.PainkillerEpic,
            basePrice: 220,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription AfakUncommon { get; } = CreateMedicine(
            id: "afak_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            usableItem: UsableItemDescription.AfakUncommon,
            basePrice: 750,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription LaserRangefinder { get; } = CreateSell(
            id: "laser_rangefinder",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 240,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Binoculars { get; } = CreateSell(
            id: "binoculars",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 160,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription ThermalImager { get; } = CreateSell(
            id: "thermal_imager",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 2000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Multitool { get; } = CreateSell(
            id: "multitool",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 220,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription FragGrenadeUncommon { get; } = CreateGrenade(
            id: "frag_grenade_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            throwableItem: ThrowableItemDescription.FragGrenadeUncommon,
            basePrice: 2250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GasGrenadeUncommon { get; } = CreateGrenade(
            id: "gas_grenade_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            throwableItem: ThrowableItemDescription.GasGrenadeUncommon,
            basePrice: 2100,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription SmokeGrenadeUncommon { get; } = CreateGrenade(
            id: "smoke_grenade_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            throwableItem: ThrowableItemDescription.SmokeGrenadeUncommon,
            basePrice: 1520,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription FlashGrenadeUncommon { get; } = CreateGrenade(
            id: "flash_grenade_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 2,
            throwableItem: ThrowableItemDescription.FlashGrenadeUncommon,
            basePrice: 1360,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription StorageBreachItem { get; } = CreateLockpick(
            id: "storage_breach_item",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 11600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription VehicleTrunkBreachItem { get; } = CreateLockpick(
            id: "vehicle_trunk_breach_item",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 11600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BreachItem0 { get; } = CreateLockpick(
            id: "breach_item_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 7250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BreachItem1 { get; } = CreateLockpick(
            id: "breach_item_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 4,
            basePrice: 16000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription BreachItem2 { get; } = CreateLockpick(
            id: "breach_item_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 5,
            basePrice: 27750,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription Tag12Km1 { get; } = CreateShotgun(
            id: "tag_12k_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.Tag12Km1,
            basePrice: 42500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 720
        );

        public static InventoryItemDescription Tag12Km2 { get; } = CreateShotgun(
            id: "tag_12k_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.Tag12Km2,
            basePrice: 43300,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 720
        );

        public static InventoryItemDescription Tag12Km3 { get; } = CreateShotgun(
            id: "tag_12k_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.Tag12Km3,
            basePrice: 44900,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 720
        );

        public static InventoryItemDescription Drm14M1 { get; } = CreateAssaultRifle(
            id: "drm_14_m_1",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.Drm14M1,
            basePrice: 57500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 9250
        );

        public static InventoryItemDescription Drm14M2 { get; } = CreateAssaultRifle(
            id: "drm_14_m_2",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.Drm14M2,
            basePrice: 63500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 9250
        );

        public static InventoryItemDescription Drm14M3 { get; } = CreateAssaultRifle(
            id: "drm_14_m_3",
            cellsSize: InventoryItemCellsSizeDescription.Size4x2,
            stackSize: 1,
            rarity: 5,
            weapon: WeaponDescription.Drm14M3,
            basePrice: 66500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            durability: 6,
            repairPrice: 9250
        );

        public static InventoryItemDescription FukurouSpark { get; } = CreateCar(
            id: "fukurou_spark",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 270,
            sellPriceModifier: InventoryItemSellPriceModifier.Free,
            carSlotDescription: CarSlotDescription.FukurouSpark
        );

        public static InventoryItemDescription FukurouMillennium { get; } = CreateCar(
            id: "fukurou_millennium",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 12500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            carSlotDescription: CarSlotDescription.FukurouMillennium
        );

        public static InventoryItemDescription LonghornVan { get; } = CreateCar(
            id: "longhorn_van",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 115000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            carSlotDescription: CarSlotDescription.LonghornVan
        );

        public static InventoryItemDescription KronenE { get; } = CreateCar(
            id: "kronen_e",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 97650,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            carSlotDescription: CarSlotDescription.KronenE
        );

        public static InventoryItemDescription LonghornTitan { get; } = CreateCar(
            id: "longhorn_titan",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 270000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            carSlotDescription: CarSlotDescription.LonghornTitan
        );

        public static InventoryItemDescription KronenJ { get; } = CreateCar(
            id: "kronen_j",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 275000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            carSlotDescription: CarSlotDescription.KronenJ
        );

        public static InventoryItemDescription LonghornWild { get; } = CreateCar(
            id: "longhorn_wild",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 370250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            carSlotDescription: CarSlotDescription.LonghornWild
        );

        public static InventoryItemDescription BulettiThunderX { get; } = CreateCar(
            id: "buletti_thunder_x",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 850000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            carSlotDescription: CarSlotDescription.BulettiThunderX
        );

        public static InventoryItemDescription RomanovR8 { get; } = CreateHelicopter(
            id: "romanov_r8",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 1500000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            helicopterSlotDescription: HelicopterSlotDescription.RomanovR8
        );

        public static InventoryItemDescription Home0 { get; } = CreateMainBuilding(
            id: "home_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 400,
            sellPriceModifier: InventoryItemSellPriceModifier.Free,
            mainBuildingDescription: MainBuildingDescription.Home0
        );

        public static InventoryItemDescription Home1 { get; } = CreateMainBuilding(
            id: "home_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 52500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            mainBuildingDescription: MainBuildingDescription.Home1
        );

        public static InventoryItemDescription Home2 { get; } = CreateMainBuilding(
            id: "home_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 1500000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            mainBuildingDescription: MainBuildingDescription.Home2
        );

        public static InventoryItemDescription Home3 { get; } = CreateMainBuilding(
            id: "home_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 400,
            sellPriceModifier: InventoryItemSellPriceModifier.Free,
            mainBuildingDescription: MainBuildingDescription.Home3
        );

        public static InventoryItemDescription CompoundWall0 { get; } = CreateCompoundWall(
            id: "compound_wall_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 3800,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            compoundWallDescription: CompoundWallDescription.CompoundWall0
        );

        public static InventoryItemDescription CompoundWall1 { get; } = CreateCompoundWall(
            id: "compound_wall_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 37500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            compoundWallDescription: CompoundWallDescription.CompoundWall1
        );

        public static InventoryItemDescription CompoundWall2 { get; } = CreateCompoundWall(
            id: "compound_wall_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 68100,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            compoundWallDescription: CompoundWallDescription.CompoundWall2
        );

        public static InventoryItemDescription CarGarage0 { get; } = CreateOutbuilding(
            id: "car_garage_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 17650,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            outbuildingDescription: OutbuildingDescription.CarGarage0
        );

        public static InventoryItemDescription CarGarage1 { get; } = CreateOutbuilding(
            id: "car_garage_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 68150,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            outbuildingDescription: OutbuildingDescription.CarGarage1
        );

        public static InventoryItemDescription HelicopterGarage { get; } = CreateOutbuilding(
            id: "helicopter_garage",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 170000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            outbuildingDescription: OutbuildingDescription.HelicopterGarage0
        );

        public static InventoryItemDescription StorageOutbuilding { get; } = CreateOutbuilding(
            id: "storage_outbuilding",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 36350,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            outbuildingDescription: OutbuildingDescription.Storage
        );

        public static InventoryItemDescription Watchtower { get; } = CreateOutbuilding(
            id: "watchtower",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 12000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            outbuildingDescription: OutbuildingDescription.Watchtower
        );

        public static InventoryItemDescription CopFurniture { get; } = CreateCopFurniture(
            id: "cop_furniture",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 5900,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureDescription: CopFurnitureDescription.CopFurniture0
        );

        public static InventoryItemDescription CollectionTable { get; } = CreateCollectionTable(
            id: "collection_table",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 6700,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            collectionTableFunc: () => CollectionTableDescription.CollectionTable2
        );

        public static InventoryItemDescription CargoWorkbench { get; } = CreateCargoWorkbench(
            id: "cargo_workbench",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 3900,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            cargoWorkbenchDescription: CargoWorkbenchDescription.Workbench2
        );

        public static InventoryItemDescription StorageSmall { get; } = CreateStorage(
            id: "storage_small",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 4250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            storageDescription: StorageDescription.StorageSmall
        );

        public static InventoryItemDescription StorageMedium { get; } = CreateStorage(
            id: "storage_medium",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 23250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            storageDescription: StorageDescription.StorageMedium
        );

        public static InventoryItemDescription StorageBig { get; } = CreateStorage(
            id: "storage_big",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 71000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            storageDescription: StorageDescription.StorageBig
        );

        public static InventoryItemDescription ShelvingSmall { get; } = CreateShelving(
            id: "shelving_small",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 1650,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            shelvingDescription: ShelvingDescription.ShelvingSmall
        );

        public static InventoryItemDescription ShelvingMedium { get; } = CreateShelving(
            id: "shelving_medium",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 16250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            shelvingDescription: ShelvingDescription.ShelvingMedium
        );

        public static InventoryItemDescription ShelvingBig { get; } = CreateShelving(
            id: "shelving_big",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 48200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            shelvingDescription: ShelvingDescription.ShelvingBig
        );

        public static InventoryItemDescription CargoWorkbenchUpgradeAdditionalSlot { get; } = CreateCargoWorkbenchUpgrade(
            id: "cargo_workbench_upgrade_additional_slot",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 19250,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            cargoWorkbenchUpgradeDescription: CargoWorkbenchUpgradeDescription.AdditionalSlot
        );

        public static InventoryItemDescription CargoWorkbenchUpgradeToolbox0 { get; } = CreateCargoWorkbenchUpgrade(
            id: "cargo_workbench_upgrade_toolbox_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 6000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            cargoWorkbenchUpgradeDescription: CargoWorkbenchUpgradeDescription.Toolbox0
        );

        public static InventoryItemDescription CargoWorkbenchUpgradeToolbox1 { get; } = CreateCargoWorkbenchUpgrade(
            id: "cargo_workbench_upgrade_toolbox_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 6000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            cargoWorkbenchUpgradeDescription: CargoWorkbenchUpgradeDescription.Toolbox1
        );

        public static InventoryItemDescription CargoWorkbenchUpgradeCircularSaw0 { get; } = CreateCargoWorkbenchUpgrade(
            id: "cargo_workbench_upgrade_circular_saw_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 6000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            cargoWorkbenchUpgradeDescription: CargoWorkbenchUpgradeDescription.CircularSaw0
        );

        public static InventoryItemDescription CargoWorkbenchUpgradeCircularSaw1 { get; } = CreateCargoWorkbenchUpgrade(
            id: "cargo_workbench_upgrade_circular_saw_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 6000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            cargoWorkbenchUpgradeDescription: CargoWorkbenchUpgradeDescription.CircularSaw1
        );

        public static InventoryItemDescription CargoWorkbenchUpgradePlasmaCutter0 { get; } = CreateCargoWorkbenchUpgrade(
            id: "cargo_workbench_upgrade_plasma_cutter_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 6000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            cargoWorkbenchUpgradeDescription: CargoWorkbenchUpgradeDescription.PlasmaCutter0
        );

        public static InventoryItemDescription CargoWorkbenchUpgradePlasmaCutter1 { get; } = CreateCargoWorkbenchUpgrade(
            id: "cargo_workbench_upgrade_plasma_cutter_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 6000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            cargoWorkbenchUpgradeDescription: CargoWorkbenchUpgradeDescription.PlasmaCutter1
        );

        public static InventoryItemDescription CollectionTableUpgradeRare { get; } = CreateCollectionTableUpgrade(
            id: "collection_table_upgrade_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 3900,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            collectionTableUpgradeSlotDescription: CollectionTableUpgradeSlotDescription.Rare
        );

        public static InventoryItemDescription CollectionTableUpgradeLegendary { get; } = CreateCollectionTableUpgrade(
            id: "collection_table_upgrade_legendary",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 65000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            collectionTableUpgradeSlotDescription: CollectionTableUpgradeSlotDescription.Legendary
        );

        public static InventoryItemDescription CopFurnitureUpgradeEquip0 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_equip_0",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 1,
            basePrice: 4700,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Equip0
        );

        public static InventoryItemDescription CopFurnitureUpgradeEquip1 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_equip_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 21500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Equip1
        );

        public static InventoryItemDescription CopFurnitureUpgradeEquip2 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_equip_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 72000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Equip2
        );

        public static InventoryItemDescription CopFurnitureUpgradeWeapon0 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_weapon_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 7350,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Weapon0
        );

        public static InventoryItemDescription CopFurnitureUpgradeWeapon1 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_weapon_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 16350,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Weapon1
        );

        public static InventoryItemDescription CopFurnitureUpgradeWeapon2 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_weapon_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 58500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Weapon2
        );

        public static InventoryItemDescription CopFurnitureUpgradeReward0 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_reward_0",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 1,
            basePrice: 6500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Reward0
        );

        public static InventoryItemDescription CopFurnitureUpgradeReward1 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_reward_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 21000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Reward1
        );

        public static InventoryItemDescription CopFurnitureUpgradeReward2 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_reward_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 66500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Reward2
        );

        public static InventoryItemDescription CopFurnitureUpgradeCar1 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_car_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 100000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Car1
        );

        public static InventoryItemDescription CopFurnitureUpgradeCar0 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_car_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 160000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Car0
        );

        public static InventoryItemDescription CopFurnitureUpgradeHelicopter0 { get; } = CreateCopFurnitureUpgrade(
            id: "cop_furniture_upgrade_helicopter_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 350200,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            copFurnitureUpgradeDescription: CopFurnitureUpgradeDescription.Helicopter0
        );

        public static InventoryItemDescription MoneyWorkbenchUpgradeSpeedup0 { get; } = CreateMoneyWorkbenchUpgrade(
            id: "money_workbench_upgrade_speedup_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 5800,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            moneyWorkbenchUpgradeDescription: MoneyWorkbenchUpgradeDescription.Speedup0
        );

        public static InventoryItemDescription MoneyWorkbenchUpgradeSpeedup1 { get; } = CreateMoneyWorkbenchUpgrade(
            id: "money_workbench_upgrade_speedup_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 21500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            moneyWorkbenchUpgradeDescription: MoneyWorkbenchUpgradeDescription.Speedup1
        );

        public static InventoryItemDescription MoneyWorkbenchUpgradeSpeedup2 { get; } = CreateMoneyWorkbenchUpgrade(
            id: "money_workbench_upgrade_speedup_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 68500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            moneyWorkbenchUpgradeDescription: MoneyWorkbenchUpgradeDescription.Speedup2
        );

        public static InventoryItemDescription MoneyWorkbenchUpgradeLaunderingRate0 { get; } = CreateMoneyWorkbenchUpgrade(
            id: "money_workbench_upgrade_laundering_rate_0",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 7500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            moneyWorkbenchUpgradeDescription: MoneyWorkbenchUpgradeDescription.LaunderingRate0
        );

        public static InventoryItemDescription MoneyWorkbenchUpgradeLaunderingRate1 { get; } = CreateMoneyWorkbenchUpgrade(
            id: "money_workbench_upgrade_laundering_rate_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 19600,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            moneyWorkbenchUpgradeDescription: MoneyWorkbenchUpgradeDescription.LaunderingRate1
        );

        public static InventoryItemDescription MoneyWorkbenchUpgradeLaunderingRate2 { get; } = CreateMoneyWorkbenchUpgrade(
            id: "money_workbench_upgrade_laundering_rate_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 65000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            moneyWorkbenchUpgradeDescription: MoneyWorkbenchUpgradeDescription.LaunderingRate2
        );

        public static InventoryItemDescription Lock0 { get; } = CreateBreachProtectionItem(
            id: "lock_0",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 16500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            breachProtectionItemDescription: BreachProtectionItemDescription.Lock0
        );

        public static InventoryItemDescription Lock1 { get; } = CreateBreachProtectionItem(
            id: "lock_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 51500,
            sellPriceModifier: InventoryItemSellPriceModifier.Common,
            breachProtectionItemDescription: BreachProtectionItemDescription.Lock1
        );

        public static InventoryItemDescription GarbageBag { get; } = CreateSell(
            id: "garbage_bag",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 40,
            rarity: 1,
            basePrice: 50,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GarbageCamera { get; } = CreateSell(
            id: "garbage_camera",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 1,
            basePrice: 40,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GarbageHeadphones { get; } = CreateSell(
            id: "garbage_headphones",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 3,
            rarity: 1,
            basePrice: 50,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GarbageLaptop { get; } = CreateSell(
            id: "garbage_laptop",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 190,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GarbageLighter { get; } = CreateSell(
            id: "garbage_lighter",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 5,
            rarity: 1,
            basePrice: 30,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription GarbageToaster { get; } = CreateSell(
            id: "garbage_toaster",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 70,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription PoliceRadioCarUpgradeCommon { get; } = CreatePoliceRadioCarUpgrade(
            id: "police_radio_car_upgrade_common",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 26500,
            policeRadioCarUpgradeDescription: PoliceRadioCarUpgradeDescription.Common
        );

        public static InventoryItemDescription PoliceRadioCarUpgradeRare { get; } = CreatePoliceRadioCarUpgrade(
            id: "police_radio_car_upgrade_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 26500,
            policeRadioCarUpgradeDescription: PoliceRadioCarUpgradeDescription.Rare
        );

        public static InventoryItemDescription EcuCarUpgradeCommon { get; } = CreateEcuCarUpgrade(
            id: "ecu_car_upgrade_common",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 2200,
            ecuCarUpgradeDescription: EcuCarUpgradeDescription.Common
        );

        public static InventoryItemDescription EcuCarUpgradeRare { get; } = CreateEcuCarUpgrade(
            id: "ecu_car_upgrade_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 9400,
            ecuCarUpgradeDescription: EcuCarUpgradeDescription.Rare
        );

        public static InventoryItemDescription TurbochargerCarUpgradeCommon { get; } = CreateTurbochargerCarUpgrade(
            id: "turbocharger_car_upgrade_common",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 2200,
            turbochargerCarUpgradeDescription: TurbochargerCarUpgradeDescription.Common
        );

        public static InventoryItemDescription TurbochargerCarUpgradeRare { get; } = CreateTurbochargerCarUpgrade(
            id: "turbocharger_car_upgrade_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 9600,
            turbochargerCarUpgradeDescription: TurbochargerCarUpgradeDescription.Rare
        );

        public static InventoryItemDescription EngineCoolingCarUpgradeCommon { get; } = CreateEngineCoolingCarUpgrade(
            id: "engine_cooling_car_upgrade_common",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 1200,
            engineCoolingCarUpgradeDescription: EngineCoolingCarUpgradeDescription.Common
        );

        public static InventoryItemDescription EngineCoolingCarUpgradeRare { get; } = CreateEngineCoolingCarUpgrade(
            id: "engine_cooling_car_upgrade_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 12000,
            engineCoolingCarUpgradeDescription: EngineCoolingCarUpgradeDescription.Rare
        );

        public static InventoryItemDescription SuspensionCarUpgradeCommon { get; } = CreateSuspensionCarUpgrade(
            id: "suspension_car_upgrade_common",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 1450,
            suspensionCarUpgradeDescription: SuspensionCarUpgradeDescription.Common
        );

        public static InventoryItemDescription SuspensionCarUpgradeRare { get; } = CreateSuspensionCarUpgrade(
            id: "suspension_car_upgrade_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 9600,
            suspensionCarUpgradeDescription: SuspensionCarUpgradeDescription.Rare
        );

        public static InventoryItemDescription SteeringRackCarUpgradeCommon { get; } = CreateSteeringRackCarUpgrade(
            id: "steering_rack_car_upgrade_common",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 1750,
            steeringRackCarUpgradeDescription: SteeringRackCarUpgradeDescription.Common
        );

        public static InventoryItemDescription SteeringRackCarUpgradeRare { get; } = CreateSteeringRackCarUpgrade(
            id: "steering_rack_car_upgrade_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size1x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 10600,
            steeringRackCarUpgradeDescription: SteeringRackCarUpgradeDescription.Rare
        );

        public static InventoryItemDescription GrenadeLauncherShell { get; } = CreateSell(
            id: "grenade_launcher_shell",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 150000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription YachtKey { get; } = CreateSell(
            id: "yacht_key",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 6,
            basePrice: 1000000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription UvLamp { get; } = CreateSell(
            id: "uv_lamp",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 89000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription DepositBoxKeyCumulative { get; } = CreateCumulativeItem(
            id: "deposit_box_key_cumulative",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.DepositBoxKey
        );

        public static InventoryItemDescription DepositBoxKey { get; } = CreateKeyWarehouse(
            id: "deposit_box_key",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 2000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription UsbRubberDuckyCumulative { get; } = CreateCumulativeItem(
            id: "usb_rubber_ducky_cumulative",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.UsbRubberDucky
        );

        public static InventoryItemDescription UsbRubberDucky { get; } = CreateSell(
            id: "usb_rubber_ducky",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 80000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription TabletCumulative { get; } = CreateCumulativeItem(
            id: "tablet_cumulative",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.Tablet
        );

        public static InventoryItemDescription Tablet { get; } = CreateKeyContainer(
            id: "tablet",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 10000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription CargoManifestCumulative1 { get; } = CreateCumulativeItem(
            id: "cargo_manifest_cumulative_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.CargoManifest1
        );

        public static InventoryItemDescription CargoManifest1 { get; } = CreateSell(
            id: "cargo_manifest_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 10000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );
        
        public static InventoryItemDescription CargoManifestCumulative2 { get; } = CreateCumulativeItem(
            id: "cargo_manifest_cumulative_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.CargoManifest2
        );

        public static InventoryItemDescription CargoManifest2 { get; } = CreateSell(
            id: "cargo_manifest_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 80000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );
        
        public static InventoryItemDescription CargoManifestCumulative3 { get; } = CreateCumulativeItem(
            id: "cargo_manifest_cumulative_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.CargoManifest3
        );
        public static InventoryItemDescription CargoManifest3 { get; } = CreateSell(
            id: "cargo_manifest_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 5,
            basePrice: 400000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription ActionCameraCumulative1 { get; } = CreateCumulativeItem(
            id: "action_camera_cumulative_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.ActionCamera1
        );

        public static InventoryItemDescription ActionCamera1 { get; } = CreateSell(
            id: "action_camera_1",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 10000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );
        public static InventoryItemDescription ActionCameraCumulative2 { get; } = CreateCumulativeItem(
            id: "action_camera_cumulative_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.ActionCamera2
        );
        public static InventoryItemDescription ActionCamera2 { get; } = CreateSell(
            id: "action_camera_2",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 80000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );
        
        public static InventoryItemDescription ActionCameraCumulative3 { get; } = CreateCumulativeItem(
            id: "action_camera_cumulative_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.ActionCamera3
        );

        public static InventoryItemDescription ActionCamera3 { get; } = CreateSell(
            id: "action_camera_3",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 400000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );
        public static InventoryItemDescription LabFlaskCumulative1 { get; } = CreateCumulativeItem(
            id: "lab_flask_cumulative_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.LabFlask1
        );

        public static InventoryItemDescription LabFlask1 { get; } = CreateSell(
            id: "lab_flask_1",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 2,
            basePrice: 10000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription LabFlaskCumulative2 { get; } = CreateCumulativeItem(
            id: "lab_flask_cumulative_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.LabFlask2
        );

        public static InventoryItemDescription LabFlask2 { get; } = CreateSell(
            id: "lab_flask_2",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 4,
            basePrice: 80000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );
        
        public static InventoryItemDescription LabFlaskCumulative3 { get; } = CreateCumulativeItem(
            id: "lab_flask_cumulative_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 2,
            cumulativeItemDescription: CumulativeItemDescription.LabFlask3
        );

        public static InventoryItemDescription LabFlask3 { get; } = CreateSell(
            id: "lab_flask_3",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 400000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );
        
        public static InventoryItemDescription PlaneTestRight { get; } = CreateOutbuilding(
            id: "plane_test_right",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 3,
            rarity: 4,
            basePrice: 100,
            sellPriceModifier: InventoryItemSellPriceModifier.Free,
            outbuildingDescription: OutbuildingDescription.PlaneTestRight
        );

        public static InventoryItemDescription CompoundWall1Test { get; } = CreateCompoundWall(
            id: "compound_wall_1_test",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 2,
            rarity: 4,
            basePrice: 75000,
            sellPriceModifier: InventoryItemSellPriceModifier.Free,
            compoundWallDescription: CompoundWallDescription.CompoundWall3
        );

        public static InventoryItemDescription CompoundWallWood { get; } = CreateCompoundWall(
            id: "compound_wall_wood",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 1,
            basePrice: 500,
            sellPriceModifier: InventoryItemSellPriceModifier.Free,
            compoundWallDescription: CompoundWallDescription.CompoundWall4
        );

        public static InventoryItemDescription TrunkInventoryCarUpgradeCommon { get; } = CreateTrunkInventoryCarUpgrade(
            id: "trunk_inventory_car_upgrade_common",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 2600,
            trunkInventoryCarUpgradeDescription: TrunkInventoryCarUpgradeDescription.Common
        );

        public static InventoryItemDescription TrunkInventoryCarUpgradeRare { get; } = CreateTrunkInventoryCarUpgrade(
            id: "trunk_inventory_car_upgrade_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 22400,
            trunkInventoryCarUpgradeDescription: TrunkInventoryCarUpgradeDescription.Rare
        );

        public static InventoryItemDescription TrunkCargoInventoryCarUpgradeCommon { get; } = CreateTrunkCargoInventoryCarUpgrade(
            id: "trunk_cargo_inventory_car_upgrade_common",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 2,
            basePrice: 2900,
            trunkCargoInventoryCarUpgradeDescription: TrunkCargoInventoryCarUpgradeDescription.Common
        );

        public static InventoryItemDescription TrunkCargoInventoryCarUpgradeRare { get; } = CreateTrunkCargoInventoryCarUpgrade(
            id: "trunk_cargo_inventory_car_upgrade_rare",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 4,
            basePrice: 24600,
            trunkCargoInventoryCarUpgradeDescription: TrunkCargoInventoryCarUpgradeDescription.Rare
        );

        public static InventoryItemDescription PlotBuildingDefenseModuleUncommon { get; } = CreatePlotBuildingDefenseModule(
            id: "plot_building_defense_module_uncommon",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 200,
            plotBuildingDefenseModuleDescription: PlotBuildingDefenseModuleDescription.Uncommon
        );

        public static InventoryItemDescription PlotBuildingDefenseModuleEpic { get; } = CreatePlotBuildingDefenseModule(
            id: "plot_building_defense_module_epic",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 200,
            plotBuildingDefenseModuleDescription: PlotBuildingDefenseModuleDescription.Epic
        );

        public static InventoryItemDescription PlotBuildingDefenseModuleLegendary { get; } = CreatePlotBuildingDefenseModule(
            id: "plot_building_defense_module_legendary",
            cellsSize: InventoryItemCellsSizeDescription.Size2x1,
            stackSize: 5,
            rarity: 2,
            basePrice: 200,
            plotBuildingDefenseModuleDescription: PlotBuildingDefenseModuleDescription.Legendary
        );

        public static InventoryItemDescription EggFaberge { get; } = CreateSell(
            id: "egg_faberge",
            cellsSize: InventoryItemCellsSizeDescription.Size2x2,
            stackSize: 1,
            rarity: 6,
            basePrice: 100000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription AutoFlipper { get; } = CreateSell(
            id: "auto_flipper",
            cellsSize: InventoryItemCellsSizeDescription.Size1x1,
            stackSize: 1,
            rarity: 5,
            basePrice: 500000,
            sellPriceModifier: InventoryItemSellPriceModifier.Common
        );

        public static InventoryItemDescription DecoderUncommon { get; } = CreateDecoder(
            id: "decoder_uncommon",
            stackSize: 5,
            basePrice: 1000,
            rarity: RarityItem.Uncommon,
            decoderDescription: PlotBuildingDecoderDescription.Uncommon);

        public static InventoryItemDescription DecoderEpic { get; } = CreateDecoder(
            id: "decoder_epic",
            stackSize: 5,
            basePrice: 5000,
            rarity: RarityItem.Epic,
            decoderDescription: PlotBuildingDecoderDescription.Epic);

        public static InventoryItemDescription DecoderLegendary { get; } = CreateDecoder(
            id: "decoder_legendary",
            stackSize: 5,
            basePrice: 10000,
            rarity: RarityItem.Legendary,
            decoderDescription: PlotBuildingDecoderDescription.Legendary);
    }
}