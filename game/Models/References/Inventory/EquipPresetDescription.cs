using System;
using Framework.Core.Identified;

namespace Models.References.Inventory
{
    public class EquipPresetDescription : Identified
    {
        private EquipPresetDescription(string id,
            InventoryItemDescription backpack,
            InventoryItemDescription vest,
            InventoryItemDescription belt,
            InventoryItemDescription[] pockets,
            InventoryItemDescription[] inventoryItems) : base(id)
        {
            Vest = vest;
            Belt = belt;
            Pockets = pockets;
            InventoryItems = inventoryItems;
            Backpack = backpack;
        }

        public InventoryItemDescription Backpack { get; }
        public InventoryItemDescription Vest { get; }
        public InventoryItemDescription Belt { get; }
        public InventoryItemDescription[] Pockets { get; }
        public InventoryItemDescription[] InventoryItems { get; }

        public static EquipPresetDescription Cop0 { get; } = new EquipPresetDescription(
            id: "cop0",
            backpack: InventoryItemDescription.BackpackUncommon,
            vest: null,
            belt: InventoryItemDescription.BeltUncommon,
            pockets: new[] {InventoryItemDescription.PainkillerUncommon},
            inventoryItems: new[] {InventoryItemDescription.PoliceRadio,}
        );

        public static EquipPresetDescription Cop1 { get; } = new EquipPresetDescription(
            id: "cop1",
            backpack: InventoryItemDescription.BackpackUncommon,
            vest: InventoryItemDescription.Vest1Mk1,
            belt: InventoryItemDescription.BeltRare,
            pockets: new[] {InventoryItemDescription.PainkillerUncommon, InventoryItemDescription.ArmorPlateUncommon},
            inventoryItems: new[] {InventoryItemDescription.PoliceRadio,}
        );

        public static EquipPresetDescription Cop2 { get; } = new EquipPresetDescription(
            id: "cop2",
            backpack: InventoryItemDescription.BackpackUncommon,
            vest: InventoryItemDescription.Vest2Mk1,
            belt: InventoryItemDescription.BeltEpic,
            pockets: new[] {InventoryItemDescription.PainkillerEpic, InventoryItemDescription.ArmorPlateUncommon, InventoryItemDescription.SmokeGrenadeUncommon},
            inventoryItems: new[] {InventoryItemDescription.PoliceRadio,}
        );

        public static EquipPresetDescription Cop3 { get; } = new EquipPresetDescription(
            id: "cop3",
            backpack: InventoryItemDescription.BackpackUncommon,
            vest: InventoryItemDescription.Vest3Mk1,
            belt: InventoryItemDescription.BeltLegend,
            pockets: new[] {InventoryItemDescription.SyringeEpic, InventoryItemDescription.ArmorPlateUncommon, InventoryItemDescription.SmokeGrenadeUncommon, InventoryItemDescription.FlashGrenadeUncommon},
            inventoryItems: new[] {InventoryItemDescription.PoliceRadio,}
        );

        public static EquipPresetDescription StartEquip { get; } = new EquipPresetDescription(
            id: "start_equip",
            backpack: InventoryItemDescription.BackpackUncommon,
            vest: null,
            belt: InventoryItemDescription.BeltUncommon,
            pockets: new[] {InventoryItemDescription.PainkillerUncommon},
            inventoryItems: new[] {InventoryItemDescription.Home3, InventoryItemDescription.CompoundWallWood, InventoryItemDescription.FukurouSpark}
        );

        public static EquipPresetDescription DefaultEquip { get; } = new EquipPresetDescription(
            id: "default_equip",
            backpack: InventoryItemDescription.BackpackUncommon,
            vest: null,
            belt: null,
            pockets: Array.Empty<InventoryItemDescription>(),
            inventoryItems: Array.Empty<InventoryItemDescription>()
        );
    }
}