using Framework.Replication.Data;
using Framework.Replication.Data.Primitive;
using Models.References;
using Models.References.Door;

namespace Models.Data.Hangar.Builder
{
    public class PlotCompoundWallData : CompositeData
    {
        public PlotCompoundWallData(HangarCompoundWallData data)
        {
            IsBlockingColliderActive = Add("is_blocking_collider_active", new BoolData());
            
            Doors = Add("doors", new ReferenceMapData<CompoundWallDoorSlotDescription, BuilderDoorData>(CompoundWallDoorSlotDescription.Enum, doorSlotDescription => new BuilderDoorData(data.Doors[doorSlotDescription])));
            RaidLockTs = Add("raid_lock_ts", data.DefenseBlock.RaidLockTs);
            IsRaided = Add("is_raided", data.DefenseBlock.IsRaided);
            IsDefenseBlockSlotsEmpty = Add("is_defense_block_slots_empty", new BoolData());
        }

        public IPrimitiveData<bool> IsBlockingColliderActive { get; }
        
        public IReferenceMapData<CompoundWallDoorSlotDescription, BuilderDoorData> Doors { get; }
        public TimestampData RaidLockTs { get; }
        public BoolData IsRaided { get; }
        public BoolData IsDefenseBlockSlotsEmpty { get; }
    }
}