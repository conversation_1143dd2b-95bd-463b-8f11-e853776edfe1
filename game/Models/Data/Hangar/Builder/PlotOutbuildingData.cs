using Framework.Replication.Data;
using Framework.Replication.Data.Primitive;
using Models.Data.Hangar.Shelving;
using Models.Data.Hangar.Storage;
using Models.References;
using Models.References.Door;
using Models.References.HangarShelving;
using Models.References.HangarStorage;

namespace Models.Data.Hangar.Builder
{
    public class PlotOutbuildingData : CompositeData
    {
        public PlotOutbuildingData(HangarOutbuildingData data)
        {
            CarSlots = Add("cars", new ReferenceMapData<PlotCarSlotDescription, PlotCarData>(PlotCarSlotDescription.Enum, description => new PlotCarData(data.CarSlots[description])));
            HelicopterSlots = Add("helicopters", new ReferenceMapData<PlotHelicopterSlotDescription, PlotHelicopterData>(PlotHelicopterSlotDescription.Enum, description => new PlotHelicopterData(data.HelicopterSlots[description])));

            StorageSlots = Add("storage_slots", data.StorageSlots);
            Storages = Add("storages", new ReferenceMapData<StorageSlotDescription, StorageBoxData>(StorageSlotDescription.Enum, description => new StorageBoxData(data.Storages[description].BreachEntityData)));

            ShelvingSlots = Add("shelving_slots", data.ShelvingSlots);
            Shelvings = Add("shelvings", data.Shelvings);
            
            Doors = Add("doors", new ReferenceMapData<OutbuildingDoorSlotDescription, BuilderDoorData>(OutbuildingDoorSlotDescription.Enum, doorSlotDescription => new BuilderDoorData(data.Doors[doorSlotDescription])));

            IsBlockingColliderActive = Add("is_blocking_collider_active", new BoolData());
            
            RaidLockTs = Add("raid_lock_ts", data.DefenseBlock.RaidLockTs);
            IsRaided = Add("is_raided", data.DefenseBlock.IsRaided);
            IsDefenseBlockSlotsEmpty = Add("is_defense_block_slots_empty", new BoolData());
        }

        public IReferenceMapData<PlotCarSlotDescription, PlotCarData> CarSlots { get; }
        public IReferenceMapData<PlotHelicopterSlotDescription, PlotHelicopterData> HelicopterSlots { get; }

        public IReferenceMapData<StorageSlotDescription, IPrimitiveData<StorageDescription>> StorageSlots { get; }
        public IReferenceMapData<StorageSlotDescription, StorageBoxData> Storages { get; }

        public IReferenceMapData<ShelvingSlotDescription, IPrimitiveData<ShelvingDescription>> ShelvingSlots { get; }
        public IReferenceMapData<ShelvingSlotDescription, ShelvingData> Shelvings { get; }
        
        public IReferenceMapData<OutbuildingDoorSlotDescription, BuilderDoorData> Doors { get; }

        public IPrimitiveData<bool> IsBlockingColliderActive { get; }
        public TimestampData RaidLockTs { get; }
        public BoolData IsRaided { get; }
        public BoolData IsDefenseBlockSlotsEmpty { get; }
    }
}