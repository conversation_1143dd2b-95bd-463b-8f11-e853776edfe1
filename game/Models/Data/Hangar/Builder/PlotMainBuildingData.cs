using Framework.Replication.Data;
using Framework.Replication.Data.Primitive;
using Models.Data.Hangar.CargoWorkbench;
using Models.Data.Hangar.CopFurniture;
using Models.Data.Hangar.MoneyWorkbench;
using Models.Data.Hangar.PlotContainer;
using Models.Data.Hangar.Shelving;
using Models.Data.Hangar.Storage;
using Models.References;
using Models.References.Builder;
using Models.References.CargoWorkbench;
using Models.References.Collection;
using Models.References.Door;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.PlotContainers;
using Models.References.WindowBlinds;

namespace Models.Data.Hangar.Builder
{
    public class PlotMainBuildingData : CompositeData
    {
        public PlotMainBuildingData(HangarMainBuildingData data)
        {
            CopFurnitureSlot = Add("cop_furniture_slot", data.CopFurnitureSlot);
            CopFurnitureUpgrades = Add("cop_furniture_upgrades", data.CopFurnitureUpgrades);

            CollectionTableSlot = Add("collection_table_slot", data.CollectionTableSlot);
            CollectionTable = Add("collection_table", data.CollectionTable);

            CargoWorkbenchSlot = Add("cargo_workbench_slot", data.CargoWorkbenchSlot);
            CargoWorkbench = Add("cargo_workbench", new CargoWorkbenchData(data.CargoWorkbench));

            StorageSlots = Add("storage_slots", data.StorageSlots);
            Storages = Add("storages", new ReferenceMapData<StorageSlotDescription, StorageBoxData>(StorageSlotDescription.Enum, description => new StorageBoxData(data.Storages[description].BreachEntityData)));

            ShelvingSlots = Add("shelving_slots", data.ShelvingSlots);
            Shelvings = Add("shelvings", data.Shelvings);

            CarSlots = Add("cars", new ReferenceMapData<PlotCarSlotDescription, PlotCarData>(PlotCarSlotDescription.Enum, description => new PlotCarData(data.CarSlots[description])));

            Doors = Add("doors", new ReferenceMapData<MainBuildingDoorSlotDescription, BuilderDoorData>(MainBuildingDoorSlotDescription.Enum, doorSlotDescription => new BuilderDoorData(data.Doors[doorSlotDescription])));
            WindowBlinds = Add("window_blinds", new ReferenceMapData<MainBuildingWindowBlindsSlotDescription, IPrimitiveData<bool>>(MainBuildingWindowBlindsSlotDescription.Enum, windowBlinds => data.WindowBlinds[windowBlinds]));
            PlotContainers = Add("plot_containers", new ReferenceMapData<MainBuildingPlotContainerSlotDescription, BuilderPlotContainerData>(MainBuildingPlotContainerSlotDescription.Enum, plotContainerSlotDescription => new BuilderPlotContainerData(data.PlotContainers[plotContainerSlotDescription].BreachEntityData)));

            MoneyWorkbench = Add("money_workbench", data.MoneyWorkbench);

            IsBlockingColliderActive = Add("is_blocking_collider_active", new BoolData());
            
            RaidLockTs = Add("raid_lock_ts", data.DefenseBlock.RaidLockTs);
            IsRaided = Add("is_raided", data.DefenseBlock.IsRaided);
            IsDefenseBlockSlotsEmpty = Add("is_defense_block_slots_empty", new BoolData());
        }

        public IPrimitiveData<CopFurnitureDescription> CopFurnitureSlot { get; }

        public CopFurnitureUpgradesData CopFurnitureUpgrades { get; }

        public IPrimitiveData<CollectionTableDescription> CollectionTableSlot { get; }
        public CollectionTableData CollectionTable { get; }

        public IPrimitiveData<CargoWorkbenchDescription> CargoWorkbenchSlot { get; }
        public CargoWorkbenchData CargoWorkbench { get; }

        public IReferenceMapData<StorageSlotDescription, IPrimitiveData<StorageDescription>> StorageSlots { get; }
        public IReferenceMapData<StorageSlotDescription, StorageBoxData> Storages { get; }

        public IReferenceMapData<ShelvingSlotDescription, IPrimitiveData<ShelvingDescription>> ShelvingSlots { get; }
        public IReferenceMapData<ShelvingSlotDescription, ShelvingData> Shelvings { get; }

        public IReferenceMapData<PlotCarSlotDescription, PlotCarData> CarSlots { get; }
        public IReferenceMapData<MainBuildingDoorSlotDescription, BuilderDoorData> Doors { get; }
        public IReferenceMapData<MainBuildingWindowBlindsSlotDescription, IPrimitiveData<bool>> WindowBlinds { get; }
        public IReferenceMapData<MainBuildingPlotContainerSlotDescription, BuilderPlotContainerData> PlotContainers { get; }
        public MoneyWorkbenchData MoneyWorkbench { get; }

        public IPrimitiveData<bool> IsBlockingColliderActive { get; }
        public TimestampData RaidLockTs { get; }
        public BoolData IsRaided { get; }
        public BoolData IsDefenseBlockSlotsEmpty { get; }
    }
}