using System.Collections.Generic;
using Models.References;
using Models.References.Car;
using Models.References.Cargo;
using Models.References.Grenade;
using Models.References.HangarStorage;
using Models.References.Helicopter;
using Models.References.PlotBuildingDefenseBlock;
using Models.References.PlotContainers;
using Models.References.Vehicle.Trunk;

namespace Models.Replication.EntitiesParamsModel
{
    public class EntitiesParamsModel : IEntitiesParamsModel
    {
        private readonly Dictionary<int, (PlotSlotDescription, int)> _plotKinematicObjectsControllers = new();
        private readonly Dictionary<int, CarDescription> _cars = new();
        private readonly Dictionary<int, HelicopterDescription> _helicopters = new();
        private readonly Dictionary<int, CarTrunkDescription> _carTrunks = new();
        private readonly Dictionary<int, HelicopterTrunkDescription> _helicopterTrunks = new();
        private readonly Dictionary<int, CargoDescription> _cargos = new();
        private readonly Dictionary<int, PlotSlotDescription> _plots = new();
        private readonly Dictionary<int, GrenadeDescription> _grenades = new();
        private readonly Dictionary<int, StorageDescription> _storageInventories = new();
        private readonly Dictionary<int, PlotContainerDescription> _plotContainerInventories = new();
        private readonly Dictionary<int, PlotBuildingDefenseBlockDescription> _plotBuildingDefenseBlocks = new();

        public IReadOnlyDictionary<int, (PlotSlotDescription, int)> PlotKinematicObjectsControllers => _plotKinematicObjectsControllers;
        public IReadOnlyDictionary<int, CarDescription> Cars => _cars;
        public IReadOnlyDictionary<int, HelicopterDescription> Helicopters => _helicopters;
        public IReadOnlyDictionary<int, CarTrunkDescription> CarTrunks => _carTrunks;
        public IReadOnlyDictionary<int, HelicopterTrunkDescription> HelicopterTrunks => _helicopterTrunks;
        public IReadOnlyDictionary<int, CargoDescription> Cargos => _cargos;
        public IReadOnlyDictionary<int, PlotSlotDescription> Plots => _plots;
        public IReadOnlyDictionary<int, GrenadeDescription> Grenades => _grenades;
        public IReadOnlyDictionary<int, StorageDescription> StorageInventories => _storageInventories;
        public IReadOnlyDictionary<int, PlotContainerDescription> PlotContainerInventories => _plotContainerInventories;
        public IReadOnlyDictionary<int, PlotBuildingDefenseBlockDescription> PlotBuildingDefenseBlocks => _plotBuildingDefenseBlocks;

        public void AddPlotKinematicObjectsController(int id, (PlotSlotDescription, int) value)
        {
            _plotKinematicObjectsControllers[id] = value;
        }

        public void AddCar(int id, CarDescription description)
        {
            _cars[id] = description;
        }

        public void AddHelicopter(int id, HelicopterDescription description)
        {
            _helicopters[id] = description;
        }

        public void AddCarTrunk(int id, CarTrunkDescription description)
        {
            _carTrunks[id] = description;
        }

        public void AddHelicopterTrunk(int id, HelicopterTrunkDescription description)
        {
            _helicopterTrunks[id] = description;
        }

        public void AddCargo(int id, CargoDescription description)
        {
            _cargos[id] = description;
        }

        public void AddPlot(int id, PlotSlotDescription description)
        {
            _plots[id] = description;
        }

        public void AddGrenade(int id, GrenadeDescription description)
        {
            _grenades[id] = description;
        }

        public void AddStorageInventory(int id, StorageDescription description)
        {
            _storageInventories[id] = description;
        }

        public void AddPlotContainerInventory(int id, PlotContainerDescription description)
        {
            _plotContainerInventories[id] = description;
        }

        public void AddPlotBuildingDefenseBlock(int id, PlotBuildingDefenseBlockDescription description)
        {
            _plotBuildingDefenseBlocks[id] = description;
        }

        public void Remove(int id)
        {
            _plotKinematicObjectsControllers.Remove(id);
            _cars.Remove(id);
            _helicopters.Remove(id);
            _carTrunks.Remove(id);
            _helicopterTrunks.Remove(id);
            _cargos.Remove(id);
            _plots.Remove(id);
            _grenades.Remove(id);
            _storageInventories.Remove(id);
            _plotContainerInventories.Remove(id);
            _plotBuildingDefenseBlocks.Remove(id);
        }

        public void Clear()
        {
            _plotKinematicObjectsControllers.Clear();
            _cars.Clear();
            _helicopters.Clear();
            _carTrunks.Clear();
            _helicopterTrunks.Clear();
            _cargos.Clear();
            _plots.Clear();
            _grenades.Clear();
            _storageInventories.Clear();
            _plotContainerInventories.Clear();
            _plotBuildingDefenseBlocks.Clear();
        }
    }
}