using Framework.Core.BinaryStream;
using Models.References.Vehicle.Trunk;
using Models.Replication.EntitiesParamsModel;
using Models.Replication.ReplicationIndexes;
using Models.Utils.Data;

namespace Models.Replication.EntityStreamReader
{
    public class HelicopterTrunkEntityStreamReader : RuntimeEntityStreamReader
    {
        private readonly IEntitiesParamsModel _paramsModel;

        public HelicopterTrunkEntityStreamReader(IEntitiesParamsModel paramsModel, int maxCount, ReplicationIndexesMap replicationIndexesMap) : base(maxCount, replicationIndexesMap)
        {
            _paramsModel = paramsModel;
        }

        protected override bool TryReadParams(int entityId, IBinaryReadStream stream)
        {
            if (stream.TryReadReference(HelicopterTrunkDescription.Enum, out var description))
            {
                _paramsModel.AddHelicopterTrunk(entityId, description);
                return true;
            }

            return false;
        }
    }
}