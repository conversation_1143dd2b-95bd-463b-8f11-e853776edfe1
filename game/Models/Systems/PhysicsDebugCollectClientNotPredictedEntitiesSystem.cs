using System.Collections.Generic;
using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.Models.BattleCharacterModel;
using Models.Models.CarModel;
using Models.Models.HelicopterModel;
using Models.Models.KinematicObjectsControllerModel;
using Models.Models.PhysicsDebug.Models.PhysicsDebugEnabledModel;
using Models.Models.WorldEntitiesModel;

namespace Models.Systems
{
    public class PhysicsDebugCollectClientNotPredictedEntitiesSystem : System<int>
    {
        private readonly IPhysicsDebugEnabledModel _physicsDebugEnabledModel;
        private readonly List<ICarModel> _notPredictedCars;
        private readonly List<IHelicopterModel> _notPredictedHelicopters;
        private readonly List<IBattleCharacterModel> _notPredictedBattleCharacters;
        private readonly List<IKinematicObjectsControllerModel> _notPredictedKinematicObjectsControllers;
        private readonly IWorldEntitiesModel _worldEntitiesModel;

        private readonly HashSet<int> _predictedEntities = new();

        public PhysicsDebugCollectClientNotPredictedEntitiesSystem(IPhysicsDebugEnabledModel physicsDebugEnabledModel, List<ICarModel> notPredictedCars, List<IHelicopterModel> notPredictedHelicopters, List<IBattleCharacterModel> notPredictedBattleCharacters, List<IKinematicObjectsControllerModel> notPredictedKinematicObjectsControllers, IWorldEntitiesModel worldEntitiesModel)
        {
            _physicsDebugEnabledModel = physicsDebugEnabledModel;
            _notPredictedCars = notPredictedCars;
            _notPredictedHelicopters = notPredictedHelicopters;
            _notPredictedBattleCharacters = notPredictedBattleCharacters;
            _notPredictedKinematicObjectsControllers = notPredictedKinematicObjectsControllers;
            _worldEntitiesModel = worldEntitiesModel;
        }
    
        protected override void Tick(ITick tick)
        {
            if (!_physicsDebugEnabledModel.IsEnabled)
            {
                return;
            }
        
            _predictedEntities.Clear();
            for (int i = 0; i < _count; i++)
            {
                int entityId = _components[i];

                _predictedEntities.Add(entityId);
            }

            _notPredictedCars.Clear();
            _notPredictedHelicopters.Clear();
            _notPredictedBattleCharacters.Clear();
            _notPredictedKinematicObjectsControllers.Clear();

            Dictionary<int, ICarModel>.Enumerator cars = _worldEntitiesModel.Cars.GetNoAllocEnumerator();
            while (cars.MoveNext())
            {
                KeyValuePair<int, ICarModel> carItem = cars.Current;
                if (_predictedEntities.Contains(carItem.Key))
                {
                    continue;
                }
                _notPredictedCars.Add(carItem.Value);
            }
        
            Dictionary<int, IHelicopterModel>.Enumerator helicopters = _worldEntitiesModel.Helicopters.GetNoAllocEnumerator();
            while (helicopters.MoveNext())
            {
                KeyValuePair<int, IHelicopterModel> helicopterItem = helicopters.Current;
                if (_predictedEntities.Contains(helicopterItem.Key))
                {
                    continue;
                }
                _notPredictedHelicopters.Add(helicopterItem.Value);
            }

            Dictionary<int, IBattleCharacterModel>.Enumerator battleCharacters = _worldEntitiesModel.BattleCharacters.GetNoAllocEnumerator();
            while (battleCharacters.MoveNext())
            {
                KeyValuePair<int, IBattleCharacterModel> battleCharacterItem = battleCharacters.Current;
                if (_predictedEntities.Contains(battleCharacterItem.Key))
                {
                    continue;
                }
                _notPredictedBattleCharacters.Add(battleCharacterItem.Value);
            }

            Dictionary<int, IKinematicObjectsControllerModel>.Enumerator kinematicObjectsControllers = _worldEntitiesModel.KinematicObjectsControllers.GetNoAllocEnumerator();
            while (kinematicObjectsControllers.MoveNext())
            {
                KeyValuePair<int, IKinematicObjectsControllerModel> kinematicObjectsControllerItem = kinematicObjectsControllers.Current;
                if (_predictedEntities.Contains(kinematicObjectsControllerItem.Key))
                {
                    continue;
                }
                _notPredictedKinematicObjectsControllers.Add(kinematicObjectsControllerItem.Value);
            }
        }
    }
}