using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.Models.ProlongedInteractionModel;

namespace Models.Systems
{
    public class PredictFinishRepairBreachSystem : System<IProlongedInteractionModel>
    {
        protected override void Tick(ITick tick)
        {
            long now = tick.Ts;
            for (int i = 0; i < _count; i++)
            {
                ref var repairBreachModel = ref _components[i];

                if (repairBreachModel.IsInProcess && now >= repairBreachModel.EndTs)
                {
                    repairBreachModel.Stop();
                }
            }
        }
    }
}