using System.Collections.Generic;
using System.Numerics;
using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.MathUtils;
using Models.Models.BattleCharacterStateModel;
using Models.Models.BuildingModel;
using Models.Models.Parties;
using Models.Models.RobberQuestModel;
using Models.Models.TransformModel;
using Models.References.Building;
using Models.References.Location;
using Models.References.RobberQuest;
using Models.Utils.Collections;

namespace Models.Systems
{
    public class RobberQuestAutoCompleteSystem : System<(int id, IRobberQuestModel characterRobberQuestModel, IBattleCharacterStateModel stateModel, ITransformModel transformModel)>
    {
        private readonly IParties _parties;
        private readonly LocationDescription _locationDescription;
        private readonly ReadOnlyDictionaryNonAlloc<LocationBuildingDescription, IBuildingModel> _buildings;

        public RobberQuestAutoCompleteSystem(IParties parties, LocationDescription locationDescription, ReadOnlyDictionaryNonAlloc<LocationBuildingDescription, IBuildingModel> buildings)
        {
            _parties = parties;
            _locationDescription = locationDescription;
            _buildings = buildings;
        }
    
        protected override void Tick(ITick tick)
        {
            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var id = component.id;
                var characterRobberQuestModel = component.characterRobberQuestModel;
                var stateModel = component.stateModel;
                var transformModel = component.transformModel;

                if (stateModel.Value is BattleCharacterState.Dead or BattleCharacterState.Arrested) continue;

                var robberQuestModel = _parties.TryGetPartyByEntity(id, out var party) ? party.QuestModel : characterRobberQuestModel;

                if (!robberQuestModel.IsStarted || !robberQuestModel.Task.CanAutoComlete) continue;
            
                Vector3 characterPosition = new Vector3(transformModel.X, transformModel.Y, transformModel.Z);
                switch (robberQuestModel.Task.Type)
                {
                    case RobberQuestTaskType.UnlockLootNodes:
                        CompleteUnlockLootNodes(robberQuestModel, characterPosition);
                        break;
                    case RobberQuestTaskType.ThreatNpc:
                        CompleteThreatNpc(robberQuestModel, characterPosition);
                        break;
                    case RobberQuestTaskType.KillNpc:
                        CompleteKillNpc(robberQuestModel, characterPosition);
                        break;
                    case RobberQuestTaskType.TakeLoot:
                        CompleteTakeLoot(robberQuestModel, characterPosition);
                        break;
                    case RobberQuestTaskType.BreakGlasses:
                        CompleteBreakGlasses(robberQuestModel, characterPosition);
                        break;
                }
            }
        }

        private void CompleteUnlockLootNodes(IRobberQuestModel robberQuestModel, Vector3 characterPosition)
        {
            if (TryGetBuilding(robberQuestModel.Task.UnlockLootNodesTask.BuildingTypes, characterPosition, out var building))
            {
                IncTaskProgress(robberQuestModel, building.CompletedQuestTargetsModel.UnlockedLootNodes);
            }
        }

        private void CompleteThreatNpc(IRobberQuestModel robberQuestModel, Vector3 characterPosition)
        {
            if (TryGetBuilding(robberQuestModel.Task.ThreatNpcTask.BuildingTypes, characterPosition, out var building))
            {
                IncTaskProgress(robberQuestModel, building.CompletedQuestTargetsModel.ThreatNpc);
            }
        }

        private void CompleteKillNpc(IRobberQuestModel robberQuestModel, Vector3 characterPosition)
        {
            if (TryGetBuilding(robberQuestModel.Task.KillNpcTask.BuildingTypes, characterPosition, out var building))
            {
                IncTaskProgress(robberQuestModel, building.CompletedQuestTargetsModel.KilledNpc);
            }
        }
    
        private void CompleteTakeLoot(IRobberQuestModel robberQuestModel, Vector3 characterPosition)
        {
            if (TryGetBuilding(robberQuestModel.Task.TakeLootTask.BuildingTypes, characterPosition, out var building))
            {
                IncTakeLootTaskProgress(robberQuestModel, building.CompletedQuestTargetsModel.TakenLoot);
            }
        }

        private void CompleteBreakGlasses(IRobberQuestModel robberQuestModel, Vector3 characterPosition)
        {
            if (TryGetBuilding(robberQuestModel.Task.BreakGlassesTask.BuildingTypes, characterPosition, out var building))
            {
                IncTaskProgress(robberQuestModel, building.CompletedQuestTargetsModel.BrokenGlasses);
            }
        }

        private void IncTaskProgress(IRobberQuestModel robberQuestModel, HashSet<int> completedQuestTargets)
        {
            foreach (int targetId in completedQuestTargets)
            {
                if (!robberQuestModel.ContainsTarget(targetId))
                {
                    robberQuestModel.AutoIncTaskProgress(targetId);
                }
            }
        }

        private void IncTakeLootTaskProgress(IRobberQuestModel robberQuestModel, Dictionary<int, int> takenLoot)
        {
            foreach (int lootEntityId in takenLoot.Keys)
            {
                int lootProgress = takenLoot[lootEntityId];
                int questProgress = robberQuestModel.GetTargetProgress(lootEntityId);
            
                if (lootProgress > questProgress)
                {
                    int progress = lootProgress - questProgress;
                    robberQuestModel.AutoIncTaskProgress(lootEntityId, progress);
                }
            }
        }

        private bool TryGetBuilding(BuildingTypeDescription[] buildingTypes, Vector3 characterPosition, out IBuildingModel building)
        {
            foreach (var buildingType in buildingTypes)
            {
                if (_locationDescription.TryGetBuildingsByType(buildingType, out var buildingDescriptions))
                {
                    foreach (var buildingDescription in buildingDescriptions)
                    {
                        if (CheckPointInBuilding(characterPosition, buildingDescription))
                        {
                            return _buildings.TryGetValue(buildingDescription, out building);
                        }
                    }
                }
            }

            building = null;
            return false;
        }

        private bool CheckPointInBuilding(Vector3 point, LocationBuildingDescription buildingDescription)
        {
            return buildingDescription.Aabb.Contains(point) && OBBExtension.IsPointInsideRegion(buildingDescription.Region, point);
        }
    }
}