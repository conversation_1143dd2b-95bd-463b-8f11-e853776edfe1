using System;
using System.Numerics;
using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.MathUtils;
using Models.Models.BattleCharacterStateModel;
using Models.Models.BattleStanceModel;
using Models.Models.CharacterVelocityModel;
using Models.Models.KnockdownModel;
using Models.Models.LookDirectionModel;
using Models.Models.TransformModel;
using Models.Models.TurningModel;
using Models.References;
using Models.References.Character;

namespace Models.Systems
{
    public class TurningSystem : System<(ITurningModel turningModel, ICharacterVelocityModel velocityModel, ITransformModel transformModel, ILookDirectionModel lookDirectionModel, BattleCharacterSkeletonDescription battleCharacterSkeletonDescription, IBattleStanceModel battleStanceModel, IBattleCharacterStateModel stateModel, IKnockdownModel knockdownModel)>
    {
        private const float _eps = 0.00001f;

        protected override void Tick(ITick tick)
        {
            long now = tick.Ts;
            
            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var turningModel = component.turningModel;
                var velocityModel = component.velocityModel;
                var transformModel = component.transformModel;
                var lookDirectionModel = component.lookDirectionModel;
                var battleCharacterSkeletonDescription = component.battleCharacterSkeletonDescription;
                var battleStanceModel = component.battleStanceModel;
                var stateModel = component.stateModel;
                var knockdownModel = component.knockdownModel;

                BattleCharacterState state = stateModel.Value;
                if (!(state == BattleCharacterState.Idle && !knockdownModel.IsActive) && state != BattleCharacterState.Dead && state != BattleCharacterState.KnockedOut)
                {
                    turningModel.Stop();
                    continue;
                }

                if (battleStanceModel.BattleStance == BattleStanceDescription.Disabled)
                {
                    turningModel.Stop();
                    continue;
                }
                
                Vector3 velocity = velocityModel.Value;
                bool isStanding = velocity.LengthSquared() <= _eps;
                if (!isStanding)
                {
                    turningModel.Stop();
                    continue;
                }
                
                if (turningModel.StartTs + battleCharacterSkeletonDescription.TurnDuration <= now)
                {
                    turningModel.Stop();
                }
                
                float lookYaw = lookDirectionModel.Yaw;
                if (battleStanceModel.BattleStance == BattleStanceDescription.Boxing)
                {
                    lookYaw += battleCharacterSkeletonDescription.BoxingYawOffset;
                }
                
                float yaw = transformModel.Yaw;
                float deltaYaw = MathExtension.DeltaAngleRad(lookYaw, yaw);
                float turnAngle = battleCharacterSkeletonDescription.TurnAngle;
                bool isDeltaOverThreshold = Math.Abs(deltaYaw) > turnAngle;
                
                bool isTurning = isDeltaOverThreshold;
                if (isTurning && !turningModel.InProcessState)
                {
                    bool isLeftSide = deltaYaw > 0;
                    float originYaw = transformModel.Yaw;
                    turningModel.Start(now, isLeftSide, originYaw);
                }
            }
        }
    }
}