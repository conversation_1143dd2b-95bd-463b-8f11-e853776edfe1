using System.Collections.Generic;
using System.Numerics;
using Models.MathUtils;
using Models.Models.EntityIdGenerator;
using Models.Models.MainBuildingModel;
using Models.Models.MoneyWorkbenchRules;
using Models.Models.PlotModel;
using Models.Models.PositionModel;
using Models.Models.SettlementBonusModel;
using Models.Models.WorldEntitiesModel;
using Models.References;
using Models.References.Builder;
using Models.References.Door;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.Location;
using Models.References.Location.LocationTraders;
using Models.References.PlotBuildingDefenseBlock;
using Models.References.PlotContainers;
using Models.References.Trader;
using Models.References.WindowBlinds;
using Models.Utils.Extensions.Vehicles;
using Models.Utils.Extensions.Vehicles.Car;

namespace Models.Utils.Extensions.Builder
{
    public static class MainBuildingExtensions
    {
        public static MainBuildingEntityIds BuildEntityIds(IEntityIdGenerator entityIdGenerator)
        {
            var copFurnitureSlot = entityIdGenerator.Generate();
            var copFurnitureEntityIds = CopFurnitureExtensions.BuildEntityIds(entityIdGenerator);

            var collectionTableSlot = entityIdGenerator.Generate();
            var collectionTableEntityIds = CollectionTableExtensions.BuildEntityIds(entityIdGenerator);

            var cargoWorkbenchSlot = entityIdGenerator.Generate();
            var cargoWorkbenchEntityIds = CargoWorkbenchExtensions.BuildEntityIds(entityIdGenerator);

            var storageSlots = new Dictionary<StorageSlotDescription, int>(StorageSlotDescription.Enum.Count);
            foreach (var slot in StorageSlotDescription.Enum)
            {
                storageSlots.Add(slot, entityIdGenerator.Generate());
            }

            var storages = new Dictionary<StorageSlotDescription, StorageEntityIds>(StorageSlotDescription.Enum.Count);
            var storageSlotById = new Dictionary<int, StorageSlotDescription>(StorageSlotDescription.Enum.Count);
            foreach (var slot in StorageSlotDescription.Enum)
            {
                var storageEntityIds = StorageBoxExtensions.BuildEntityIds(entityIdGenerator);
                storages.Add(slot, storageEntityIds);
                storageSlotById.Add(storageEntityIds.ModelEntityId, slot);
            }
            
            var shelvingSlots = new Dictionary<ShelvingSlotDescription, int>(ShelvingSlotDescription.Enum.Count);
            foreach (var slot in ShelvingSlotDescription.Enum)
            {
                shelvingSlots.Add(slot, entityIdGenerator.Generate());
            }

            var shelvings = new Dictionary<ShelvingSlotDescription, ShelvingEntityIds>(ShelvingSlotDescription.Enum.Count);
            foreach (var slot in ShelvingSlotDescription.Enum)
            {
                shelvings.Add(slot, ShelvingExtensions.BuildEntityIds(entityIdGenerator));
            }

            var npcSlot = entityIdGenerator.Generate();
            var planningBoard = entityIdGenerator.Generate();

            var carGarageEntityIds = PlotGarageExtensions.BuildEntityIds(entityIdGenerator, PlotCarSlotDescription.Enum);
            var doorSlots = new Dictionary<MainBuildingDoorSlotDescription, int>(MainBuildingDoorSlotDescription.Enum.Count);
            foreach (var slot in MainBuildingDoorSlotDescription.Enum)
            {
                var doorEntityId = BuilderDoorExtensions.BuildEntityId(entityIdGenerator);
                doorSlots.Add(slot, doorEntityId);
            }

            var windowBlinds = new Dictionary<MainBuildingWindowBlindsSlotDescription, int>(MainBuildingWindowBlindsSlotDescription.Enum.Count);
            foreach (var slot in MainBuildingWindowBlindsSlotDescription.Enum)
            {
                windowBlinds.Add(slot, entityIdGenerator.Generate());
            }
            
            var plotContainerIds = new Dictionary<MainBuildingPlotContainerSlotDescription, PlotContainerEntityIds>(MainBuildingPlotContainerSlotDescription.Enum.Count);
            var plotContainerSlotById = new Dictionary<int, MainBuildingPlotContainerSlotDescription>(MainBuildingPlotContainerSlotDescription.Enum.Count);
            foreach (var slot in MainBuildingPlotContainerSlotDescription.Enum)
            {
                var plotContainerEntityIds = BuilderPlotContainerExtensions.BuildEntityIds(entityIdGenerator);
                plotContainerIds.Add(slot, plotContainerEntityIds);
                plotContainerSlotById.Add(plotContainerEntityIds.ModelEntityId, slot);
            }
            
            var moneyWorkbench = MoneyWorkbenchExtensions.BuildEntityIds(entityIdGenerator);
            var defenseBlockEntityIds = PlotBuildingDefenseBlockExtensions.BuildEntityIds(entityIdGenerator);

            return new MainBuildingEntityIds(copFurnitureSlot, copFurnitureEntityIds, collectionTableSlot, collectionTableEntityIds, cargoWorkbenchSlot, cargoWorkbenchEntityIds, storageSlots, storages, storageSlotById,  plotContainerSlotById, plotContainerIds, shelvingSlots, shelvings, npcSlot, planningBoard, carGarageEntityIds, doorSlots, windowBlinds, moneyWorkbench, defenseBlockEntityIds);
        }

        public static LocationMainBuildingDescription BuildLocationDescription(IPlotModel plotModel, MainBuildingDescription description, MainBuildingEntityIds entityIds)
        {
            var mainBuildingSlot = plotModel.PlotDescription.MainBuildingSlot;
            Vector3 worldPosition = mainBuildingSlot.Position;
            Quaternion worldOrientation = mainBuildingSlot.Orientation;
            var slotsInteractPosition = worldPosition + Vector3.Transform(description.FurnitureSlotsInteractPosition, worldOrientation);

            var copFurniturePoint = BuilderExtensions.BuildOrientedPoint(description.CopFurnitureSlot, worldPosition, worldOrientation);
            var collectionTablePoint = BuilderExtensions.BuildOrientedPoint(description.CollectionTableSlot, worldPosition, worldOrientation);
            var cargoWorkbenchPoint = BuilderExtensions.BuildOrientedPoint(description.CargoWorkbenchSlot, worldPosition, worldOrientation);

            var storagePoints = new Dictionary<StorageSlotDescription, OrientedPoint>();
            foreach (var (slot, plotConstructionSlotDescription) in description.StorageSlots)
            {
                storagePoints.Add(slot, BuilderExtensions.BuildOrientedPoint(plotConstructionSlotDescription, worldPosition, worldOrientation));
            }

            var plotContainerPoints = new Dictionary<MainBuildingPlotContainerSlotDescription, OrientedPoint>();
            foreach (var (slot, plotContainerLocationDescription) in description.PlotContainers)
            {
                plotContainerPoints.Add(slot, BuilderExtensions.BuildOrientedPoint(plotContainerLocationDescription, worldPosition, worldOrientation));
            }
            
            var shelvingPoints = new Dictionary<ShelvingSlotDescription, OrientedPoint>();
            foreach (var (slot, plotConstructionSlotDescription) in description.ShelvingSlots)
            {
                shelvingPoints.Add(slot, BuilderExtensions.BuildOrientedPoint(plotConstructionSlotDescription, worldPosition, worldOrientation));
            }

            var npcPoint = worldPosition + Vector3.Transform(description.NpcPosition, worldOrientation);
            var planningBoardPoint = worldPosition + Vector3.Transform(description.PlanningBoardPosition, worldOrientation);

            var carGarageDescription = PlotGarageExtensions.BuildLocationDescription(description.CarGarage, entityIds.CarGarageEntityIds, worldPosition, worldOrientation);

            var builderDoors = new Dictionary<MainBuildingDoorSlotDescription, LocationBuilderDoorDescription>();
            foreach (var (slot, doorDescription) in description.Doors)
            {
                var centerPosition = worldPosition + Vector3.Transform(doorDescription.CenterPosition, worldOrientation);
                var point = new OrientedPoint(worldPosition + Vector3.Transform(doorDescription.Position, worldOrientation), worldOrientation * doorDescription.Orientation);
                var interactAreas = doorDescription.InteractAreas.Transform(worldPosition, worldOrientation);

                var builderDoorEntityIds = entityIds.DoorIds[slot];
                var locationBuilderDescription = BuilderDoorExtensions.BuildLocationBuilderDescription(builderDoorEntityIds, point, centerPosition, interactAreas, description.Doors[slot]);
                builderDoors.Add(slot, locationBuilderDescription);
            }
            
            var windowBlinds = new Dictionary<MainBuildingWindowBlindsSlotDescription, LocationWindowBlindsDescription>();
            foreach (var (slot, windowBlindsDescription) in description.WindowBlinds)
            {
                var windowBlindEntityIds = entityIds.WindowBlindsIds[slot];
                var centerPosition = new PositionModel(worldPosition + Vector3.Transform(windowBlindsDescription.CenterPosition, worldOrientation));
                var interactAreas = windowBlindsDescription.InteractAreas.Transform(worldPosition, worldOrientation);
                windowBlinds.Add(slot, new LocationWindowBlindsDescription(windowBlindEntityIds, centerPosition, interactAreas));
            }

            var moneyWorkbenchDescription = MoneyWorkbenchExtensions.BuildLocationDescription(description.MoneyWorkbench, entityIds.MoneyWorkbench, worldPosition, worldOrientation);

            var obbs = description.Obbs.Transform(worldPosition, worldOrientation);
            var aabb = OBBExtension.CalculateRegionAabb(obbs);

            var spawnPoint = new OrientedPoint(worldPosition + Vector3.Transform(description.SpawnPoint.Position, worldOrientation), worldOrientation);

            LocationPlotBuildingDefenseBlockDescription defenseBlockDescription = null;
            if (description.DefenseSettings.CanBeRaided)
            {
                defenseBlockDescription = PlotBuildingDefenseBlockExtensions.BuildLocationDescription(entityIds.DefenseBlockEntityIds, worldPosition, worldOrientation, description.DefenseInteractionPoint, description.DefenseSettings.DefenseBlock);
            }

            return new LocationMainBuildingDescription(slotsInteractPosition, copFurniturePoint, collectionTablePoint, cargoWorkbenchPoint, storagePoints, plotContainerPoints, shelvingPoints, npcPoint, planningBoardPoint, carGarageDescription, builderDoors, windowBlinds, moneyWorkbenchDescription, obbs, aabb, spawnPoint, defenseBlockDescription);
        }

        public static LocationNpcDescription BuildNpcDescription(LocationMainBuildingDescription locationMainBuildingDescription, MainBuildingEntityIds entityIds)
        {
            return new LocationNpcDescription("0", 0, entityIds.Npc, NPCDescription.Laptop, locationMainBuildingDescription.Npc, 
                Quaternion.Identity, TraderInventoryDescription.Laptop, null);
        }
        
        public static LocationPlanningBoardDescription BuildPlanningBoardDescription(LocationMainBuildingDescription locationMainBuildingDescription, MainBuildingEntityIds entityIds)
        {
            return new LocationPlanningBoardDescription(entityIds.PlanningBoard, locationMainBuildingDescription.PlanningBoard);
        }

        public static bool HasBreachedItemWithActiveTimer(IMainBuildingModel mainBuildingModel, long now)
        {
            foreach (var storageBoxModel in mainBuildingModel.Storages.Values)
            {
                if (BreachEntityExtensions.IsBreachTimerActive(storageBoxModel.BreachStateEntityModel, now))
                {
                    return true;
                }
            }

            return false;
        }

        public static bool HasLootEntity(IPlotModel plotModel)
        {
            return plotModel.MainBuildingLoot.IsActive;
        }

        public static bool HasLootInside(IPlotModel plotModel)
        {
            var mainBuildingModel = plotModel.MainBuilding;
            if (mainBuildingModel.CopFurnitureSlot.Value != null)
            {
                return true;
            }

            if (mainBuildingModel.CollectionTableSlot.Value != null)
            {
                return true;
            }

            if (mainBuildingModel.CargoWorkbenchSlot.Value != null)
            {
                return true;
            }

            var moneyWorkbench = mainBuildingModel.MoneyWorkbench;
            if (moneyWorkbench.DirtyAmount > 0 || moneyWorkbench.CleanAmount > 0)
            {
                return true;
            }

            var moneyWorkbenchUpgradesModel = moneyWorkbench.UpgradesModel;
            if (moneyWorkbenchUpgradesModel.LaunderingRateSlot.Value != null)
            {
                return true;
            }

            foreach (var moneyWorkbenchSpeedupSlotUpgradeSlotModel in moneyWorkbenchUpgradesModel.SpeedupSlots.Values)
            {
                if (moneyWorkbenchSpeedupSlotUpgradeSlotModel.Value != null)
                {
                    return true;
                }
            }

            var description = mainBuildingModel.DescriptionModel.Value;
            foreach (var plotCarSlotDescription in description.CarGarageDescription.Slots.Keys)
            {
                var plotCarModel = mainBuildingModel.CarGarage.Cars[plotCarSlotDescription];
                if (plotCarModel.HasVehicle)
                {
                    return true;
                }
            }

            foreach (var storageSlotDescription in description.StoragePoints.Keys)
            {
                var storageSlotModel = mainBuildingModel.StorageSlots[storageSlotDescription];
                if (storageSlotModel.Value != null)
                {
                    return true;
                }
            }
            
            foreach (var plotContainerSlotDescription in description.PlotContainerPoints.Keys)
            {
                var plotContainerModel = mainBuildingModel.PlotContainers[plotContainerSlotDescription];
                if (!plotContainerModel.IsEmpty.Value)
                {
                    return true;
                }
            }

            foreach (var shelvingSlotDescription in description.ShelvingPoints.Keys)
            {
                var shelvingSlotModel = mainBuildingModel.ShelvingSlots[shelvingSlotDescription];
                if (shelvingSlotModel.Value != null)
                {
                    return true;
                }
            }

            if (description.DefenseBlockDescription != null)
            {
                var defenseBlockPublicModel = mainBuildingModel.DefenseBlockPublicModel;
                if (!defenseBlockPublicModel.IsSlotsEmpty.Value)
                    return true;
            }
            
            return false;
        }

        public static int GetAssetsTotalValue(IMainBuildingModel mainBuildingModel, int clearCashRate, float taxCashModifier, IWorldEntitiesModel worldEntitiesModel)
        {
            var value = 0;

            var copFurnitureDescription = mainBuildingModel.CopFurnitureSlot.Value;
            if (copFurnitureDescription != null)
            {
                value += RealmAssetsValueExtensions.GetItemAssetsValue(copFurnitureDescription.GetInventoryItem());
                value += CopFurnitureExtensions.GetAssetsValue(mainBuildingModel.CopFurniture);
            }

            var collectionTableDescription = mainBuildingModel.CollectionTableSlot.Value;
            if (collectionTableDescription != null)
            {
                value += RealmAssetsValueExtensions.GetItemAssetsValue(collectionTableDescription.GetInventoryItem());
                value += CollectionTableExtensions.GetAssetsValue(mainBuildingModel.CollectionTableModel);
            }

            var cargoWorkbenchDescription = mainBuildingModel.CargoWorkbenchSlot.Value;
            if (cargoWorkbenchDescription != null)
            {
                value += RealmAssetsValueExtensions.GetItemAssetsValue(cargoWorkbenchDescription.GetInventoryItem());
                value += CargoWorkbenchExtensions.GetAssetsValue(mainBuildingModel.CargoWorkbenchModel, clearCashRate, taxCashModifier, worldEntitiesModel);
            }

            var moneyWorkbench = mainBuildingModel.MoneyWorkbench;
            value += MoneyWorkbenchExtensions.GetAssetsValue(moneyWorkbench, clearCashRate, taxCashModifier);

            var description = mainBuildingModel.DescriptionModel.Value;
            foreach (var plotCarSlotDescription in description.CarGarageDescription.Slots.Keys)
            {
                var plotCarModel = mainBuildingModel.CarGarage.Cars[plotCarSlotDescription];
                if (plotCarModel.HasVehicle)
                {
                    var carDescription = plotCarModel.VehicleDescription;
                    value += RealmAssetsValueExtensions.GetItemAssetsValue(carDescription.CarSlot.InventoryItem);

                    if (!plotCarModel.IsVehicleSpawned && plotCarModel.HasTrunk)
                    {
                        value += PlotVehicleTrunkExtensions.GetAssetsValue(plotCarModel.Id, clearCashRate, taxCashModifier, worldEntitiesModel);
                    }

                    value += CarUpgradesExtension.GetAssetsValue(plotCarModel.UpgradesSlots);
                }
            }

            foreach (var storageSlotDescription in description.StoragePoints.Keys)
            {
                var storageSlotModel = mainBuildingModel.StorageSlots[storageSlotDescription];
                var storageDescription = storageSlotModel.Value;
                if (storageDescription != null)
                {
                    value += RealmAssetsValueExtensions.GetItemAssetsValue(storageDescription.GetInventoryItem());

                    var storageBoxModel = mainBuildingModel.Storages[storageSlotDescription];
                    value += StorageBoxExtensions.GetAssetsValue(storageBoxModel, clearCashRate, taxCashModifier, worldEntitiesModel);
                }
            }
            
            foreach (var plotContainerSlotDescription in description.PlotContainerPoints.Keys)
            {
                var plotContainerModel = mainBuildingModel.PlotContainers[plotContainerSlotDescription];
                value += PlotContainerExtensions.GetAssetsValue(plotContainerModel, clearCashRate, taxCashModifier, worldEntitiesModel);
            }

            foreach (var shelvingSlotDescription in description.ShelvingPoints.Keys)
            {
                var shelvingSlotModel = mainBuildingModel.ShelvingSlots[shelvingSlotDescription];
                var shelvingDescription = shelvingSlotModel.Value;
                if (shelvingDescription != null)
                {
                    value += RealmAssetsValueExtensions.GetItemAssetsValue(shelvingDescription.GetInventoryItem());
                }
            }

            if (description.DefenseBlockDescription != null)
            {
                var defenseBlockPrivateModel = worldEntitiesModel.PlotBuildingDefenseBlockPrivateModels[description.DefenseBlockDescription.EntityIds.PrivateModelEntityId];
                value += PlotBuildingDefenseBlockExtensions.GetAssetsValue(defenseBlockPrivateModel);
            }

            return value;
        }

        public static MoneyWorkbenchState GetMoneyWorkbenchState(IPlotModel plotModel, ISettlementBonusModel settlementBonusModel, long now)
        {
            var clanBonus = plotModel.ClanBonus;
            var ownedSettlementsBuildingsLaunderingRateClearCashBonus = plotModel.OwnedSettlementsBuildingsLaunderingRateClearCashBonus;
            var moneyWorkbenchRateBonuses = MoneyWorkbenchExtensions.GetLaunderingRateBonuses(clanBonus, ownedSettlementsBuildingsLaunderingRateClearCashBonus, settlementBonusModel);
            return MoneyWorkbenchRules.GetState(now, plotModel.MainBuilding.MoneyWorkbench, moneyWorkbenchRateBonuses);
        }
    }
}