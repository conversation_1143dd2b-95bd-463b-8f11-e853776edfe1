using System.Collections.Generic;
using System.Numerics;
using Models.MathUtils;
using Models.Models.CompoundWallModel;
using Models.Models.EntityIdGenerator;
using Models.Models.PlotModel;
using Models.Models.WorldEntitiesModel;
using Models.References.Builder;
using Models.References.Door;
using Models.References.Location;
using Models.References.PlotBuildingDefenseBlock;

namespace Models.Utils.Extensions.Builder
{
    public static class CompoundWallExtensions
    {
        public static CompoundWallEntityIds BuildEntityIds(IEntityIdGenerator entityIdGenerator)
        {
            var doorSlots = new Dictionary<CompoundWallDoorSlotDescription, int>(CompoundWallDoorSlotDescription.Enum.Count);
            foreach (var slot in CompoundWallDoorSlotDescription.Enum)
            {
                var doorEntityId = BuilderDoorExtensions.BuildEntityId(entityIdGenerator);
                doorSlots.Add(slot, doorEntityId);
            }

            var defenseBlockEntityIds = PlotBuildingDefenseBlockExtensions.BuildEntityIds(entityIdGenerator);

            return new CompoundWallEntityIds(doorSlots, defenseBlockEntityIds);
        }

        public static LocationCompoundWallDescription BuildLocationDescription(CompoundWallDescription description, IPlotModel plotModel, CompoundWallEntityIds entityIds)
        {
            var compoundWallSlot = plotModel.PlotDescription.CompoundWallSlot;
            Vector3 worldPosition = compoundWallSlot.Position;
            Quaternion worldOrientation = compoundWallSlot.Orientation;
            
            var builderDoors = new Dictionary<CompoundWallDoorSlotDescription, LocationBuilderDoorDescription>();
            foreach (var (slot, doorDescription) in description.Doors)
            {
                var builderDoorEntityIds = entityIds.DoorIds[slot];
                var centerPosition = worldPosition + Vector3.Transform(doorDescription.CenterPosition, worldOrientation);
                var point = new OrientedPoint(worldPosition + Vector3.Transform(doorDescription.Position, worldOrientation), worldOrientation * doorDescription.Orientation);
                var interactAreas = doorDescription.InteractAreas.Transform(worldPosition, worldOrientation);
                
                var locationBuilderDescription = BuilderDoorExtensions.BuildLocationBuilderDescription(builderDoorEntityIds, point, centerPosition, interactAreas, description.Doors[slot]);
                builderDoors.Add(slot, locationBuilderDescription);
            }

            var obbs = description.Obbs.Transform(worldPosition, worldOrientation);
            var aabb = OBBExtension.CalculateRegionAabb(obbs);
            
            LocationPlotBuildingDefenseBlockDescription defenseBlockDescription = null;
            if (description.DefenseSettings.CanBeRaided)
            {
                defenseBlockDescription = PlotBuildingDefenseBlockExtensions.BuildLocationDescription(entityIds.DefenseBlockEntityIds, worldPosition, worldOrientation, description.DefenseInteractionPoint, description.DefenseSettings.DefenseBlock);
            }

            return new LocationCompoundWallDescription(builderDoors, obbs, aabb, defenseBlockDescription);
        }

        public static bool HasLootEntity(IPlotModel plotModel)
        {
            return plotModel.CompoundWallLoot.IsActive;
        }

        public static bool HasLootInside(ICompoundWallModel compoundWallModel)
        {
            var description = compoundWallModel.DescriptionModel.Value;

            if (description.DefenseBlockDescription != null)
            {
                var defenseBlockPublicModel = compoundWallModel.DefenseBlockPublicModel;
                if (!defenseBlockPublicModel.IsSlotsEmpty.Value)
                    return true;
            }
            
            return false;
        }

        public static int GetAssetsTotalValue(ICompoundWallModel compoundWallModel, IWorldEntitiesModel worldEntitiesModel)
        {
            var value = 0;
            
            var description = compoundWallModel.DescriptionModel.Value;
            
            if (description.DefenseBlockDescription != null)
            {
                var defenseBlockPrivateModel = worldEntitiesModel.PlotBuildingDefenseBlockPrivateModels[description.DefenseBlockDescription.EntityIds.PrivateModelEntityId];
                value += PlotBuildingDefenseBlockExtensions.GetAssetsValue(defenseBlockPrivateModel);
            }
            
            return value;
        }
    }
}