using System.Numerics;
using Models.Models.EntityIdGenerator;
using Models.Models.PlotBuildingDefenseBlockPrivateModel;
using Models.Models.PlotBuildingDefenseBlockPublicModel;
using Models.References.Builder;
using Models.References.PlotBuildingDefenseBlock;

namespace Models.Utils.Extensions
{
    public static class PlotBuildingDefenseBlockExtensions
    {
        public static PlotBuildingDefenseBlockEntityIds BuildEntityIds(IEntityIdGenerator entityIdGenerator)
        {
            var panelEntityId = entityIdGenerator.Generate();
            var blockEntityId = entityIdGenerator.Generate();

            return new PlotBuildingDefenseBlockEntityIds(panelEntityId, blockEntityId);
        }
        
        public static LocationPlotBuildingDefenseBlockDescription BuildLocationDescription(PlotBuildingDefenseBlockEntityIds entityIds, Vector3 worldPosition, Quaternion worldOrientation, Vector3 localPosition, PlotBuildingDefenseBlockDescription description)
        {
            var position = worldPosition + Vector3.Transform(localPosition, worldOrientation);
            return new LocationPlotBuildingDefenseBlockDescription(entityIds, position, description);
        }

        public static int GetAssetsValue(IPlotBuildingDefenseBlockPrivateModel defenseBlockPrivateModel)
        {
            int value = 0;

            foreach (var slot in defenseBlockPrivateModel.SlotsModel)
            {
                if (!slot.IsEmpty)
                {
                    value += RealmAssetsValueExtensions.GetItemAssetsValue(slot.Item.GetItem());
                }
            }

            return value;
        }

        public static void UpdateDefenseBlockSlotsIsEmptyFlag(this IPlotBuildingDefenseBlockPublicModel publicModel, IPlotBuildingDefenseBlockPrivateModel privateModel)
        {
            var isEmpty = true;
            foreach (var slot in privateModel.SlotsModel)
            {
                if (slot.IsEmpty) continue;
                isEmpty = false;
                break;
            }
            if (publicModel.IsSlotsEmpty.Value != isEmpty)
                publicModel.IsSlotsEmpty.Value = isEmpty;
        }
    }
}