using Models.Models.HangarOwnerModel;
using Models.Models.IPartyMemberAccessModel;
using Models.Models.WorldEntitiesModel;
using Models.References.Vehicle.Trunk;

namespace Models.Utils.Extensions.Vehicles
{
    public static class PlotVehicleTrunkExtensions
    {
        public static bool HasAccess(int entityId, int characterId, IWorldEntitiesModel worldEntitiesModel, IHangarOwnerModel hangarOwnerModel, IPartyMemberAccessModel partyMemberAccessModel)
        {
            if (!worldEntitiesModel.PlotSlotByEntity.TryGetValue(entityId, out var plotSlotDescription))
            {
                return false;
            }

            var isOwner = hangarOwnerModel.IsOwnerByEntityId(plotSlotDescription, characterId);
            if (isOwner)
            {
                return true;
            }

            var hasAccessThroughParty = partyMemberAccessModel.HasPlotAccess(characterId, entityId);
            if (hasAccessThroughParty)
            {
                return true;
            }

            return false;
        }

        public static int GetAssetsValue(int vehicleEntityId, int clearCashRate, float taxCashModifier, IWorldEntitiesModel worldEntitiesModel)
        {
            int vehicleTrunkEntityId = VehicleTrunkSettingsDescription.GetVehicleTrunkEntityId(vehicleEntityId);
            var vehicleTrunkInventoriesModel = worldEntitiesModel.VehicleTrunkInventories[vehicleTrunkEntityId];

            return RealmAssetsValueExtensions.GetInventoryAssetsValue(vehicleTrunkInventoriesModel.Inventory, clearCashRate, taxCashModifier);
        }
    }
}