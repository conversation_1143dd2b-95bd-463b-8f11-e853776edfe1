using System.Collections.Generic;
using Models.Models.BattleCharacterModel;
using Models.Models.BreakableColliderSetModel;
using Models.Models.CameraModel;
using Models.Models.CarModel;
using Models.Models.DoorModel;
using Models.Models.HelicopterModel;
using Models.Models.KinematicObjectsControllerModel;
using Models.Models.LootObjectModel;
using Models.Models.MetalDetectorModel;
using Models.Models.PlotContainerModel;
using Models.Models.StorageBoxModel;
using Models.Models.VehicleModel;
using Models.Models.VigilantNpcModel;
using Models.Physics.PhysicsBodies;

namespace Models.Models.PhysicsBodiesModel
{
    public class PhysicsBodiesModel : IPhysicsBodiesModel
    {
        private readonly Dictionary<PhysicsBodyId, int> _bodyIdToEntityId = new();
        private readonly Dictionary<PhysicsBodyId, IBattleCharacterModel> _battleCharacters = new();
        private readonly Dictionary<PhysicsBodyId, IDoorModel> _doors = new();
        private readonly Dictionary<PhysicsBodyId, IKinematicObjectsControllerModel> _kinematicObjectControllers = new();
        private readonly Dictionary<PhysicsBodyId, IVehicleModel> _vehicles = new();
        private readonly Dictionary<PhysicsBodyId, ICarModel> _cars = new();
        private readonly Dictionary<PhysicsBodyId, IHelicopterModel> _helicopters = new();
        private readonly Dictionary<PhysicsBodyId, IBreakableColliderSetModel> _breakableColliderSetModels = new();
        private readonly Dictionary<PhysicsBodyId, ICameraModel> _cameras = new();
        private readonly Dictionary<PhysicsBodyId, IVigilantNpcModel> _vigilantNpcs = new();
        private readonly Dictionary<PhysicsBodyId, IStorageBoxModel> _storages = new();
        private readonly Dictionary<PhysicsBodyId, int> _hideObjects = new();
        private readonly Dictionary<PhysicsBodyId, int> _staticVehicles = new();
        private readonly Dictionary<PhysicsBodyId, ILootObjectModel> _lootObjects = new();
        private readonly HashSet<PhysicsBodyId> _triggers = new();

        public bool TryGetEntityId(PhysicsBodyId bodyId, out int entityId)
        {
            return _bodyIdToEntityId.TryGetValue(bodyId, out entityId);
        }

        public bool TryGetBattleCharacter(PhysicsBodyId bodyId, out IBattleCharacterModel battleCharacterModel)
        {
            return _battleCharacters.TryGetValue(bodyId, out battleCharacterModel);
        }

        public void AddBattleCharacter(PhysicsBodyId bodyId, IBattleCharacterModel battleCharacterModel)
        {
            _bodyIdToEntityId.Add(bodyId, battleCharacterModel.Id);
            _battleCharacters.Add(bodyId, battleCharacterModel);
        }

        public void RemoveBattleCharacter(PhysicsBodyId bodyId)
        {
            _bodyIdToEntityId.Remove(bodyId);
            _battleCharacters.Remove(bodyId);
        }

        public bool TryGetDoor(PhysicsBodyId bodyId, out IDoorModel doorModel)
        {
            return _doors.TryGetValue(bodyId, out doorModel);
        }

        public void AddDoor(PhysicsBodyId bodyId, IDoorModel doorModel)
        {
            _bodyIdToEntityId.Add(bodyId, doorModel.Id);
            _doors.Add(bodyId, doorModel);
        }

        public void RemoveDoor(PhysicsBodyId bodyId)
        {
            _bodyIdToEntityId.Remove(bodyId);
            _doors.Remove(bodyId);
        }

        public void AddPlotContainer(PhysicsBodyId bodyId, IPlotContainerModel plotContainerModel)
        {
            var modelEntityId = plotContainerModel.DescriptionModel.Value.EntityIds.ModelEntityId;
            _bodyIdToEntityId.Add(bodyId, modelEntityId);
        }

        public void RemovePlotContainer(PhysicsBodyId bodyId)
        {
            _bodyIdToEntityId.Remove(bodyId);
        }
        
        public bool TryGetKinematicObjectsController(PhysicsBodyId bodyId, out IKinematicObjectsControllerModel kinematicObjectsControllerModel)
        {
            return _kinematicObjectControllers.TryGetValue(bodyId, out kinematicObjectsControllerModel);
        }
        
        public void AddKinematicObjectsController(PhysicsBodyId bodyId, IKinematicObjectsControllerModel kinematicObjectsControllerModel)
        {
            _bodyIdToEntityId.Add(bodyId, kinematicObjectsControllerModel.Id);
            _kinematicObjectControllers.Add(bodyId, kinematicObjectsControllerModel);
        }
        
        public void RemoveKinematicObjectsController(PhysicsBodyId bodyId)
        {
            _bodyIdToEntityId.Remove(bodyId);
            _kinematicObjectControllers.Remove(bodyId);
        }
        
        public void AddMetalDetector(PhysicsBodyId bodyId, IMetalDetectorModel metalDetectorModel)
        {
            _bodyIdToEntityId.Add(bodyId, metalDetectorModel.Id);
        }

        public void RemoveMetalDetector(PhysicsBodyId bodyId)
        {
            _bodyIdToEntityId.Remove(bodyId);
        }

        public void AddMetalDetectorDetectArea(PhysicsBodyId bodyId)
        {
            _triggers.Add(bodyId);
        }

        public void RemoveMetalDetectorDetectArea(PhysicsBodyId bodyId)
        {
            _triggers.Remove(bodyId);
        }

        public bool TryGetCamera(PhysicsBodyId bodyId, out ICameraModel cameraModel)
        {
            return _cameras.TryGetValue(bodyId, out cameraModel);
        }

        public void AddCamera(PhysicsBodyId bodyId, ICameraModel cameraModel)
        {
            _cameras.Add(bodyId, cameraModel);
        }

        public void RemoveCamera(PhysicsBodyId bodyId)
        {
            _cameras.Remove(bodyId);
        }
        
        public void AddVigilantNpc(PhysicsBodyId bodyId, IVigilantNpcModel vigilantNpcModel)
        {
            _vigilantNpcs.Add(bodyId, vigilantNpcModel);
        }

        public void RemoveVigilantNpc(PhysicsBodyId bodyId)
        {
            _vigilantNpcs.Remove(bodyId);
        }
        
        public bool TryGetVigilantNpc(PhysicsBodyId bodyId, out IVigilantNpcModel vigilantNpcModel)
        {
            return _vigilantNpcs.TryGetValue(bodyId, out vigilantNpcModel);
        }

        public bool TryGetVehicle(PhysicsBodyId bodyId, out IVehicleModel vehicleModel)
        {
            return _vehicles.TryGetValue(bodyId, out vehicleModel);
        }

        public void AddVehicle(PhysicsBodyId bodyId, IVehicleModel vehicleModel)
        {
            _vehicles.Add(bodyId, vehicleModel);
        }

        public void RemoveVehicle(PhysicsBodyId bodyId)
        {
            _vehicles.Remove(bodyId);
        }

        public bool TryGetCar(PhysicsBodyId bodyId, out ICarModel carModel)
        {
            return _cars.TryGetValue(bodyId, out carModel);
        }

        public void AddCar(PhysicsBodyId bodyId, ICarModel carModel)
        {
            _bodyIdToEntityId.Add(bodyId, carModel.Id);
            _cars.Add(bodyId, carModel);
        }

        public void RemoveCar(PhysicsBodyId bodyId)
        {
            _bodyIdToEntityId.Remove(bodyId);
            _cars.Remove(bodyId);
        }

        public bool TryGetHelicopter(PhysicsBodyId bodyId, out IHelicopterModel helicopterModel)
        {
            return _helicopters.TryGetValue(bodyId, out helicopterModel);
        }

        public void AddHelicopter(PhysicsBodyId bodyId, IHelicopterModel helicopterModel)
        {
            _bodyIdToEntityId.Add(bodyId, helicopterModel.Id);
            _helicopters.Add(bodyId, helicopterModel);
        }

        public void RemoveHelicopter(PhysicsBodyId bodyId)
        {
            _bodyIdToEntityId.Remove(bodyId);
            _helicopters.Remove(bodyId);
        }

        public bool TryGetBreakableColliderSet(PhysicsBodyId bodyId, out IBreakableColliderSetModel breakableColliderSetModel)
        {
            return _breakableColliderSetModels.TryGetValue(bodyId, out breakableColliderSetModel);
        }

        public void AddBreakableColliderSet(PhysicsBodyId bodyId, IBreakableColliderSetModel breakableColliderSetModel)
        {
            _bodyIdToEntityId.Add(bodyId, breakableColliderSetModel.Id);
            _breakableColliderSetModels.Add(bodyId, breakableColliderSetModel);
        }

        public void RemoveBreakableColliderSet(PhysicsBodyId bodyId)
        {
            _bodyIdToEntityId.Remove(bodyId);
            _breakableColliderSetModels.Remove(bodyId);
        }

        public void AddBuildingZone(PhysicsBodyId bodyId)
        {
            _triggers.Add(bodyId);
        }

        public void RemoveBuildingZone(PhysicsBodyId bodyId)
        {
            _triggers.Remove(bodyId);
        }

        public bool TryGetHideObject(PhysicsBodyId bodyId, out int entityId)
        {
            return _hideObjects.TryGetValue(bodyId, out entityId);
        }

        public void AddHideObject(PhysicsBodyId bodyId, int entityId)
        {
            _hideObjects.Add(bodyId, entityId);
        }

        public void RemoveHideObject(PhysicsBodyId bodyId)
        {
            _hideObjects.Remove(bodyId);
        }

        public bool TryGetStaticVehicle(PhysicsBodyId bodyId, out int entityId)
        {
            return _staticVehicles.TryGetValue(bodyId, out entityId);
        }

        public void AddStaticVehicle(PhysicsBodyId bodyId, int entityId)
        {
            _staticVehicles.Add(bodyId, entityId);
        }

        public void RemoveStaticVehicle(PhysicsBodyId bodyId)
        {
            _staticVehicles.Remove(bodyId);
        }

        public bool TryGetStorageBox(PhysicsBodyId bodyId, out IStorageBoxModel model)
        {
            return _storages.TryGetValue(bodyId, out model);
        }

        public void AddStorageBox(PhysicsBodyId bodyId, IStorageBoxModel model)
        {
            var modelEntityId = model.DescriptionModel.Value.EntityIds.ModelEntityId;
            _bodyIdToEntityId.Add(bodyId, modelEntityId);
            _storages.Add(bodyId, model);
        }

        public void RemoveStorageBox(PhysicsBodyId bodyId)
        {
            _bodyIdToEntityId.Remove(bodyId);
            _storages.Remove(bodyId);
        }

        public bool TryGetLootObject(PhysicsBodyId bodyId, out ILootObjectModel lootObjectModel)
        {
            return _lootObjects.TryGetValue(bodyId, out lootObjectModel);
        }

        public void AddLootObject(PhysicsBodyId bodyId, ILootObjectModel lootObjectModel)
        {
            _lootObjects.Add(bodyId, lootObjectModel);
        }

        public void RemoveLootObject(PhysicsBodyId bodyId)
        {
            _lootObjects.Remove(bodyId);
        }

        public bool IsTrigger(PhysicsBodyId bodyId)
        {
            return _triggers.Contains(bodyId);
        }

        public void Remove(PhysicsBodyId bodyId)
        {
            RemoveBattleCharacter(bodyId);
            RemoveDoor(bodyId);
            RemoveKinematicObjectsController(bodyId);
            RemoveMetalDetector(bodyId);
            RemoveMetalDetectorDetectArea(bodyId);
            RemoveCamera(bodyId);
            RemoveVigilantNpc(bodyId);
            RemoveVehicle(bodyId);
            RemoveCar(bodyId);
            RemoveHelicopter(bodyId);
            RemoveBreakableColliderSet(bodyId);
            RemoveBuildingZone(bodyId);
            RemoveStorageBox(bodyId);
            RemoveHideObject(bodyId);
            RemoveStaticVehicle(bodyId);
            RemoveLootObject(bodyId);
        }

        public void Clear()
        {
            _bodyIdToEntityId.Clear();
            _battleCharacters.Clear();
            _doors.Clear();
            _kinematicObjectControllers.Clear();
            _vehicles.Clear();
            _cars.Clear();
            _helicopters.Clear();
            _breakableColliderSetModels.Clear();
            _cameras.Clear();
            _vigilantNpcs.Clear();
            _triggers.Clear();
            _storages.Clear();
            _hideObjects.Clear();
            _staticVehicles.Clear();
            _lootObjects.Clear();
        }
    }
}