using Models.Data;
using Models.Models.LocationEventCargoStateModel;
using Models.Models.PrimitiveModel;
using Models.Models.SelectedCargoDeliveryPointsModel;
using Models.References;
using Models.References.Cargo;

namespace Models.Models.LocationEventDataModel
{
    public class RobberLocationEventModels : IRobberLocationEventModels
    {
        public RobberLocationEventModels(LocationEventData data)
        {
            EventModel = new PrimitiveModel<LocationEventDescription>(data.Description);
            StartTsModel = new PrimitiveModel<long>(data.StartTs);
            CargoModel = new PrimitiveModel<LocationEventCargoSpawnPointDescription>(data.LocationEventCargo);
            SelectedCargoDeliveryPointsModel = new SelectedCargoDeliveryPointsModel.SelectedCargoDeliveryPointsModel(data.CargoDeliveryPoints);
            CargoStateModel = new LocationEventCargoStateModel.LocationEventCargoStateModel(data.IsCargoAtPosition, data.CargoPosition, data.CargoEntity);
            ContainerEntity = new PrimitiveModel<int>(data.LocationEventContainerEntity);
        }
        
        public IPrimitiveModel<LocationEventDescription> EventModel { get; }
        public IPrimitiveModel<long> StartTsModel { get; }
        public IPrimitiveModel<LocationEventCargoSpawnPointDescription> CargoModel { get; }
        public ISelectedCargoDeliveryPointsModel SelectedCargoDeliveryPointsModel { get; }
        public ILocationEventCargoStateModel CargoStateModel { get; }
        public IPrimitiveModel<int> ContainerEntity { get; }
        
        public bool IsActiveEventContainer(int entityId, long nowTs)
        {
            if (EventModel.Value == null) return false;
                
            return ContainerEntity.Value == entityId && nowTs >= StartTsModel.Value + EventModel.Value.PrepareTime;
        }
    }
}