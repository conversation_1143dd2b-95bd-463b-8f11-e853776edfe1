using System.Collections.Generic;
using Models.Data.Hangar.Builder;
using Models.Models.BuilderDoorModel;
using Models.Models.DescriptionModel;
using Models.Models.DestroyOutbuildingModel;
using Models.Models.InitializeOutbuildingModel;
using Models.Models.PlotBuildingDefenseBlockPublicModel;
using Models.Models.PlotCarGarageModel;
using Models.Models.PlotBuildingKinematicEntitiesModel;
using Models.Models.PlotHelicopterGarageModel;
using Models.Models.PlotModel;
using Models.Models.PrimitiveModel;
using Models.Models.ShelvingModel;
using Models.Models.StorageBoxModel;
using Models.Physics.PhysicsBodies;
using Models.References.Builder;
using Models.References.Door;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.Utils.Data.Colliders;

namespace Models.Models.OutbuildingModel
{
    public class OutbuildingModel : IOutbuildingModel
    {
        public OutbuildingModel(PlotOutbuildingData data, OutbuildingSlotDescription outbuildingSlotDescription, IPlotModel plotModel)
        {
            DescriptionModel = new DescriptionModel<LocationOutbuildingDescription>();

            InitializeModel = new InitializeOutbuildingModel.InitializeOutbuildingModel(this, outbuildingSlotDescription, plotModel);
            DestroyModel = new DestroyOutbuildingModel.DestroyOutbuildingModel(this, outbuildingSlotDescription, plotModel);

            CarGarage = new PlotCarGarageModel.PlotCarGarageModel(data.CarSlots);
            HelicopterGarage = new PlotHelicopterGarageModel.PlotHelicopterGarageModel(data.HelicopterSlots);

            var storageSlots = new Dictionary<StorageSlotDescription, IPrimitiveModel<StorageDescription>>();
            foreach (var (slot, primitiveData) in data.StorageSlots)
            {
                storageSlots.Add(slot, new PrimitiveModel<StorageDescription>(primitiveData));
            }

            StorageSlots = storageSlots;

            var storages = new Dictionary<StorageSlotDescription, IStorageBoxModel>(StorageSlotDescription.Enum.Count);
            foreach (var (slot, storageData) in data.Storages)
            {
                storages.Add(slot, new StorageBoxModel.StorageBoxModel(slot, storageData));
            }

            Storages = storages;

            var shelvingSlots = new Dictionary<ShelvingSlotDescription, IPrimitiveModel<ShelvingDescription>>();
            foreach (var (slot, primitiveData) in data.ShelvingSlots)
            {
                shelvingSlots.Add(slot, new PrimitiveModel<ShelvingDescription>(primitiveData));
            }

            ShelvingSlots = shelvingSlots;

            var shelvings = new Dictionary<ShelvingSlotDescription, IShelvingModel>(ShelvingSlotDescription.Enum.Count);
            foreach (var (slot, shelvingData) in data.Shelvings)
            {
                shelvings.Add(slot, new ShelvingModel.ShelvingModel(shelvingData));
            }

            Shelvings = shelvings;
            
            var doors = new Dictionary<OutbuildingDoorSlotDescription, IBuilderDoorModel>();
            foreach (var (slot, doorData) in data.Doors)
            {
                doors.Add(slot, new BuilderDoorModel.BuilderDoorModel(doorData));
            }

            Doors = doors;
            BuildingKinematicEntitiesModel = new PlotBuildingKinematicEntitiesModel.PlotBuildingKinematicEntitiesModel();

            BlockingColliderFlagModel = new PrimitiveModel<bool>(data.IsBlockingColliderActive);

            DefenseBlockPublicModel = new PlotBuildingDefenseBlockPublicModel.PlotBuildingDefenseBlockPublicModel(data.RaidLockTs, data.IsRaided, data.IsDefenseBlockSlotsEmpty);
        }

        public DescriptionModel<LocationOutbuildingDescription> DescriptionModel { get; }
        
        public IInitializeOutbuildingModel InitializeModel { get; }
        public IDestroyOutbuildingModel DestroyModel { get; }

        public IPlotCarGarageModel CarGarage { get; }
        public IPlotHelicopterGarageModel HelicopterGarage { get; }

        public IReadOnlyDictionary<StorageSlotDescription, IPrimitiveModel<StorageDescription>> StorageSlots { get; }
        public IReadOnlyDictionary<StorageSlotDescription, IStorageBoxModel> Storages { get; }

        public IReadOnlyDictionary<ShelvingSlotDescription, IPrimitiveModel<ShelvingDescription>> ShelvingSlots { get; }
        public IReadOnlyDictionary<ShelvingSlotDescription, IShelvingModel> Shelvings { get; }

        public List<PhysicsBodyId> BlockingCollidersBodies { get; } = new();
        public CollidersIdData CollidersIdData { get; } = new();

        public IPlotBuildingKinematicEntitiesModel BuildingKinematicEntitiesModel { get; }
        public IReadOnlyDictionary<OutbuildingDoorSlotDescription, IBuilderDoorModel> Doors { get; }

        public IPrimitiveModel<bool> BlockingColliderFlagModel { get; }
        
        public IPlotBuildingDefenseBlockPublicModel DefenseBlockPublicModel { get; }
    }
}