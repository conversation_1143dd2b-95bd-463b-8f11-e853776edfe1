using System.Collections.Generic;
using Models.Data;
using Models.Models.BattleCharacterSlotModel;
using Models.Models.BattleCharacterSlotsModel;
using Models.References;

namespace Models.Models.OccupiedCharacterSlotsModel
{
    public class OccupiedCharacterSlotsModel : IOccupiedCharacterSlotsModel
    {
        private readonly IBattleCharacterSlotsModel _battleCharacterSlotsModel;

        private readonly Dictionary<int, IBattleCharacterSlotModel> _slotByEntityId = new();
        private readonly Dictionary<long, IBattleCharacterSlotModel> _slotByPublicId = new();

        public OccupiedCharacterSlotsModel(IBattleCharacterSlotsModel battleCharacterSlotsModel)
        {
            _battleCharacterSlotsModel = battleCharacterSlotsModel;
        }

        public IBattleCharacterSlotModel Occupy(BattleCharacterSlotDescription slot, int entityId, long publicId)
        {
            var battleCharacterSlotModel = _battleCharacterSlotsModel[slot];
            battleCharacterSlotModel.Set(entityId, publicId);
            _slotByEntityId.Add(entityId, battleCharacterSlotModel);
            _slotByPublicId.Add(publicId, battleCharacterSlotModel);
            return battleCharacterSlotModel;
        }

        public BattleCharacterSlotDescription Release(int entityId)
        {
            var battleCharacterSlotModel = _slotByEntityId[entityId];
            _slotByEntityId.Remove(entityId);
            _slotByPublicId.Remove(battleCharacterSlotModel.PublicId);
            battleCharacterSlotModel.Unset();
            return battleCharacterSlotModel.Description;
        }

        public bool TryGetSlotByEntityId(int entityId, out IBattleCharacterSlotModel model) => _slotByEntityId.TryGetValue(entityId, out model);
        public bool TryGetSlotByPublicId(long publicId, out IBattleCharacterSlotModel model) => _slotByPublicId.TryGetValue(publicId, out model);

        public bool TryGetPublicIdByEntityId(int entityId, out long publicId)
        {
            if (TryGetSlotByEntityId(entityId, out var battleCharacterSlotModel))
            {
                publicId = battleCharacterSlotModel.PublicId;
                return true;
            }

            publicId = PublicIdData.DefaultValue;
            return false;
        }

        public bool TryGetEntityIdByPublicId(long publicId, out int entityId)
        {
            if (TryGetSlotByPublicId(publicId, out var battleCharacterSlotModel))
            {
                entityId = battleCharacterSlotModel.EntityId;
                return true;
            }

            entityId = BattleEntityIdData.Empty;
            return false;
        }
    }
}