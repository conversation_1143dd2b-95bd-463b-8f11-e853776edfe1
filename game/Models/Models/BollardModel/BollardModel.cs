using Framework.Replication.Data.Primitive;
using Models.Data.Bollard;
using Models.References;
using Models.References.Bollard;
using Models.References.KinematicObject;
using Models.Utils.Extensions;

namespace Models.Models.BollardModel
{
    public class BollardModel : IBollardModel
    {
        private readonly IPrimitiveData<KinematicObjectMovementStateDescription> _state;
        private readonly IPrimitiveData<long> _cooldownTs;

        public BollardModel(BollardData data, LocationBollardDescription description)
        {
            Description = description;

            _state = data.State;
            _cooldownTs = data.CooldownTs;
        }
    
        public LocationBollardDescription Description { get; }

        public KinematicObjectMovementStateDescription State => _state.Value;
        public long CooldownTs
        {
            get => _cooldownTs.Value;
            set => _cooldownTs.TrySetValue(value);
        }
    }
}