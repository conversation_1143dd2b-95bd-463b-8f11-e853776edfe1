using Models.Models.CarPhysicsDragSettingsOverrideModel;
using Models.References.Car;

namespace Models.Models.CarPhysicsDragSettingsModel
{
    public class CarPhysicsDragSettingsModel : ICarPhysicsDragSettingsModel
    {
        private readonly CarPhysicsDragDescription _physicsDragDescription;
        private readonly ICarPhysicsDragSettingsOverrideModel _overrideModel;

        public CarPhysicsDragSettingsModel(CarPhysicsDragDescription physicsDragDescription, ICarPhysicsDragSettingsOverrideModel overrideModel)
        {
            _physicsDragDescription = physicsDragDescription;
            _overrideModel = overrideModel;
        }
        
        public float NotFullGroundedAngularDrag => _overrideModel.OverrideEnabled ? _overrideModel.NotFullGroundedAngularDrag : _physicsDragDescription.NotFullGroundedAngularDrag;
        public float FullGroundedAngularDragZ => _overrideModel.OverrideEnabled ? _overrideModel.FullGroundedAngularDragZ : _physicsDragDescription.FullGroundedAngularDragZ;
    }
}