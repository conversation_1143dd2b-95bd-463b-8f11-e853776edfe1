using Models.Data.Hangar.Storage;
using Models.Models.InventoryModel;
using Models.References.HangarStorage;

namespace Models.Models.StorageBoxInventoryModel
{
    public class StorageBoxInventoryModel : IStorageBoxInventoryModel
    {
        public StorageBoxInventoryModel(int id, StorageBoxInventoryData data, StorageDescription storageDescription)
        {
            Id = id;
            InventoryModel = new StaticLinesCountInventoryModel(data.Inventory, storageDescription.InventoryCellsSizeDescription);
        }

        public int Id { get; }
        public IInventoryModel InventoryModel { get; }
    }
}