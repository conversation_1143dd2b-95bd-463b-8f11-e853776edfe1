using Models.Models.DescriptionModel;
using Models.Models.PlotBuildingDefenseBlockPublicDestroyModel;
using Models.Models.PlotBuildingDefenseBlockPublicInitializeModel;
using Models.Models.PrimitiveModel;
using Models.References.PlotBuildingDefenseBlock;

namespace Models.Models.PlotBuildingDefenseBlockPublicModel
{
    public interface IPlotBuildingDefenseBlockPublicModel
    {
        DescriptionModel<LocationPlotBuildingDefenseBlockDescription> DescriptionModel { get; }
        IPrimitiveModel<long> RaidLockTs { get; }
        IPrimitiveModel<bool> IsRaided { get; }
        IPrimitiveModel<bool> IsSlotsEmpty { get; }

        IPlotBuildingDefenseBlockPublicInitializeModel InitializeModel { get; }
        IPlotBuildingDefenseBlockPublicDestroyModel DestroyModel { get; }
    }
}