using Framework.Replication.Data.Primitive;
using Models.Data;
using Models.Models.DescriptionModel;
using Models.Models.PlotBuildingDefenseBlockPublicDestroyModel;
using Models.Models.PlotBuildingDefenseBlockPublicInitializeModel;
using Models.Models.PrimitiveModel;
using Models.References.PlotBuildingDefenseBlock;

namespace Models.Models.PlotBuildingDefenseBlockPublicModel
{
    public class PlotBuildingDefenseBlockPublicModel : IPlotBuildingDefenseBlockPublicModel
    {
        public PlotBuildingDefenseBlockPublicModel(TimestampData raidLockTs, BoolData isRaided, BoolData isSlotsEmpty)
        {
            DescriptionModel = new DescriptionModel<LocationPlotBuildingDefenseBlockDescription>();

            InitializeModel = new PlotBuildingDefenseBlockPublicInitializeModel.PlotBuildingDefenseBlockPublicInitializeModel(this);
            DestroyModel = new PlotBuildingDefenseBlockPublicDestroyModel.PlotBuildingDefenseBlockPublicDestroyModel(this);
            
            RaidLockTs = new PrimitiveModel<long>(raidLockTs);
            IsRaided = new PrimitiveModel<bool>(isRaided);
            IsSlotsEmpty = new PrimitiveModel<bool>(isSlotsEmpty);
        }
        
        public DescriptionModel<LocationPlotBuildingDefenseBlockDescription> DescriptionModel { get; }
        public IPrimitiveModel<long> RaidLockTs { get; }
        public IPrimitiveModel<bool> IsRaided { get; }
        public IPrimitiveModel<bool> IsSlotsEmpty { get; }

        public IPlotBuildingDefenseBlockPublicInitializeModel InitializeModel { get; }
        public IPlotBuildingDefenseBlockPublicDestroyModel DestroyModel { get; }
    }
}