using System.Collections.Generic;
using Models.Data.Hangar.Builder;
using Models.Models.BuilderDoorModel;
using Models.Models.CargoWorkbenchModel;
using Models.Models.CollectionTableModel;
using Models.Models.CopFurnitureModel;
using Models.Models.DescriptionModel;
using Models.Models.DestroyMainBuildingModel;
using Models.Models.InitializeMainBuildingModel;
using Models.Models.MoneyWorkbenchModel;
using Models.Models.PlotBuildingDefenseBlockPublicModel;
using Models.Models.PlotBuildingKinematicEntitiesModel;
using Models.Models.PlotCarGarageModel;
using Models.Models.PlotContainerModel;
using Models.Models.PlotModel;
using Models.Models.PrimitiveModel;
using Models.Models.ShelvingModel;
using Models.Models.StorageBoxModel;
using Models.Models.WindowBlindsModel;
using Models.Physics.PhysicsBodies;
using Models.References.Builder;
using Models.References.CargoWorkbench;
using Models.References.Collection;
using Models.References.Door;
using Models.References.HangarShelving;
using Models.References.HangarStorage;
using Models.References.PlotContainers;
using Models.References.WindowBlinds;
using Models.Utils.Data.Colliders;

namespace Models.Models.MainBuildingModel
{
    public class MainBuildingModel : IMainBuildingModel
    {
        public MainBuildingModel(PlotMainBuildingData data, IPlotModel plotModel)
        {
            DescriptionModel = new DescriptionModel<LocationMainBuildingDescription>();

            InitializeModel = new InitializeMainBuildingModel.InitializeMainBuildingModel(plotModel, plotModel.MainBuildingSlot, DescriptionModel);
            DestroyModel = new DestroyMainBuildingModel.DestroyMainBuildingModel(plotModel, DescriptionModel);

            CopFurnitureSlot = new PrimitiveModel<CopFurnitureDescription>(data.CopFurnitureSlot);
            CopFurniture = new CopFurnitureModel.CopFurnitureModel(data.CopFurnitureUpgrades);

            CollectionTableSlot = new PrimitiveModel<CollectionTableDescription>(data.CollectionTableSlot);
            CollectionTableModel = new CollectionTableModel.CollectionTableModel(data.CollectionTable);

            CargoWorkbenchSlot = new PrimitiveModel<CargoWorkbenchDescription>(data.CargoWorkbenchSlot);
            CargoWorkbenchModel = new CargoWorkbenchModel.CargoWorkbenchModel(data.CargoWorkbench);

            var storageSlots = new Dictionary<StorageSlotDescription, IPrimitiveModel<StorageDescription>>();
            foreach (var (slot, primitiveData) in data.StorageSlots)
            {
                storageSlots.Add(slot, new PrimitiveModel<StorageDescription>(primitiveData));
            }

            StorageSlots = storageSlots;

            var storages = new Dictionary<StorageSlotDescription, IStorageBoxModel>(StorageSlotDescription.Enum.Count);
            foreach (var (slot, storageData) in data.Storages)
            {
                storages.Add(slot, new StorageBoxModel.StorageBoxModel(slot, storageData));
            }

            Storages = storages;

            var shelvingSlots = new Dictionary<ShelvingSlotDescription, IPrimitiveModel<ShelvingDescription>>();
            foreach (var (slot, primitiveData) in data.ShelvingSlots)
            {
                shelvingSlots.Add(slot, new PrimitiveModel<ShelvingDescription>(primitiveData));
            }

            ShelvingSlots = shelvingSlots;

            var shelvings = new Dictionary<ShelvingSlotDescription, IShelvingModel>(ShelvingSlotDescription.Enum.Count);
            foreach (var (slot, shelvingData) in data.Shelvings)
            {
                shelvings.Add(slot, new ShelvingModel.ShelvingModel(shelvingData));
            }

            Shelvings = shelvings;

            CarGarage = new PlotCarGarageModel.PlotCarGarageModel(data.CarSlots);

            var doors = new Dictionary<MainBuildingDoorSlotDescription, IBuilderDoorModel>();
            foreach (var (slot, doorData) in data.Doors)
            {
                doors.Add(slot, new BuilderDoorModel.BuilderDoorModel(doorData));
            }
            Doors = doors;

            var windowBlinds = new Dictionary<MainBuildingWindowBlindsSlotDescription, IWindowBlindsModel>();
            foreach (var (slot, windowBlindsData) in data.WindowBlinds)
            {
                windowBlinds.Add(slot, new WindowBlindsModel.WindowBlindsModel(windowBlindsData));
            }
            WindowBlinds = windowBlinds;
            
            var plotContainers = new Dictionary<MainBuildingPlotContainerSlotDescription, IPlotContainerModel>();
            foreach (var (slot, plotContainerData) in data.PlotContainers)
            {
                plotContainers.Add(slot, new PlotContainerModel.PlotContainerModel(slot, plotContainerData));
            }
            PlotContainers = plotContainers;

            MoneyWorkbench = new MoneyWorkbenchModel.MoneyWorkbenchModel(data.MoneyWorkbench);
            BuildingKinematicEntitiesModel = new PlotBuildingKinematicEntitiesModel.PlotBuildingKinematicEntitiesModel();

            BlockingColliderFlagModel = new PrimitiveModel<bool>(data.IsBlockingColliderActive);

            DefenseBlockPublicModel = new PlotBuildingDefenseBlockPublicModel.PlotBuildingDefenseBlockPublicModel(data.RaidLockTs, data.IsRaided, data.IsDefenseBlockSlotsEmpty);
        }

        public DescriptionModel<LocationMainBuildingDescription> DescriptionModel { get; }

        public IInitializeMainBuildingModel InitializeModel { get; }
        public IDestroyMainBuildingModel DestroyModel { get; }

        public IPrimitiveModel<CopFurnitureDescription> CopFurnitureSlot { get; }
        public ICopFurnitureModel CopFurniture { get; }

        public IPrimitiveModel<CollectionTableDescription> CollectionTableSlot { get; }
        public ICollectionTableModel CollectionTableModel { get; }

        public IPrimitiveModel<CargoWorkbenchDescription> CargoWorkbenchSlot { get; }
        public ICargoWorkbenchModel CargoWorkbenchModel { get; }

        public IReadOnlyDictionary<StorageSlotDescription, IPrimitiveModel<StorageDescription>> StorageSlots { get; }
        public IReadOnlyDictionary<MainBuildingPlotContainerSlotDescription, IPrimitiveModel<PlotContainerDescription>> PlotContainerSlots { get; }
        public IReadOnlyDictionary<StorageSlotDescription, IStorageBoxModel> Storages { get; }

        public IReadOnlyDictionary<ShelvingSlotDescription, IPrimitiveModel<ShelvingDescription>> ShelvingSlots { get; }
        public IReadOnlyDictionary<ShelvingSlotDescription, IShelvingModel> Shelvings { get; }

        public IPlotCarGarageModel CarGarage { get; }

        public IMoneyWorkbenchModel MoneyWorkbench { get; }

        public List<PhysicsBodyId> BlockingCollidersBodies { get; } = new();
        public CollidersIdData CollidersIdData { get; } = new();

        public IPlotBuildingKinematicEntitiesModel BuildingKinematicEntitiesModel { get; }
        public IReadOnlyDictionary<MainBuildingDoorSlotDescription, IBuilderDoorModel> Doors { get; }
        public IReadOnlyDictionary<MainBuildingWindowBlindsSlotDescription, IWindowBlindsModel> WindowBlinds { get; }
        public IReadOnlyDictionary<MainBuildingPlotContainerSlotDescription, IPlotContainerModel> PlotContainers { get; }

        public IPrimitiveModel<bool> BlockingColliderFlagModel { get; }
        
        public IPlotBuildingDefenseBlockPublicModel DefenseBlockPublicModel { get; }
    }
}