namespace Models.Models.PhysicsDebug.Data.Helicopter
{
    public class PhysicsDebugHelicopterWheelTickData
    {
        public bool GroundFound;
        public float GroundContactDistance;

        public bool IsGroundWithLargeSlope;
    
        public float SpringLengthFromData;
        public float SpringLengthCalculated;
    
        public float SpringContactDepth;
        public float SpringContactSpeed;
    
        public float ContactDepth;
        public float ContactSpeed;
        public float Stiffness;
        public float Damper;

        public float LinkVelocityAlongSuspensionDirection;
    
        public float IdleForce;
        public float StiffnessForce;
        public float DamperForce;
        public float TotalForce;
    
        public void Clear()
        {
            GroundFound = false;
            GroundContactDistance = 0;

            IsGroundWithLargeSlope = false;

            SpringLengthFromData = 0;
            SpringLengthCalculated = 0;

            SpringContactDepth = 0;
            SpringContactSpeed = 0;

            ContactDepth = 0;
            ContactSpeed = 0;
            Stiffness = 0;
            Damper = 0;

            LinkVelocityAlongSuspensionDirection = 0;

            IdleForce = 0;
            StiffnessForce = 0;
            DamperForce = 0;
            TotalForce = 0;
        }

        public void CopyTo(PhysicsDebugHelicopterWheelTickData copy)
        {
            copy.GroundFound = GroundFound;
            copy.GroundContactDistance = GroundContactDistance;

            copy.IsGroundWithLargeSlope = IsGroundWithLargeSlope;

            copy.SpringLengthFromData = SpringLengthFromData;
            copy.SpringLengthCalculated = SpringLengthCalculated;

            copy.SpringContactDepth = SpringContactDepth;
            copy.SpringContactSpeed = SpringContactSpeed;

            copy.ContactDepth = ContactDepth;
            copy.ContactSpeed = ContactSpeed;
            copy.Stiffness = Stiffness;
            copy.Damper = Damper;

            copy.LinkVelocityAlongSuspensionDirection = LinkVelocityAlongSuspensionDirection;

            copy.IdleForce = IdleForce;
            copy.StiffnessForce = StiffnessForce;
            copy.DamperForce = DamperForce;
            copy.TotalForce = TotalForce;
        }
    }
}