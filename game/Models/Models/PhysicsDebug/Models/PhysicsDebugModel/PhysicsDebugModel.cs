using Models.Models.PhysicsDebug.Data;
using Models.Models.PhysicsDebug.Data.BattleCharacter;
using Models.Models.PhysicsDebug.Data.Car;
using Models.Models.PhysicsDebug.Data.Helicopter;
using Models.Models.PhysicsDebug.Data.KinematicObject;
using Models.References.Car;
using Models.References.Character;
using Models.References.Helicopter;

namespace Models.Models.PhysicsDebug.Models.PhysicsDebugModel
{
    public class PhysicsDebugModel : IPhysicsDebugModel
    {
        private readonly PhysicsDebugTickData _data = new();
        private int _subTickIndex;

        public bool ContainsCar(int entityId)
        {
            return _data.Cars.ContainsKey(entityId);
        }

        public PhysicsDebugCarTickData GetCarTickData(int entityId)
        {
            return _data.Cars[entityId];
        }

        public PhysicsDebugCarSubTickData GetCarSubTickData(int entityId)
        {
            return GetCarTickData(entityId).SubTicks[_subTickIndex];
        }

        public void AddCar(int entityId, CarDescription description)
        {
            _data.Cars.Add(entityId, new PhysicsDebugCarTickData(description));
        }

        public bool ContainsHelicopter(int entityId)
        {
            return _data.Helicopters.ContainsKey(entityId);
        }

        public PhysicsDebugHelicopterTickData GetHelicopterTickData(int entityId)
        {
            return _data.Helicopters[entityId];
        }

        public PhysicsDebugHelicopterSubTickData GetHelicopterSubTickData(int entityId)
        {
            return GetHelicopterTickData(entityId).SubTicks[_subTickIndex];
        }

        public void AddHelicopter(int entityId, HelicopterDescription description)
        {
            _data.Helicopters.Add(entityId, new PhysicsDebugHelicopterTickData(description));
        }

        public bool ContainsBattleCharacter(int entityId)
        {
            return _data.BattleCharacters.ContainsKey(entityId);
        }

        public PhysicsDebugBattleCharacterTickData GetBattleCharacterTickData(int entityId)
        {
            return _data.BattleCharacters[entityId];
        }

        public PhysicsDebugBattleCharacterSubTickData GetBattleCharacterSubTickData(int entityId)
        {
            return GetBattleCharacterTickData(entityId).SubTicks[_subTickIndex];
        }

        public void AddBattleCharacter(int entityId, BattleCharacterDescription description)
        {
            _data.BattleCharacters.Add(entityId, new PhysicsDebugBattleCharacterTickData(description));
        }

        public bool ContainsKinematicObjectsController(int entityId)
        {
            return _data.KinematicObjectsControllers.ContainsKey(entityId);
        }

        public PhysicsDebugKinematicObjectsControllerTickData GetKinematicObjectsControllerTickData(int entityId)
        {
            return _data.KinematicObjectsControllers[entityId];
        }

        public PhysicsDebugKinematicObjectsControllerSubTickData GetKinematicObjectsControllerSubTickData(int entityId)
        {
            return GetKinematicObjectsControllerTickData(entityId).SubTicks[_subTickIndex];
        }

        public void AddKinematicObjectsController(int entityId)
        {
            _data.KinematicObjectsControllers.Add(entityId, new PhysicsDebugKinematicObjectsControllerTickData());
        }

        public void Clear()
        {
            _data.Cars.Clear();
            _data.Helicopters.Clear();
            _data.BattleCharacters.Clear();
            _data.KinematicObjectsControllers.Clear();
        }

        public void StartNewTick(long tickIndex)
        {
            _data.Clear();
            _data.TickIndex = tickIndex;
            _subTickIndex = -1;
        }

        public void StartNewSubTick()
        {
            _subTickIndex++;
        }

        public PhysicsDebugTickData GetTickData()
        {
            return _data;
        }
    }
}