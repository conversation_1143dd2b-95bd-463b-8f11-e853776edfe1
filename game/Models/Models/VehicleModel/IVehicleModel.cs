using System.Numerics;
using Models.Models.BreachStateEntityModel;
using Models.Models.ClanBattleVehicleTeamOwnerModel;
using Models.Models.EventModel;
using Models.Models.InteractableObjectOpenedModel;
using Models.Models.PartyVehicleOwnerModel;
using Models.Models.PhysicsAwakeObserverModel;
using Models.Models.PhysicsEntitySubStepSnapshotsModel;
using Models.Models.PrimitiveModel;
using Models.Models.RadioStationModel;
using Models.Models.RequestModel;
using Models.Models.VehicleCriminalStateModel;
using Models.Models.VehicleDirtLevelModel;
using Models.Models.VehicleExitModel;
using Models.Models.VehicleOwnerModel;
using Models.Models.VehicleSeatsModel;
using Models.Models.VehicleSleepModel;
using Models.Models.VehicleTransformModel;
using Models.Models.VehicleTrunkPositionModel;
using Models.Models.VelocityModel;
using Models.Physics.PhysicsBodies;
using Models.Physics.Shapes.Compound;
using Models.References.Vehicle;

namespace Models.Models.VehicleModel
{
    public interface IVehicleModel
    {
        int Id { get; }
        PhysicsBodyId BodyId { get; }
    
        bool HasTrunk { get; }
    
        VehicleGroupType GroupType { get; }
        VehicleInteractDescription Interact { get; }
    
        IVehicleTransformModel Transform { get; }
    
        IVehicleSeatsModel Seats { get; }
    
        IVehicleOwnerModel CharacterOwner { get; }
        IPartyVehicleOwnerModel PartyOwner { get; }
    
        IVehicleExitModel Exit { get; }
        IVehicleCriminalStateModel CriminalStateModel { get; }
    
        IVelocityModel LinearVelocity { get; }
        IVelocityModel AngularVelocity { get; }
    
        IVehicleSleepModel Sleep { get; }
        IPhysicsAwakeObserverModel PhysicsAwakeObserver { get; }
    
        IPhysicsEntitySubStepSnapshotsModel SubStepSnapshots { get; }
    
        IPrimitiveModel<bool> IsWarped { get; }
        IPrimitiveModel<bool> IsUnowned { get; }
    
        CompoundShape ColliderShapeRelativeToCenterOfMass { get; }
    
        Vector3 BoundingBoxLocalPositionRelativeToCenterOfMass { get; }
        Vector3 BoundingBoxSize { get; }
    
        Vector3 InertiaBoxShapeSize { get; }
    
        IRequestModel<(Vector3 Position, Quaternion Orientation, Vector3 LinearVelocity, Vector3 AngularVelocity)> TeleportRequest { get; }
        IEventModel WarpedEvent { get; }

        IPrimitiveModel<bool> HasFreeCargoSlot { get; }
        IPrimitiveModel<bool> HasCargo { get; }
        bool CanRefillCopAmmo { get; }
        IVehicleTrunkPositionModel TrunkPosition { get; }
        Vector3 TrunkLocalPosition { get; }
        IInteractableObjectOpenedModel TrunkOpenedModel { get; }
    
        VehicleTypeDescription VehicleType { get; }
    
        int LeaveSafeZoneFlags { get; }

        IRadioStationModel RadioStation { get; }
        IVehicleDirtLevelModel DirtLevel { get; }
        IClanBattleVehicleTeamOwnerModel ClanBattleTeamOwner { get; }
    
        float VerticalDistanceFromPivotToGroundInRest { get; }
        float VerticalLocalCenterFromGroundInRestRelativeToPivot { get; }

        IBreachStateEntityModel TrunkBreachModel { get; }
    }
}