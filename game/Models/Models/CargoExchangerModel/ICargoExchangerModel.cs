using Models.Models.PositionModel;
using Models.References.Cargo;

namespace Models.Models.CargoExchangerModel
{
    public interface ICargoExchangerModel
    {
        LocationCargoExchangerDescription Description { get; }
        IPositionModel PositionModel { get; }
        int GetCargoCount(CargoDescription cargoDescription);
        int GetAllCargosCount();
        int CashAmount { get; }
        bool CheckAddCargo();
        void AddCargo(CargoDescription cargoDescription);
        bool CheckTakeCash();
        void TakeCash(int amount);
        bool CheckExchange();
        void Exchange(int amount);
        bool CheckArrest();
        void Arrest();
        int PutCargoQueueLength { get; }
        CargoDescription GetCargoFromQueue(int index);
    }
}