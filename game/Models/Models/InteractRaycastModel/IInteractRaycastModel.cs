using System.Collections.Generic;
using System.Numerics;
using Models.Models.BattleCharacterCameraModel;
using Models.Models.InteractEntityModel;
using Models.Models.LookDirectionModel;
using Models.Models.TransformModel;
using Models.Physics.PhysicsBodies;

namespace Models.Models.InteractRaycastModel
{
    public interface IInteractRaycastModel
    {
        bool CheckRaycast(long now, ITransformModel transform, ILookDirectionModel lookDirectionModel, IInteractEntityModel interactEntityModel, IBattleCharacterCameraModel camera);
        bool CheckRaycast(long now, ITransformModel transform, ILookDirectionModel lookDirectionModel, IBattleCharacterCameraModel camera, Vector3 interactEntityPosition, HashSet<PhysicsBodyId> ignoreBodies);
    }
}