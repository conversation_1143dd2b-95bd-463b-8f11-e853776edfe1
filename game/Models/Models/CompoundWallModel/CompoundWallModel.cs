using System.Collections.Generic;
using Models.Data.Hangar.Builder;
using Models.Models.BuilderDoorModel;
using Models.Models.PlotBuildingKinematicEntitiesModel;
using Models.Models.PlotModel;
using Models.References.Builder;
using Models.Models.DescriptionModel;
using Models.Models.DestroyCompoundWallModel;
using Models.Models.InitializeCompoundWallModel;
using Models.Models.PlotBuildingDefenseBlockPublicModel;
using Models.Models.PrimitiveModel;
using Models.Physics.PhysicsBodies;
using Models.References.Door;
using Models.Utils.Data.Colliders;

namespace Models.Models.CompoundWallModel
{
    public class CompoundWallModel : ICompoundWallModel
    {
        public CompoundWallModel(PlotCompoundWallData data, IPlotModel plotModel)
        {
            DescriptionModel = new DescriptionModel<LocationCompoundWallDescription>();

            InitializeModel = new InitializeCompoundWallModel.InitializeCompoundWallModel(plotModel, plotModel.CompoundWallSlot, DescriptionModel);
            DestroyModel = new DestroyCompoundWallModel.DestroyCompoundWallModel(plotModel, DescriptionModel);

            BuildingKinematicEntitiesModel = new PlotBuildingKinematicEntitiesModel.PlotBuildingKinematicEntitiesModel();

            BlockingColliderFlagModel = new PrimitiveModel<bool>(data.IsBlockingColliderActive);
            
            var doors = new Dictionary<CompoundWallDoorSlotDescription, IBuilderDoorModel>();
            foreach (var (slot, doorData) in data.Doors)
            {
                doors.Add(slot, new BuilderDoorModel.BuilderDoorModel(doorData));
            }

            Doors = doors;

            DefenseBlockPublicModel = new PlotBuildingDefenseBlockPublicModel.PlotBuildingDefenseBlockPublicModel(data.RaidLockTs, data.IsRaided, data.IsDefenseBlockSlotsEmpty);
        }

        public DescriptionModel<LocationCompoundWallDescription> DescriptionModel { get; }
        public IInitializeCompoundWallModel InitializeModel { get; }
        public IDestroyCompoundWallModel DestroyModel { get; }

        public IReadOnlyDictionary<CompoundWallDoorSlotDescription, IBuilderDoorModel> Doors { get; }
        
        public List<PhysicsBodyId> BlockingCollidersBodies { get; } = new();
        public CollidersIdData CollidersIdData { get; } = new();
        
        public IPlotBuildingKinematicEntitiesModel BuildingKinematicEntitiesModel { get; }

        public IPrimitiveModel<bool> BlockingColliderFlagModel { get; }
        
        public IPlotBuildingDefenseBlockPublicModel DefenseBlockPublicModel { get; }
    }
}