using System;
using Framework.Async.Awaiter;
using Framework.Core.BinaryStream;
using Framework.Core.Either;
using Framework.Core.Unit;
using Models.Physics.Collections.Data;
using Models.Physics.Serialization.Serializers;
using Models.Physics.Serialization.Settings;
using Models.Physics.Shapes.Data;
using Models.Physics.Shapes.Data.Compound;
using Models.Physics.Shapes.Data.Hull;

namespace Models.Physics.Serialization.AsyncSerializers
{
    public class PhysicsShapesDataAsyncSerializer : IPhysicsAsyncSerializer<PhysicsShapesData>
    {
        private readonly IPhysicsAsyncSerializer<PhysicsShapeLinkedListData<PlayerShapeData>> _playersSerializer;
        private readonly IPhysicsAsyncSerializer<PhysicsShapeLinkedListData<SphereShapeData>> _spheresSerializer;
        private readonly IPhysicsAsyncSerializer<PhysicsShapeLinkedListData<BoxShapeData>> _boxesSerializer;
        private readonly IPhysicsAsyncSerializer<PhysicsShapeLinkedListData<RoundedBoxShapeData>> _roundedBoxesSerializer;
        private readonly IPhysicsAsyncSerializer<PhysicsLinkedListData<TriangleShapeData>> _trianglesSerializer;
        private readonly IPhysicsAsyncSerializer<PhysicsShapeLinkedListData<CapsuleShapeData>> _capsulesSerializer;
        private readonly IPhysicsAsyncSerializer<PhysicsShapeLinkedListData<RuntimeHullShapeData>> _hullsSerializer;
        private readonly IPhysicsAsyncSerializer<PhysicsLinkedListData<TerrainShapeData>> _terrainsSerializer;
        private readonly IPhysicsAsyncSerializer<PhysicsLinkedListData<CompoundShapeData>> _compoundsSerializer;
        
        private readonly IPhysicsAsyncSerializer<PhysicsLinkedListData<HullShapeData>> _hullDataSerializer;
        
        private readonly IPhysicsAsyncSerializer<PhysicsLinkedListData<PhysicsShapeLocalTransformData>> _localTransformsSerializer;
        private readonly IPhysicsAsyncSerializer<PhysicsLinkedListData<PhysicsShapeCompoundItemData>> _compoundItemsSerializer;

        public PhysicsShapesDataAsyncSerializer()
        {
            _playersSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<PlayerShapeData>(new PhysicsPlayerShapeDataSerializer());
            _spheresSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<SphereShapeData>(new PhysicsSphereShapeDataSerializer());
            _boxesSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<BoxShapeData>(new PhysicsBoxShapeDataSerializer());
            _roundedBoxesSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<RoundedBoxShapeData>(new PhysicsRoundedBoxShapeDataSerializer());
            _trianglesSerializer = new PhysicsLinkedListDataAsyncSerializer<TriangleShapeData>(new PhysicsTriangleShapeDataSerializer());
            _capsulesSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<CapsuleShapeData>(new PhysicsCapsuleShapeDataSerializer());
            _hullsSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<RuntimeHullShapeData>(new PhysicsRuntimeHullShapeDataSerializer());
            _terrainsSerializer = new PhysicsLinkedListDataAsyncSerializer<TerrainShapeData>(new PhysicsTerrainShapeDataAsyncSerializer());
            _compoundsSerializer = new PhysicsLinkedListDataAsyncSerializer<CompoundShapeData>(new PhysicsCompoundShapeDataSerializer());
            
            _hullDataSerializer = new PhysicsLinkedListDataAsyncSerializer<HullShapeData>(new PhysicsHullShapeDataSerializer());
            
            _localTransformsSerializer = new PhysicsLinkedListDataAsyncSerializer<PhysicsShapeLocalTransformData>(new PhysicsShapeLocalTransformDataSerializer());
            _compoundItemsSerializer = new PhysicsLinkedListDataAsyncSerializer<PhysicsShapeCompoundItemData>(new PhysicsShapeCompoundItemDataSerializer());
        }
        
        public PhysicsShapesDataAsyncSerializer(Action<int, Action> enqueueAsyncAction, in PhysicsShapesDataAsyncSettings asyncSettings)
        {
            _playersSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<PlayerShapeData>(new PhysicsPlayerShapeDataSerializer(), enqueueAsyncAction, asyncSettings.Players);
            _spheresSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<SphereShapeData>(new PhysicsSphereShapeDataSerializer(), enqueueAsyncAction, asyncSettings.Spheres);
            _boxesSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<BoxShapeData>(new PhysicsBoxShapeDataSerializer(), enqueueAsyncAction, asyncSettings.Boxes);
            _roundedBoxesSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<RoundedBoxShapeData>(new PhysicsRoundedBoxShapeDataSerializer(), enqueueAsyncAction, asyncSettings.RoundedBoxes);
            _trianglesSerializer = new PhysicsLinkedListDataAsyncSerializer<TriangleShapeData>(new PhysicsTriangleShapeDataSerializer(), enqueueAsyncAction, asyncSettings.Triangles);
            _capsulesSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<CapsuleShapeData>(new PhysicsCapsuleShapeDataSerializer(), enqueueAsyncAction, asyncSettings.Capsules);
            _hullsSerializer = new PhysicsShapeLinkedListDataAsyncSerializer<RuntimeHullShapeData>(new PhysicsRuntimeHullShapeDataSerializer(), enqueueAsyncAction, asyncSettings.Hulls);
            _terrainsSerializer = new PhysicsLinkedListDataAsyncSerializer<TerrainShapeData>(new PhysicsTerrainShapeDataAsyncSerializer(enqueueAsyncAction, asyncSettings.Terrain), enqueueAsyncAction, asyncSettings.Terrains);
            _compoundsSerializer = new PhysicsLinkedListDataAsyncSerializer<CompoundShapeData>(new PhysicsCompoundShapeDataSerializer(), enqueueAsyncAction, asyncSettings.Compounds);
            
            _hullDataSerializer = new PhysicsLinkedListDataAsyncSerializer<HullShapeData>(new PhysicsHullShapeDataSerializer(), enqueueAsyncAction, asyncSettings.HullData);
            
            _localTransformsSerializer = new PhysicsLinkedListDataAsyncSerializer<PhysicsShapeLocalTransformData>(new PhysicsShapeLocalTransformDataSerializer(), enqueueAsyncAction, asyncSettings.LocalTransforms);
            _compoundItemsSerializer = new PhysicsLinkedListDataAsyncSerializer<PhysicsShapeCompoundItemData>(new PhysicsShapeCompoundItemDataSerializer(), enqueueAsyncAction, asyncSettings.CompoundItems);
        }
        
        public void Put(PhysicsShapesData value, IBinaryWriteStream stream)
        {
            _playersSerializer.Put(value.Players, stream);
            _spheresSerializer.Put(value.Spheres, stream);
            _boxesSerializer.Put(value.Boxes, stream);
            _roundedBoxesSerializer.Put(value.RoundedBoxes, stream);
            _trianglesSerializer.Put(value.Triangles, stream);
            _capsulesSerializer.Put(value.Capsules, stream);
            _hullsSerializer.Put(value.Hulls, stream);
            _terrainsSerializer.Put(value.Terrains, stream);
            _compoundsSerializer.Put(value.Compounds, stream);
            
            _hullDataSerializer.Put(value.HullData, stream);
            
            _localTransformsSerializer.Put(value.LocalTransforms, stream);
            _compoundItemsSerializer.Put(value.CompoundItems, stream);
        }

        public bool TryGet(IBinaryReadStream stream, out PhysicsShapesData value)
        {
            value = new PhysicsShapesData();

            if (!_playersSerializer.TryGet(stream, out value.Players)) return false;
            if (!_spheresSerializer.TryGet(stream, out value.Spheres)) return false;
            if (!_boxesSerializer.TryGet(stream, out value.Boxes)) return false;
            if (!_roundedBoxesSerializer.TryGet(stream, out value.RoundedBoxes)) return false;
            if (!_trianglesSerializer.TryGet(stream, out value.Triangles)) return false;
            if (!_capsulesSerializer.TryGet(stream, out value.Capsules)) return false;
            if (!_hullsSerializer.TryGet(stream, out value.Hulls)) return false;
            if (!_terrainsSerializer.TryGet(stream, out value.Terrains)) return false;
            if (!_compoundsSerializer.TryGet(stream, out value.Compounds)) return false;
            
            if (!_hullDataSerializer.TryGet(stream, out value.HullData)) return false;
            
            if (!_localTransformsSerializer.TryGet(stream, out value.LocalTransforms)) return false;
            if (!_compoundItemsSerializer.TryGet(stream, out value.CompoundItems)) return false;

            return true;
        }

        public async IAwaiter<IEither<Unit, PhysicsShapesData>> TryGetAsync(IBinaryReadStream stream)
        {
            IEither<Unit, PhysicsShapeLinkedListData<PlayerShapeData>> getPlayersResult;
            IEither<Unit, PhysicsShapeLinkedListData<SphereShapeData>> getSpheresResult;
            IEither<Unit, PhysicsShapeLinkedListData<BoxShapeData>> getBoxesResult;
            IEither<Unit, PhysicsShapeLinkedListData<RoundedBoxShapeData>> getRoundedBoxesResult;
            IEither<Unit, PhysicsLinkedListData<TriangleShapeData>> getTrianglesResult;
            IEither<Unit, PhysicsShapeLinkedListData<CapsuleShapeData>> getCapsulesResult;
            IEither<Unit, PhysicsShapeLinkedListData<RuntimeHullShapeData>> getHullsResult;
            IEither<Unit, PhysicsLinkedListData<TerrainShapeData>> getTerrainsResult;
            IEither<Unit, PhysicsLinkedListData<CompoundShapeData>> getCompoundsResult;
            IEither<Unit, PhysicsLinkedListData<HullShapeData>> getHullDataResult;
            IEither<Unit, PhysicsLinkedListData<PhysicsShapeLocalTransformData>> getLocalTransformsResult;
            IEither<Unit, PhysicsLinkedListData<PhysicsShapeCompoundItemData>> getCompoundItemsResult;
            
            if ((getPlayersResult = await _playersSerializer.TryGetAsync(stream)).IsLeft ||
                (getSpheresResult = await _spheresSerializer.TryGetAsync(stream)).IsLeft ||
                (getBoxesResult = await _boxesSerializer.TryGetAsync(stream)).IsLeft ||
                (getRoundedBoxesResult = await _roundedBoxesSerializer.TryGetAsync(stream)).IsLeft ||
                (getTrianglesResult = await _trianglesSerializer.TryGetAsync(stream)).IsLeft ||
                (getCapsulesResult = await _capsulesSerializer.TryGetAsync(stream)).IsLeft ||
                (getHullsResult = await _hullsSerializer.TryGetAsync(stream)).IsLeft ||
                (getTerrainsResult = await _terrainsSerializer.TryGetAsync(stream)).IsLeft ||
                (getCompoundsResult = await _compoundsSerializer.TryGetAsync(stream)).IsLeft ||
                (getHullDataResult = await _hullDataSerializer.TryGetAsync(stream)).IsLeft ||
                (getLocalTransformsResult = await _localTransformsSerializer.TryGetAsync(stream)).IsLeft ||
                (getCompoundItemsResult = await _compoundItemsSerializer.TryGetAsync(stream)).IsLeft)
            {
                return new Left<Unit, PhysicsShapesData>(Unit.unit);
            }
            
            return new Right<Unit, PhysicsShapesData>(new PhysicsShapesData
            {
                Players = getPlayersResult.AsRight,
                Spheres = getSpheresResult.AsRight,
                Boxes = getBoxesResult.AsRight,
                RoundedBoxes = getRoundedBoxesResult.AsRight,
                Triangles = getTrianglesResult.AsRight,
                Capsules = getCapsulesResult.AsRight,
                Hulls = getHullsResult.AsRight,
                Terrains = getTerrainsResult.AsRight,
                Compounds = getCompoundsResult.AsRight,
                
                HullData = getHullDataResult.AsRight,
                
                LocalTransforms = getLocalTransformsResult.AsRight,
                CompoundItems = getCompoundItemsResult.AsRight
            });
        }
    }
}