using System;
using System.Numerics;
using Models.MathUtils;
using Models.Physics.Manifolds.Data;
using Models.Physics.Manifolds.Data.Output;
using Models.Physics.Manifolds.Data.TestsInput;
using Models.Physics.Manifolds.Types;
using Models.Physics.NarrowPhase.Utils;
using Models.Physics.Utils;

namespace Models.Physics.Manifolds.Tests
{
    public static class RoundedBoxTriangleManifoldTest
    {
        private const int _polygon1Length = 8;
        private const int _polygon2Length = 3;
        
        private static readonly Vector3[] _polygon1 = new Vector3[_polygon1Length];
        private static readonly Vector3[] _polygon2 = new Vector3[_polygon2Length];
    
        private const int _gjkMaxIterations = 15;
        private const int _epaMaxIterations = 15;
        
        public const int MaxContactsCount = GJKWithEPAManifoldTest.MaxContactsCount;
        
        public static readonly PhysicsManifoldDelegates.ManifoldTestDelegate<RoundedBoxTriangleManifoldTestInput> TestDelegate = Test;

        private static int Test(in RoundedBoxTriangleManifoldTestInput input, in ManifoldSettings settings, NarrowPhaseContactCache[] contactCachesPrev, NarrowPhaseContactCache[] contactCaches, in ManifoldTestOutputPointer outputPointer, in ManifoldPoint[] outputMemory)
        {
            return Test(
                input.Position1, input.Orientation1, input.Size1.X, input.Size1.Y, input.Size1.Z, input.RoundRadius1, input.SurfaceType1, settings.Margin,
                input.A2, input.B2, input.C2, input.SurfaceType2, settings.Margin,
                outputPointer.GetSpanByCapacity(outputMemory));
        }
    
        public static int Test(in Vector3 position1, in Quaternion orientation1, float width1, float height1, float length1, float roundRadius1, byte surfaceType1, float margin1, in Vector3 a2, in Vector3 b2, in Vector3 c2, byte surfaceType2, float margin2, in Span<ManifoldPoint> manifolds)
        {
            Vector3 trianglePlaneNormal = Vector3.Normalize(Vector3.Cross(b2 - a2, c2 - a2));
            float roundedBoxPositionDistanceToPlane = Vector3.Dot(position1 - a2, trianglePlaneNormal);
            
            bool isRoundedBoxPositionBehindPlane = roundedBoxPositionDistanceToPlane < -float.Epsilon;
            if (isRoundedBoxPositionBehindPlane)
            {
                return 0;
            }
        
            Span<float> transformMatrix1 = stackalloc float[16];
            Span<float> transformMatrix2 = stackalloc float[16];
        
            Matrix4x4Extension.CreateTransformMatrix(position1, orientation1, transformMatrix1);
            Matrix4x4Extension.CreateIdentity(transformMatrix2);

            Vector3 box1BasisX = Matrix4x4Extension.GetBasisX(transformMatrix1);
            Vector3 box1BasisY = Matrix4x4Extension.GetBasisY(transformMatrix1);
            Vector3 box1BasisZ = Matrix4x4Extension.GetBasisZ(transformMatrix1);

            float box1HalfWidth = width1 * 0.5f - roundRadius1;
            float box1HalfHeight = height1 * 0.5f - roundRadius1;
            float box1HalfLength = length1 * 0.5f - roundRadius1;

            Vector3[] polygon1 = _polygon1;
            polygon1[0] = position1 + box1BasisX * box1HalfWidth + box1BasisY * box1HalfHeight + box1BasisZ * box1HalfLength;
            polygon1[1] = position1 - box1BasisX * box1HalfWidth + box1BasisY * box1HalfHeight + box1BasisZ * box1HalfLength;
            polygon1[2] = position1 - box1BasisX * box1HalfWidth + box1BasisY * box1HalfHeight - box1BasisZ * box1HalfLength;
            polygon1[3] = position1 + box1BasisX * box1HalfWidth + box1BasisY * box1HalfHeight - box1BasisZ * box1HalfLength;
            polygon1[4] = position1 + box1BasisX * box1HalfWidth - box1BasisY * box1HalfHeight + box1BasisZ * box1HalfLength;
            polygon1[5] = position1 - box1BasisX * box1HalfWidth - box1BasisY * box1HalfHeight + box1BasisZ * box1HalfLength;
            polygon1[6] = position1 - box1BasisX * box1HalfWidth - box1BasisY * box1HalfHeight - box1BasisZ * box1HalfLength;
            polygon1[7] = position1 + box1BasisX * box1HalfWidth - box1BasisY * box1HalfHeight - box1BasisZ * box1HalfLength;

            Vector3[] polygon2 = _polygon2;
            polygon2[0] = a2;
            polygon2[1] = b2;
            polygon2[2] = c2;

            return GJKWithEPAManifoldTest.Test(polygon1, _polygon1Length, roundRadius1 + margin1, transformMatrix1, surfaceType1, polygon2, _polygon2Length, margin2, transformMatrix2, surfaceType2, _gjkMaxIterations, _epaMaxIterations, manifolds);
        }
    }
}