using System;
using System.Numerics;
using Models.Physics.Manifolds.Data;
using Models.Physics.Manifolds.Data.Output;
using Models.Physics.Manifolds.Data.TestsInput;
using Models.Physics.Manifolds.Types;
using Models.Physics.NarrowPhase.Utils;
using Models.Physics.Shapes.Data.Hull;
using Models.Physics.Utils;

namespace Models.Physics.Manifolds.Tests
{
    public static class PlayerHullManifoldTest
    {
        public const int MaxContactsCount = 1;
        
        public static readonly PhysicsManifoldDelegates.ManifoldTestDelegate<PlayerHullManifoldTestInput> TestDelegate = Test;

        private static int Test(in PlayerHullManifoldTestInput input, in ManifoldSettings settings, NarrowPhaseContactCache[] contactCachesPrev, NarrowPhaseContactCache[] contactCaches, in ManifoldTestOutputPointer outputPointer, in ManifoldPoint[] outputMemory)
        {
            return Test(
                input.Position1, input.Length1, input.Radius1, input.SurfaceType1, settings.<PERSON><PERSON>,
                input.Position2, input.Orientation2, input.Hull2.GetRef(), input.SurfaceType2,
                outputPointer.GetSpanByCapacity(outputMemory));
        }
        
        public static int Test(in Vector3 position1, float length1, float radius1, byte surfaceType1, float margin1, in Vector3 position2, in Quaternion orientation2, in HullShapeData hull2, byte surfaceType2, in Span<ManifoldPoint> manifolds)
        {
            return CapsuleHullManifoldTest.Test(position1, Quaternion.Identity, length1, radius1, surfaceType1, margin1, position2, orientation2, hull2, surfaceType2, MaxContactsCount, manifolds);
        }
    }
}