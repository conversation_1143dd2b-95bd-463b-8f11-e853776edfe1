using System;
using System.Runtime.CompilerServices;
using Models.Physics.Types;

namespace Models.Physics.PhysicsBodies
{
    public readonly struct PhysicsBodyId : IEquatable<PhysicsBodyId>
    {
        public readonly int Value;
        public readonly PhysicsBodyIdType Type;
        
        private const int _nullValue = -1;
        private const PhysicsBodyIdType _nullType = PhysicsBodyIdType.BakedStatic;
        
        public PhysicsBodyId(int value, PhysicsBodyIdType type)
        {
            Value = value;
            Type = type;
        }
        
        public static readonly PhysicsBodyId Null = new(_nullValue, _nullType);
        
        public bool IsValid => Value is _nullValue or >= 0 &&
                               Enum.IsDefined(typeof(PhysicsBodyIdType), Type);
        
        public bool IsNull => Value == _nullValue;
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool operator ==(PhysicsBodyId left, PhysicsBodyId right) => left.Equals(right);
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool operator !=(PhysicsBodyId left, PhysicsBodyId right) => !left.Equals(right);
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool Equals(PhysicsBodyId other)
        {
            return Value == other.Value &&
                   Type == other.Type;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool Equals(object obj)
        {
            return obj is PhysicsBodyId other &&
                   Equals(other);
        }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override int GetHashCode()
        {
            // note: from rider codegen
            unchecked
            {
                return (Value * 397) ^ (int)Type;
            }
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override string ToString()
        {
            return $"({Value}, {Enum.GetName(typeof(PhysicsBodyIdType), Type)})";
        }
    }
}