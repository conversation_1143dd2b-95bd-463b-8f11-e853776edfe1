using System;
using System.Collections.Generic;
using Models.Physics.Collections;
using Models.Physics.Collections.Data;
using Models.Physics.RegularGrid.Data;
using Models.Utils.Assertion;

namespace Models.Physics.RegularGrid
{
    public class PhysicsRegularGrid : IPhysicsRegularGrid
    {
        private const int NullPtr = -1;
    
        public PhysicsRegularGridData Build(float startX, float startY, float startZ, float sizeX, float sizeY, float sizeZ, float nodeSize, int itemsStartCapacity = 32, int linksStartCapacity = 32)
        {
            PhysicsRegularGridData data = new PhysicsRegularGridData();

            data.StartX = startX;
            data.StartY = startY;
            data.StartZ = startZ;

            data.NodeSize = nodeSize;

            float invNodeSize = 1.0f / nodeSize;
            int nodesCountX = (int)MathF.Ceiling(sizeX * invNodeSize);
            int nodesCountY = (int)MathF.Ceiling(sizeY * invNodeSize);
            int nodesCountZ = (int)MathF.Ceiling(sizeZ * invNodeSize);

            data.NodesCountX = nodesCountX;
            data.NodesCountY = nodesCountY;
            data.NodesCountZ = nodesCountZ;

            int nodesCount = nodesCountX * nodesCountY * nodesCountZ;
            PhysicsRegularGridNodeData[] nodes = new PhysicsRegularGridNodeData[nodesCount];
            
            data.Nodes = nodes;
            data.Items = PhysicsLinkedList.Build<PhysicsRegularGridItemData>(itemsStartCapacity);
            data.Links = PhysicsLinkedList.Build<PhysicsRegularGridLinkData>(linksStartCapacity);

            for (int i = 0; i < nodesCount; i++)
            {
                nodes[i].LinksPtr = NullPtr;
            }

            return data;
        }

        public int Insert(PhysicsRegularGridData data, float minX, float minY, float minZ, float maxX, float maxY, float maxZ, int userData = 0)
        {
            PhysicsLinkedListData<PhysicsRegularGridItemData> items = data.Items;
            int itemId = PhysicsLinkedList.Add(items);
            
            ref PhysicsRegularGridItemData item = ref items.Nodes[itemId];
            item.LinksPtr = NullPtr;
            item.MinX = minX;
            item.MinY = minY;
            item.MinZ = minZ;
            item.MaxX = maxX;
            item.MaxY = maxY;
            item.MaxZ = maxZ;
            item.UserData = userData;
            
            AddItemToNodes(data, ref item, itemId);

            return itemId;
        }

        public void Remove(PhysicsRegularGridData data, int id)
        {
            PhysicsLinkedListData<PhysicsRegularGridItemData> items = data.Items;
            ref PhysicsRegularGridItemData item = ref items.Nodes[id];
            
            RemoveItemFromNodes(data, ref item);
            
            PhysicsLinkedList.Remove(items, id);
        }

        public void Warp(PhysicsRegularGridData data, int id, float minX, float minY, float minZ, float maxX, float maxY, float maxZ)
        {
            PhysicsLinkedListData<PhysicsRegularGridItemData> items = data.Items;
            ref PhysicsRegularGridItemData item = ref items.Nodes[id];
        
            RemoveItemFromNodes(data, ref item);

            item.MinX = minX;
            item.MinY = minY;
            item.MinZ = minZ;
            item.MaxX = maxX;
            item.MaxY = maxY;
            item.MaxZ = maxZ;
            
            AddItemToNodes(data, ref item, id);
        }

        private void AddItemToNodes(PhysicsRegularGridData data, ref PhysicsRegularGridItemData item, int itemId)
        {
            float minX = item.MinX;
            float minY = item.MinY;
            float minZ = item.MinZ;
            float maxX = item.MaxX;
            float maxY = item.MaxY;
            float maxZ = item.MaxZ;
        
            float startX = data.StartX;
            float startY = data.StartY;
            float startZ = data.StartZ;

            int nodesCountX = data.NodesCountX;
            int nodesCountY = data.NodesCountY;
            int nodesCountZ = data.NodesCountZ;

            int nodesCountYZ = nodesCountY * nodesCountZ;

            int lastNodeIndexX = nodesCountX - 1;
            int lastNodeIndexY = nodesCountY - 1;
            int lastNodeIndexZ = nodesCountZ - 1;

            float nodeSize = data.NodeSize;
            float invNodeSize = 1.0f / nodeSize;

            int xStartIndex = (int)((minX - startX) * invNodeSize);
            int yStartIndex = (int)((minY - startY) * invNodeSize);
            int zStartIndex = (int)((minZ - startZ) * invNodeSize);

            xStartIndex = xStartIndex < 0 ? 0 : xStartIndex;
            xStartIndex = xStartIndex > lastNodeIndexX ? lastNodeIndexX : xStartIndex;
            yStartIndex = yStartIndex < 0 ? 0 : yStartIndex;
            yStartIndex = yStartIndex > lastNodeIndexY ? lastNodeIndexY : yStartIndex;
            zStartIndex = zStartIndex < 0 ? 0 : zStartIndex;
            zStartIndex = zStartIndex > lastNodeIndexZ ? lastNodeIndexZ : zStartIndex;

            int xEndIndex = (int)((maxX - startX) * invNodeSize);
            int yEndIndex = (int)((maxY - startY) * invNodeSize);
            int zEndIndex = (int)((maxZ - startZ) * invNodeSize);

            xEndIndex = xEndIndex < 0 ? 0 : xEndIndex;
            xEndIndex = xEndIndex > lastNodeIndexX ? lastNodeIndexX : xEndIndex;
            yEndIndex = yEndIndex < 0 ? 0 : yEndIndex;
            yEndIndex = yEndIndex > lastNodeIndexY ? lastNodeIndexY : yEndIndex;
            zEndIndex = zEndIndex < 0 ? 0 : zEndIndex;
            zEndIndex = zEndIndex > lastNodeIndexZ ? lastNodeIndexZ : zEndIndex;

            PhysicsRegularGridNodeData[] nodes = data.Nodes;
            PhysicsLinkedListData<PhysicsRegularGridLinkData> links = data.Links;
        
            for (int xIndex = xStartIndex; xIndex <= xEndIndex; xIndex++)
            {
                int nodeIdPartX = xIndex * nodesCountYZ;
                for (int yIndex = yStartIndex; yIndex <= yEndIndex; yIndex++)
                {
                    int nodeIdPartY = yIndex * nodesCountZ;
                    for (int zIndex = zStartIndex; zIndex <= zEndIndex; zIndex++)
                    {
                        int nodeId = nodeIdPartX + nodeIdPartY + zIndex;
                        ref PhysicsRegularGridNodeData node = ref nodes[nodeId];

                        int linkId = PhysicsLinkedList.Add(links);
                        ref PhysicsRegularGridLinkData link = ref links.Nodes[linkId];

                        link.NodePtr = nodeId;
                        link.ItemPtr = itemId;

                        int oldNodeLinksPtr = node.LinksPtr;

                        link.PrevItemInNodeLinkPtr = NullPtr;
                        link.NextItemInNodeLinkPtr = oldNodeLinksPtr;
                        if (oldNodeLinksPtr != NullPtr)
                        {
                            ref PhysicsRegularGridLinkData nextLink = ref links.Nodes[oldNodeLinksPtr];
                            nextLink.PrevItemInNodeLinkPtr = linkId;
                        }
                        node.LinksPtr = linkId;

                        int oldItemLinksPtr = item.LinksPtr;
                    
                        link.PrevNodeInItemPtr = NullPtr;
                        link.NextNodeInItemPtr = oldItemLinksPtr;
                        if (oldItemLinksPtr != NullPtr)
                        {
                            ref PhysicsRegularGridLinkData nextLink = ref links.Nodes[oldItemLinksPtr];
                            nextLink.PrevNodeInItemPtr = linkId;
                        }
                        item.LinksPtr = linkId;
                    }
                }
            }
        }
    
        private void RemoveItemFromNodes(PhysicsRegularGridData data, ref PhysicsRegularGridItemData item)
        {
            PhysicsRegularGridNodeData[] nodes = data.Nodes;
            PhysicsLinkedListData<PhysicsRegularGridLinkData> links = data.Links;
            PhysicsRegularGridLinkData[] linksNodes = links.Nodes;

            int iterator = item.LinksPtr;
            while (iterator != NullPtr)
            {
                int linkId = iterator;
                
                ref PhysicsRegularGridLinkData link = ref linksNodes[linkId];
                iterator = link.NextNodeInItemPtr;

                int prevItemInNodeLinkPtr = link.PrevItemInNodeLinkPtr;
                int nextItemInNodeLinkPtr = link.NextItemInNodeLinkPtr;
                
                if (prevItemInNodeLinkPtr != NullPtr)
                {
                    ref PhysicsRegularGridLinkData prevLink = ref linksNodes[prevItemInNodeLinkPtr];
                    prevLink.NextItemInNodeLinkPtr = nextItemInNodeLinkPtr;
                }
                if (nextItemInNodeLinkPtr != NullPtr)
                {
                    ref PhysicsRegularGridLinkData nextLink = ref linksNodes[nextItemInNodeLinkPtr];
                    nextLink.PrevItemInNodeLinkPtr = prevItemInNodeLinkPtr;
                }

                int nodeId = link.NodePtr;
                ref PhysicsRegularGridNodeData node = ref nodes[nodeId];

                if (node.LinksPtr == linkId)
                {
                    node.LinksPtr = nextItemInNodeLinkPtr;
                }
                
                PhysicsLinkedList.Remove(links, linkId);
            }

            item.LinksPtr = NullPtr;
        }

        public void RaycastQuery(PhysicsRegularGridData data, float rayFromX, float rayFromY, float rayFromZ, float rayDirectionX, float rayDirectionY, float rayDirectionZ, float rayLength, List<int> result)
        {
            if (rayLength < float.Epsilon)
            {
                return;
            }

            float rayDirectionXAbs = rayDirectionX < 0 ? -rayDirectionX : rayDirectionX;
            float rayDirectionYAbs = rayDirectionY < 0 ? -rayDirectionY : rayDirectionY;
            float rayDirectionZAbs = rayDirectionZ < 0 ? -rayDirectionZ : rayDirectionZ;

            float startX = data.StartX;
            float startY = data.StartY;
            float startZ = data.StartZ;

            int nodesCountX = data.NodesCountX;
            int nodesCountY = data.NodesCountY;
            int nodesCountZ = data.NodesCountZ;

            int nodesCountYZ = nodesCountY * nodesCountZ;

            float nodeSize = data.NodeSize;

            float sizeX = nodesCountX * nodeSize;
            float sizeY = nodesCountY * nodeSize;
            float sizeZ = nodesCountZ * nodeSize;

            float endX = startX + sizeX;
            float endY = startY + sizeY;
            float endZ = startZ + sizeZ;

            float rayToX = rayFromX + rayDirectionX * rayLength;
            float rayToY = rayFromY + rayDirectionY * rayLength;
            float rayToZ = rayFromZ + rayDirectionZ * rayLength;

            PhysicsRegularGridRaycastData raycastData = new PhysicsRegularGridRaycastData();
            raycastData.FromX = rayFromX;
            raycastData.FromY = rayFromY;
            raycastData.FromZ = rayFromZ;
            raycastData.ToX = rayToX;
            raycastData.ToY = rayToY;
            raycastData.ToZ = rayToZ;

            raycastData.MinX = startX;
            raycastData.MinY = startY;
            raycastData.MinZ = startZ;
            raycastData.MaxX = endX;
            raycastData.MaxY = endY;
            raycastData.MaxZ = endZ;

            bool isRayHitRegularGrid = RaycastAABB(ref raycastData);
            if (!isRayHitRegularGrid)
            {
                return;
            }
        
            raycastData.DirectionX = rayDirectionX;
            raycastData.DirectionY = rayDirectionY;
            raycastData.DirectionZ = rayDirectionZ;

            raycastData.Length = rayLength;
        
            bool isRayInsideRegularGrid = rayFromX >= startX && rayFromX <= endX &&
                                          rayFromY >= startY && rayFromY <= endY &&
                                          rayFromZ >= startZ && rayFromZ <= endZ &&
                                          rayToX >= startX && rayToX <= endX &&
                                          rayToY >= startY && rayToY <= endY &&
                                          rayToZ >= startZ && rayToZ <= endZ;
            if (!isRayInsideRegularGrid)
            {
                float regularGridPositionX = (startX + endX) * 0.5f;
                float regularGridPositionY = (startY + endY) * 0.5f;
                float regularGridPositionZ = (startZ + endZ) * 0.5f;
                CutRayToRegularGridSpace(ref raycastData, regularGridPositionX, regularGridPositionY, regularGridPositionZ, sizeX, sizeY, sizeZ);
            }

            float invNodeSize = 1.0f / nodeSize;

            float rayDirectionInvX = rayDirectionXAbs < float.Epsilon ? 0 : 1.0f / rayDirectionX;
            float rayDirectionInvY = rayDirectionYAbs < float.Epsilon ? 0 : 1.0f / rayDirectionY;
            float rayDirectionInvZ = rayDirectionZAbs < float.Epsilon ? 0 : 1.0f / rayDirectionZ;

            int xStep = rayDirectionX > 0 ? 1 : -1;
            int yStep = rayDirectionY > 0 ? 1 : -1;
            int zStep = rayDirectionZ > 0 ? 1 : -1;

            int targetIndexOffsetX = rayDirectionX > 0 ? 1 : 0;
            int targetIndexOffsetY = rayDirectionY > 0 ? 1 : 0;
            int targetIndexOffsetZ = rayDirectionZ > 0 ? 1 : 0;

            (int xIndex, float x) = CalculateGridCoordByPositionForRaycastQuery(raycastData.FromX, startX, nodesCountX, nodeSize, invNodeSize);
            (int yIndex, float y) = CalculateGridCoordByPositionForRaycastQuery(raycastData.FromY, startY, nodesCountY, nodeSize, invNodeSize);
            (int zIndex, float z) = CalculateGridCoordByPositionForRaycastQuery(raycastData.FromZ, startZ, nodesCountZ, nodeSize, invNodeSize);

            (int xEndIndex, _) = CalculateGridCoordByPositionForRaycastQuery(raycastData.ToX, startX, nodesCountX, nodeSize, invNodeSize);
            (int yEndIndex, _) = CalculateGridCoordByPositionForRaycastQuery(raycastData.ToY, startY, nodesCountY, nodeSize, invNodeSize);
            (int zEndIndex, _) = CalculateGridCoordByPositionForRaycastQuery(raycastData.ToZ, startZ, nodesCountZ, nodeSize, invNodeSize);

#if DEVELOP
            if (xIndex < 0 || xIndex >= nodesCountX ||
                yIndex < 0 || yIndex >= nodesCountY ||
                zIndex < 0 || zIndex >= nodesCountZ)
            {
                Assert.Condition(false, $"OnStart (xyz indices) rayFromX={rayFromX}, rayFromY={rayFromY}, rayFromZ={rayFromZ}, rayDirectionX={rayDirectionX}, rayDirectionY={rayDirectionY}, rayDirectionZ={rayDirectionZ}, rayLength={rayLength}, xIndex={xIndex}, yIndex={yIndex}, zIndex={zIndex}, nodesCountX={nodesCountX}, nodesCountY={nodesCountY}, nodesCountZ={nodesCountZ}");
            }
#endif

            PhysicsRegularGridNodeData[] nodes = data.Nodes;
            PhysicsRegularGridLinkData[] linksNodes = data.Links.Nodes;
            PhysicsRegularGridItemData[] itemsNodes = data.Items.Nodes;
        
            int nextUsedItem = NullPtr;

            float traveledDistance = 0;
            float targetDistance = raycastData.Length;
            while (traveledDistance < targetDistance)
            {
                {
#if DEVELOP
                    if (xIndex < 0 || xIndex >= nodesCountX ||
                        yIndex < 0 || yIndex >= nodesCountY ||
                        zIndex < 0 || zIndex >= nodesCountZ)
                    {
                        Assert.Condition(false, $"InProcess (xyz indices) rayFromX={rayFromX}, rayFromY={rayFromY}, rayFromZ={rayFromZ}, rayDirectionX={rayDirectionX}, rayDirectionY={rayDirectionY}, rayDirectionZ={rayDirectionZ}, rayLength={rayLength}, xIndex={xIndex}, yIndex={yIndex}, zIndex={zIndex}, nodesCountX={nodesCountX}, nodesCountY={nodesCountY}, nodesCountZ={nodesCountZ}, traveledDistance={traveledDistance}, targetDistance={targetDistance}");
                    }
#endif
                    
                    int nodeId = xIndex * nodesCountYZ + yIndex * nodesCountZ + zIndex;
                    
#if DEVELOP
                    if (nodeId < 0 || nodeId >= nodes.Length)
                    {
                        Assert.Condition(false, $"InProcess (nodeId) rayFromX={rayFromX}, rayFromY={rayFromY}, rayFromZ={rayFromZ}, rayDirectionX={rayDirectionX}, rayDirectionY={rayDirectionY}, rayDirectionZ={rayDirectionZ}, rayLength={rayLength}, nodeId={nodeId}, nodesLength={nodes.Length}, traveledDistance={traveledDistance}, targetDistance={targetDistance}");
                    }
#endif
                
                    ref PhysicsRegularGridNodeData node = ref nodes[nodeId];
                
                    int nodeLinksIterator = node.LinksPtr;
                    while (nodeLinksIterator >= 0)
                    {
                        int linkId = nodeLinksIterator;
                    
#if DEVELOP
                        if (linkId >= linksNodes.Length)
                        {
                            Assert.Condition(false, $"InProcess (linkId) rayFromX={rayFromX}, rayFromY={rayFromY}, rayFromZ={rayFromZ}, rayDirectionX={rayDirectionX}, rayDirectionY={rayDirectionY}, rayDirectionZ={rayDirectionZ}, rayLength={rayLength}, linkId={linkId}, linkNodes={linksNodes.Length}, linksCount={data.Links.Links.Count}, traveledDistance={traveledDistance}, targetDistance={targetDistance}");
                        }
#endif
                        
                        ref PhysicsRegularGridLinkData link = ref linksNodes[linkId];

                        nodeLinksIterator = link.NextItemInNodeLinkPtr;

                        int itemId = link.ItemPtr;
                    
#if DEVELOP
                        if (itemId >= itemsNodes.Length)
                        {
                            Assert.Condition(false, $"InProcess (itemId) rayFromX={rayFromX}, rayFromY={rayFromY}, rayFromZ={rayFromZ}, rayDirectionX={rayDirectionX}, rayDirectionY={rayDirectionY}, rayDirectionZ={rayDirectionZ}, rayLength={rayLength}, itemId={itemId}, itemsNodes={itemsNodes.Length}, itemsCount={data.Items.Links.Count}, traveledDistance={traveledDistance}, targetDistance={targetDistance}");
                        }
#endif
                    
                        ref PhysicsRegularGridItemData item = ref itemsNodes[itemId];

                        if (item.Used)
                        {
                            continue;
                        }
                    
                        item.Used = true;
                        item.NextUsedItem = nextUsedItem;
                        nextUsedItem = itemId;

                        raycastData.MinX = item.MinX;
                        raycastData.MinY = item.MinY;
                        raycastData.MinZ = item.MinZ;
                        raycastData.MaxX = item.MaxX;
                        raycastData.MaxY = item.MaxY;
                        raycastData.MaxZ = item.MaxZ;

                        if (RaycastAABB(ref raycastData))
                        {
                            result.Add(itemId);
                        }
                    }
                }
            
                if (xIndex == xEndIndex && yIndex == yEndIndex && zIndex == zEndIndex)
                {
                    break;
                }

                int targetIndexX = xIndex + targetIndexOffsetX;
                int targetIndexY = yIndex + targetIndexOffsetY;
                int targetIndexZ = zIndex + targetIndexOffsetZ;

                float distX = startX + targetIndexX * nodeSize - x;
                float distY = startY + targetIndexY * nodeSize - y;
                float distZ = startZ + targetIndexZ * nodeSize - z;

                float timeX = rayDirectionXAbs < float.Epsilon ? float.PositiveInfinity : distX * rayDirectionInvX;
                float timeY = rayDirectionYAbs < float.Epsilon ? float.PositiveInfinity : distY * rayDirectionInvY;
                float timeZ = rayDirectionZAbs < float.Epsilon ? float.PositiveInfinity : distZ * rayDirectionInvZ;
                
#if DEVELOP
                const float assertTimeEps = -0.001f;
                if (timeX < assertTimeEps || timeY < assertTimeEps || timeZ < assertTimeEps)
                {
                    Assert.Condition(false, $"InProcess (time) rayFromX={rayFromX}, rayFromY={rayFromY}, rayFromZ={rayFromZ}, rayDirectionX={rayDirectionX}, rayDirectionY={rayDirectionY}, rayDirectionZ={rayDirectionZ}, rayLength={rayLength}, timeX={timeX}, timeY={timeY}, timeZ={timeZ}, traveledDistance={traveledDistance}, targetDistance={targetDistance}, x={x}, y={y}, z={z} startX={startX} startY={startY} startZ={startZ} nodeSize={nodeSize} targetIndexX={targetIndexX} targetIndexY={targetIndexY} targetIndexZ={targetIndexZ} distX={distX} distY={distY} distZ={distZ} rayDirectionInvX={rayDirectionInvX} rayDirectionInvY={rayDirectionInvY} rayDirectionInvZ={rayDirectionInvZ}");
                }
#endif

                timeX = timeX < float.Epsilon ? 0 : timeX;
                timeY = timeY < float.Epsilon ? 0 : timeY;
                timeZ = timeZ < float.Epsilon ? 0 : timeZ;

                if (timeX < timeY && timeX < timeZ)
                {
                    x += rayDirectionX * timeX;
                    y += rayDirectionY * timeX;
                    z += rayDirectionZ * timeX;

                    xIndex += xStep;
                    traveledDistance += timeX;
                }
                else if (timeY < timeX && timeY < timeZ)
                {
                    x += rayDirectionX * timeY;
                    y += rayDirectionY * timeY;
                    z += rayDirectionZ * timeY;

                    yIndex += yStep;
                    traveledDistance += timeY;
                }
                else
                {
                    x += rayDirectionX * timeZ;
                    y += rayDirectionY * timeZ;
                    z += rayDirectionZ * timeZ;

                    zIndex += zStep;
                    traveledDistance += timeZ;
                }
            }
        
            while (nextUsedItem != NullPtr)
            {
                ref PhysicsRegularGridItemData item = ref itemsNodes[nextUsedItem];
            
                nextUsedItem = item.NextUsedItem;
                item.NextUsedItem = NullPtr;

                item.Used = false;
            }
        }

        private (int Index, float Position) CalculateGridCoordByPositionForRaycastQuery(float position, float startPosition, int dimensionNodesCount, float nodeSize, float invNodeSize)
        {
            int index = (int)((position - startPosition) * invNodeSize);

            index = index < 0 ? 0 : index;
            index = index >= dimensionNodesCount ? dimensionNodesCount - 1 : index;

            float indexStartPosition = startPosition + index * nodeSize;
            if (position < indexStartPosition)
            {
                position = indexStartPosition;
            }
        
            float indexEndPosition = startPosition + (index + 1) * nodeSize;
            if (position > indexEndPosition)
            {
                position = indexEndPosition;
            }

            return (index, position);
        }

        public void OverlapsQuery(PhysicsRegularGridData data, float minX, float minY, float minZ, float maxX, float maxY, float maxZ, List<int> result)
        {
            float startX = data.StartX;
            float startY = data.StartY;
            float startZ = data.StartZ;

            int nodesCountX = data.NodesCountX;
            int nodesCountY = data.NodesCountY;
            int nodesCountZ = data.NodesCountZ;

            int nodesCountYZ = nodesCountY * nodesCountZ;

            float nodeSize = data.NodeSize;
            float invNodeSize = 1.0f / nodeSize;

            float sizeX = nodesCountX * nodeSize;
            float sizeY = nodesCountY * nodeSize;
            float sizeZ = nodesCountZ * nodeSize;

            float endX = startX + sizeX;
            float endY = startY + sizeY;
            float endZ = startZ + sizeZ;

            const float borderEps = 0.1f;
        
            minX = minX <= startX ? startX + borderEps : minX;
            minY = minY <= startY ? startY + borderEps : minY;
            minZ = minZ <= startZ ? startZ + borderEps : minZ;
        
            minX = minX >= endX ? endX - borderEps : minX;
            minY = minY >= endY ? endY - borderEps : minY;
            minZ = minZ >= endZ ? endZ - borderEps : minZ;
        
            maxX = maxX <= startX ? startX + borderEps : maxX;
            maxY = maxY <= startY ? startY + borderEps : maxY;
            maxZ = maxZ <= startZ ? startZ + borderEps : maxZ;
        
            maxX = maxX >= endX ? endX - borderEps : maxX;
            maxY = maxY >= endY ? endY - borderEps : maxY;
            maxZ = maxZ >= endZ ? endZ - borderEps : maxZ;
        
            int xStartIndex = (int)((minX - startX) * invNodeSize);
            int yStartIndex = (int)((minY - startY) * invNodeSize);
            int zStartIndex = (int)((minZ - startZ) * invNodeSize);

            int xEndIndex = (int)((maxX - startX) * invNodeSize);
            int yEndIndex = (int)((maxY - startY) * invNodeSize);
            int zEndIndex = (int)((maxZ - startZ) * invNodeSize);
        
            PhysicsRegularGridNodeData[] nodes = data.Nodes;
            PhysicsRegularGridLinkData[] linksNodes = data.Links.Nodes;
            PhysicsRegularGridItemData[] itemsNodes = data.Items.Nodes;

            int nextUsedItem = NullPtr;
            for (int xIndex = xStartIndex; xIndex <= xEndIndex; xIndex++)
            {
                int nodeIdPartX = xIndex * nodesCountYZ;
                for (int yIndex = yStartIndex; yIndex <= yEndIndex; yIndex++)
                {
                    int nodeIdPartY = yIndex * nodesCountZ;
                    for (int zIndex = zStartIndex; zIndex <= zEndIndex; zIndex++)
                    {
                        int nodeId = nodeIdPartX + nodeIdPartY + zIndex;
                        ref PhysicsRegularGridNodeData node = ref nodes[nodeId];
                    
                        int nodeLinksIterator = node.LinksPtr;
                        while (nodeLinksIterator >= 0)
                        {
                            int linkId = nodeLinksIterator;
                            ref PhysicsRegularGridLinkData link = ref linksNodes[linkId];

                            nodeLinksIterator = link.NextItemInNodeLinkPtr;

                            int itemId = link.ItemPtr;
                            ref PhysicsRegularGridItemData item = ref itemsNodes[itemId];

                            if (item.Used)
                            {
                                continue;
                            }
                        
                            item.Used = true;
                            item.NextUsedItem = nextUsedItem;
                            nextUsedItem = itemId;

                            if (item.MinX <= maxX && item.MinY <= maxY && item.MinZ <= maxZ && minX <= item.MaxX && minY <= item.MaxY && minZ <= item.MaxZ)
                            {
                                result.Add(itemId);
                            }
                        }
                    }
                }
            }

            while (nextUsedItem != NullPtr)
            {
                ref PhysicsRegularGridItemData item = ref itemsNodes[nextUsedItem];
            
                nextUsedItem = item.NextUsedItem;
                item.NextUsedItem = NullPtr;

                item.Used = false;
            }
        }

        private void CutRayToRegularGridSpace(ref PhysicsRegularGridRaycastData data, float regularGridPositionX, float regularGridPositionY, float regularGridPositionZ, float regularGridSizeX, float regularGridSizeY, float regularGridSizeZ)
        {
            float rayDirectionX = data.DirectionX;
            float rayDirectionY = data.DirectionY;
            float rayDirectionZ = data.DirectionZ;
        
            float rayDirectionInRegularGridSpaceX = rayDirectionX;
            float rayDirectionInRegularGridSpaceY = rayDirectionY;
            float rayDirectionInRegularGridSpaceZ = rayDirectionZ;
        
            int normalSignX = -1;
            int normalSignY = -1;
            int normalSignZ = -1;
        
            if (rayDirectionInRegularGridSpaceX < 0)
            {
                rayDirectionInRegularGridSpaceX = -rayDirectionInRegularGridSpaceX;
                normalSignX = 1;
            }
            if (rayDirectionInRegularGridSpaceY < 0)
            {
                rayDirectionInRegularGridSpaceY = -rayDirectionInRegularGridSpaceY;
                normalSignY = 1;
            }
            if (rayDirectionInRegularGridSpaceZ < 0)
            {
                rayDirectionInRegularGridSpaceZ = -rayDirectionInRegularGridSpaceZ;
                normalSignZ = 1;
            }

            float rayDirectionInRegularGridSpaceInvX = rayDirectionInRegularGridSpaceX == 0 ? 0 : 1.0f / rayDirectionInRegularGridSpaceX;
            float rayDirectionInRegularGridSpaceInvY = rayDirectionInRegularGridSpaceY == 0 ? 0 : 1.0f / rayDirectionInRegularGridSpaceY;
            float rayDirectionInRegularGridSpaceInvZ = rayDirectionInRegularGridSpaceZ == 0 ? 0 : 1.0f / rayDirectionInRegularGridSpaceZ;
        
            float regularGridHalfSizeX = regularGridSizeX * 0.5f;
            float regularGridHalfSizeY = regularGridSizeY * 0.5f;
            float regularGridHalfSizeZ = regularGridSizeZ * 0.5f;

            float rayFromInRegularGridSpaceX = data.FromX - regularGridPositionX;
            float rayFromInRegularGridSpaceY = data.FromY - regularGridPositionY;
            float rayFromInRegularGridSpaceZ = data.FromZ - regularGridPositionZ;

            rayFromInRegularGridSpaceX = normalSignX < 0 ? rayFromInRegularGridSpaceX : -rayFromInRegularGridSpaceX;
            rayFromInRegularGridSpaceY = normalSignY < 0 ? rayFromInRegularGridSpaceY : -rayFromInRegularGridSpaceY;
            rayFromInRegularGridSpaceZ = normalSignZ < 0 ? rayFromInRegularGridSpaceZ : -rayFromInRegularGridSpaceZ;

            float low = float.NegativeInfinity;
            float high = float.PositiveInfinity;

            if (rayDirectionInRegularGridSpaceInvX != 0)
            {
                float lowTime = (-regularGridHalfSizeX - rayFromInRegularGridSpaceX) * rayDirectionInRegularGridSpaceInvX;
                float highTime = (regularGridHalfSizeX - rayFromInRegularGridSpaceX) * rayDirectionInRegularGridSpaceInvX;

                if (lowTime > low)
                {
                    low = lowTime;
                }
                if (highTime < high)
                {
                    high = highTime;
                }
            }
            if (rayDirectionInRegularGridSpaceInvY != 0)
            {
                float lowTime = (-regularGridHalfSizeY - rayFromInRegularGridSpaceY) * rayDirectionInRegularGridSpaceInvY;
                float highTime = (regularGridHalfSizeY - rayFromInRegularGridSpaceY) * rayDirectionInRegularGridSpaceInvY;

                if (lowTime > low)
                {
                    low = lowTime;
                }
                if (highTime < high)
                {
                    high = highTime;
                }
            }
            if (rayDirectionInRegularGridSpaceInvZ != 0)
            {
                float lowTime = (-regularGridHalfSizeZ - rayFromInRegularGridSpaceZ) * rayDirectionInRegularGridSpaceInvZ;
                float highTime = (regularGridHalfSizeZ - rayFromInRegularGridSpaceZ) * rayDirectionInRegularGridSpaceInvZ;

                if (lowTime > low)
                {
                    low = lowTime;
                }
                if (highTime < high)
                {
                    high = highTime;
                }
            }

            float castLength = data.Length;

            const float extraLengthEps = 0.1f;
            if (low > 0)
            {
                float extraLength = low + extraLengthEps;
                data.FromX += rayDirectionX * extraLength;
                data.FromY += rayDirectionY * extraLength;
                data.FromZ += rayDirectionZ * extraLength;
                data.Length -= extraLength;
            }
            if(high < castLength)
            {
                float extraLength = castLength - high + extraLengthEps;
                data.ToX -= rayDirectionX * extraLength;
                data.ToY -= rayDirectionY * extraLength;
                data.ToZ -= rayDirectionZ * extraLength;
                data.Length -= extraLength;
            }
        }
    
        private bool RaycastAABB(ref PhysicsRegularGridRaycastData data)
        {
            float fromX = data.FromX;
            float fromY = data.FromY;
            float fromZ = data.FromZ;
            
            float toX = data.ToX;
            float toY = data.ToY;
            float toZ = data.ToZ;
            
            float minX = data.MinX;
            float minY = data.MinY;
            float minZ = data.MinZ;
            float maxX = data.MaxX;
            float maxY = data.MaxY;
            float maxZ = data.MaxZ;
            
            float ex = maxX - minX;
            float ey = maxY - minY;
            float ez = maxZ - minZ;

            float dx = toX - fromX;
            float dy = toY - fromY;
            float dz = toZ - fromZ;

            float mx = fromX + toX - minX - maxX;
            float my = fromY + toY - minY - maxY;
            float mz = fromZ + toZ - minZ - maxZ;

            float adx = dx > 0 ? dx : -dx;
            float amx = mx > 0 ? mx : -mx;
            if (amx > ex + adx) return false;

            float ady = dy > 0 ? dy : -dy;
            float amy = my > 0 ? my : -my;
            if (amy > ey + ady) return false;

            float adz = dz > 0 ? dz : -dz;
            float amz = mz > 0 ? mz : -mz;
            if (amz > ez + adz) return false;

            float lhs = my * dz - mz * dy;
            float alhs = lhs > 0 ? lhs : -lhs;
            if (alhs > ey * adz + ez * ady) return false;
        
            lhs = mz * dx - mx * dz;
            alhs = lhs > 0 ? lhs : -lhs;
            if (alhs > ex * adz + ez * adx) return false;
        
            lhs = mx * dy - my * dx;
            alhs = lhs > 0 ? lhs : -lhs;
            if (alhs > ex * ady + ey * adx) return false;

            return true;
        }
    }
}