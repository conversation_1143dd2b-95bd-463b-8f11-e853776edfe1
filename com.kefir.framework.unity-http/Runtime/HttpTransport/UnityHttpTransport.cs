using System;
using System.Net.Http;
using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Core.Extension;
using Framework.Http.HttpTransport;
using Framework.UnityHttp.Extension;
using Framework.UnityHttp.HttpHeaders;
using UnityEngine.Networking;

namespace Framework.UnityHttp.HttpTransport
{
    public class UnityHttpTransport : IHttpTransport
    {
        private readonly TimeSpan _timeout;

        public UnityHttpTransport(TimeSpan timeout)
        {
            _timeout = timeout;
        }

        public IAwaiter<IEither<string, HttpResponse>> Send(HttpMethod method, Uri uri)
        {
            UnityWebRequest request = BuildRequest(method, uri);
            return Send(request);
        }

        public IAwaiter<IEither<string, HttpResponse>> Send(HttpMethod method, Uri uri, (string Key, string Value)[] headers)
        {
            UnityWebRequest request = BuildRequest(method, uri);
            AddHeaders(request, headers);
            return Send(request);
        }

        public IAwaiter<IEither<string, HttpResponse>> Send(HttpMethod method, Uri uri, (string Key, string Value)[] headers, byte[] content)
        {
            UnityWebRequest request = BuildRequest(method, uri);
            AddHeaders(request, headers);
            SetContent(request, content);
            return Send(request);
        }
        
        private UnityWebRequest BuildRequest(HttpMethod method, Uri uri)
        {
            UnityWebRequest request = new UnityWebRequest(uri, method.Method);
            request.downloadHandler = new DownloadHandlerBuffer();
            request.timeout = (int)_timeout.TotalSeconds;
            return request;
        }

        private void AddHeaders(UnityWebRequest request, (string Key, string Value)[] headers)
        {
            foreach (var header in headers)
            {
                request.SetRequestHeader(header.Key, header.Value);
            }
        }
        
        private void SetContent(UnityWebRequest request, byte[] content)
        {
            request.uploadHandler = new UploadHandlerRaw(content);
        }
        
        private IAwaiter<IEither<string, HttpResponse>> Send(UnityWebRequest request)
        {
            var deferred = new Awaiter<IEither<string, HttpResponse>>();
            SendAsync(request, deferred);
            return deferred;
        }

        private async void SendAsync(UnityWebRequest request, IDeferred<IEither<string, HttpResponse>> deferred)
        {
            await request.SendWebRequest();

            var responseCode = request.responseCode;
            if (responseCode > 0)
            {
                deferred.Right(new HttpResponse((int)responseCode, request.downloadHandler.data, new UnityHttpHeaders(request.GetResponseHeaders())));
            }
            else
            {
                deferred.Left(request.error);
            }
            
            request.Dispose();
        }
    }
}
