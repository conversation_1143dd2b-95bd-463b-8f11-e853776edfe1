using System.Collections.Generic;
using System.Numerics;
using Framework.Ecs.System;
using Framework.Ecs.Tick;
using Models.Models.BreakableColliderModel;
using Models.Models.BreakableColliderSetModel;
using Models.Models.BreakeColliderModel;
using Models.Models.CharacterValidationModel;
using Models.Models.PhysicsBodiesModel;
using Models.Models.PhysicsSceneModel;
using Models.Physics.PhysicsBodies;
using Models.References.BreakableCollider;
using Models.Utils.Extensions;

namespace Server.Systems
{
    public class ColliderBreakingSystem : System<(int id, PhysicsBodyId bodyId, IBreakColliderModel breakColliderModel, ICharacterValidationModel validationModel)>
    {
        private readonly IReadOnlyDictionary<int, IBreakableColliderModel> _breakableColliderModels;
        private readonly IPhysicsBodiesModel _physicsBodiesModel;
        private readonly IPhysicsSceneModel _physicsSceneModel;

        public ColliderBreakingSystem(IReadOnlyDictionary<int, IBreakableColliderModel> breakableColliderModels, IPhysicsBodiesModel physicsBodiesModel, IPhysicsSceneModel physicsSceneModel)
        {
            _breakableColliderModels = breakableColliderModels;
            _physicsBodiesModel = physicsBodiesModel;
            _physicsSceneModel = physicsSceneModel;
        }

        protected override void Tick(ITick tick)
        {
            long now = tick.Ts;
            for (int i = 0; i < _count; i++)
            {
                ref var component = ref _components[i];

                var id = component.id;
                var bodyId = component.bodyId;
                var breakColliderModel = component.breakColliderModel;
                var validationModel = component.validationModel;

                if (!breakColliderModel.IsActive)
                {
                    continue;
                }

                int colliderEntityId = breakColliderModel.EntityId;
            
                if (validationModel.CanPerformIdleActions()
                    && now >= breakColliderModel.HitTs
                    && _breakableColliderModels.TryGetValue(colliderEntityId, out IBreakableColliderModel colliderModel)
                    && _physicsBodiesModel.TryGetBreakableColliderSet(colliderModel.BodyId, out IBreakableColliderSetModel breakableColliderSetModel)
                    && BreakableColliderExtensions.TryGetSetBreakableColliderDescription(breakableColliderSetModel, colliderModel.BodyId, out var breakableCollider)
                    && breakableCollider.IsBreakableByShotOrMelee)
                {
                    Vector3 hitSourcePosition = _physicsSceneModel.GetBodyPosition(bodyId);
                    Vector3 hitPosition = _physicsSceneModel.GetBodyPosition(colliderModel.BodyId);
                    
                    BreakableColliderExtensions.TryHitBreakableCollider(_physicsSceneModel, breakableColliderSetModel, id, colliderModel.BodyId, hitSourcePosition, hitPosition, BreakableColliderHitTypeDescription.MeleeAttack);
                }
            }
        }
    }
}