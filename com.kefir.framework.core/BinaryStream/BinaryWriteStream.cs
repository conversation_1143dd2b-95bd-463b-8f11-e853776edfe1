#define BINARY_STREAM_ARGUMENTS_CHECK

using System;

namespace Framework.Core.BinaryStream
{
    public class BinaryWriteStream : IBinaryWriteStream
    {
        private const int _bufferExtensionPolicy = 2;

        private byte[] _buffer;

        private int _curBitsBufferBytePos;
        private int _curBytePos;

        private int _curBitPos;

        private ulong _bitsBuffer;

        public BinaryWriteStream(int capacity = 2048)
        {
#if BINARY_STREAM_ARGUMENTS_CHECK
            if (capacity <= 0) throw new ArgumentOutOfRangeException(nameof(capacity));
#endif
            _buffer = new byte[capacity];
        }

        public void WriteBool(bool value)
        {
            WriteInternal(value ? 1UL : 0UL, 1);
        }

        public void WriteByte(byte value, byte minValue, byte maxValue)
        {
#if BINARY_STREAM_ARGUMENTS_CHECK
            if (minValue >= maxValue) throw new ArgumentException($"Invalid range values: min ({minValue}) max ({maxValue})");

            if (value < minValue || value > maxValue) throw new ArgumentOutOfRangeException($"Value {value} out of range [{minValue}, {maxValue}]");
#endif

            var diff = (byte) (maxValue - minValue);

            var convertedValue = (byte) (value - minValue);

            var bitCount = BinaryStreamUtil.BitSize(diff);

            WriteInternal(convertedValue, bitCount);
        }

        public void WriteByte(byte value)
        {
            WriteInternal(value, sizeof(byte) * BinaryStreamUtil.BitsInByteCount);
        }

        public void WriteShort(short value, short minValue, short maxValue)
        {
#if BINARY_STREAM_ARGUMENTS_CHECK
            if (minValue >= maxValue) throw new ArgumentException($"Invalid range values: min ({minValue}) max ({maxValue})");

            if (value < minValue || value > maxValue) throw new ArgumentOutOfRangeException($"Value {value} out of range [{minValue}, {maxValue}]");
#endif

            var diff = (ushort)(maxValue - minValue);

            var convertedValue = (ushort)(value - minValue);
            var bitCount = BinaryStreamUtil.BitSize(diff);

            WriteInternal(convertedValue, bitCount);
        }

        public void WriteShort(short value)
        {
            WriteInternal((ushort)value, sizeof(ushort) * BinaryStreamUtil.BitsInByteCount);
        }

        public void WriteInt(int value, int minValue, int maxValue)
        {
#if BINARY_STREAM_ARGUMENTS_CHECK
            if (minValue >= maxValue) throw new ArgumentException($"Invalid range values: min ({minValue}) max ({maxValue})");

            if (value < minValue || value > maxValue) throw new ArgumentOutOfRangeException($"Value {value} out of range [{minValue}, {maxValue}]");
#endif

            var diff = (uint)(maxValue - minValue);

            var convertedValue = (uint)(value - minValue);
            var bitCount = BinaryStreamUtil.BitSize(diff);

            WriteInternal(convertedValue, bitCount);
        }

        public void WriteInt(int value)
        {
            WriteInternal((uint)value, sizeof(uint) * BinaryStreamUtil.BitsInByteCount);
        }

        public void WriteLong(long value, long minValue, long maxValue)
        {
#if BINARY_STREAM_ARGUMENTS_CHECK
            if (minValue >= maxValue) throw new ArgumentException($"Invalid range values: min ({minValue}) max ({maxValue})");

            if (value < minValue || value > maxValue) throw new ArgumentOutOfRangeException($"Value {value} out of range [{minValue}, {maxValue}]");
#endif

            var diff = (ulong)(maxValue - minValue);

            var convertedValue = (ulong)(value - minValue);
            var bitCount = BinaryStreamUtil.BitSize(diff);

            WriteInternal(convertedValue, bitCount);
        }

        public void WriteLong(long value)
        {
            WriteInternal((ulong)value, sizeof(ulong) * BinaryStreamUtil.BitsInByteCount);
        }

        public void WriteFloat(float value)
        {
            var float32Value = BitConverter.SingleToInt32Bits(value);
            WriteInternal((uint)float32Value, BinaryStreamUtil.FloatBitCount);
        }

        public void WriteDouble(double value)
        {
            var double64Value = BitConverter.DoubleToInt64Bits(value);
            WriteInternal((ulong)double64Value, BinaryStreamUtil.DoubleBitCount);
        }

        public void WriteByteArray(byte[] value, int maxLength)
        {
#if BINARY_STREAM_ARGUMENTS_CHECK
            if (maxLength <= 0) throw new ArgumentOutOfRangeException($"MaxLength ({maxLength}) is zero of negative");

            if (value == null) throw new ArgumentNullException(nameof(value));

            if (value.Length > maxLength) throw new ArgumentOutOfRangeException($"Value length ({value.Length}) is greater than maxLength ({maxLength})");
#endif

            var bitCount = BinaryStreamUtil.BitSize((uint)maxLength);

            WriteInternal((ulong)value.Length, bitCount);

            var bytePosAfterBitsLastBuffer = _curBitsBufferBytePos + BinaryStreamUtil.BitsBufferSizeInBytes;

            var beginWritePos = Max(_curBytePos, bytePosAfterBitsLastBuffer);

            var bytePosAfterWrite = beginWritePos + value.Length;

            EnsureBufferSpace(bytePosAfterWrite);

            Buffer.BlockCopy(value, 0, _buffer, beginWritePos, value.Length);

            _curBytePos = bytePosAfterWrite;
        }

        public void WriteByteArray(byte[] value)
        {
            WriteByteArray(value, BinaryStreamUtil.MaxByteArrayLength);
        }

        public void WriteString(string value, int maxLength)
        {
#if BINARY_STREAM_ARGUMENTS_CHECK
            if (maxLength <= 0 || maxLength > BinaryStreamUtil.MaxStringLength) throw new ArgumentOutOfRangeException($"MaxLength {maxLength} is out of range [1, {BinaryStreamUtil.MaxStringLength}]");

            if (value == null) throw new ArgumentNullException(nameof(value));

            if (value.Length > maxLength) throw new ArgumentOutOfRangeException($"Value length {value.Length} is greater than maxLength {maxLength}");
#endif
            var encoding = BinaryStreamUtil.StringEncoding;

            var bitCount = BinaryStreamUtil.BitSize((uint)(maxLength * BinaryStreamUtil.MaxBytesPerCharacter));

            var stringSizeInBytes = encoding.GetByteCount(value);

            WriteInternal((ulong)stringSizeInBytes, bitCount);

            var bytePosAfterBitsLastBuffer = _curBitsBufferBytePos + BinaryStreamUtil.BitsBufferSizeInBytes;

            var beginWritePos = Max(_curBytePos, bytePosAfterBitsLastBuffer);

            var bytePosAfterWrite = beginWritePos + stringSizeInBytes;

            EnsureBufferSpace(bytePosAfterWrite);

            encoding.GetBytes(value, 0, value.Length, _buffer, beginWritePos);

            _curBytePos = bytePosAfterWrite;
        }

        public void WriteString(string value)
        {
            WriteString(value, BinaryStreamUtil.MaxStringLength);
        }

        public byte[] ToArray()
        {
            _bitsBuffer &= BinaryStreamUtil.GetMask(_curBitPos);

            var bufferTotalLength = _curBitsBufferBytePos;

            var needToWriteBitsBufferByteCount = 0;

            if (_curBitPos > 0)
            {
                var setBitCount = _curBitPos;

                //Equal with (positive only): setBitCount / BinaryStreamUtil.BitsInByteCount
                var fullByteCount = setBitCount >> BinaryStreamUtil.BitsForByteShift;
                //Equal with (positive only): setBitCount % BinaryStreamUtil.BitsInByteCount > 0 ? 1 : 0
                var notFullByteCount = (setBitCount - (fullByteCount << BinaryStreamUtil.BitsForByteShift) + (BinaryStreamUtil.BitsInByteCount - 1)) >> BinaryStreamUtil.BitsForByteShift;

                needToWriteBitsBufferByteCount = fullByteCount + notFullByteCount;

                bufferTotalLength += needToWriteBitsBufferByteCount;
            }

            if (_curBitsBufferBytePos < _curBytePos)
            {
                var lastBufferWritePos = _curBitsBufferBytePos + BinaryStreamUtil.BitsBufferSizeInBytes;
                var lastBufferLength = _curBytePos - lastBufferWritePos;

                var newArr = new byte[_curBytePos];

                Buffer.BlockCopy(_buffer, 0, newArr, 0, _curBitsBufferBytePos);

                for (var i = 0; i < needToWriteBitsBufferByteCount; i++)
                {
                    newArr[_curBitsBufferBytePos + i] = (byte)(_bitsBuffer >> BinaryStreamUtil.BitsInByteCount * i);
                }

                Buffer.BlockCopy(_buffer, lastBufferWritePos, newArr, _curBitsBufferBytePos + BinaryStreamUtil.BitsBufferSizeInBytes, lastBufferLength);

                return newArr;
            }
            else
            {
                var newArr = new byte[bufferTotalLength];

                Buffer.BlockCopy(_buffer, 0, newArr, 0, _curBitsBufferBytePos);

                for (var i = 0; i < needToWriteBitsBufferByteCount; i++)
                {
                    newArr[_curBitsBufferBytePos + i] = (byte)(_bitsBuffer >> BinaryStreamUtil.BitsInByteCount * i);
                }

                return newArr;
            }
        }

        public void Reset()
        {
            _curBitsBufferBytePos = 0;
            _curBytePos = 0;
            _curBitPos = 0;
            _bitsBuffer = 0;
        }

        private void WriteInternal(ulong value, byte bitCount)
        {
            var remainingSpaceInBuffer = BinaryStreamUtil.BitsBufferSize - _curBitPos;

            if (remainingSpaceInBuffer >= bitCount)
            {
                _bitsBuffer |= value << _curBitPos;

                _curBitPos += bitCount;
            }
            else
            {
                _bitsBuffer |= (value << _curBitPos) * (ulong)BinaryStreamUtil.Sign_0_1(remainingSpaceInBuffer);

                WriteBitsBufferToStreamBuffer();

                _bitsBuffer = value >> remainingSpaceInBuffer;

                _curBitPos = bitCount - remainingSpaceInBuffer;
            }
        }

        private void WriteBitsBufferToStreamBuffer()
        {
            EnsureBufferSpace(_curBitsBufferBytePos + BinaryStreamUtil.BitsBufferSizeInBytes);

            _buffer[_curBitsBufferBytePos++] = (byte)_bitsBuffer;
            _buffer[_curBitsBufferBytePos++] = (byte)(_bitsBuffer >> BinaryStreamUtil.BitsInByteCount);
            _buffer[_curBitsBufferBytePos++] = (byte)(_bitsBuffer >> BinaryStreamUtil.BitsInByteCount * 2);
            _buffer[_curBitsBufferBytePos++] = (byte)(_bitsBuffer >> BinaryStreamUtil.BitsInByteCount * 3);
            _buffer[_curBitsBufferBytePos++] = (byte)(_bitsBuffer >> BinaryStreamUtil.BitsInByteCount * 4);
            _buffer[_curBitsBufferBytePos++] = (byte)(_bitsBuffer >> BinaryStreamUtil.BitsInByteCount * 5);
            _buffer[_curBitsBufferBytePos++] = (byte)(_bitsBuffer >> BinaryStreamUtil.BitsInByteCount * 6);
            _buffer[_curBitsBufferBytePos++] = (byte)(_bitsBuffer >> BinaryStreamUtil.BitsInByteCount * 7);

            _curBytePos = _curBitsBufferBytePos = Max(_curBitsBufferBytePos, _curBytePos);
        }

        private static int Max(int x, int y)
        {
            return x > y ? x : y;
        }

        private void EnsureBufferSpace(int amount)
        {
            if (amount > _buffer.Length)
            {
                var newSize = _buffer.Length * _bufferExtensionPolicy;

                if (newSize < amount) newSize = amount;

                Array.Resize(ref _buffer, newSize);
            }
        }

        public int BitsCount
        {
            get
            {
                if (_curBytePos > _curBitsBufferBytePos)
                {
                    return _curBytePos * BinaryStreamUtil.BitsInByteCount - BinaryStreamUtil.BitsBufferSize + _curBitPos;
                }

                return _curBitsBufferBytePos * BinaryStreamUtil.BitsInByteCount + _curBitPos;
            }
        }
    }
}
