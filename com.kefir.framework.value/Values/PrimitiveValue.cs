using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;

namespace Framework.Value.Values
{
    public abstract class PrimitiveValue<T> : Value, IPrimitiveValue<T> where T : notnull
    {
        protected readonly T _value;

        protected PrimitiveValue(T value)
        {
            _value = value;
        }

        public override IReadOnlyList<IValue> AsList() => throw new InvalidCastException();

        public override bool TryAsList([MaybeNullWhen(false)] out IReadOnlyList<IValue> values)
        {
            values = null;
            return false;
        }

        public override IEnumerable<KeyValuePair<string, IValue>> AsDictionary() => throw new InvalidCastException();

        public override IEnumerable<KeyValuePair<string, IValue>> TryAsDictionary()
        {
            yield break;
        }

        public override IEnumerable<KeyValuePair<IValue, IValue>> AsMap() => throw new InvalidCastException();

        public override IEnumerable<KeyValuePair<IValue, IValue>> TryAsMap()
        {
            yield break;
        }

        public override IValue this[string key] => throw new InvalidCastException();

        public override IValue this[IValue key] => throw new InvalidCastException();

        public override IValue TryGetNode(string key)
        {
            return new NullValue();
        }

        public override IValue TryGetNode(IValue key)
        {
            return new NullValue();
        }

        public override bool TryGetValue(string key, out IValue value)
        {
            throw new InvalidCastException();
        }

        public override bool TryGetValue(IValue key, out IValue value)
        {
            throw new InvalidCastException();
        }

        public override bool Contains(string key)
        {
            throw new InvalidCastException();
        }

        public override bool Contains(IValue key)
        {
            throw new InvalidCastException();
        }

        public override IEnumerable<string> Keys => throw new InvalidCastException();

        public override int Count => throw new InvalidCastException();

        public override bool IsNull => false;
        public override bool IsDictionary => false;

        public override string ToString()
        {
            return _value.ToString();
        }

        public T Value => _value;
    }
}
