using System;
using System.Collections.Specialized;
using System.Diagnostics.CodeAnalysis;
using System.Net.Http;
using System.Text.Json;
using System.Text.Json.Serialization.Metadata;
using System.Web;
using Framework.Async.Awaiter;
using Framework.Core.Either;
using Framework.Http.HttpTransport;
using Framework.Http.Response;

namespace Framework.Http.BtlHttpProtocol
{
    using Context = Context.Context;

    public class BtlHttpProtocol : IBtlHttpProtocol
    {
        private const string _invalidContent = "invalid_content";

        private readonly IHttpTransport _transport;

        public BtlHttpProtocol(IHttpTransport transport)
        {
            _transport = transport;
        }

        public IAwaiter<IEither<string, GameResponse>> Channel(string url, string uid, string token)
        {
            var uri = BuildUri(url, new[] { ("method", "channel"), ("uid", uid), ("token", token) });
            return Await(_transport.Send(HttpMethod.Get, uri), Context.Default.GameResponse);
        }

        private static async IAwaiter<IEither<string, T>> Await<T>(IAwaiter<IEither<string, HttpResponse>> awaiter, JsonTypeInfo<T> jsonTypeInfo)
        {
            var result = await awaiter;
            if (result.IsRight)
            {
                HttpResponse response = result.AsRight;
                int statusCode = response.StatusCode;
                switch (statusCode)
                {
                    case 200:
                        return ReceiveResponse(response.Data, jsonTypeInfo);
                    case 400:
                        return ReceiveError<T>(response.Data);
                    default:
                        return new Left<string, T>(statusCode.ToString());
                }
            }

            return new Left<string, T>(result.AsLeft);
        }

        private static IEither<string, T> ReceiveResponse<T>(byte[] data, JsonTypeInfo<T> jsonTypeInfo)
        {
            return TryJsonDeserialize(data, jsonTypeInfo, out var value) ? new Right<string, T>(value) : new Left<string, T>(_invalidContent);
        }

        private static IEither<string, T> ReceiveError<T>(byte[] data)
        {
            return new Left<string, T>(TryJsonDeserialize(data, Context.Default.ErrorMessage, out var value) ? value.Error : _invalidContent);
        }

        private static Uri BuildUri(string url, (string Key, string Value)[] arguments)
        {
            UriBuilder builder = new UriBuilder(url);
            NameValueCollection query = HttpUtility.ParseQueryString(builder.Query);

            foreach (var pair in arguments)
            {
                query.Add(pair.Key, pair.Value);
            }

            builder.Query = query.ToString();

            return builder.Uri;
        }

        private static bool TryJsonDeserialize<T>(byte[] data, JsonTypeInfo<T> jsonTypeInfo, [MaybeNullWhen(false)] out T result)
        {
            try
            {
                result = JsonSerializer.Deserialize(data, jsonTypeInfo)!;
                return true;
            }
            catch (JsonException)
            {
                result = default;
                return false;
            }
        }
    }
}